Title: Graceful Shutdown :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/web/graceful-shutdown.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/web/Graceful_Shutdown_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/web/Graceful_Shutdown_Spring_Boot.png
crawled_at: 2025-06-04T15:39:58.607012
---
Search CTRL + k

### Graceful Shutdown

  * Rejecting Requests During the Grace Period
  * Disabling Graceful Shutdown



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/web/graceful-shutdown.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Web](index.html)
  * [Graceful Shutdown](graceful-shutdown.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/web/graceful-shutdown.html)!  
---|---  
  
# Graceful Shutdown

### Graceful Shutdown

  * Rejecting Requests During the Grace Period
  * Disabling Graceful Shutdown



Graceful shutdown is enabled by default with all four embedded web servers (Jetty, Reactor Netty, Tomcat, and Undertow) and with both reactive and servlet-based web applications. It occurs as part of closing the application context and is performed in the earliest phase of stopping [`SmartLifecycle`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/SmartLifecycle.html) beans. This stop processing uses a timeout which provides a grace period during which existing requests will be allowed to complete but no new requests will be permitted.

To configure the timeout period, configure the `spring.lifecycle.timeout-per-shutdown-phase` property, as shown in the following example:

  * Properties

  * YAML



    
    
    spring.lifecycle.timeout-per-shutdown-phase=20s
    
    Copied!
    
    
    spring:
      lifecycle:
        timeout-per-shutdown-phase: "20s"
    
    Copied!

__ |  Shutdown in your IDE may be immediate rather than graceful if it does not send a proper `SIGTERM` signal. See the documentation of your IDE for more details.   
---|---  
  
## Rejecting Requests During the Grace Period

The exact way in which new requests are not permitted varies depending on the web server that is being used. Implementations may stop accepting requests at the network layer, or they may return a response with a specific HTTP status code or HTTP header. The use of persistent connections can also change the way that requests stop being accepted.

__ |  To learn more about the specific method used with your web server, see the `shutDownGracefully` API documentation for [`TomcatWebServer.shutDownGracefully(GracefulShutdownCallback)`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/tomcat/TomcatWebServer.html#shutDownGracefully\(org.springframework.boot.web.server.GracefulShutdownCallback\)), [`NettyWebServer.shutDownGracefully(GracefulShutdownCallback)`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/netty/NettyWebServer.html#shutDownGracefully\(org.springframework.boot.web.server.GracefulShutdownCallback\)), [`JettyWebServer.shutDownGracefully(GracefulShutdownCallback)`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/jetty/JettyWebServer.html#shutDownGracefully\(org.springframework.boot.web.server.GracefulShutdownCallback\)) or [`UndertowWebServer.shutDownGracefully(GracefulShutdownCallback)`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/undertow/UndertowWebServer.html#shutDownGracefully\(org.springframework.boot.web.server.GracefulShutdownCallback\)).   
---|---  
  
Jetty, Reactor Netty, and Tomcat will stop accepting new requests at the network layer. Undertow will accept new connections but respond immediately with a service unavailable (503) response.

## Disabling Graceful Shutdown

To disable graceful shutdown, configure the `server.shutdown` property, as shown in the following example:

  * Properties

  * YAML



    
    
    server.shutdown=immediate
    
    Copied!
    
    
    server:
      shutdown: "immediate"
    
    Copied!

[Reactive Web Applications](reactive.html) [Spring Security](spring-security.html)
---
