Title: Testing Spring Applications :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/testing/spring-applications.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/testing/Testing_Spring_Applications_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/testing/Testing_Spring_Applications_Spring_Boot.png
crawled_at: 2025-06-04T15:40:54.952310
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/testing/spring-applications.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Testing](index.html)
  * [Testing Spring Applications](spring-applications.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/testing/spring-applications.html)!  
---|---  
  
# Testing Spring Applications

One of the major advantages of dependency injection is that it should make your code easier to unit test. You can instantiate objects by using the `new` operator without even involving Spring. You can also use _mock objects_ instead of real dependencies.

Often, you need to move beyond unit testing and start integration testing (with a Spring [`ApplicationContext`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html)). It is useful to be able to perform integration testing without requiring deployment of your application or needing to connect to other infrastructure.

The Spring Framework includes a dedicated test module for such integration testing. You can declare a dependency directly to `org.springframework:spring-test` or use the `spring-boot-starter-test` starter to pull it in transitively.

If you have not used the `spring-test` module before, you should start by reading the [relevant section](https://docs.spring.io/spring-framework/reference/6.2/testing.html) of the Spring Framework reference documentation.

[Test Scope Dependencies](test-scope-dependencies.html) [Testing Spring Boot Applications](spring-boot-applications.html)
---
