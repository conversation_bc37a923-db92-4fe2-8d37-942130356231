Title: Monitoring and Management Over HTTP :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/actuator/monitoring.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/actuator/Monitoring_and_Management_Over_HTTP_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/actuator/Monitoring_and_Management_Over_HTTP_Spring_Boot.png
crawled_at: 2025-06-04T15:38:14.594449
---
Search CTRL + k

### Monitoring and Management Over HTTP

  * Customizing the Management Endpoint Paths
  * Customizing the Management Server Port
  * Configuring Management-specific SSL
  * Customizing the Management Server Address
  * Disabling HTTP Endpoints



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/actuator/monitoring.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Production-ready Features](index.html)
  * [Monitoring and Management Over HTTP](monitoring.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/actuator/monitoring.html)!  
---|---  
  
# Monitoring and Management Over HTTP

### Monitoring and Management Over HTTP

  * Customizing the Management Endpoint Paths
  * Customizing the Management Server Port
  * Configuring Management-specific SSL
  * Customizing the Management Server Address
  * Disabling HTTP Endpoints



If you are developing a web application, Spring Boot Actuator auto-configures all enabled endpoints to be exposed over HTTP. The default convention is to use the `id` of the endpoint with a prefix of `/actuator` as the URL path. For example, `health` is exposed as `/actuator/health`.

__ |  Actuator is supported natively with Spring MVC, Spring WebFlux, and Jersey. If both Jersey and Spring MVC are available, Spring MVC is used.   
---|---  
  
__ |  Jackson is a required dependency in order to get the correct JSON responses as documented in the [API documentation](../../api/rest/actuator/index.html).   
---|---  
  
## Customizing the Management Endpoint Paths

Sometimes, it is useful to customize the prefix for the management endpoints. For example, your application might already use `/actuator` for another purpose. You can use the `management.endpoints.web.base-path` property to change the prefix for your management endpoint, as the following example shows:

  * Properties

  * YAML



    
    
    management.endpoints.web.base-path=/manage
    
    Copied!
    
    
    management:
      endpoints:
        web:
          base-path: "/manage"
    
    Copied!

The preceding `application.properties` example changes the endpoint from `/actuator/{id}` to `/manage/{id}` (for example, `/manage/info`).

__ |  Unless the management port has been configured to expose endpoints by using a different HTTP port, `management.endpoints.web.base-path` is relative to `server.servlet.context-path` (for servlet web applications) or `spring.webflux.base-path` (for reactive web applications). If `management.server.port` is configured, `management.endpoints.web.base-path` is relative to `management.server.base-path`.   
---|---  
  
If you want to map endpoints to a different path, you can use the `management.endpoints.web.path-mapping` property.

The following example remaps `/actuator/health` to `/healthcheck`:

  * Properties

  * YAML



    
    
    management.endpoints.web.base-path=/
    management.endpoints.web.path-mapping.health=healthcheck
    
    Copied!
    
    
    management:
      endpoints:
        web:
          base-path: "/"
          path-mapping:
            health: "healthcheck"
    
    Copied!

## Customizing the Management Server Port

Exposing management endpoints by using the default HTTP port is a sensible choice for cloud-based deployments. If, however, your application runs inside your own data center, you may prefer to expose endpoints by using a different HTTP port.

You can set the `management.server.port` property to change the HTTP port, as the following example shows:

  * Properties

  * YAML



    
    
    management.server.port=8081
    
    Copied!
    
    
    management:
      server:
        port: 8081
    
    Copied!

__ |  On Cloud Foundry, by default, applications receive requests only on port 8080 for both HTTP and TCP routing. If you want to use a custom management port on Cloud Foundry, you need to explicitly set up the application’s routes to forward traffic to the custom port.   
---|---  
  
## Configuring Management-specific SSL

When configured to use a custom port, you can also configure the management server with its own SSL by using the various `management.server.ssl.*` properties. For example, doing so lets a management server be available over HTTP while the main application uses HTTPS, as the following property settings show:

  * Properties

  * YAML



    
    
    server.port=8443
    server.ssl.enabled=true
    server.ssl.key-store=classpath:store.jks
    server.ssl.key-password=secret
    management.server.port=8080
    management.server.ssl.enabled=false
    
    Copied!
    
    
    server:
      port: 8443
      ssl:
        enabled: true
        key-store: "classpath:store.jks"
        key-password: "secret"
    management:
      server:
        port: 8080
        ssl:
          enabled: false
    
    Copied!

Alternatively, both the main server and the management server can use SSL but with different key stores, as follows:

  * Properties

  * YAML



    
    
    server.port=8443
    server.ssl.enabled=true
    server.ssl.key-store=classpath:main.jks
    server.ssl.key-password=secret
    management.server.port=8080
    management.server.ssl.enabled=true
    management.server.ssl.key-store=classpath:management.jks
    management.server.ssl.key-password=secret
    
    Copied!
    
    
    server:
      port: 8443
      ssl:
        enabled: true
        key-store: "classpath:main.jks"
        key-password: "secret"
    management:
      server:
        port: 8080
        ssl:
          enabled: true
          key-store: "classpath:management.jks"
          key-password: "secret"
    
    Copied!

## Customizing the Management Server Address

You can customize the address on which the management endpoints are available by setting the `management.server.address` property. Doing so can be useful if you want to listen only on an internal or ops-facing network or to listen only for connections from `localhost`.

__ |  You can listen on a different address only when the port differs from the main server port.   
---|---  
  
The following example `application.properties` does not allow remote management connections:

  * Properties

  * YAML



    
    
    management.server.port=8081
    management.server.address=127.0.0.1
    
    Copied!
    
    
    management:
      server:
        port: 8081
        address: "127.0.0.1"
    
    Copied!

## Disabling HTTP Endpoints

If you do not want to expose endpoints over HTTP, you can set the management port to `-1`, as the following example shows:

  * Properties

  * YAML



    
    
    management.server.port=-1
    
    Copied!
    
    
    management:
      server:
        port: -1
    
    Copied!

You can also achieve this by using the `management.endpoints.web.exposure.exclude` property, as the following example shows:

  * Properties

  * YAML



    
    
    management.endpoints.web.exposure.exclude=*
    
    Copied!
    
    
    management:
      endpoints:
        web:
          exposure:
            exclude: "*"
    
    Copied!

[Endpoints](endpoints.html) [Monitoring and Management over JMX](jmx.html)
---
