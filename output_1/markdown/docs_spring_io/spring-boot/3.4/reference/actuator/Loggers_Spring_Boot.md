Title: Loggers :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/actuator/loggers.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/actuator/Loggers_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/actuator/Loggers_Spring_Boot.png
crawled_at: 2025-06-04T15:49:26.807650
---
Search CTRL + k

### Loggers

  * Configure a Logger
  * OpenTelemetry



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/actuator/loggers.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Production-ready Features](index.html)
  * [Loggers](loggers.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/actuator/loggers.html)!  
---|---  
  
# Loggers

### Loggers

  * Configure a Logger
  * OpenTelemetry



Spring Boot Actuator includes the ability to view and configure the log levels of your application at runtime. You can view either the entire list or an individual logger’s configuration, which is made up of both the explicitly configured logging level as well as the effective logging level given to it by the logging framework. These levels can be one of:

  * `TRACE`

  * `DEBUG`

  * `INFO`

  * `WARN`

  * `ERROR`

  * `FATAL`

  * `OFF`

  * `null`




`null` indicates that there is no explicit configuration.

## Configure a Logger

To configure a given logger, `POST` a partial entity to the resource’s URI, as the following example shows:
    
    
    {
    	"configuredLevel": "DEBUG"
    }
    
    Copied!

__ |  To “reset” the specific level of the logger (and use the default configuration instead), you can pass a value of `null` as the `configuredLevel`.   
---|---  
  
## OpenTelemetry

By default, logging via OpenTelemetry is not configured. You have to provide the location of the OpenTelemetry logs endpoint to configure it:

  * Properties

  * YAML



    
    
    management.otlp.logging.endpoint=https://otlp.example.com:4318/v1/logs
    
    Copied!
    
    
    management:
      otlp:
        logging:
          endpoint: "https://otlp.example.com:4318/v1/logs"
    
    Copied!

__ |  The OpenTelemetry Logback appender and Log4j appender are not part of Spring Boot. For more details, see the [OpenTelemetry Logback appender](https://github.com/open-telemetry/opentelemetry-java-instrumentation/tree/main/instrumentation/logback/logback-appender-1.0/library) or the [OpenTelemetry Log4j2 appender](https://github.com/open-telemetry/opentelemetry-java-instrumentation/tree/main/instrumentation/log4j/log4j-appender-2.17/library) in the [OpenTelemetry Java instrumentation GitHub repository](https://github.com/open-telemetry/opentelemetry-java-instrumentation).   
---|---  
  
__ |  You have to configure the appender in your `logback-spring.xml` or `log4j2-spring.xml` configuration to get OpenTelemetry logging working.   
---|---  
  
The `OpenTelemetryAppender` for both Logback and Log4j requires access to an [`OpenTelemetry`](https://javadoc.io/doc/io.opentelemetry/opentelemetry-api/1.43.0/io/opentelemetry/api/OpenTelemetry.html) instance to function properly. This instance must be set programmatically during application startup, which can be done like this:
    
    
    import io.opentelemetry.api.OpenTelemetry;
    import io.opentelemetry.instrumentation.logback.appender.v1_0.OpenTelemetryAppender;
    
    import org.springframework.beans.factory.InitializingBean;
    import org.springframework.stereotype.Component;
    
    @Component
    class OpenTelemetryAppenderInitializer implements InitializingBean {
    
    	private final OpenTelemetry openTelemetry;
    
    	OpenTelemetryAppenderInitializer(OpenTelemetry openTelemetry) {
    		this.openTelemetry = openTelemetry;
    	}
    
    	@Override
    	public void afterPropertiesSet() {
    		OpenTelemetryAppender.install(this.openTelemetry);
    	}
    
    }
    
    Copied!

[Observability](observability.html) [Metrics](metrics.html)
---
