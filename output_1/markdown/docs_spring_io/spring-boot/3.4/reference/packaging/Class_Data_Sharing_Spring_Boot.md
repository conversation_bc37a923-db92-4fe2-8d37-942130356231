Title: Class Data Sharing :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/packaging/class-data-sharing.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/packaging/Class_Data_Sharing_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/packaging/Class_Data_Sharing_Spring_Boot.png
crawled_at: 2025-06-04T15:48:23.743612
---
Search CTRL + k

### Class Data Sharing

  * CDS
  * AOT Cache



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/packaging/class-data-sharing.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Packaging Spring Boot Applications](index.html)
  * [Class Data Sharing](class-data-sharing.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/packaging/class-data-sharing.html)!  
---|---  
  
# Class Data Sharing

### Class Data Sharing

  * CDS
  * AOT Cache



Class Data Sharing (CDS) is a [JVM feature](https://docs.oracle.com/en/java/javase/17/vm/class-data-sharing.html) that can help reduce the startup time and memory footprint of Java applications.

In Java 24, CDS is succeeded by the AOT Cache via [JEP 483](https://openjdk.org/jeps/483). Spring Boot supports both CDS and AOT cache, and it is recommended that you use the latter if it is available in the JVM version you are using (Java 24+).

## CDS

To use CDS, you should first perform a training run on your application in extracted form:
    
    
    $ java -Djarmode=tools -jar my-app.jar extract --destination application
    $ cd application
    $ java -XX:ArchiveClassesAtExit=application.jsa -Dspring.context.exit=onRefresh -jar my-app.jar
    
    Copied!

This creates an `application.jsa` archive file that can be reused as long as the application is not updated.

To use the archive file, you need to add an extra parameter when starting the application:
    
    
    $ java -XX:SharedArchiveFile=application.jsa -jar my-app.jar
    
    Copied!

__ |  For more details about CDS, refer to the [CDS how-to guide](../../how-to/class-data-sharing.html) and the [Spring Framework reference documentation](https://docs.spring.io/spring-framework/reference/6.2/integration/cds.html).   
---|---  
  
## AOT Cache

To use the AOT cache, you should first perform a training run on your application in extracted form:
    
    
    $ java -Djarmode=tools -jar my-app.jar extract --destination application
    $ cd application
    $ java -XX:AOTMode=record -XX:AOTConfiguration=app.aotconf -Dspring.context.exit=onRefresh -jar my-app.jar
    $ java -XX:AOTMode=create -XX:AOTConfiguration=app.aotconf -XX:AOTCache=app.aot -jar my-app.jar
    
    Copied!

This creates an `app.aot` cache file that can be reused as long as the application is not updated. The intermediate `app.aotconf` file is no longer needed and can be safely deleted.

To use the cache file, you need to add an extra parameter when starting the application:
    
    
    $ java -XX:AOTCache=app.aot -jar my-app.jar
    
    Copied!

[Efficient Deployments](efficient.html) [Ahead-of-Time Processing With the JVM](aot.html)
---
