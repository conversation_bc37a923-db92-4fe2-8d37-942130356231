Title: Efficient Deployments :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/packaging/efficient.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/packaging/Efficient_Deployments_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/packaging/Efficient_Deployments_Spring_Boot.png
crawled_at: 2025-06-04T15:47:04.808414
---
Search CTRL + k

### Efficient Deployments

  * Unpacking the Executable jar



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/packaging/efficient.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Packaging Spring Boot Applications](index.html)
  * [Efficient Deployments](efficient.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/packaging/efficient.html)!  
---|---  
  
# Efficient Deployments

### Efficient Deployments

  * Unpacking the Executable jar



## Unpacking the Executable jar

You can run your application using the executable jar, but loading the classes from nested jars has a small startup cost. Depending on the size of the jar, running the application from an exploded structure is faster and recommended in production. Certain PaaS implementations may also choose to extract archives before they run. For example, Cloud Foundry operates this way.

Spring Boot supports extracting your application to a directory using different layouts. The default layout is the most efficient, and it is [CDS](class-data-sharing.html#packaging.class-data-sharing.cds) and [AOT cache](class-data-sharing.html#packaging.class-data-sharing.aot-cache) friendly.

In this layout, the libraries are extracted to a `lib/` folder, and the application jar contains the application classes and a manifest which references the libraries in the `lib/` folder.

To unpack the executable jar, run this command:
    
    
    $ java -Djarmode=tools -jar my-app.jar extract
    
    Copied!

And then in production, you can run the extracted jar:
    
    
    $ java -jar my-app/my-app.jar
    
    Copied!

After startup, you should not expect any differences in execution time between running an executable jar and running an extracted jar.

__ |  Run `java -Djarmode=tools -jar my-app.jar help extract` to see all possible options.   
---|---  
  
[Packaging Spring Boot Applications](index.html) [Class Data Sharing](class-data-sharing.html)
---
