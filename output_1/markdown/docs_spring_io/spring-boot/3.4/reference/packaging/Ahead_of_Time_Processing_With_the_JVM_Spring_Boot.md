Title: Ahead-of-Time Processing With the JVM :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/packaging/aot.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/packaging/Ahead_of_Time_Processing_With_the_JVM_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/packaging/Ahead_of_Time_Processing_With_the_JVM_Spring_Boot.png
crawled_at: 2025-06-04T15:48:47.349271
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/packaging/aot.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Packaging Spring Boot Applications](index.html)
  * [Ahead-of-Time Processing With the JVM](aot.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/packaging/aot.html)!  
---|---  
  
# Ahead-of-Time Processing With the JVM

It’s beneficial for the startup time to run your application using the AOT generated initialization code. First, you need to ensure that the jar you are building includes AOT generated code.

__ |  CDS and AOT can be combined to further improve startup time.   
---|---  
  
For Maven, this means that you should build with `-Pnative` to activate the `native` profile:
    
    
    $ mvn -Pnative package
    
    Copied!

For Gradle, you need to ensure that your build includes the `org.springframework.boot.aot` plugin.

When the JAR has been built, run it with `spring.aot.enabled` system property set to `true`. For example:
    
    
    $ java -Dspring.aot.enabled=true -jar myapplication.jar
    
    ........ Starting AOT-processed MyApplication ...
    
    Copied!

Beware that using the ahead-of-time processing has drawbacks. It implies the following restrictions:

  * The classpath is fixed and fully defined at build time

  * The beans defined in your application cannot change at runtime, meaning:

    * The Spring [`@Profile`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Profile.html) annotation and profile-specific configuration [have limitations](../../how-to/aot.html#howto.aot.conditions).

    * Properties that change if a bean is created are not supported (for example, [`@ConditionalOnProperty`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/condition/ConditionalOnProperty.html) and `.enabled` properties).




To learn more about ahead-of-time processing, please see the [Understanding Spring Ahead-of-Time Processing](native-image/introducing-graalvm-native-images.html#packaging.native-image.introducing-graalvm-native-images.understanding-aot-processing) section.

[Class Data Sharing](class-data-sharing.html) [GraalVM Native Images](native-image/index.html)
---
