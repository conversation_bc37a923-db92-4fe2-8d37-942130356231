Title: Build Systems :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/using/build-systems.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/using/Build_Systems_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/using/Build_Systems_Spring_Boot.png
crawled_at: 2025-06-04T15:55:24.550647
---
Search CTRL + k

### Build Systems

  * Dependency Management
  * Maven
  * Gradle
  * Ant
  * Starters



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/using/build-systems.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Developing with Spring Boot](index.html)
  * [Build Systems](build-systems.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/using/build-systems.html)!  
---|---  
  
# Build Systems

### Build Systems

  * Dependency Management
  * Maven
  * Gradle
  * Ant
  * Starters



It is strongly recommended that you choose a build system that supports dependency management and that can consume artifacts published to the Maven Central repository. We would recommend that you choose Maven or Gradle. It is possible to get Spring Boot to work with other build systems (Ant, for example), but they are not particularly well supported.

## Dependency Management

Each release of Spring Boot provides a curated list of dependencies that it supports. In practice, you do not need to provide a version for any of these dependencies in your build configuration, as Spring Boot manages that for you. When you upgrade Spring Boot itself, these dependencies are upgraded as well in a consistent way.

__ |  You can still specify a version and override Spring Boot’s recommendations if you need to do so.   
---|---  
  
The curated list contains all the Spring modules that you can use with Spring Boot as well as a refined list of third party libraries. The list is available as a standard Bills of Materials (`spring-boot-dependencies`) that can be used with both Maven and Gradle.

__ |  Each release of Spring Boot is associated with a base version of the Spring Framework. We **highly** recommend that you do not specify its version.   
---|---  
  
## Maven

To learn about using Spring Boot with Maven, see the documentation for Spring Boot’s Maven plugin:

  * [Reference](../../maven-plugin/index.html)

  * [API](../../maven-plugin/api/java/index.html)




## Gradle

To learn about using Spring Boot with Gradle, see the documentation for Spring Boot’s Gradle plugin:

  * [Reference](../../gradle-plugin/index.html)

  * [API](../../gradle-plugin/api/java/index.html)




## Ant

It is possible to build a Spring Boot project using Apache Ant+Ivy. The `spring-boot-antlib` “AntLib” module is also available to help Ant create executable jars.

To declare dependencies, a typical `ivy.xml` file looks something like the following example:
    
    
    <ivy-module version="2.0">
    	<info organisation="org.springframework.boot" module="spring-boot-sample-ant" />
    	<configurations>
    		<conf name="compile" description="everything needed to compile this module" />
    		<conf name="runtime" extends="compile" description="everything needed to run this module" />
    	</configurations>
    	<dependencies>
    		<dependency org="org.springframework.boot" name="spring-boot-starter"
    			rev="${spring-boot.version}" conf="compile" />
    	</dependencies>
    </ivy-module>
    
    Copied!

A typical `build.xml` looks like the following example:
    
    
    <project
    	xmlns:ivy="antlib:org.apache.ivy.ant"
    	xmlns:spring-boot="antlib:org.springframework.boot.ant"
    	name="myapp" default="build">
    
    	<property name="spring-boot.version" value="3.4.6" />
    
    	<target name="resolve" description="--> retrieve dependencies with ivy">
    		<ivy:retrieve pattern="lib/[conf]/[artifact]-[type]-[revision].[ext]" />
    	</target>
    
    	<target name="classpaths" depends="resolve">
    		<path id="compile.classpath">
    			<fileset dir="lib/compile" includes="*.jar" />
    		</path>
    	</target>
    
    	<target name="init" depends="classpaths">
    		<mkdir dir="build/classes" />
    	</target>
    
    	<target name="compile" depends="init" description="compile">
    		<javac srcdir="src/main/java" destdir="build/classes" classpathref="compile.classpath" />
    	</target>
    
    	<target name="build" depends="compile">
    		<spring-boot:exejar destfile="build/myapp.jar" classes="build/classes">
    			<spring-boot:lib>
    				<fileset dir="lib/runtime" />
    			</spring-boot:lib>
    		</spring-boot:exejar>
    	</target>
    </project>
    
    Copied!

__ |  If you do not want to use the `spring-boot-antlib` module, see the [Build an Executable Archive From Ant without Using spring-boot-antlib](../../how-to/build.html#howto.build.build-an-executable-archive-with-ant-without-using-spring-boot-antlib) section of “How-to Guides”.   
---|---  
  
## Starters

Starters are a set of convenient dependency descriptors that you can include in your application. You get a one-stop shop for all the Spring and related technologies that you need without having to hunt through sample code and copy-paste loads of dependency descriptors. For example, if you want to get started using Spring and JPA for database access, include the `spring-boot-starter-data-jpa` dependency in your project.

The starters contain a lot of the dependencies that you need to get a project up and running quickly and with a consistent, supported set of managed transitive dependencies.

What is in a name

All **official** starters follow a similar naming pattern; `spring-boot-starter-*`, where `*` is a particular type of application. This naming structure is intended to help when you need to find a starter. The Maven integration in many IDEs lets you search dependencies by name. For example, with the appropriate Eclipse or Spring Tools plugin installed, you can press `ctrl-space` in the POM editor and type “spring-boot-starter” for a complete list.

As explained in the [Creating Your Own Starter](../features/developing-auto-configuration.html#features.developing-auto-configuration.custom-starter) section, third party starters should not start with `spring-boot`, as it is reserved for official Spring Boot artifacts. Rather, a third-party starter typically starts with the name of the project. For example, a third-party starter project called `thirdpartyproject` would typically be named `thirdpartyproject-spring-boot-starter`.

The following application starters are provided by Spring Boot under the `org.springframework.boot` group:

Table 1. Spring Boot application starters Name | Description  
---|---  
`spring-boot-starter` | Core starter, including auto-configuration support, logging and YAML  
`spring-boot-starter-activemq` | Starter for JMS messaging using Apache ActiveMQ  
`spring-boot-starter-amqp` | Starter for using Spring AMQP and Rabbit MQ  
`spring-boot-starter-aop` | Starter for aspect-oriented programming with Spring AOP and AspectJ  
`spring-boot-starter-artemis` | Starter for JMS messaging using Apache Artemis  
`spring-boot-starter-batch` | Starter for using Spring Batch  
`spring-boot-starter-cache` | Starter for using Spring Framework’s caching support  
`spring-boot-starter-data-cassandra` | Starter for using Cassandra distributed database and Spring Data Cassandra  
`spring-boot-starter-data-cassandra-reactive` | Starter for using Cassandra distributed database and Spring Data Cassandra Reactive  
`spring-boot-starter-data-couchbase` | Starter for using Couchbase document-oriented database and Spring Data Couchbase  
`spring-boot-starter-data-couchbase-reactive` | Starter for using Couchbase document-oriented database and Spring Data Couchbase Reactive  
`spring-boot-starter-data-elasticsearch` | Starter for using Elasticsearch search and analytics engine and Spring Data Elasticsearch  
`spring-boot-starter-data-jdbc` | Starter for using Spring Data JDBC  
`spring-boot-starter-data-jpa` | Starter for using Spring Data JPA with Hibernate  
`spring-boot-starter-data-ldap` | Starter for using Spring Data LDAP  
`spring-boot-starter-data-mongodb` | Starter for using MongoDB document-oriented database and Spring Data MongoDB  
`spring-boot-starter-data-mongodb-reactive` | Starter for using MongoDB document-oriented database and Spring Data MongoDB Reactive  
`spring-boot-starter-data-neo4j` | Starter for using Neo4j graph database and Spring Data Neo4j  
`spring-boot-starter-data-r2dbc` | Starter for using Spring Data R2DBC  
`spring-boot-starter-data-redis` | Starter for using Redis key-value data store with Spring Data Redis and the Lettuce client  
`spring-boot-starter-data-redis-reactive` | Starter for using Redis key-value data store with Spring Data Redis reactive and the Lettuce client  
`spring-boot-starter-data-rest` | Starter for exposing Spring Data repositories over REST using Spring Data REST and Spring MVC  
`spring-boot-starter-freemarker` | Starter for building MVC web applications using FreeMarker views  
`spring-boot-starter-graphql` | Starter for building GraphQL applications with Spring GraphQL  
`spring-boot-starter-groovy-templates` | Starter for building MVC web applications using Groovy Templates views  
`spring-boot-starter-hateoas` | Starter for building hypermedia-based RESTful web application with Spring MVC and Spring HATEOAS  
`spring-boot-starter-integration` | Starter for using Spring Integration  
`spring-boot-starter-jdbc` | Starter for using JDBC with the HikariCP connection pool  
`spring-boot-starter-jersey` | Starter for building RESTful web applications using JAX-RS and Jersey. An alternative to `spring-boot-starter-web`  
`spring-boot-starter-jooq` | Starter for using jOOQ to access SQL databases with JDBC. An alternative to `spring-boot-starter-data-jpa` or `spring-boot-starter-jdbc`  
`spring-boot-starter-json` | Starter for reading and writing json  
`spring-boot-starter-mail` | Starter for using Java Mail and Spring Framework’s email sending support  
`spring-boot-starter-mustache` | Starter for building web applications using Mustache views  
`spring-boot-starter-oauth2-authorization-server` | Starter for using Spring Authorization Server features  
`spring-boot-starter-oauth2-client` | Starter for using Spring Security’s OAuth2/OpenID Connect client features  
`spring-boot-starter-oauth2-resource-server` | Starter for using Spring Security’s OAuth2 resource server features  
`spring-boot-starter-pulsar` | Starter for using Spring for Apache Pulsar  
`spring-boot-starter-pulsar-reactive` | Starter for using Spring for Apache Pulsar Reactive  
`spring-boot-starter-quartz` | Starter for using the Quartz scheduler  
`spring-boot-starter-rsocket` | Starter for building RSocket clients and servers  
`spring-boot-starter-security` | Starter for using Spring Security  
`spring-boot-starter-test` | Starter for testing Spring Boot applications with libraries including JUnit Jupiter, Hamcrest and Mockito  
`spring-boot-starter-thymeleaf` | Starter for building MVC web applications using Thymeleaf views  
`spring-boot-starter-validation` | Starter for using Java Bean Validation with Hibernate Validator  
`spring-boot-starter-web` | Starter for building web, including RESTful, applications using Spring MVC. Uses Tomcat as the default embedded container  
`spring-boot-starter-web-services` | Starter for using Spring Web Services  
`spring-boot-starter-webflux` | Starter for building WebFlux applications using Spring Framework’s Reactive Web support  
`spring-boot-starter-websocket` | Starter for building WebSocket applications using Spring Framework’s MVC WebSocket support  
  
In addition to the application starters, the following starters can be used to add [production ready](../../how-to/actuator.html) features:

Table 2. Spring Boot production starters Name | Description  
---|---  
`spring-boot-starter-actuator` | Starter for using Spring Boot’s Actuator which provides production ready features to help you monitor and manage your application  
  
Finally, Spring Boot also includes the following starters that can be used if you want to exclude or swap specific technical facets:

Table 3. Spring Boot technical starters Name | Description  
---|---  
`spring-boot-starter-jetty` | Starter for using Jetty as the embedded servlet container. An alternative to `spring-boot-starter-tomcat`  
`spring-boot-starter-log4j2` | Starter for using Log4j2 for logging. An alternative to `spring-boot-starter-logging`  
`spring-boot-starter-logging` | Starter for logging using Logback. Default logging starter  
`spring-boot-starter-reactor-netty` | Starter for using Reactor Netty as the embedded reactive HTTP server.  
`spring-boot-starter-tomcat` | Starter for using Tomcat as the embedded servlet container. Default servlet container starter used by `spring-boot-starter-web`  
`spring-boot-starter-undertow` | Starter for using Undertow as the embedded servlet container. An alternative to `spring-boot-starter-tomcat`  
  
To learn how to swap technical facets, please see the how-to documentation for [swapping web server](../../how-to/webserver.html#howto.webserver.use-another) and [logging system](../../how-to/logging.html#howto.logging.log4j).

__ |  For a list of additional community contributed starters, see the [README file](https://github.com/spring-projects/spring-boot/tree/main/spring-boot-project/spring-boot-starters/README.adoc) in the `spring-boot-starters` module on GitHub.   
---|---  
  
[Developing with Spring Boot](index.html) [Structuring Your Code](structuring-your-code.html)
---
