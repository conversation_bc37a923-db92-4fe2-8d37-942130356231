Title: Developer Tools :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/using/devtools.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/using/Developer_Tools_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/using/Developer_Tools_Spring_Boot.png
crawled_at: 2025-06-04T15:39:40.415185
---
Search CTRL + k

### Developer Tools

  * Diagnosing Classloading Issues
  * Property Defaults
  * Automatic Restart
  * Logging Changes in Condition Evaluation
  * Excluding Resources
  * Watching Additional Paths
  * Disabling Restart
  * Using a Trigger File
  * Customizing the Restart Classloader
  * Known Limitations
  * LiveReload
  * Global Settings
  * Configuring File System Watcher
  * Remote Applications
  * Running the Remote Client Application
  * Remote Update



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/using/devtools.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Developing with Spring Boot](index.html)
  * [Developer Tools](devtools.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/using/devtools.html)!  
---|---  
  
# Developer Tools

### Developer Tools

  * Diagnosing Classloading Issues
  * Property Defaults
  * Automatic Restart
  * Logging Changes in Condition Evaluation
  * Excluding Resources
  * Watching Additional Paths
  * Disabling Restart
  * Using a Trigger File
  * Customizing the Restart Classloader
  * Known Limitations
  * LiveReload
  * Global Settings
  * Configuring File System Watcher
  * Remote Applications
  * Running the Remote Client Application
  * Remote Update



Spring Boot includes an additional set of tools that can make the application development experience a little more pleasant. The `spring-boot-devtools` module can be included in any project to provide additional development-time features. To include devtools support, add the module dependency to your build, as shown in the following listings for Maven and Gradle:

Maven
    
    
    <dependencies>
    	<dependency>
    		<groupId>org.springframework.boot</groupId>
    		<artifactId>spring-boot-devtools</artifactId>
    		<optional>true</optional>
    	</dependency>
    </dependencies>
    
    Copied!

Gradle
    
    
    dependencies {
    	developmentOnly("org.springframework.boot:spring-boot-devtools")
    }
    
    Copied!

__ |  Devtools might cause classloading issues, in particular in multi-module projects. Diagnosing Classloading Issues explains how to diagnose and solve them.   
---|---  
  
__ |  Developer tools are automatically disabled when running a fully packaged application. If your application is launched from `java -jar` or if it is started from a special classloader, then it is considered a “production application”. You can control this behavior by using the `spring.devtools.restart.enabled` system property. To enable devtools, irrespective of the classloader used to launch your application, set the `-Dspring.devtools.restart.enabled=true` system property. This must not be done in a production environment where running devtools is a security risk. To disable devtools, exclude the dependency or set the `-Dspring.devtools.restart.enabled=false` system property.   
---|---  
  
__ |  Flagging the dependency as optional in Maven or using the `developmentOnly` configuration in Gradle (as shown above) prevents devtools from being transitively applied to other modules that use your project.   
---|---  
  
__ |  Repackaged archives do not contain devtools by default. If you want to use a certain remote devtools feature, you need to include it. When using the Maven plugin, set the `excludeDevtools` property to `false`. When using the Gradle plugin, [configure the task’s classpath to include the `developmentOnly` configuration](../../gradle-plugin/packaging.html#packaging-executable.configuring.including-development-only-dependencies).   
---|---  
  
## Diagnosing Classloading Issues

As described in the Restart vs Reload section, restart functionality is implemented by using two classloaders. For most applications, this approach works well. However, it can sometimes cause classloading issues, in particular in multi-module projects.

To diagnose whether the classloading issues are indeed caused by devtools and its two classloaders, try disabling restart. If this solves your problems, customize the restart classloader to include your entire project.

## Property Defaults

Several of the libraries supported by Spring Boot use caches to improve performance. For example, [template engines](../web/servlet.html#web.servlet.spring-mvc.template-engines) cache compiled templates to avoid repeatedly parsing template files. Also, Spring MVC can add HTTP caching headers to responses when serving static resources.

While caching is very beneficial in production, it can be counter-productive during development, preventing you from seeing the changes you just made in your application. For this reason, spring-boot-devtools disables the caching options by default.

Cache options are usually configured by settings in your `application.properties` file. For example, Thymeleaf offers the `spring.thymeleaf.cache` property. Rather than needing to set these properties manually, the `spring-boot-devtools` module automatically applies sensible development-time configuration.

The following table lists all the properties that are applied:

Name | Default Value  
---|---  
`server.error.include-binding-errors` | `always`  
`server.error.include-message` | `always`  
`server.error.include-stacktrace` | `always`  
`server.servlet.jsp.init-parameters.development` | `true`  
`server.servlet.session.persistent` | `true`  
`spring.docker.compose.readiness.wait` | `only-if-started`  
`spring.freemarker.cache` | `false`  
`spring.graphql.graphiql.enabled` | `true`  
`spring.groovy.template.cache` | `false`  
`spring.h2.console.enabled` | `true`  
`spring.mustache.servlet.cache` | `false`  
`spring.mvc.log-resolved-exception` | `true`  
`spring.reactor.netty.shutdown-quiet-period` | `0s`  
`spring.template.provider.cache` | `false`  
`spring.thymeleaf.cache` | `false`  
`spring.web.resources.cache.period` | `0`  
`spring.web.resources.chain.cache` | `false`  
  
__ |  If you do not want property defaults to be applied you can set `spring.devtools.add-properties` to `false` in your `application.properties`.   
---|---  
  
Because you need more information about web requests while developing Spring MVC and Spring WebFlux applications, developer tools suggests you to enable `DEBUG` logging for the `web` logging group. This will give you information about the incoming request, which handler is processing it, the response outcome, and other details. If you wish to log all request details (including potentially sensitive information), you can turn on the `spring.mvc.log-request-details` or `spring.codec.log-request-details` configuration properties.

## Automatic Restart

Applications that use `spring-boot-devtools` automatically restart whenever files on the classpath change. This can be a useful feature when working in an IDE, as it gives a very fast feedback loop for code changes. By default, any entry on the classpath that points to a directory is monitored for changes. Note that certain resources, such as static assets and view templates, do not need to restart the application.

Triggering a restart

As DevTools monitors classpath resources, the only way to trigger a restart is to update the classpath. Whether you’re using an IDE or one of the build plugins, the modified files have to be recompiled to trigger a restart. The way in which you cause the classpath to be updated depends on the tool that you are using:

  * In Eclipse, saving a modified file causes the classpath to be updated and triggers a restart.

  * In IntelliJ IDEA, building the project (`Build -> Build Project`) has the same effect.

  * If using a build plugin, running `mvn compile` for Maven or `gradle build` for Gradle will trigger a restart.




__ |  If you are restarting with Maven or Gradle using the build plugin you must leave the `forking` set to `enabled`. If you disable forking, the isolated application classloader used by devtools will not be created and restarts will not operate properly.   
---|---  
  
__ |  Automatic restart works very well when used with LiveReload. See the LiveReload section for details. If you use JRebel, automatic restarts are disabled in favor of dynamic class reloading. Other devtools features (such as LiveReload and property overrides) can still be used.   
---|---  
  
__ |  DevTools relies on the application context’s shutdown hook to close it during a restart. It does not work correctly if you have disabled the shutdown hook (`SpringApplication.setRegisterShutdownHook(false)`).   
---|---  
  
__ |  DevTools needs to customize the [`ResourceLoader`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/io/ResourceLoader.html) used by the [`ApplicationContext`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html). If your application provides one already, it is going to be wrapped. Direct override of the `getResource` method on the [`ApplicationContext`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html) is not supported.   
---|---  
  
__ |  Automatic restart is not supported when using AspectJ weaving.   
---|---  
  
Restart vs Reload

The restart technology provided by Spring Boot works by using two classloaders. Classes that do not change (for example, those from third-party jars) are loaded into a _base_ classloader. Classes that you are actively developing are loaded into a _restart_ classloader. When the application is restarted, the _restart_ classloader is thrown away and a new one is created. This approach means that application restarts are typically much faster than “cold starts”, since the _base_ classloader is already available and populated.

If you find that restarts are not quick enough for your applications or you encounter classloading issues, you could consider reloading technologies such as [JRebel](https://jrebel.com/software/jrebel/) from ZeroTurnaround. These work by rewriting classes as they are loaded to make them more amenable to reloading.

### Logging Changes in Condition Evaluation

By default, each time your application restarts, a report showing the condition evaluation delta is logged. The report shows the changes to your application’s auto-configuration as you make changes such as adding or removing beans and setting configuration properties.

To disable the logging of the report, set the following property:

  * Properties

  * YAML



    
    
    spring.devtools.restart.log-condition-evaluation-delta=false
    
    Copied!
    
    
    spring:
      devtools:
        restart:
          log-condition-evaluation-delta: false
    
    Copied!

### Excluding Resources

Certain resources do not necessarily need to trigger a restart when they are changed. For example, Thymeleaf templates can be edited in-place. By default, changing resources in `/META-INF/maven`, `/META-INF/resources`, `/resources`, `/static`, `/public`, or `/templates` does not trigger a restart but does trigger a live reload. If you want to customize these exclusions, you can use the `spring.devtools.restart.exclude` property. For example, to exclude only `/static` and `/public` you would set the following property:

  * Properties

  * YAML



    
    
    spring.devtools.restart.exclude=static/**,public/**
    
    Copied!
    
    
    spring:
      devtools:
        restart:
          exclude: "static/**,public/**"
    
    Copied!

__ |  If you want to keep those defaults and _add_ additional exclusions, use the `spring.devtools.restart.additional-exclude` property instead.   
---|---  
  
### Watching Additional Paths

You may want your application to be restarted or reloaded when you make changes to files that are not on the classpath. To do so, use the `spring.devtools.restart.additional-paths` property to configure additional paths to watch for changes. You can use the `spring.devtools.restart.exclude` property described earlier to control whether changes beneath the additional paths trigger a full restart or a live reload.

### Disabling Restart

If you do not want to use the restart feature, you can disable it by using the `spring.devtools.restart.enabled` property. In most cases, you can set this property in your `application.properties` (doing so still initializes the restart classloader, but it does not watch for file changes).

If you need to _completely_ disable restart support (for example, because it does not work with a specific library), you need to set the `spring.devtools.restart.enabled` [`System`](https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/System.html) property to `false` before calling `SpringApplication.run(…​)`, as shown in the following example:

  * Java

  * Kotlin



    
    
    import org.springframework.boot.SpringApplication;
    import org.springframework.boot.autoconfigure.SpringBootApplication;
    
    @SpringBootApplication
    public class MyApplication {
    
    	public static void main(String[] args) {
    		System.setProperty("spring.devtools.restart.enabled", "false");
    		SpringApplication.run(MyApplication.class, args);
    	}
    
    }
    
    Copied!
    
    
    import org.springframework.boot.SpringApplication
    import org.springframework.boot.autoconfigure.SpringBootApplication
    
    @SpringBootApplication
    object MyApplication {
    
    	@JvmStatic
    	fun main(args: Array<String>) {
    		System.setProperty("spring.devtools.restart.enabled", "false")
    		SpringApplication.run(MyApplication::class.java, *args)
    	}
    
    }
    
    Copied!

### Using a Trigger File

If you work with an IDE that continuously compiles changed files, you might prefer to trigger restarts only at specific times. To do so, you can use a “trigger file”, which is a special file that must be modified when you want to actually trigger a restart check.

__ |  Any update to the file will trigger a check, but restart only actually occurs if Devtools has detected it has something to do.   
---|---  
  
To use a trigger file, set the `spring.devtools.restart.trigger-file` property to the name (excluding any path) of your trigger file. The trigger file must appear somewhere on your classpath.

For example, if you have a project with the following structure:
    
    
    src
    +- main
       +- resources
          +- .reloadtrigger
    
    Copied!

Then your `trigger-file` property would be:

  * Properties

  * YAML



    
    
    spring.devtools.restart.trigger-file=.reloadtrigger
    
    Copied!
    
    
    spring:
      devtools:
        restart:
          trigger-file: ".reloadtrigger"
    
    Copied!

Restarts will now only happen when the `src/main/resources/.reloadtrigger` is updated.

__ |  You might want to set `spring.devtools.restart.trigger-file` as a global setting, so that all your projects behave in the same way.   
---|---  
  
Some IDEs have features that save you from needing to update your trigger file manually. [Spring Tools for Eclipse](https://spring.io/tools) and [IntelliJ IDEA (Ultimate Edition)](https://www.jetbrains.com/idea/) both have such support. With Spring Tools, you can use the “reload” button from the console view (as long as your `trigger-file` is named `.reloadtrigger`). For IntelliJ IDEA, you can follow the [instructions in their documentation](https://www.jetbrains.com/help/idea/spring-boot.html#application-update-policies).

### Customizing the Restart Classloader

As described earlier in the Restart vs Reload section, restart functionality is implemented by using two classloaders. If this causes issues, you can diagnose the problem by using the `spring.devtools.restart.enabled` system property, and if the app works with restart switched off, you might need to customize what gets loaded by which classloader.

By default, any open project in your IDE is loaded with the “restart” classloader, and any regular `.jar` file is loaded with the “base” classloader. The same is true if you use `mvn spring-boot:run` or `gradle bootRun`: the project containing your [`@SpringBootApplication`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html) is loaded with the “restart” classloader, and everything else with the “base” classloader. The classpath is printed on the console when you start the app, which can help to identify any problematic entries. Classes used reflectively, especially annotations, can be loaded into the parent (fixed) classloader on startup before the application classes which use them, and this might lead to them not being detected by Spring in the application.

You can instruct Spring Boot to load parts of your project with a different classloader by creating a `META-INF/spring-devtools.properties` file. The `spring-devtools.properties` file can contain properties prefixed with `restart.exclude` and `restart.include`. The `include` elements are items that should be pulled up into the “restart” classloader, and the `exclude` elements are items that should be pushed down into the “base” classloader. The value of the property is a regex pattern that is applied to the classpath passed to the JVM on startup. Here is an example where some local class files are excluded and some extra libraries are included in the restart class loader:
    
    
    restart:
      exclude:
        companycommonlibs: "/mycorp-common-[\\w\\d-\\.]/(build|bin|out|target)/"
      include:
        projectcommon: "/mycorp-myproj-[\\w\\d-\\.]+\\.jar"
    
    Copied!

__ |  All property keys must be unique. As long as a property starts with `restart.include.` or `restart.exclude.` it is considered.   
---|---  
  
__ |  All `META-INF/spring-devtools.properties` from the classpath are loaded. You can package files inside your project, or in the libraries that the project consumes. System properties can not be used, only the properties file.   
---|---  
  
### Known Limitations

Restart functionality does not work well with objects that are deserialized by using a standard [`ObjectInputStream`](https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/ObjectInputStream.html). If you need to deserialize data, you may need to use Spring’s [`ConfigurableObjectInputStream`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/ConfigurableObjectInputStream.html) in combination with `Thread.currentThread().getContextClassLoader()`.

Unfortunately, several third-party libraries deserialize without considering the context classloader. If you find such a problem, you need to request a fix with the original authors.

## LiveReload

The `spring-boot-devtools` module includes an embedded LiveReload server that can be used to trigger a browser refresh when a resource is changed. LiveReload browser extensions are freely available for Chrome, Firefox and Safari. You can find these extensions by searching 'LiveReload' in the marketplace or store of your chosen browser.

If you do not want to start the LiveReload server when your application runs, you can set the `spring.devtools.livereload.enabled` property to `false`.

__ |  You can only run one LiveReload server at a time. Before starting your application, ensure that no other LiveReload servers are running. If you start multiple applications from your IDE, only the first has LiveReload support.   
---|---  
  
__ |  To trigger LiveReload when a file changes, Automatic Restart must be enabled.   
---|---  
  
## Global Settings

You can configure global devtools settings by adding any of the following files to the `$HOME/.config/spring-boot` directory:

  1. `spring-boot-devtools.properties`

  2. `spring-boot-devtools.yaml`

  3. `spring-boot-devtools.yml`




Any properties added to these files apply to _all_ Spring Boot applications on your machine that use devtools. For example, to configure restart to always use a trigger file, you would add the following property to your `spring-boot-devtools` file:

  * Properties

  * YAML



    
    
    spring.devtools.restart.trigger-file=.reloadtrigger
    
    Copied!
    
    
    spring:
      devtools:
        restart:
          trigger-file: ".reloadtrigger"
    
    Copied!

By default, `$HOME` is the user’s home directory. To customize this location, set the `SPRING_DEVTOOLS_HOME` environment variable or the `spring.devtools.home` system property.

__ |  If devtools configuration files are not found in `$HOME/.config/spring-boot`, the root of the `$HOME` directory is searched for the presence of a `.spring-boot-devtools.properties` file. This allows you to share the devtools global configuration with applications that are on an older version of Spring Boot that does not support the `$HOME/.config/spring-boot` location.   
---|---  
  
__ |  Profiles are not supported in devtools properties/yaml files. Any profiles activated in `.spring-boot-devtools.properties` will not affect the loading of [profile-specific configuration files](../features/external-config.html#features.external-config.files.profile-specific). Profile specific filenames (of the form `spring-boot-devtools-<profile>.properties`) and `spring.config.activate.on-profile` documents in both YAML and Properties files are not supported.  
---|---  
  
### Configuring File System Watcher

[`FileSystemWatcher`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/devtools/filewatch/FileSystemWatcher.html) works by polling the class changes with a certain time interval, and then waiting for a predefined quiet period to make sure there are no more changes. Since Spring Boot relies entirely on the IDE to compile and copy files into the location from where Spring Boot can read them, you might find that there are times when certain changes are not reflected when devtools restarts the application. If you observe such problems constantly, try increasing the `spring.devtools.restart.poll-interval` and `spring.devtools.restart.quiet-period` parameters to the values that fit your development environment:

  * Properties

  * YAML



    
    
    spring.devtools.restart.poll-interval=2s
    spring.devtools.restart.quiet-period=1s
    
    Copied!
    
    
    spring:
      devtools:
        restart:
          poll-interval: "2s"
          quiet-period: "1s"
    
    Copied!

The monitored classpath directories are now polled every 2 seconds for changes, and a 1 second quiet period is maintained to make sure there are no additional class changes.

## Remote Applications

The Spring Boot developer tools are not limited to local development. You can also use several features when running applications remotely. Remote support is opt-in as enabling it can be a security risk. It should only be enabled when running on a trusted network or when secured with SSL. If neither of these options is available to you, you should not use DevTools' remote support. You should never enable support on a production deployment.

To enable it, you need to make sure that `devtools` is included in the repackaged archive, as shown in the following listing:
    
    
    <build>
    	<plugins>
    		<plugin>
    			<groupId>org.springframework.boot</groupId>
    			<artifactId>spring-boot-maven-plugin</artifactId>
    			<configuration>
    				<excludeDevtools>false</excludeDevtools>
    			</configuration>
    		</plugin>
    	</plugins>
    </build>
    
    Copied!

Then you need to set the `spring.devtools.remote.secret` property. Like any important password or secret, the value should be unique and strong such that it cannot be guessed or brute-forced.

Remote devtools support is provided in two parts: a server-side endpoint that accepts connections and a client application that you run in your IDE. The server component is automatically enabled when the `spring.devtools.remote.secret` property is set. The client component must be launched manually.

__ |  Remote devtools is not supported for Spring WebFlux applications.   
---|---  
  
### Running the Remote Client Application

The remote client application is designed to be run from within your IDE. You need to run [`RemoteSpringApplication`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/devtools/RemoteSpringApplication.html) with the same classpath as the remote project that you connect to. The application’s single required argument is the remote URL to which it connects.

For example, if you are using Eclipse or Spring Tools and you have a project named `my-app` that you have deployed to Cloud Foundry, you would do the following:

  * Select `Run Configurations…​` from the `Run` menu.

  * Create a new `Java Application` “launch configuration”.

  * Browse for the `my-app` project.

  * Use [`RemoteSpringApplication`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/devtools/RemoteSpringApplication.html) as the main class.

  * Add `https://myapp.cfapps.io` to the `Program arguments` (or whatever your remote URL is).




A running remote client might resemble the following listing:
    
    
      .   ____          _                                              __ _ _
     /\\ / ___'_ __ _ _(_)_ __  __ _          ___               _      \ \ \ \
    ( ( )\___ | '_ | '_| | '_ \/ _` |        | _ \___ _ __  ___| |_ ___ \ \ \ \
     \\/  ___)| |_)| | | | | || (_| []::::::[]   / -_) '  \/ _ \  _/ -_) ) ) ) )
      '  |____| .__|_| |_|_| |_\__, |        |_|_\___|_|_|_\___/\__\___|/ / / /
     =========|_|==============|___/===================================/_/_/_/
     :: Spring Boot Remote ::  (v3.4.6)
    
    2025-05-22T10:12:33.531Z  INFO 132752 --- [           main] o.s.b.devtools.RemoteSpringApplication   : Starting RemoteSpringApplication v3.4.6 using Java 17.0.15 with PID 132752 (/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.4.6/spring-boot-devtools-3.4.6.jar started by myuser in /opt/apps/)
    2025-05-22T10:12:33.558Z  INFO 132752 --- [           main] o.s.b.devtools.RemoteSpringApplication   : No active profile set, falling back to 1 default profile: "default"
    2025-05-22T10:12:34.929Z  INFO 132752 --- [           main] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
    2025-05-22T10:12:35.033Z  INFO 132752 --- [           main] o.s.b.devtools.RemoteSpringApplication   : Started RemoteSpringApplication in 3.805 seconds (process running for 5.072)
    
    Copied!

__ |  Because the remote client is using the same classpath as the real application it can directly read application properties. This is how the `spring.devtools.remote.secret` property is read and passed to the server for authentication.   
---|---  
  
__ |  It is always advisable to use `https://` as the connection protocol, so that traffic is encrypted and passwords cannot be intercepted.   
---|---  
  
__ |  If you need to use a proxy to access the remote application, configure the `spring.devtools.remote.proxy.host` and `spring.devtools.remote.proxy.port` properties.   
---|---  
  
### Remote Update

The remote client monitors your application classpath for changes in the same way as the local restart. Any updated resource is pushed to the remote application and (_if required_) triggers a restart. This can be helpful if you iterate on a feature that uses a cloud service that you do not have locally. Generally, remote updates and restarts are much quicker than a full rebuild and deploy cycle.

On a slower development environment, it may happen that the quiet period is not enough, and the changes in the classes may be split into batches. The server is restarted after the first batch of class changes is uploaded. The next batch can’t be sent to the application, since the server is restarting.

This is typically manifested by a warning in the [`RemoteSpringApplication`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/devtools/RemoteSpringApplication.html) logs about failing to upload some of the classes, and a consequent retry. But it may also lead to application code inconsistency and failure to restart after the first batch of changes is uploaded. If you observe such problems constantly, try increasing the `spring.devtools.restart.poll-interval` and `spring.devtools.restart.quiet-period` parameters to the values that fit your development environment. See the Configuring File System Watcher section for configuring these properties.

__ |  Files are only monitored when the remote client is running. If you change a file before starting the remote client, it is not pushed to the remote server.   
---|---  
  
[Running Your Application](running-your-application.html) [Packaging Your Application for Production](packaging-for-production.html)
---
