Title: Configuration Classes :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/using/configuration-classes.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/using/Configuration_Classes_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/using/Configuration_Classes_Spring_Boot.png
crawled_at: 2025-06-04T15:39:22.651878
---
Search CTRL + k

### Configuration Classes

  * Importing Additional Configuration Classes
  * Importing XML Configuration



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/using/configuration-classes.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Developing with Spring Boot](index.html)
  * [Configuration Classes](configuration-classes.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/using/configuration-classes.html)!  
---|---  
  
# Configuration Classes

### Configuration Classes

  * Importing Additional Configuration Classes
  * Importing XML Configuration



Spring Boot favors Java-based configuration. Although it is possible to use [`SpringApplication`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringApplication.html) with XML sources, we generally recommend that your primary source be a single [`@Configuration`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html) class. Usually the class that defines the `main` method is a good candidate as the primary [`@Configuration`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html).

__ |  Many Spring configuration examples have been published on the Internet that use XML configuration. If possible, always try to use the equivalent Java-based configuration. Searching for `Enable*` annotations can be a good starting point.   
---|---  
  
## Importing Additional Configuration Classes

You need not put all your [`@Configuration`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html) into a single class. The [`@Import`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Import.html) annotation can be used to import additional configuration classes. Alternatively, you can use [`@ComponentScan`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/ComponentScan.html) to automatically pick up all Spring components, including [`@Configuration`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html) classes.

## Importing XML Configuration

If you absolutely must use XML based configuration, we recommend that you still start with a [`@Configuration`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html) class. You can then use an [`@ImportResource`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/ImportResource.html) annotation to load XML configuration files.

[Structuring Your Code](structuring-your-code.html) [Auto-configuration](auto-configuration.html)
---
