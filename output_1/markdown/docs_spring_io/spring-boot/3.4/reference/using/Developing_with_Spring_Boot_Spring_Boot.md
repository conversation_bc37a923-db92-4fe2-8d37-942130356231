Title: Developing with Spring Boot :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/using/index.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/using/Developing_with_Spring_Boot_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/using/Developing_with_Spring_Boot_Spring_Boot.png
crawled_at: 2025-06-04T15:48:34.075700
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/using/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Developing with Spring Boot](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/using/index.html)!  
---|---  
  
# Developing with Spring Boot

This section goes into more detail about how you should use Spring Boot. It covers topics such as build systems, auto-configuration, and how to run your applications. We also cover some Spring Boot best practices. Although there is nothing particularly special about Spring Boot (it is just another library that you can consume), there are a few recommendations that, when followed, make your development process a little easier.

If you are starting out with Spring Boot, you should probably read the [Developing Your First Spring Boot Application](../../tutorial/first-application/index.html) tutorial before diving into this section.

[Reference](../index.html) [Build Systems](build-systems.html)
---
