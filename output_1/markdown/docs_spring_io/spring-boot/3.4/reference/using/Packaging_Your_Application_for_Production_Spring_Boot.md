Title: Packaging Your Application for Production :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/using/packaging-for-production.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/using/Packaging_Your_Application_for_Production_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/using/Packaging_Your_Application_for_Production_Spring_Boot.png
crawled_at: 2025-06-04T15:39:04.064403
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/using/packaging-for-production.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Developing with Spring Boot](index.html)
  * [Packaging Your Application for Production](packaging-for-production.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/using/packaging-for-production.html)!  
---|---  
  
# Packaging Your Application for Production

Once your Spring Boot application is ready for production deployment, there are many options for packaging and optimizing the application. See the [Packaging Spring Boot Applications](../packaging/index.html) section of the documentation to read about these features.

For additional "production ready" features, such as health, auditing, and metric REST or JMX end-points, consider adding `spring-boot-actuator`. See [Actuator](../../how-to/actuator.html) for details.

[Developer Tools](devtools.html) [Core Features](../features/index.html)
---
