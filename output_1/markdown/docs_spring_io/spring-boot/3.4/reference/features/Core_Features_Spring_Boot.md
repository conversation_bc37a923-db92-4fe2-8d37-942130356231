Title: Core Features :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/features/index.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/features/Core_Features_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/features/Core_Features_Spring_Boot.png
crawled_at: 2025-06-04T15:38:57.413677
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/features/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Core Features](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/features/index.html)!  
---|---  
  
# Core Features

This section dives into the details of Spring Boot. Here you can learn about the key features that you may want to use and customize. If you have not already done so, you might want to read the [Tutorials](../../tutorial/index.html) and [Developing with Spring Boot](../using/index.html) sections, so that you have a good grounding of the basics.

[Packaging Your Application for Production](../using/packaging-for-production.html) [SpringApplication](spring-application.html)
---
