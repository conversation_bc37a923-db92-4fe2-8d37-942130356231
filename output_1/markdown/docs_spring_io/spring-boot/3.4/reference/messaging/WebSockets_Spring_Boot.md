Title: WebSockets :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/messaging/websockets.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/messaging/WebSockets_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/messaging/WebSockets_Spring_Boot.png
crawled_at: 2025-06-04T15:48:03.802467
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/messaging/websockets.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Messaging](index.html)
  * [WebSockets](websockets.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/messaging/websockets.html)!  
---|---  
  
# WebSockets

Spring Boot provides WebSockets auto-configuration for embedded Tomcat, Jetty, and Undertow. If you deploy a war file to a standalone container, Spring Boot assumes that the container is responsible for the configuration of its WebSocket support.

Spring Framework provides [rich WebSocket support](https://docs.spring.io/spring-framework/reference/6.2/web/websocket.html) for MVC web applications that can be easily accessed through the `spring-boot-starter-websocket` module.

WebSocket support is also available for [reactive web applications](https://docs.spring.io/spring-framework/reference/6.2/web/webflux-websocket.html) and requires to include the WebSocket API alongside `spring-boot-starter-webflux`:
    
    
    <dependency>
    	<groupId>jakarta.websocket</groupId>
    	<artifactId>jakarta.websocket-api</artifactId>
    </dependency>
    
    Copied!

[Spring Integration](spring-integration.html) [Testing](../testing/index.html)
---
