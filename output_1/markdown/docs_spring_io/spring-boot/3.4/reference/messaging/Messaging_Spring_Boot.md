Title: Messaging :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/messaging/index.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/messaging/Messaging_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/messaging/Messaging_Spring_Boot.png
crawled_at: 2025-06-04T15:55:13.121727
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/messaging/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Messaging](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/messaging/index.html)!  
---|---  
  
# Messaging

The Spring Framework provides extensive support for integrating with messaging systems, from simplified use of the JMS API using [`JmsTemplate`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jms/core/JmsTemplate.html) to a complete infrastructure to receive messages asynchronously. Spring AMQP provides a similar feature set for the Advanced Message Queuing Protocol. Spring Boot also provides auto-configuration options for [`RabbitTemplate`](https://docs.spring.io/spring-amqp/docs/3.2.x/api/org/springframework/amqp/rabbit/core/RabbitTemplate.html) and RabbitMQ. Spring WebSocket natively includes support for STOMP messaging, and Spring Boot has support for that through starters and a small amount of auto-configuration. Spring Boot also has support for Apache Kafka and Apache Pulsar.

[Distributed Transactions With JTA](../io/jta.html) [JMS](jms.html)
---
