Title: Configuration Metadata :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/specification/configuration-metadata/index.html
HTML: html/docs_spring_io/spring-boot/3.4/specification/configuration-metadata/Configuration_Metadata_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/specification/configuration-metadata/Configuration_Metadata_Spring_Boot.png
crawled_at: 2025-06-04T15:48:15.467938
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/specification/pages/configuration-metadata/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * Specifications
  * [Configuration Metadata](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../specification/configuration-metadata/index.html)!  
---|---  
  
# Configuration Metadata

Spring Boot jars include metadata files that provide details of all supported configuration properties. The files are designed to let IDE developers offer contextual help and “code completion” as users are working with `application.properties` or `application.yaml` files.

The majority of the metadata file is generated automatically at compile time by processing all items annotated with [`@ConfigurationProperties`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html). However, it is possible to [write part of the metadata manually](annotation-processor.html#appendix.configuration-metadata.annotation-processor.adding-additional-metadata) for corner cases or more advanced use cases.

[Spring Boot](../../api/kotlin/index.html) [Metadata Format](format.html)
---
