Title: The Executable Jar Format :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/specification/executable-jar/index.html
HTML: html/docs_spring_io/spring-boot/3.4/specification/executable-jar/The_Executable_Jar_Format_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/specification/executable-jar/The_Executable_Jar_Format_Spring_Boot.png
crawled_at: 2025-06-04T15:55:15.825687
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/specification/pages/executable-jar/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * Specifications
  * [The Executable Jar Format](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../specification/executable-jar/index.html)!  
---|---  
  
# The Executable Jar Format

The `spring-boot-loader` modules lets Spring Boot support executable jar and war files. If you use the Maven plugin or the Gradle plugin, executable jars are automatically generated, and you generally do not need to know the details of how they work.

If you need to create executable jars from a different build system or if you are just curious about the underlying technology, this appendix provides some background.

[Generating Your Own Metadata by Using the Annotation Processor](../configuration-metadata/annotation-processor.html) [Nested JARs](nested-jars.html)
---
