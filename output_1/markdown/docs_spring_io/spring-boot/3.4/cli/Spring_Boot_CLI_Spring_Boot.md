Title: Spring Boot CLI :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/cli/index.html
HTML: html/docs_spring_io/spring-boot/3.4/cli/Spring_Boot_CLI_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/cli/Spring_Boot_CLI_Spring_Boot.png
crawled_at: 2025-06-04T15:48:13.184956
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/cli/pages/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Spring Boot CLI](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../cli/index.html)!  
---|---  
  
# Spring Boot CLI

The Spring Boot CLI is a command line tool that you can use to bootstrap a new project from [start.spring.io](https://start.spring.io) or encode a password.

[Supporting Other Build Systems](../build-tool-plugin/other-build-systems.html) [Installing the CLI](installation.html)
---
