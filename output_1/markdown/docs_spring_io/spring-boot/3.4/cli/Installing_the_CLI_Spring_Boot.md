Title: Installing the CLI :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/cli/installation.html
HTML: html/docs_spring_io/spring-boot/3.4/cli/Installing_the_CLI_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/cli/Installing_the_CLI_Spring_Boot.png
crawled_at: 2025-06-04T15:55:01.527344
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/cli/pages/installation.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Spring Boot CLI](index.html)
  * [Installing the CLI](installation.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../cli/installation.html)!  
---|---  
  
# Installing the CLI

The Spring Boot CLI (Command-Line Interface) can be installed manually by using SDKMAN! (the SDK Manager) or by using Homebrew or MacPorts if you are an OSX user. See [Installing the Spring Boot CLI](../installing.html#getting-started.installing.cli) in the “Getting Started” section for comprehensive installation instructions.

[Spring Boot CLI](index.html) [Using the CLI](using-the-cli.html)
---
