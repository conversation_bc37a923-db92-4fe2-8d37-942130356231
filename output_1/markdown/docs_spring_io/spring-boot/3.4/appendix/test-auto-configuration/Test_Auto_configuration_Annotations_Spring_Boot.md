Title: Test Auto-configuration Annotations :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/appendix/test-auto-configuration/index.html
HTML: html/docs_spring_io/spring-boot/3.4/appendix/test-auto-configuration/Test_Auto_configuration_Annotations_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/appendix/test-auto-configuration/Test_Auto_configuration_Annotations_Spring_Boot.png
crawled_at: 2025-06-04T15:41:06.033844
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/appendix/pages/test-auto-configuration/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * Appendix
  * [Test Auto-configuration Annotations](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../appendix/test-auto-configuration/index.html)!  
---|---  
  
# Test Auto-configuration Annotations

This appendix describes the `@…​Test` auto-configuration annotations that Spring Boot provides to test slices of your application.

[spring-boot-actuator-autoconfigure](../auto-configuration-classes/actuator.html) [Test Slices](slices.html)
---
