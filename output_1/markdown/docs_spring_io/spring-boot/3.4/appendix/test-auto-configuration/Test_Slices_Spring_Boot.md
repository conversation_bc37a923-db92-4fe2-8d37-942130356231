Title: Test Slices :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/appendix/test-auto-configuration/slices.html
HTML: html/docs_spring_io/spring-boot/3.4/appendix/test-auto-configuration/Test_Slices_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/appendix/test-auto-configuration/Test_Slices_Spring_Boot.png
crawled_at: 2025-06-04T15:48:50.207566
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/appendix/pages/test-auto-configuration/slices.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * Appendix
  * [Test Auto-configuration Annotations](index.html)
  * [Test Slices](slices.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../appendix/test-auto-configuration/slices.html)!  
---|---  
  
# Test Slices

The following table lists the various `@…​Test` annotations that can be used to test slices of your application and the auto-configuration that they import by default:

Test slice | Imported auto-configuration  
---|---  
`@DataCassandraTest` |  `optional:org.springframework.boot.testcontainers.service.connection.ServiceConnectionAutoConfiguration` `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.cassandra.CassandraAutoConfiguration` `org.springframework.boot.autoconfigure.data.cassandra.CassandraDataAutoConfiguration` `org.springframework.boot.autoconfigure.data.cassandra.CassandraReactiveDataAutoConfiguration` `org.springframework.boot.autoconfigure.data.cassandra.CassandraReactiveRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.data.cassandra.CassandraRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration`  
`@DataCouchbaseTest` |  `optional:org.springframework.boot.testcontainers.service.connection.ServiceConnectionAutoConfiguration` `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.couchbase.CouchbaseAutoConfiguration` `org.springframework.boot.autoconfigure.data.couchbase.CouchbaseDataAutoConfiguration` `org.springframework.boot.autoconfigure.data.couchbase.CouchbaseReactiveDataAutoConfiguration` `org.springframework.boot.autoconfigure.data.couchbase.CouchbaseReactiveRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.data.couchbase.CouchbaseRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration`  
`@DataElasticsearchTest` |  `optional:org.springframework.boot.testcontainers.service.connection.ServiceConnectionAutoConfiguration` `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration` `org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.data.elasticsearch.ReactiveElasticsearchRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchClientAutoConfiguration` `org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration` `org.springframework.boot.autoconfigure.elasticsearch.ReactiveElasticsearchClientAutoConfiguration` `org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration` `org.springframework.boot.autoconfigure.jsonb.JsonbAutoConfiguration` `org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration`  
`@DataJdbcTest` |  `optional:org.springframework.boot.testcontainers.service.connection.ServiceConnectionAutoConfiguration` `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.data.jdbc.JdbcRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.JdbcClientAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration` `org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration` `org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration` `org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration` `org.springframework.boot.test.autoconfigure.jdbc.TestDatabaseAutoConfiguration`  
`@DataJpaTest` |  `optional:org.springframework.boot.testcontainers.service.connection.ServiceConnectionAutoConfiguration` `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.JdbcClientAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration` `org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration` `org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration` `org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration` `org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration` `org.springframework.boot.test.autoconfigure.jdbc.TestDatabaseAutoConfiguration` `org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManagerAutoConfiguration`  
`@DataLdapTest` |  `optional:org.springframework.boot.testcontainers.service.connection.ServiceConnectionAutoConfiguration` `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.data.ldap.LdapRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.ldap.LdapAutoConfiguration` `org.springframework.boot.autoconfigure.ldap.embedded.EmbeddedLdapAutoConfiguration`  
`@DataMongoTest` |  `optional:org.springframework.boot.testcontainers.service.connection.ServiceConnectionAutoConfiguration` `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration` `org.springframework.boot.autoconfigure.data.mongo.MongoReactiveDataAutoConfiguration` `org.springframework.boot.autoconfigure.data.mongo.MongoReactiveRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.data.mongo.MongoRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration` `org.springframework.boot.autoconfigure.mongo.MongoReactiveAutoConfiguration` `org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration` `org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration`  
`@DataNeo4jTest` |  `optional:org.springframework.boot.testcontainers.service.connection.ServiceConnectionAutoConfiguration` `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.data.neo4j.Neo4jDataAutoConfiguration` `org.springframework.boot.autoconfigure.data.neo4j.Neo4jReactiveDataAutoConfiguration` `org.springframework.boot.autoconfigure.data.neo4j.Neo4jReactiveRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.data.neo4j.Neo4jRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.neo4j.Neo4jAutoConfiguration` `org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration`  
`@DataR2dbcTest` |  `optional:org.springframework.boot.testcontainers.service.connection.ServiceConnectionAutoConfiguration` `org.springframework.boot.autoconfigure.data.r2dbc.R2dbcDataAutoConfiguration` `org.springframework.boot.autoconfigure.data.r2dbc.R2dbcRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration` `org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration` `org.springframework.boot.autoconfigure.r2dbc.R2dbcAutoConfiguration` `org.springframework.boot.autoconfigure.r2dbc.R2dbcTransactionManagerAutoConfiguration` `org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration` `org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration`  
`@DataRedisTest` |  `optional:org.springframework.boot.testcontainers.service.connection.ServiceConnectionAutoConfiguration` `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration` `org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration` `org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration` `org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration`  
`@GraphQlTest` |  `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.graphql.GraphQlAutoConfiguration` `org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration` `org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration` `org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration` `org.springframework.boot.autoconfigure.jsonb.JsonbAutoConfiguration` `org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration` `org.springframework.boot.test.autoconfigure.graphql.tester.GraphQlTesterAutoConfiguration`  
`@JdbcTest` |  `optional:org.springframework.boot.testcontainers.service.connection.ServiceConnectionAutoConfiguration` `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.JdbcClientAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration` `org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration` `org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration` `org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration` `org.springframework.boot.test.autoconfigure.jdbc.TestDatabaseAutoConfiguration`  
`@JooqTest` |  `optional:org.springframework.boot.testcontainers.service.connection.ServiceConnectionAutoConfiguration` `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration` `org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration` `org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration` `org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration` `org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration` `org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration`  
`@JsonTest` |  `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.test.autoconfigure.json.JsonTestersAutoConfiguration`  
`@RestClientTest` |  `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration` `org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration` `org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration` `org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration` `org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientAutoConfiguration` `org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerAutoConfiguration` `org.springframework.boot.test.autoconfigure.web.client.WebClientRestTemplateAutoConfiguration`  
`@WebFluxTest` |  `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration` `org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration` `org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration` `org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration` `org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration` `org.springframework.boot.autoconfigure.jsonb.JsonbAutoConfiguration` `org.springframework.boot.autoconfigure.mustache.MustacheAutoConfiguration` `org.springframework.boot.autoconfigure.security.oauth2.client.reactive.ReactiveOAuth2ClientAutoConfiguration` `org.springframework.boot.autoconfigure.security.oauth2.resource.reactive.ReactiveOAuth2ResourceServerAutoConfiguration` `org.springframework.boot.autoconfigure.security.reactive.ReactiveSecurityAutoConfiguration` `org.springframework.boot.autoconfigure.security.reactive.ReactiveUserDetailsServiceAutoConfiguration` `org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration` `org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration` `org.springframework.boot.autoconfigure.web.reactive.WebFluxAutoConfiguration` `org.springframework.boot.autoconfigure.web.reactive.error.ErrorWebFluxAutoConfiguration` `org.springframework.boot.test.autoconfigure.web.reactive.WebTestClientAutoConfiguration`  
`@WebMvcTest` |  `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration` `org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration` `org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration` `org.springframework.boot.autoconfigure.groovy.template.GroovyTemplateAutoConfiguration` `org.springframework.boot.autoconfigure.hateoas.HypermediaAutoConfiguration` `org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration` `org.springframework.boot.autoconfigure.mustache.MustacheAutoConfiguration` `org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration` `org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration` `org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration` `org.springframework.boot.autoconfigure.security.servlet.SecurityFilterAutoConfiguration` `org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration` `org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration` `org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration` `org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration` `org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration` `org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration` `org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration` `org.springframework.boot.test.autoconfigure.web.reactive.WebTestClientAutoConfiguration` `org.springframework.boot.test.autoconfigure.web.servlet.MockMvcAutoConfiguration` `org.springframework.boot.test.autoconfigure.web.servlet.MockMvcSecurityConfiguration` `org.springframework.boot.test.autoconfigure.web.servlet.MockMvcWebClientAutoConfiguration` `org.springframework.boot.test.autoconfigure.web.servlet.MockMvcWebDriverAutoConfiguration`  
`@WebServiceClientTest` |  `org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration` `org.springframework.boot.autoconfigure.webservices.client.WebServiceTemplateAutoConfiguration` `org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerAutoConfiguration` `org.springframework.boot.test.autoconfigure.webservices.client.WebServiceClientTemplateAutoConfiguration`  
`@WebServiceServerTest` |  `org.springframework.boot.autoconfigure.webservices.WebServicesAutoConfiguration` `org.springframework.boot.test.autoconfigure.webservices.server.MockWebServiceClientAutoConfiguration`  
[Test Auto-configuration Annotations](index.html) [Dependency Versions](../dependency-versions/index.html)
---
