Title: Audit Events (auditevents) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/auditevents.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Audit_Events_auditevents_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Audit_Events_auditevents_Spring_Boot.png
crawled_at: 2025-06-04T15:41:48.942181
---
Search CTRL + k

### Audit Events (auditevents)

  * Retrieving Audit Events
  * Query Parameters
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/auditevents.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Audit Events (`auditevents`)](auditevents.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/auditevents.html)!  
---|---  
  
# Audit Events (`auditevents`)

### Audit Events (auditevents)

  * Retrieving Audit Events
  * Query Parameters
  * Response Structure



The `auditevents` endpoint provides information about the application’s audit events.

## Retrieving Audit Events

To retrieve the audit events, make a `GET` request to `/actuator/auditevents`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/auditevents?principal=alice&after=2025-05-22T09%3A59%3A41.743436132Z&type=logout' -i -X GET
    
    Copied!

The preceding example retrieves `logout` events for the principal, `alice`, that occurred after 09:37 on 7 November 2017 in the UTC timezone. The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 127
    
    {
      "events" : [ {
        "timestamp" : "2025-05-22T09:59:41.744015125Z",
        "principal" : "alice",
        "type" : "logout"
      } ]
    }
    
    Copied!

### Query Parameters

The endpoint uses query parameters to limit the events that it returns. The following table shows the supported query parameters:

Parameter | Description  
---|---  
`after` | Restricts the events to those that occurred after the given time. Optional.  
`principal` | Restricts the events to those with the given principal. Optional.  
`type` | Restricts the events to those with the given type. Optional.  
  
### Response Structure

The response contains details of all of the audit events that matched the query. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`events` | `Array` | An array of audit events.  
`events.[].timestamp` | `String` | The timestamp of when the event occurred.  
`events.[].principal` | `String` | The principal that triggered the event.  
`events.[].type` | `String` | The type of the event.  
  
[Actuator](index.html) [Beans (`beans`)](beans.html)
---
