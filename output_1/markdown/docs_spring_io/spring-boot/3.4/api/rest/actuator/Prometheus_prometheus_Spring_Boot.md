Title: Prometheus (prometheus) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/prometheus.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Prometheus_prometheus_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Prometheus_prometheus_Spring_Boot.png
crawled_at: 2025-06-04T15:38:17.568204
---
Search CTRL + k

### Prometheus (prometheus)

  * Retrieving All Metrics
  * Query Parameters
  * Retrieving Filtered Metrics



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/prometheus.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Prometheus (`prometheus`)](prometheus.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/prometheus.html)!  
---|---  
  
# Prometheus (`prometheus`)

### Prometheus (prometheus)

  * Retrieving All Metrics
  * Query Parameters
  * Retrieving Filtered Metrics



The `prometheus` endpoint provides Spring Boot application’s metrics in the format required for scraping by a Prometheus server.

## Retrieving All Metrics

To retrieve all metrics, make a `GET` request to `/actuator/prometheus`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/prometheus' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: text/plain;version=0.0.4;charset=utf-8
    Content-Length: 3088
    
    # HELP jvm_buffer_count_buffers An estimate of the number of buffers in the pool
    # TYPE jvm_buffer_count_buffers gauge
    jvm_buffer_count_buffers{id="direct"} 3.0
    jvm_buffer_count_buffers{id="mapped"} 0.0
    jvm_buffer_count_buffers{id="mapped - 'non-volatile memory'"} 0.0
    # HELP jvm_buffer_memory_used_bytes An estimate of the memory that the Java virtual machine is using for this buffer pool
    # TYPE jvm_buffer_memory_used_bytes gauge
    jvm_buffer_memory_used_bytes{id="direct"} 45056.0
    jvm_buffer_memory_used_bytes{id="mapped"} 0.0
    jvm_buffer_memory_used_bytes{id="mapped - 'non-volatile memory'"} 0.0
    # HELP jvm_buffer_total_capacity_bytes An estimate of the total capacity of the buffers in this pool
    # TYPE jvm_buffer_total_capacity_bytes gauge
    jvm_buffer_total_capacity_bytes{id="direct"} 45056.0
    jvm_buffer_total_capacity_bytes{id="mapped"} 0.0
    jvm_buffer_total_capacity_bytes{id="mapped - 'non-volatile memory'"} 0.0
    # HELP jvm_memory_committed_bytes The amount of memory in bytes that is committed for the Java virtual machine to use
    # TYPE jvm_memory_committed_bytes gauge
    jvm_memory_committed_bytes{area="heap",id="G1 Eden Space"} 1.42606336E8
    jvm_memory_committed_bytes{area="heap",id="G1 Old Gen"} 1.24780544E8
    jvm_memory_committed_bytes{area="heap",id="G1 Survivor Space"} 3145728.0
    jvm_memory_committed_bytes{area="nonheap",id="CodeHeap 'non-nmethods'"} 2555904.0
    jvm_memory_committed_bytes{area="nonheap",id="CodeHeap 'non-profiled nmethods'"} 4390912.0
    jvm_memory_committed_bytes{area="nonheap",id="CodeHeap 'profiled nmethods'"} 1.8612224E7
    jvm_memory_committed_bytes{area="nonheap",id="Compressed Class Space"} 1.114112E7
    jvm_memory_committed_bytes{area="nonheap",id="Metaspace"} 7.897088E7
    # HELP jvm_memory_max_bytes The maximum amount of memory in bytes that can be used for memory management
    # TYPE jvm_memory_max_bytes gauge
    jvm_memory_max_bytes{area="heap",id="G1 Eden Space"} -1.0
    jvm_memory_max_bytes{area="heap",id="G1 Old Gen"} 1.073741824E9
    jvm_memory_max_bytes{area="heap",id="G1 Survivor Space"} -1.0
    jvm_memory_max_bytes{area="nonheap",id="CodeHeap 'non-nmethods'"} 5836800.0
    jvm_memory_max_bytes{area="nonheap",id="CodeHeap 'non-profiled nmethods'"} 1.22912768E8
    jvm_memory_max_bytes{area="nonheap",id="CodeHeap 'profiled nmethods'"} 1.22908672E8
    jvm_memory_max_bytes{area="nonheap",id="Compressed Class Space"} 1.073741824E9
    jvm_memory_max_bytes{area="nonheap",id="Metaspace"} -1.0
    # HELP jvm_memory_used_bytes The amount of used memory
    # TYPE jvm_memory_used_bytes gauge
    jvm_memory_used_bytes{area="heap",id="G1 Eden Space"} 8.912896E7
    jvm_memory_used_bytes{area="heap",id="G1 Old Gen"} 7.9549952E7
    jvm_memory_used_bytes{area="heap",id="G1 Survivor Space"} 2989992.0
    jvm_memory_used_bytes{area="nonheap",id="CodeHeap 'non-nmethods'"} 1378432.0
    jvm_memory_used_bytes{area="nonheap",id="CodeHeap 'non-profiled nmethods'"} 4379904.0
    jvm_memory_used_bytes{area="nonheap",id="CodeHeap 'profiled nmethods'"} 1.8612096E7
    jvm_memory_used_bytes{area="nonheap",id="Compressed Class Space"} 1.0808384E7
    jvm_memory_used_bytes{area="nonheap",id="Metaspace"} 7.8190304E7
    
    Copied!

The default response content type is `text/plain;version=0.0.4`. The endpoint can also produce `application/openmetrics-text;version=1.0.0` when called with an appropriate `Accept` header, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/prometheus' -i -X GET \
        -H 'Accept: application/openmetrics-text; version=1.0.0; charset=utf-8'
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/openmetrics-text;version=1.0.0;charset=utf-8
    Content-Length: 3094
    
    # TYPE jvm_buffer_count_buffers gauge
    # HELP jvm_buffer_count_buffers An estimate of the number of buffers in the pool
    jvm_buffer_count_buffers{id="direct"} 3.0
    jvm_buffer_count_buffers{id="mapped"} 0.0
    jvm_buffer_count_buffers{id="mapped - 'non-volatile memory'"} 0.0
    # TYPE jvm_buffer_memory_used_bytes gauge
    # HELP jvm_buffer_memory_used_bytes An estimate of the memory that the Java virtual machine is using for this buffer pool
    jvm_buffer_memory_used_bytes{id="direct"} 45056.0
    jvm_buffer_memory_used_bytes{id="mapped"} 0.0
    jvm_buffer_memory_used_bytes{id="mapped - 'non-volatile memory'"} 0.0
    # TYPE jvm_buffer_total_capacity_bytes gauge
    # HELP jvm_buffer_total_capacity_bytes An estimate of the total capacity of the buffers in this pool
    jvm_buffer_total_capacity_bytes{id="direct"} 45056.0
    jvm_buffer_total_capacity_bytes{id="mapped"} 0.0
    jvm_buffer_total_capacity_bytes{id="mapped - 'non-volatile memory'"} 0.0
    # TYPE jvm_memory_committed_bytes gauge
    # HELP jvm_memory_committed_bytes The amount of memory in bytes that is committed for the Java virtual machine to use
    jvm_memory_committed_bytes{area="heap",id="G1 Eden Space"} 1.42606336E8
    jvm_memory_committed_bytes{area="heap",id="G1 Old Gen"} 1.24780544E8
    jvm_memory_committed_bytes{area="heap",id="G1 Survivor Space"} 3145728.0
    jvm_memory_committed_bytes{area="nonheap",id="CodeHeap 'non-nmethods'"} 2555904.0
    jvm_memory_committed_bytes{area="nonheap",id="CodeHeap 'non-profiled nmethods'"} 4325376.0
    jvm_memory_committed_bytes{area="nonheap",id="CodeHeap 'profiled nmethods'"} 1.8546688E7
    jvm_memory_committed_bytes{area="nonheap",id="Compressed Class Space"} 1.114112E7
    jvm_memory_committed_bytes{area="nonheap",id="Metaspace"} 7.8774272E7
    # TYPE jvm_memory_max_bytes gauge
    # HELP jvm_memory_max_bytes The maximum amount of memory in bytes that can be used for memory management
    jvm_memory_max_bytes{area="heap",id="G1 Eden Space"} -1.0
    jvm_memory_max_bytes{area="heap",id="G1 Old Gen"} 1.073741824E9
    jvm_memory_max_bytes{area="heap",id="G1 Survivor Space"} -1.0
    jvm_memory_max_bytes{area="nonheap",id="CodeHeap 'non-nmethods'"} 5836800.0
    jvm_memory_max_bytes{area="nonheap",id="CodeHeap 'non-profiled nmethods'"} 1.22912768E8
    jvm_memory_max_bytes{area="nonheap",id="CodeHeap 'profiled nmethods'"} 1.22908672E8
    jvm_memory_max_bytes{area="nonheap",id="Compressed Class Space"} 1.073741824E9
    jvm_memory_max_bytes{area="nonheap",id="Metaspace"} -1.0
    # TYPE jvm_memory_used_bytes gauge
    # HELP jvm_memory_used_bytes The amount of used memory
    jvm_memory_used_bytes{area="heap",id="G1 Eden Space"} 8.7031808E7
    jvm_memory_used_bytes{area="heap",id="G1 Old Gen"} 7.9549952E7
    jvm_memory_used_bytes{area="heap",id="G1 Survivor Space"} 2989992.0
    jvm_memory_used_bytes{area="nonheap",id="CodeHeap 'non-nmethods'"} 1378432.0
    jvm_memory_used_bytes{area="nonheap",id="CodeHeap 'non-profiled nmethods'"} 4311040.0
    jvm_memory_used_bytes{area="nonheap",id="CodeHeap 'profiled nmethods'"} 1.8515456E7
    jvm_memory_used_bytes{area="nonheap",id="Compressed Class Space"} 1.07958E7
    jvm_memory_used_bytes{area="nonheap",id="Metaspace"} 7.8041728E7
    # EOF
    
    Copied!

### Query Parameters

The endpoint uses query parameters to limit the samples that it returns. The following table shows the supported query parameters:

Parameter | Description  
---|---  
`includedNames` | Restricts the samples to those that match the names. Optional.  
  
## Retrieving Filtered Metrics

To retrieve metrics matching specific names, make a `GET` request to `/actuator/prometheus` with the `includedNames` query parameter, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/prometheus?includedNames=jvm_memory_used_bytes%2Cjvm_memory_committed_bytes' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: text/plain;version=0.0.4;charset=utf-8
    Content-Length: 1460
    
    # HELP jvm_memory_committed_bytes The amount of memory in bytes that is committed for the Java virtual machine to use
    # TYPE jvm_memory_committed_bytes gauge
    jvm_memory_committed_bytes{area="heap",id="G1 Eden Space"} 1.42606336E8
    jvm_memory_committed_bytes{area="heap",id="G1 Old Gen"} 1.24780544E8
    jvm_memory_committed_bytes{area="heap",id="G1 Survivor Space"} 3145728.0
    jvm_memory_committed_bytes{area="nonheap",id="CodeHeap 'non-nmethods'"} 2555904.0
    jvm_memory_committed_bytes{area="nonheap",id="CodeHeap 'non-profiled nmethods'"} 4390912.0
    jvm_memory_committed_bytes{area="nonheap",id="CodeHeap 'profiled nmethods'"} 1.867776E7
    jvm_memory_committed_bytes{area="nonheap",id="Compressed Class Space"} 1.114112E7
    jvm_memory_committed_bytes{area="nonheap",id="Metaspace"} 7.897088E7
    # HELP jvm_memory_used_bytes The amount of used memory
    # TYPE jvm_memory_used_bytes gauge
    jvm_memory_used_bytes{area="heap",id="G1 Eden Space"} 9.0177536E7
    jvm_memory_used_bytes{area="heap",id="G1 Old Gen"} 7.9549952E7
    jvm_memory_used_bytes{area="heap",id="G1 Survivor Space"} 2989992.0
    jvm_memory_used_bytes{area="nonheap",id="CodeHeap 'non-nmethods'"} 1378432.0
    jvm_memory_used_bytes{area="nonheap",id="CodeHeap 'non-profiled nmethods'"} 4380672.0
    jvm_memory_used_bytes{area="nonheap",id="CodeHeap 'profiled nmethods'"} 1.864256E7
    jvm_memory_used_bytes{area="nonheap",id="Compressed Class Space"} 1.0808984E7
    jvm_memory_used_bytes{area="nonheap",id="Metaspace"} 7.8205472E7
    
    Copied!

[Metrics (`metrics`)](metrics.html) [Quartz (`quartz`)](quartz.html)
---
