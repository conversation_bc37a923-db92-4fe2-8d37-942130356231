Title: Health (health) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/health.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Health_health_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Health_health_Spring_Boot.png
crawled_at: 2025-06-04T15:49:16.678372
---
Search CTRL + k

### Health (health)

  * Retrieving the Health of the Application
  * Response Structure
  * Retrieving the Health of a Component
  * Response Structure
  * Retrieving the Health of a Nested Component
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/health.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Health (`health`)](health.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/health.html)!  
---|---  
  
# Health (`health`)

### Health (health)

  * Retrieving the Health of the Application
  * Response Structure
  * Retrieving the Health of a Component
  * Response Structure
  * Retrieving the Health of a Nested Component
  * Response Structure



The `health` endpoint provides detailed information about the health of the application.

## Retrieving the Health of the Application

To retrieve the health of the application, make a `GET` request to `/actuator/health`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/health' -i -X GET \
        -H 'Accept: application/json'
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/json
    Content-Length: 821
    
    {
      "status" : "UP",
      "components" : {
        "broker" : {
          "status" : "UP",
          "components" : {
            "us1" : {
              "status" : "UP",
              "details" : {
                "version" : "1.0.2"
              }
            },
            "us2" : {
              "status" : "UP",
              "details" : {
                "version" : "1.0.4"
              }
            }
          }
        },
        "db" : {
          "status" : "UP",
          "details" : {
            "database" : "H2",
            "validationQuery" : "isValid()"
          }
        },
        "diskSpace" : {
          "status" : "UP",
          "details" : {
            "total" : 76887154688,
            "free" : 52271083520,
            "threshold" : 10485760,
            "path" : "/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot-actuator-autoconfigure/.",
            "exists" : true
          }
        }
      }
    }
    
    Copied!

### Response Structure

The response contains details of the health of the application. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`status` | `String` | Overall status of the application.  
`components` | `Object` | The components that make up the health.  
`components.*.status` | `String` | Status of a specific part of the application.  
`components.*.components` | `Object` | The nested components that make up the health.  
`components.*.details` | `Object` | Details of the health of a specific part of the application. Presence is controlled by `management.endpoint.health.show-details`.  
  
__ |  The response fields above are for the V3 API. If you need to return V2 JSON you should use an accept header or `application/vnd.spring-boot.actuator.v2+json`  
---|---  
  
## Retrieving the Health of a Component

To retrieve the health of a particular component of the application’s health, make a `GET` request to `/actuator/health/{component}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/health/db' -i -X GET \
        -H 'Accept: application/json'
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/json
    Content-Length: 101
    
    {
      "status" : "UP",
      "details" : {
        "database" : "H2",
        "validationQuery" : "isValid()"
      }
    }
    
    Copied!

### Response Structure

The response contains details of the health of a particular component of the application’s health. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`status` | `String` | Status of a specific part of the application  
`details` | `Object` | Details of the health of a specific part of the application.  
  
## Retrieving the Health of a Nested Component

If a particular component contains other nested components (as the `broker` indicator in the example above), the health of such a nested component can be retrieved by issuing a `GET` request to `/actuator/health/{component}/{subcomponent}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/health/broker/us1' -i -X GET \
        -H 'Accept: application/json'
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/json
    Content-Length: 66
    
    {
      "status" : "UP",
      "details" : {
        "version" : "1.0.2"
      }
    }
    
    Copied!

Components of an application’s health may be nested arbitrarily deep depending on the application’s health indicators and how they have been grouped. The health endpoint supports any number of `/{component}` identifiers in the URL to allow the health of a component at any depth to be retrieved.

### Response Structure

The response contains details of the health of an instance of a particular component of the application. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`status` | `String` | Status of a specific part of the application  
`details` | `Object` | Details of the health of a specific part of the application.  
  
[Flyway (`flyway`)](flyway.html) [Heap Dump (`heapdump`)](heapdump.html)
---
