Title: Metrics (metrics) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/metrics.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Metrics_metrics_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Metrics_metrics_Spring_Boot.png
crawled_at: 2025-06-04T15:41:51.642641
---
Search CTRL + k

### Metrics (metrics)

  * Retrieving Metric Names
  * Response Structure
  * Retrieving a Metric
  * Query Parameters
  * Response Structure
  * Drilling Down



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/metrics.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Metrics (`metrics`)](metrics.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/metrics.html)!  
---|---  
  
# Metrics (`metrics`)

### Metrics (metrics)

  * Retrieving Metric Names
  * Response Structure
  * Retrieving a Metric
  * Query Parameters
  * Response Structure
  * Drilling Down



The `metrics` endpoint provides access to application metrics to diagnose the metrics the application has recorded. This endpoint should not be "scraped" or used as a metrics backend in production. Its purpose is to show the currently registered metrics so users can see what metrics are available, what their current values are, and if triggering certain operations causes any change in certain values. If you want to diagnose your applications through the metrics they collect, you should use an [external metrics backend](../../../reference/actuator/metrics.html). In this case, the `metrics` endpoint can still be useful.

## Retrieving Metric Names

To retrieve the names of the available metrics, make a `GET` request to `/actuator/metrics`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/metrics' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 154
    
    {
      "names" : [ "jvm.buffer.count", "jvm.buffer.memory.used", "jvm.buffer.total.capacity", "jvm.memory.committed", "jvm.memory.max", "jvm.memory.used" ]
    }
    
    Copied!

### Response Structure

The response contains details of the metric names. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`names` | `Array` | Names of the known metrics.  
  
## Retrieving a Metric

To retrieve a metric, make a `GET` request to `/actuator/metrics/{metric.name}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/metrics/jvm.memory.max' -i -X GET
    
    Copied!

The preceding example retrieves information about the metric named `jvm.memory.max`. The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Disposition: inline;filename=f.txt
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 555
    
    {
      "name" : "jvm.memory.max",
      "description" : "The maximum amount of memory in bytes that can be used for memory management",
      "baseUnit" : "bytes",
      "measurements" : [ {
        "statistic" : "VALUE",
        "value" : 2.399141885E9
      } ],
      "availableTags" : [ {
        "tag" : "area",
        "values" : [ "heap", "nonheap" ]
      }, {
        "tag" : "id",
        "values" : [ "CodeHeap 'profiled nmethods'", "G1 Old Gen", "CodeHeap 'non-profiled nmethods'", "G1 Survivor Space", "Compressed Class Space", "Metaspace", "G1 Eden Space", "CodeHeap 'non-nmethods'" ]
      } ]
    }
    
    Copied!

### Query Parameters

The endpoint uses query parameters to drill down into a metric by using its tags. The following table shows the single supported query parameter:

Parameter | Description  
---|---  
`tag` | A tag to use for drill-down in the form `name:value`.  
  
### Response Structure

The response contains details of the metric. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`name` | `String` | Name of the metric  
`description` | `String` | Description of the metric  
`baseUnit` | `String` | Base unit of the metric  
`measurements` | `Array` | Measurements of the metric  
`measurements[].statistic` | `String` | Statistic of the measurement. (`TOTAL`, `TOTAL_TIME`, `COUNT`, `MAX`, `VALUE`, `UNKNOWN`, `ACTIVE_TASKS`, `DURATION`).  
`measurements[].value` | `Number` | Value of the measurement.  
`availableTags` | `Array` | Tags that are available for drill-down.  
`availableTags[].tag` | `String` | Name of the tag.  
`availableTags[].values` | `Array` | Possible values of the tag.  
  
## Drilling Down

To drill down into a metric, make a `GET` request to `/actuator/metrics/{metric.name}` using the `tag` query parameter, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/metrics/jvm.memory.max?tag=area%3Anonheap&tag=id%3ACompressed+Class+Space' -i -X GET
    
    Copied!

The preceding example retrieves the `jvm.memory.max` metric, where the `area` tag has a value of `nonheap` and the `id` attribute has a value of `Compressed Class Space`. The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Disposition: inline;filename=f.txt
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 263
    
    {
      "name" : "jvm.memory.max",
      "description" : "The maximum amount of memory in bytes that can be used for memory management",
      "baseUnit" : "bytes",
      "measurements" : [ {
        "statistic" : "VALUE",
        "value" : 1.073741824E9
      } ],
      "availableTags" : [ ]
    }
    
    Copied!

[Mappings (`mappings`)](mappings.html) [Prometheus (`prometheus`)](prometheus.html)
---
