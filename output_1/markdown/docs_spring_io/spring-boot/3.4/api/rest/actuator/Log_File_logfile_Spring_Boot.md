Title: Log File (logfile) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/logfile.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Log_File_logfile_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Log_File_logfile_Spring_Boot.png
crawled_at: 2025-06-04T15:38:46.258981
---
Search CTRL + k

### Log File (logfile)

  * Retrieving the Log File
  * Retrieving Part of the Log File



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/logfile.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Log File (`logfile`)](logfile.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/logfile.html)!  
---|---  
  
# Log File (`logfile`)

### Log File (logfile)

  * Retrieving the Log File
  * Retrieving Part of the Log File



The `logfile` endpoint provides access to the contents of the application’s log file.

## Retrieving the Log File

To retrieve the log file, make a `GET` request to `/actuator/logfile`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/logfile' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Accept-Ranges: bytes
    Content-Type: text/plain;charset=UTF-8
    Content-Length: 4707
    
      .   ____          _            __ _ _
     /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
    ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
     \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
      '  |____| .__|_| |_|_| |_\__, | / / / /
     =========|_|==============|___/=/_/_/_/
     :: Spring Boot ::
    
    2017-08-08 17:12:30.910  INFO 19866 --- [           main] s.f.SampleWebFreeMarkerApplication       : Starting SampleWebFreeMarkerApplication with PID 19866
    2017-08-08 17:12:30.913  INFO 19866 --- [           main] s.f.SampleWebFreeMarkerApplication       : No active profile set, falling back to default profiles: default
    2017-08-08 17:12:30.952  INFO 19866 --- [           main] ConfigServletWebServerApplicationContext : Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@76b10754: startup date [Tue Aug 08 17:12:30 BST 2017]; root of context hierarchy
    2017-08-08 17:12:31.878  INFO 19866 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
    2017-08-08 17:12:31.889  INFO 19866 --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
    2017-08-08 17:12:31.890  INFO 19866 --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet Engine: Apache Tomcat/8.5.16
    2017-08-08 17:12:31.978  INFO 19866 --- [ost-startStop-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
    2017-08-08 17:12:31.978  INFO 19866 --- [ost-startStop-1] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 1028 ms
    2017-08-08 17:12:32.080  INFO 19866 --- [ost-startStop-1] o.s.b.w.servlet.ServletRegistrationBean  : Mapping servlet: 'dispatcherServlet' to [/]
    2017-08-08 17:12:32.084  INFO 19866 --- [ost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'characterEncodingFilter' to: [/*]
    2017-08-08 17:12:32.084  INFO 19866 --- [ost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
    2017-08-08 17:12:32.084  INFO 19866 --- [ost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'httpPutFormContentFilter' to: [/*]
    2017-08-08 17:12:32.084  INFO 19866 --- [ost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'requestContextFilter' to: [/*]
    2017-08-08 17:12:32.349  INFO 19866 --- [           main] s.w.s.m.m.a.RequestMappingHandlerAdapter : Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@76b10754: startup date [Tue Aug 08 17:12:30 BST 2017]; root of context hierarchy
    2017-08-08 17:12:32.420  INFO 19866 --- [           main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(jakarta.servlet.http.HttpServletRequest)
    2017-08-08 17:12:32.421  INFO 19866 --- [           main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)
    2017-08-08 17:12:32.444  INFO 19866 --- [           main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
    2017-08-08 17:12:32.444  INFO 19866 --- [           main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
    2017-08-08 17:12:32.471  INFO 19866 --- [           main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
    2017-08-08 17:12:32.600  INFO 19866 --- [           main] o.s.w.s.v.f.FreeMarkerConfigurer         : ClassTemplateLoader for Spring macros added to FreeMarker configuration
    2017-08-08 17:12:32.681  INFO 19866 --- [           main] o.s.j.e.a.AnnotationMBeanExporter        : Registering beans for JMX exposure on startup
    2017-08-08 17:12:32.744  INFO 19866 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http)
    2017-08-08 17:12:32.750  INFO 19866 --- [           main] s.f.SampleWebFreeMarkerApplication       : Started SampleWebFreeMarkerApplication in 2.172 seconds (JVM running for 2.479)
    
    Copied!

## Retrieving Part of the Log File

__ |  Retrieving part of the log file is not supported when using Jersey.   
---|---  
  
To retrieve part of the log file, make a `GET` request to `/actuator/logfile` by using the `Range` header, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/logfile' -i -X GET \
        -H 'Range: bytes=0-1023'
    
    Copied!

The preceding example retrieves the first 1024 bytes of the log file. The resulting response is similar to the following:
    
    
    HTTP/1.1 206 Partial Content
    Accept-Ranges: bytes
    Content-Type: text/plain;charset=UTF-8
    Content-Range: bytes 0-1023/4707
    Content-Length: 1024
    
      .   ____          _            __ _ _
     /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
    ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
     \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
      '  |____| .__|_| |_|_| |_\__, | / / / /
     =========|_|==============|___/=/_/_/_/
     :: Spring Boot ::
    
    2017-08-08 17:12:30.910  INFO 19866 --- [           main] s.f.SampleWebFreeMarkerApplication       : Starting SampleWebFreeMarkerApplication with PID 19866
    2017-08-08 17:12:30.913  INFO 19866 --- [           main] s.f.SampleWebFreeMarkerApplication       : No active profile set, falling back to default profiles: default
    2017-08-08 17:12:30.952  INFO 19866 --- [           main] ConfigServletWebServerApplicationContext : Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@76b10754: startup date [Tue Aug 08 17:12:30 BST 2017]; root of context hierarchy
    2017-08-08 17:12:31.878  INFO 19866 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
    20
    
    Copied!

[Liquibase (`liquibase`)](liquibase.html) [Loggers (`loggers`)](loggers.html)
---
