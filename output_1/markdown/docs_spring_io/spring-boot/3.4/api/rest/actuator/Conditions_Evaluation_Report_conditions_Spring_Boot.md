Title: Conditions Evaluation Report (conditions) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/conditions.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Conditions_Evaluation_Report_conditions_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Conditions_Evaluation_Report_conditions_Spring_Boot.png
crawled_at: 2025-06-04T15:40:03.976787
---
Search CTRL + k

### Conditions Evaluation Report (conditions)

  * Retrieving the Report
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/conditions.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Conditions Evaluation Report (`conditions`)](conditions.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/conditions.html)!  
---|---  
  
# Conditions Evaluation Report (`conditions`)

### Conditions Evaluation Report (conditions)

  * Retrieving the Report
  * Response Structure



The `conditions` endpoint provides information about the evaluation of conditions on configuration and auto-configuration classes.

## Retrieving the Report

To retrieve the report, make a `GET` request to `/actuator/conditions`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/conditions' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 3524
    
    {
      "contexts" : {
        "application" : {
          "positiveMatches" : {
            "EndpointAutoConfiguration#propertiesEndpointAccessResolver" : [ {
              "condition" : "OnBeanCondition",
              "message" : "@ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.EndpointAccessResolver; SearchStrategy: all) did not find any beans"
            } ],
            "EndpointAutoConfiguration#endpointOperationParameterMapper" : [ {
              "condition" : "OnBeanCondition",
              "message" : "@ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.invoke.ParameterValueMapper; SearchStrategy: all) did not find any beans"
            } ],
            "EndpointAutoConfiguration#endpointCachingOperationInvokerAdvisor" : [ {
              "condition" : "OnBeanCondition",
              "message" : "@ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.invoker.cache.CachingOperationInvokerAdvisor; SearchStrategy: all) did not find any beans"
            } ]
          },
          "negativeMatches" : {
            "WebFluxEndpointManagementContextConfiguration" : {
              "notMatched" : [ {
                "condition" : "OnWebApplicationCondition",
                "message" : "not a reactive web application"
              } ],
              "matched" : [ {
                "condition" : "OnClassCondition",
                "message" : "@ConditionalOnClass found required classes 'org.springframework.web.reactive.DispatcherHandler', 'org.springframework.http.server.reactive.HttpHandler'"
              } ]
            },
            "GsonHttpMessageConvertersConfiguration.GsonHttpMessageConverterConfiguration" : {
              "notMatched" : [ {
                "condition" : "GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition",
                "message" : "AnyNestedCondition 0 matched 2 did not; NestedCondition on GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition.JacksonJsonbUnavailable NoneNestedConditions 1 matched 1 did not; NestedCondition on GsonHttpMessageConvertersConfiguration.JacksonAndJsonbUnavailableCondition.JsonbPreferred @ConditionalOnProperty (spring.mvc.converters.preferred-json-mapper=jsonb) did not find property 'spring.mvc.converters.preferred-json-mapper'; NestedCondition on GsonHttpMessageConvertersConfiguration.JacksonAndJsonbUnavailableCondition.JacksonAvailable @ConditionalOnBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter; SearchStrategy: all) found bean 'mappingJackson2HttpMessageConverter'; NestedCondition on GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition.GsonPreferred @ConditionalOnProperty (spring.mvc.converters.preferred-json-mapper=gson) did not find property 'spring.mvc.converters.preferred-json-mapper'"
              } ],
              "matched" : [ ]
            },
            "WebMvcEndpointManagementContextConfiguration#managementHealthEndpointWebMvcHandlerMapping" : {
              "notMatched" : [ {
                "condition" : "OnManagementPortCondition",
                "message" : "Management Port actual port type (SAME) did not match required type (DIFFERENT)"
              } ],
              "matched" : [ ]
            }
          },
          "unconditionalClasses" : [ "org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration", "org.springframework.boot.actuate.autoconfigure.endpoint.jackson.JacksonEndpointAutoConfiguration", "org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration" ]
        }
      }
    }
    
    Copied!

### Response Structure

The response contains details of the application’s condition evaluation. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`contexts` | `Object` | Application contexts keyed by id.  
`contexts.*.positiveMatches` | `Object` | Classes and methods with conditions that were matched.  
`contexts.*.positiveMatches.*.[].condition` | `String` | Name of the condition.  
`contexts.*.positiveMatches.*.[].message` | `String` | Details of why the condition was matched.  
`contexts.*.negativeMatches` | `Object` | Classes and methods with conditions that were not matched.  
`contexts.*.negativeMatches.*.notMatched` | `Array` | Conditions that were matched.  
`contexts.*.negativeMatches.*.notMatched.[].condition` | `String` | Name of the condition.  
`contexts.*.negativeMatches.*.notMatched.[].message` | `String` | Details of why the condition was not matched.  
`contexts.*.negativeMatches.*.matched` | `Array` | Conditions that were matched.  
`contexts.*.negativeMatches.*.matched.[].condition` | `String` | Name of the condition.  
`contexts.*.negativeMatches.*.matched.[].message` | `String` | Details of why the condition was matched.  
`contexts.*.unconditionalClasses` | `Array` | Names of unconditional auto-configuration classes if any.  
`contexts.*.parentId` | `String` | Id of the parent application context, if any.  
  
[Caches (`caches`)](caches.html) [Configuration Properties (`configprops`)](configprops.html)
---
