Title: Quartz (quartz) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/quartz.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Quartz_quartz_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Quartz_quartz_Spring_Boot.png
crawled_at: 2025-06-04T15:55:35.620015
---
Search CTRL + k

### Quartz (quartz)

  * Retrieving Registered Groups
  * Response Structure
  * Retrieving Registered Job Names
  * Response Structure
  * Retrieving Registered Trigger Names
  * Response Structure
  * Retrieving Overview of a Job Group
  * Response Structure
  * Retrieving Overview of a Trigger Group
  * Response Structure
  * Retrieving Details of a Job
  * Response Structure
  * Retrieving Details of a Trigger
  * Common Response Structure
  * Cron Trigger Response Structure
  * Simple Trigger Response Structure
  * Daily Time Interval Trigger Response Structure
  * Calendar Interval Trigger Response Structure
  * Custom Trigger Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/quartz.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Quartz (`quartz`)](quartz.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/quartz.html)!  
---|---  
  
# Quartz (`quartz`)

### Quartz (quartz)

  * Retrieving Registered Groups
  * Response Structure
  * Retrieving Registered Job Names
  * Response Structure
  * Retrieving Registered Trigger Names
  * Response Structure
  * Retrieving Overview of a Job Group
  * Response Structure
  * Retrieving Overview of a Trigger Group
  * Response Structure
  * Retrieving Details of a Job
  * Response Structure
  * Retrieving Details of a Trigger
  * Common Response Structure
  * Cron Trigger Response Structure
  * Simple Trigger Response Structure
  * Daily Time Interval Trigger Response Structure
  * Calendar Interval Trigger Response Structure
  * Custom Trigger Response Structure



The `quartz` endpoint provides information about jobs and triggers that are managed by the Quartz Scheduler.

## Retrieving Registered Groups

Jobs and triggers are managed in groups. To retrieve the list of registered job and trigger groups, make a `GET` request to `/actuator/quartz`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/quartz' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 120
    
    {
      "jobs" : {
        "groups" : [ "samples", "tests" ]
      },
      "triggers" : {
        "groups" : [ "samples", "DEFAULT" ]
      }
    }
    
    Copied!

### Response Structure

The response contains the groups names for registered jobs and triggers. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`jobs.groups` | `Array` | An array of job group names.  
`triggers.groups` | `Array` | An array of trigger group names.  
  
## Retrieving Registered Job Names

To retrieve the list of registered job names, make a `GET` request to `/actuator/quartz/jobs`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/quartz/jobs' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 137
    
    {
      "groups" : {
        "samples" : {
          "jobs" : [ "jobOne", "jobTwo" ]
        },
        "tests" : {
          "jobs" : [ "jobThree" ]
        }
      }
    }
    
    Copied!

### Response Structure

The response contains the registered job names for each group. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`groups` | `Object` | Job groups keyed by name.  
`groups.*.jobs` | `Array` | An array of job names.  
  
## Retrieving Registered Trigger Names

To retrieve the list of registered trigger names, make a `GET` request to `/actuator/quartz/triggers`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/quartz/triggers' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 229
    
    {
      "groups" : {
        "samples" : {
          "paused" : false,
          "triggers" : [ "3am-weekdays", "every-day", "once-a-week" ]
        },
        "DEFAULT" : {
          "paused" : false,
          "triggers" : [ "every-hour-tue-thu" ]
        }
      }
    }
    
    Copied!

### Response Structure

The response contains the registered trigger names for each group. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`groups` | `Object` | Trigger groups keyed by name.  
`groups.*.paused` | `Boolean` | Whether this trigger group is paused.  
`groups.*.triggers` | `Array` | An array of trigger names.  
  
## Retrieving Overview of a Job Group

To retrieve an overview of the jobs in a particular group, make a `GET` request to `/actuator/quartz/jobs/{groupName}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/quartz/jobs/samples' -i -X GET
    
    Copied!

The preceding example retrieves the summary for jobs in the `samples` group. The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 201
    
    {
      "group" : "samples",
      "jobs" : {
        "jobOne" : {
          "className" : "org.springframework.scheduling.quartz.DelegatingJob"
        },
        "jobTwo" : {
          "className" : "org.quartz.Job"
        }
      }
    }
    
    Copied!

### Response Structure

The response contains an overview of jobs in a particular group. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`group` | `String` | Name of the group.  
`jobs` | `Object` | Job details keyed by name.  
`jobs.*.className` | `String` | Fully qualified name of the job implementation.  
  
## Retrieving Overview of a Trigger Group

To retrieve an overview of the triggers in a particular group, make a `GET` request to `/actuator/quartz/triggers/{groupName}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/quartz/triggers/tests' -i -X GET
    
    Copied!

The preceding example retrieves the summary for triggers in the `tests` group. The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 1268
    
    {
      "group" : "tests",
      "paused" : false,
      "triggers" : {
        "cron" : {
          "3am-week" : {
            "previousFireTime" : "2020-12-04T03:00:00.000+00:00",
            "nextFireTime" : "2020-12-07T03:00:00.000+00:00",
            "priority" : 3,
            "expression" : "0 0 3 ? * 1,2,3,4,5",
            "timeZone" : "Europe/Paris"
          }
        },
        "simple" : {
          "every-day" : {
            "nextFireTime" : "2020-12-04T12:00:00.000+00:00",
            "priority" : 7,
            "interval" : ********
          }
        },
        "dailyTimeInterval" : {
          "tue-thu" : {
            "priority" : 5,
            "interval" : 3600000,
            "daysOfWeek" : [ 3, 5 ],
            "startTimeOfDay" : "09:00:00",
            "endTimeOfDay" : "18:00:00"
          }
        },
        "calendarInterval" : {
          "once-a-week" : {
            "previousFireTime" : "2020-12-02T14:00:00.000+00:00",
            "nextFireTime" : "2020-12-08T14:00:00.000+00:00",
            "priority" : 5,
            "interval" : *********,
            "timeZone" : "Etc/UTC"
          }
        },
        "custom" : {
          "once-a-year-custom" : {
            "previousFireTime" : "2020-07-14T16:00:00.000+00:00",
            "nextFireTime" : "2021-07-14T16:00:00.000+00:00",
            "priority" : 10,
            "trigger" : "com.example.CustomTrigger@fdsfsd"
          }
        }
      }
    }
    
    Copied!

### Response Structure

The response contains an overview of triggers in a particular group. Trigger implementation specific details are available. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`group` | `String` | Name of the group.  
`paused` | `Boolean` | Whether the group is paused.  
`triggers.cron` | `Object` | Cron triggers keyed by name, if any.  
`triggers.simple` | `Object` | Simple triggers keyed by name, if any.  
`triggers.dailyTimeInterval` | `Object` | Daily time interval triggers keyed by name, if any.  
`triggers.calendarInterval` | `Object` | Calendar interval triggers keyed by name, if any.  
`triggers.custom` | `Object` | Any other triggers keyed by name, if any.  
`triggers.cron.*.previousFireTime` | `String` | Last time the trigger fired, if any.  
`triggers.cron.*.nextFireTime` | `String` | Next time at which the Trigger is scheduled to fire, if any.  
`triggers.cron.*.priority` | `Number` | Priority to use if two triggers have the same scheduled fire time.  
`triggers.cron.*.expression` | `String` | Cron expression to use.  
`triggers.cron.*.timeZone` | `String` | Time zone for which the expression will be resolved, if any.  
`triggers.simple.*.previousFireTime` | `String` | Last time the trigger fired, if any.  
`triggers.simple.*.nextFireTime` | `String` | Next time at which the Trigger is scheduled to fire, if any.  
`triggers.simple.*.priority` | `Number` | Priority to use if two triggers have the same scheduled fire time.  
`triggers.simple.*.interval` | `Number` | Interval, in milliseconds, between two executions.  
`triggers.dailyTimeInterval.*.previousFireTime` | `String` | Last time the trigger fired, if any.  
`triggers.dailyTimeInterval.*.nextFireTime` | `String` | Next time at which the Trigger is scheduled to fire, if any.  
`triggers.dailyTimeInterval.*.priority` | `Number` | Priority to use if two triggers have the same scheduled fire time.  
`triggers.dailyTimeInterval.*.interval` | `Number` | Interval, in milliseconds, added to the fire time in order to calculate the time of the next trigger repeat.  
`triggers.dailyTimeInterval.*.daysOfWeek` | `Array` | An array of days of the week upon which to fire.  
`triggers.dailyTimeInterval.*.startTimeOfDay` | `String` | Time of day to start firing at the given interval, if any.  
`triggers.dailyTimeInterval.*.endTimeOfDay` | `String` | Time of day to complete firing at the given interval, if any.  
`triggers.calendarInterval.*.previousFireTime` | `String` | Last time the trigger fired, if any.  
`triggers.calendarInterval.*.nextFireTime` | `String` | Next time at which the Trigger is scheduled to fire, if any.  
`triggers.calendarInterval.*.priority` | `Number` | Priority to use if two triggers have the same scheduled fire time.  
`triggers.calendarInterval.*.interval` | `Number` | Interval, in milliseconds, added to the fire time in order to calculate the time of the next trigger repeat.  
`triggers.calendarInterval.*.timeZone` | `String` | Time zone within which time calculations will be performed, if any.  
`triggers.custom.*.previousFireTime` | `String` | Last time the trigger fired, if any.  
`triggers.custom.*.nextFireTime` | `String` | Next time at which the Trigger is scheduled to fire, if any.  
`triggers.custom.*.priority` | `Number` | Priority to use if two triggers have the same scheduled fire time.  
`triggers.custom.*.trigger` | `String` | A toString representation of the custom trigger instance.  
  
## Retrieving Details of a Job

To retrieve the details about a particular job, make a `GET` request to `/actuator/quartz/jobs/{groupName}/{jobName}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/quartz/jobs/samples/jobOne' -i -X GET
    
    Copied!

The preceding example retrieves the details of the job identified by the `samples` group and `jobOne` name. The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 609
    
    {
      "group" : "samples",
      "name" : "jobOne",
      "description" : "A sample job",
      "className" : "org.springframework.scheduling.quartz.DelegatingJob",
      "durable" : false,
      "requestRecovery" : false,
      "data" : {
        "password" : "secret",
        "user" : "admin"
      },
      "triggers" : [ {
        "group" : "samples",
        "name" : "every-day",
        "previousFireTime" : "2020-12-04T03:00:00.000+00:00",
        "nextFireTime" : "2020-12-04T12:00:00.000+00:00",
        "priority" : 7
      }, {
        "group" : "samples",
        "name" : "3am-weekdays",
        "nextFireTime" : "2020-12-07T03:00:00.000+00:00",
        "priority" : 3
      } ]
    }
    
    Copied!

If a key in the data map is identified as sensitive, its value is sanitized.

### Response Structure

The response contains the full details of a job including a summary of the triggers associated with it, if any. The triggers are sorted by next fire time and priority. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`group` | `String` | Name of the group.  
`name` | `String` | Name of the job.  
`description` | `String` | Description of the job, if any.  
`className` | `String` | Fully qualified name of the job implementation.  
`durable` | `Boolean` | Whether the job should remain stored after it is orphaned.  
`requestRecovery` | `Boolean` | Whether the job should be re-executed if a 'recovery' or 'fail-over' situation is encountered.  
`data.*` | `String` | Job data map as key/value pairs, if any.  
`triggers` | `Array` | An array of triggers associated to the job, if any.  
`triggers.[].group` | `String` | Name of the trigger group.  
`triggers.[].name` | `String` | Name of the trigger.  
`triggers.[].previousFireTime` | `String` | Last time the trigger fired, if any.  
`triggers.[].nextFireTime` | `String` | Next time at which the Trigger is scheduled to fire, if any.  
`triggers.[].priority` | `Number` | Priority to use if two triggers have the same scheduled fire time.  
  
## Retrieving Details of a Trigger

To retrieve the details about a particular trigger, make a `GET` request to `/actuator/quartz/triggers/{groupName}/{triggerName}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/quartz/triggers/samples/example' -i -X GET
    
    Copied!

The preceding example retrieves the details of trigger identified by the `samples` group and `example` name.

### Common Response Structure

The response has a common structure and an additional object that is specific to the trigger’s type. There are five supported types:

  * `cron` for `CronTrigger`

  * `simple` for `SimpleTrigger`

  * `dailyTimeInterval` for `DailyTimeIntervalTrigger`

  * `calendarInterval` for `CalendarIntervalTrigger`

  * `custom` for any other trigger implementations




The following table describes the structure of the common elements of the response:

Path | Type | Description  
---|---|---  
`group` | `String` | Name of the group.  
`name` | `String` | Name of the trigger.  
`description` | `String` | Description of the trigger, if any.  
`state` | `String` | State of the trigger (`NONE`, `NORMAL`, `PAUSED`, `COMPLETE`, `ERROR`, `BLOCKED`).  
`type` | `String` | Type of the trigger (`calendarInterval`, `cron`, `custom`, `dailyTimeInterval`, `simple`). Determines the key of the object containing type-specific details.  
`calendarName` | `String` | Name of the Calendar associated with this Trigger, if any.  
`startTime` | `String` | Time at which the Trigger should take effect, if any.  
`endTime` | `String` | Time at which the Trigger should quit repeating, regardless of any remaining repeats, if any.  
`previousFireTime` | `String` | Last time the trigger fired, if any.  
`nextFireTime` | `String` | Next time at which the Trigger is scheduled to fire, if any.  
`priority` | `Number` | Priority to use if two triggers have the same scheduled fire time.  
`finalFireTime` | `String` | Last time at which the Trigger will fire, if any.  
`data` | `Object` | Job data map keyed by name, if any.  
`calendarInterval` | `Object` | Calendar time interval trigger details, if any. Present when `type` is `calendarInterval`.  
`custom` | `Object` | Custom trigger details, if any. Present when `type` is `custom`.  
`cron` | `Object` | Cron trigger details, if any. Present when `type` is `cron`.  
`dailyTimeInterval` | `Object` | Daily time interval trigger details, if any. Present when `type` is `dailyTimeInterval`.  
`simple` | `Object` | Simple trigger details, if any. Present when `type` is `simple`.  
  
### Cron Trigger Response Structure

A cron trigger defines the cron expression that is used to determine when it has to fire. The resulting response for such a trigger implementation is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 490
    
    {
      "group" : "samples",
      "name" : "example",
      "description" : "Example trigger",
      "state" : "NORMAL",
      "type" : "cron",
      "calendarName" : "bankHolidays",
      "startTime" : "2020-11-30T17:00:00.000+00:00",
      "endTime" : "2020-12-30T03:00:00.000+00:00",
      "previousFireTime" : "2020-12-04T03:00:00.000+00:00",
      "nextFireTime" : "2020-12-07T03:00:00.000+00:00",
      "priority" : 3,
      "data" : { },
      "cron" : {
        "expression" : "0 0 3 ? * 1,2,3,4,5",
        "timeZone" : "Europe/Paris"
      }
    }
    
    Copied!

Much of the response is common to all trigger types. The structure of the common elements of the response was described previously. The following table describes the structure of the parts of the response that are specific to cron triggers:

Path | Type | Description  
---|---|---  
`cron` | `Object` | Cron trigger specific details.  
`cron.expression` | `String` | Cron expression to use.  
`cron.timeZone` | `String` | Time zone for which the expression will be resolved, if any.  
  
### Simple Trigger Response Structure

A simple trigger is used to fire a Job at a given moment in time, and optionally repeated at a specified interval. The resulting response for such a trigger implementation is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 549
    
    {
      "group" : "samples",
      "name" : "example",
      "description" : "Example trigger",
      "state" : "NORMAL",
      "type" : "simple",
      "calendarName" : "bankHolidays",
      "startTime" : "2020-11-30T17:00:00.000+00:00",
      "endTime" : "2020-12-30T03:00:00.000+00:00",
      "previousFireTime" : "2020-12-04T03:00:00.000+00:00",
      "nextFireTime" : "2020-12-07T03:00:00.000+00:00",
      "priority" : 7,
      "finalFireTime" : "2020-12-29T17:00:00.000+00:00",
      "data" : { },
      "simple" : {
        "interval" : ********,
        "repeatCount" : -1,
        "timesTriggered" : 0
      }
    }
    
    Copied!

Much of the response is common to all trigger types. The structure of the common elements of the response was described previously. The following table describes the structure of the parts of the response that are specific to simple triggers:

Path | Type | Description  
---|---|---  
`simple` | `Object` | Simple trigger specific details.  
`simple.interval` | `Number` | Interval, in milliseconds, between two executions.  
`simple.repeatCount` | `Number` | Number of times the trigger should repeat, or -1 to repeat indefinitely.  
`simple.timesTriggered` | `Number` | Number of times the trigger has already fired.  
  
### Daily Time Interval Trigger Response Structure

A daily time interval trigger is used to fire a Job based upon daily repeating time intervals. The resulting response for such a trigger implementation is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 667
    
    {
      "group" : "samples",
      "name" : "example",
      "description" : "Example trigger",
      "state" : "PAUSED",
      "type" : "dailyTimeInterval",
      "calendarName" : "bankHolidays",
      "startTime" : "2020-11-30T17:00:00.000+00:00",
      "endTime" : "2020-12-30T03:00:00.000+00:00",
      "previousFireTime" : "2020-12-04T03:00:00.000+00:00",
      "nextFireTime" : "2020-12-07T03:00:00.000+00:00",
      "priority" : 5,
      "finalFireTime" : "2020-12-30T18:00:00.000+00:00",
      "data" : { },
      "dailyTimeInterval" : {
        "interval" : 3600000,
        "daysOfWeek" : [ 3, 5 ],
        "startTimeOfDay" : "09:00:00",
        "endTimeOfDay" : "18:00:00",
        "repeatCount" : -1,
        "timesTriggered" : 0
      }
    }
    
    Copied!

Much of the response is common to all trigger types. The structure of the common elements of the response was described previously. The following table describes the structure of the parts of the response that are specific to daily time interval triggers:

Path | Type | Description  
---|---|---  
`dailyTimeInterval` | `Object` | Daily time interval trigger specific details.  
`dailyTimeInterval.interval` | `Number` | Interval, in milliseconds, added to the fire time in order to calculate the time of the next trigger repeat.  
`dailyTimeInterval.daysOfWeek` | `Array` | An array of days of the week upon which to fire.  
`dailyTimeInterval.startTimeOfDay` | `String` | Time of day to start firing at the given interval, if any.  
`dailyTimeInterval.endTimeOfDay` | `String` | Time of day to complete firing at the given interval, if any.  
`dailyTimeInterval.repeatCount` | `Number` | Number of times the trigger should repeat, or -1 to repeat indefinitely.  
`dailyTimeInterval.timesTriggered` | `Number` | Number of times the trigger has already fired.  
  
### Calendar Interval Trigger Response Structure

A calendar interval trigger is used to fire a Job based upon repeating calendar time intervals. The resulting response for such a trigger implementation is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 669
    
    {
      "group" : "samples",
      "name" : "example",
      "description" : "Example trigger",
      "state" : "NORMAL",
      "type" : "calendarInterval",
      "calendarName" : "bankHolidays",
      "startTime" : "2020-11-30T17:00:00.000+00:00",
      "endTime" : "2020-12-30T03:00:00.000+00:00",
      "previousFireTime" : "2020-12-04T03:00:00.000+00:00",
      "nextFireTime" : "2020-12-07T03:00:00.000+00:00",
      "priority" : 5,
      "finalFireTime" : "2020-12-28T17:00:00.000+00:00",
      "data" : { },
      "calendarInterval" : {
        "interval" : *********,
        "timeZone" : "Etc/UTC",
        "timesTriggered" : 0,
        "preserveHourOfDayAcrossDaylightSavings" : false,
        "skipDayIfHourDoesNotExist" : false
      }
    }
    
    Copied!

Much of the response is common to all trigger types. The structure of the common elements of the response was described previously. The following table describes the structure of the parts of the response that are specific to calendar interval triggers:

Path | Type | Description  
---|---|---  
`calendarInterval` | `Object` | Calendar interval trigger specific details.  
`calendarInterval.interval` | `Number` | Interval, in milliseconds, added to the fire time in order to calculate the time of the next trigger repeat.  
`calendarInterval.timeZone` | `String` | Time zone within which time calculations will be performed, if any.  
`calendarInterval.timesTriggered` | `Number` | Number of times the trigger has already fired.  
`calendarInterval.preserveHourOfDayAcrossDaylightSavings` | `Boolean` | Whether to fire the trigger at the same time of day, regardless of daylight saving time transitions.  
`calendarInterval.skipDayIfHourDoesNotExist` | `Boolean` | Whether to skip if the hour of the day does not exist on a given day.  
  
### Custom Trigger Response Structure

A custom trigger is any other implementation. The resulting response for such a trigger implementation is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 457
    
    {
      "group" : "samples",
      "name" : "example",
      "description" : "Example trigger.",
      "state" : "NORMAL",
      "type" : "custom",
      "calendarName" : "bankHolidays",
      "startTime" : "2020-11-30T17:00:00.000+00:00",
      "endTime" : "2020-12-30T03:00:00.000+00:00",
      "previousFireTime" : "2020-12-04T03:00:00.000+00:00",
      "nextFireTime" : "2020-12-07T03:00:00.000+00:00",
      "priority" : 10,
      "custom" : {
        "trigger" : "com.example.CustomTrigger@fdsfsd"
      }
    }
    
    Copied!

Much of the response is common to all trigger types. The structure of the common elements of the response was described previously. The following table describes the structure of the parts of the response that are specific to custom triggers:

Path | Type | Description  
---|---|---  
`custom` | `Object` | Custom trigger specific details.  
`custom.trigger` | `String` | A toString representation of the custom trigger instance.  
  
[Prometheus (`prometheus`)](prometheus.html) [Software Bill of Materials (`sbom`)](sbom.html)
---
