Title: Liquibase (liquibase) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/liquibase.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Liquibase_liquibase_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Liquibase_liquibase_Spring_Boot.png
crawled_at: 2025-06-04T15:49:19.228376
---
Search CTRL + k

### Liquibase (liquibase)

  * Retrieving the Changes
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/liquibase.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Liquibase (`liquibase`)](liquibase.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/liquibase.html)!  
---|---  
  
# Liquibase (`liquibase`)

### Liquibase (liquibase)

  * Retrieving the Changes
  * Response Structure



The `liquibase` endpoint provides information about database change sets applied by Liquibase.

## Retrieving the Changes

To retrieve the changes, make a `GET` request to `/actuator/liquibase`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/liquibase' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 721
    
    {
      "contexts" : {
        "application" : {
          "liquibaseBeans" : {
            "liquibase" : {
              "changeSets" : [ {
                "author" : "marceloverdijk",
                "changeLog" : "org/springframework/boot/actuate/autoconfigure/liquibase/db.changelog-master.yaml",
                "comments" : "",
                "contexts" : [ ],
                "dateExecuted" : "2025-05-22T09:59:57.537Z",
                "deploymentId" : "7907997418",
                "description" : "createTable tableName=customer",
                "execType" : "EXECUTED",
                "id" : "1",
                "labels" : [ ],
                "checksum" : "9:d3589feb2baad02e15540750499ba311",
                "orderExecuted" : 1
              } ]
            }
          }
        }
      }
    }
    
    Copied!

### Response Structure

The response contains details of the application’s Liquibase change sets. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`contexts` | `Object` | Application contexts keyed by id  
`contexts.*.liquibaseBeans.*.changeSets` | `Array` | Change sets made by the Liquibase beans, keyed by bean name.  
`contexts.*.liquibaseBeans.*.changeSets[].author` | `String` | Author of the change set.  
`contexts.*.liquibaseBeans.*.changeSets[].changeLog` | `String` | Change log that contains the change set.  
`contexts.*.liquibaseBeans.*.changeSets[].comments` | `String` | Comments on the change set.  
`contexts.*.liquibaseBeans.*.changeSets[].contexts` | `Array` | Contexts of the change set.  
`contexts.*.liquibaseBeans.*.changeSets[].dateExecuted` | `String` | Timestamp of when the change set was executed.  
`contexts.*.liquibaseBeans.*.changeSets[].deploymentId` | `String` | ID of the deployment that ran the change set.  
`contexts.*.liquibaseBeans.*.changeSets[].description` | `String` | Description of the change set.  
`contexts.*.liquibaseBeans.*.changeSets[].execType` | `String` | Execution type of the change set (`EXECUTED`, `FAILED`, `SKIPPED`, `RERAN`, `MARK_RAN`).  
`contexts.*.liquibaseBeans.*.changeSets[].id` | `String` | ID of the change set.  
`contexts.*.liquibaseBeans.*.changeSets[].labels` | `Array` | Labels associated with the change set.  
`contexts.*.liquibaseBeans.*.changeSets[].checksum` | `String` | Checksum of the change set.  
`contexts.*.liquibaseBeans.*.changeSets[].orderExecuted` | `Number` | Order of the execution of the change set.  
`contexts.*.liquibaseBeans.*.changeSets[].tag` | `String` | Tag associated with the change set, if any.  
`contexts.*.parentId` | `String` | Id of the parent application context, if any.  
  
[Spring Integration Graph (`integrationgraph`)](integrationgraph.html) [Log File (`logfile`)](logfile.html)
---
