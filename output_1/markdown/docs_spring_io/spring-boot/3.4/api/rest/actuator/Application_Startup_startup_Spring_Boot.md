Title: Application Startup (startup) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/startup.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Application_Startup_startup_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Application_Startup_startup_Spring_Boot.png
crawled_at: 2025-06-04T15:38:55.017807
---
Search CTRL + k

### Application Startup (startup)

  * Retrieving the Application Startup Steps
  * Retrieving a snapshot of the Application Startup Steps
  * Draining the Application Startup Steps
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/startup.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Application Startup (`startup`)](startup.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/startup.html)!  
---|---  
  
# Application Startup (`startup`)

### Application Startup (startup)

  * Retrieving the Application Startup Steps
  * Retrieving a snapshot of the Application Startup Steps
  * Draining the Application Startup Steps
  * Response Structure



The `startup` endpoint provides information about the application’s startup sequence.

## Retrieving the Application Startup Steps

The application startup steps can either be retrieved as a snapshot (`GET`) or drained from the buffer (`POST`).

### Retrieving a snapshot of the Application Startup Steps

To retrieve the steps recorded so far during the application startup phase, make a `GET` request to `/actuator/startup`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/startup' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 889
    
    {
      "springBootVersion" : "3.4.6",
      "timeline" : {
        "startTime" : "2025-05-22T10:00:12.557629713Z",
        "events" : [ {
          "endTime" : "2025-05-22T10:00:13.200843748Z",
          "duration" : "PT0.000005831S",
          "startTime" : "2025-05-22T10:00:13.200837917Z",
          "startupStep" : {
            "name" : "spring.beans.instantiate",
            "id" : 3,
            "tags" : [ {
              "key" : "beanName",
              "value" : "homeController"
            } ],
            "parentId" : 2
          }
        }, {
          "endTime" : "2025-05-22T10:00:13.200850410Z",
          "duration" : "PT0.000020108S",
          "startTime" : "2025-05-22T10:00:13.200830302Z",
          "startupStep" : {
            "name" : "spring.boot.application.starting",
            "id" : 2,
            "tags" : [ {
              "key" : "mainApplicationClass",
              "value" : "com.example.startup.StartupApplication"
            } ]
          }
        } ]
      }
    }
    
    Copied!

### Draining the Application Startup Steps

To drain and return the steps recorded so far during the application startup phase, make a `POST` request to `/actuator/startup`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/startup' -i -X POST
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 889
    
    {
      "springBootVersion" : "3.4.6",
      "timeline" : {
        "startTime" : "2025-05-22T10:00:12.557629713Z",
        "events" : [ {
          "endTime" : "2025-05-22T10:00:13.101689873Z",
          "duration" : "PT0.000234569S",
          "startTime" : "2025-05-22T10:00:13.101455304Z",
          "startupStep" : {
            "name" : "spring.beans.instantiate",
            "id" : 1,
            "tags" : [ {
              "key" : "beanName",
              "value" : "homeController"
            } ],
            "parentId" : 0
          }
        }, {
          "endTime" : "2025-05-22T10:00:13.101725159Z",
          "duration" : "PT0.001236904S",
          "startTime" : "2025-05-22T10:00:13.100488255Z",
          "startupStep" : {
            "name" : "spring.boot.application.starting",
            "id" : 0,
            "tags" : [ {
              "key" : "mainApplicationClass",
              "value" : "com.example.startup.StartupApplication"
            } ]
          }
        } ]
      }
    }
    
    Copied!

### Response Structure

The response contains details of the application startup steps. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`springBootVersion` | `String` | Spring Boot version for this application.  
`timeline.startTime` | `String` | Start time of the application.  
`timeline.events` | `Array` | An array of steps collected during application startup so far.  
`timeline.events.[].startTime` | `String` | The timestamp of the start of this event.  
`timeline.events.[].endTime` | `String` | The timestamp of the end of this event.  
`timeline.events.[].duration` | `String` | The precise duration of this event.  
`timeline.events.[].startupStep.name` | `String` | The name of the StartupStep.  
`timeline.events.[].startupStep.id` | `Number` | The id of this StartupStep.  
`timeline.events.[].startupStep.parentId` | `Number` | The parent id for this StartupStep.  
`timeline.events.[].startupStep.tags` | `Array` | An array of key/value pairs with additional step info.  
`timeline.events.[].startupStep.tags[].key` | `String` | The key of the StartupStep Tag.  
`timeline.events.[].startupStep.tags[].value` | `String` | The value of the StartupStep Tag.  
  
[Shutdown (`shutdown`)](shutdown.html) [Thread Dump (`threaddump`)](threaddump.html)
---
