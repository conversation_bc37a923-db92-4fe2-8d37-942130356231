Title: Heap Dump (heapdump) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/heapdump.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Heap_Dump_heapdump_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Heap_Dump_heapdump_Spring_Boot.png
crawled_at: 2025-06-04T15:55:10.805315
---
Search CTRL + k

### Heap Dump (heapdump)

  * Retrieving the Heap Dump



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/heapdump.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Heap Dump (`heapdump`)](heapdump.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/heapdump.html)!  
---|---  
  
# Heap Dump (`heapdump`)

### Heap Dump (heapdump)

  * Retrieving the Heap Dump



The `heapdump` endpoint provides a heap dump from the application’s JVM.

## Retrieving the Heap Dump

To retrieve the heap dump, make a `GET` request to `/actuator/heapdump`. The response is binary data and can be large. Its format depends upon the JVM on which the application is running. When running on a HotSpot JVM the format is [HPROF](https://docs.oracle.com/javase/8/docs/technotes/samples/hprof.html) and on OpenJ9 it is [PHD](https://www.eclipse.org/openj9/docs/dump_heapdump/#portable-heap-dump-phd-format). Typically, you should save the response to disk for subsequent analysis. When using curl, this can be achieved by using the `-O` option, as shown in the following example:
    
    
    $ curl 'http://localhost:8080/actuator/heapdump' -O
    
    Copied!

The preceding example results in a file named `heapdump` being written to the current working directory.

[Health (`health`)](health.html) [HTTP Exchanges (`httpexchanges`)](httpexchanges.html)
---
