Title: Configuration Properties (configprops) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/configprops.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Configuration_Properties_configprops_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Configuration_Properties_configprops_Spring_Boot.png
crawled_at: 2025-06-04T15:47:29.670451
---
Search CTRL + k

### Configuration Properties (configprops)

  * Retrieving All @ConfigurationProperties Beans
  * Response Structure
  * Retrieving @ConfigurationProperties Beans By Prefix
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/configprops.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Configuration Properties (`configprops`)](configprops.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/configprops.html)!  
---|---  
  
# Configuration Properties (`configprops`)

### Configuration Properties (configprops)

  * Retrieving All @ConfigurationProperties Beans
  * Response Structure
  * Retrieving @ConfigurationProperties Beans By Prefix
  * Response Structure



The `configprops` endpoint provides information about the application’s `@ConfigurationProperties` beans.

## Retrieving All @ConfigurationProperties Beans

To retrieve all of the `@ConfigurationProperties` beans, make a `GET` request to `/actuator/configprops`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/configprops' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 3411
    
    {
      "contexts" : {
        "application" : {
          "beans" : {
            "management.endpoints.web.cors-org.springframework.boot.actuate.autoconfigure.endpoint.web.CorsEndpointProperties" : {
              "prefix" : "management.endpoints.web.cors",
              "properties" : {
                "allowedOrigins" : [ ],
                "maxAge" : "PT30M",
                "exposedHeaders" : [ ],
                "allowedOriginPatterns" : [ ],
                "allowedHeaders" : [ ],
                "allowedMethods" : [ ]
              },
              "inputs" : {
                "allowedOrigins" : [ ],
                "maxAge" : { },
                "exposedHeaders" : [ ],
                "allowedOriginPatterns" : [ ],
                "allowedHeaders" : [ ],
                "allowedMethods" : [ ]
              }
            },
            "management.endpoints.web-org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointProperties" : {
              "prefix" : "management.endpoints.web",
              "properties" : {
                "pathMapping" : { },
                "exposure" : {
                  "include" : [ "*" ],
                  "exclude" : [ ]
                },
                "basePath" : "/actuator",
                "discovery" : {
                  "enabled" : true
                }
              },
              "inputs" : {
                "pathMapping" : { },
                "exposure" : {
                  "include" : [ {
                    "value" : "*",
                    "origin" : "\"management.endpoints.web.exposure.include\" from property source \"Inlined Test Properties\""
                  } ],
                  "exclude" : [ ]
                },
                "basePath" : { },
                "discovery" : {
                  "enabled" : { }
                }
              }
            },
            "spring.web-org.springframework.boot.autoconfigure.web.WebProperties" : {
              "prefix" : "spring.web",
              "properties" : {
                "localeResolver" : "ACCEPT_HEADER",
                "resources" : {
                  "staticLocations" : [ "classpath:/META-INF/resources/", "classpath:/resources/", "classpath:/static/", "classpath:/public/" ],
                  "addMappings" : true,
                  "chain" : {
                    "cache" : true,
                    "compressed" : false,
                    "strategy" : {
                      "fixed" : {
                        "enabled" : false,
                        "paths" : [ "/**" ]
                      },
                      "content" : {
                        "enabled" : false,
                        "paths" : [ "/**" ]
                      }
                    }
                  },
                  "cache" : {
                    "cachecontrol" : { },
                    "useLastModified" : true
                  }
                }
              },
              "inputs" : {
                "localeResolver" : { },
                "resources" : {
                  "staticLocations" : [ { }, { }, { }, { } ],
                  "addMappings" : { },
                  "chain" : {
                    "cache" : { },
                    "compressed" : { },
                    "strategy" : {
                      "fixed" : {
                        "enabled" : { },
                        "paths" : [ { } ]
                      },
                      "content" : {
                        "enabled" : { },
                        "paths" : [ { } ]
                      }
                    }
                  },
                  "cache" : {
                    "cachecontrol" : { },
                    "useLastModified" : { }
                  }
                }
              }
            }
          }
        }
      }
    }
    
    Copied!

### Response Structure

The response contains details of the application’s `@ConfigurationProperties` beans. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`contexts` | `Object` | Application contexts keyed by id.  
`contexts.*.beans.*` | `Object` | `@ConfigurationProperties` beans keyed by bean name.  
`contexts.*.beans.*.prefix` | `String` | Prefix applied to the names of the bean’s properties.  
`contexts.*.beans.*.properties` | `Object` | Properties of the bean as name-value pairs.  
`contexts.*.beans.*.inputs` | `Object` | Origin and value of the configuration property used when binding to this bean.  
`contexts.*.parentId` | `String` | Id of the parent application context, if any.  
  
## Retrieving @ConfigurationProperties Beans By Prefix

To retrieve the `@ConfigurationProperties` beans mapped under a certain prefix, make a `GET` request to `/actuator/configprops/{prefix}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/configprops/spring.jackson' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Disposition: inline;filename=f.txt
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 878
    
    {
      "contexts" : {
        "application" : {
          "beans" : {
            "spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties" : {
              "prefix" : "spring.jackson",
              "properties" : {
                "serialization" : { },
                "visibility" : { },
                "parser" : { },
                "datatype" : {
                  "jsonNode" : { },
                  "enum" : { }
                },
                "deserialization" : { },
                "generator" : { },
                "mapper" : { }
              },
              "inputs" : {
                "serialization" : { },
                "visibility" : { },
                "parser" : { },
                "datatype" : {
                  "jsonNode" : { },
                  "enum" : { }
                },
                "deserialization" : { },
                "generator" : { },
                "mapper" : { }
              }
            }
          }
        }
      }
    }
    
    Copied!

__ |  The `{prefix}` does not need to be exact, a more general prefix will return all beans mapped under that prefix stem.   
---|---  
  
### Response Structure

The response contains details of the application’s `@ConfigurationProperties` beans. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`contexts` | `Object` | Application contexts keyed by id.  
`contexts.*.beans.*` | `Object` | `@ConfigurationProperties` beans keyed by bean name.  
`contexts.*.beans.*.prefix` | `String` | Prefix applied to the names of the bean’s properties.  
`contexts.*.beans.*.properties` | `Object` | Properties of the bean as name-value pairs.  
`contexts.*.beans.*.inputs` | `Object` | Origin and value of the configuration property used when binding to this bean.  
`contexts.*.parentId` | `String` | Id of the parent application context, if any.  
  
[Conditions Evaluation Report (`conditions`)](conditions.html) [Environment (`env`)](env.html)
---
