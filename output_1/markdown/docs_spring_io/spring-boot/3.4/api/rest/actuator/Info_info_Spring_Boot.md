Title: Info (info) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/info.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Info_info_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Info_info_Spring_Boot.png
crawled_at: 2025-06-04T15:47:02.349533
---
Search CTRL + k

### Info (info)

  * Retrieving the Info
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/info.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Info (`info`)](info.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/info.html)!  
---|---  
  
# Info (`info`)

### Info (info)

  * Retrieving the Info
  * Response Structure



The `info` endpoint provides general information about the application.

## Retrieving the Info

To retrieve the information about the application, make a `GET` request to `/actuator/info`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/info' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 1057
    
    {
      "git" : {
        "branch" : "main",
        "commit" : {
          "id" : "df027cf",
          "time" : "2025-05-22T09:59:53Z"
        }
      },
      "build" : {
        "artifact" : "application",
        "version" : "1.0.3",
        "group" : "com.example"
      },
      "os" : {
        "name" : "Linux",
        "version" : "6.11.0-1014-azure",
        "arch" : "amd64"
      },
      "process" : {
        "pid" : 89773,
        "parentPid" : 88574,
        "owner" : "runner",
        "memory" : {
          "heap" : {
            "max" : 1073741824,
            "committed" : 143654912,
            "used" : 100650512,
            "init" : 262144000
          },
          "nonHeap" : {
            "max" : -1,
            "committed" : 95420416,
            "used" : 93175088,
            "init" : 7667712
          }
        },
        "cpus" : 4
      },
      "java" : {
        "version" : "17.0.15",
        "vendor" : {
          "name" : "BellSoft"
        },
        "runtime" : {
          "name" : "OpenJDK Runtime Environment",
          "version" : "17.0.15+10-LTS"
        },
        "jvm" : {
          "name" : "OpenJDK 64-Bit Server VM",
          "vendor" : "BellSoft",
          "version" : "17.0.15+10-LTS"
        }
      }
    }
    
    Copied!

### Response Structure

The response contains general information about the application. Each section of the response is contributed by an `InfoContributor`. Spring Boot provides several contributors that are described below.

#### Build Response Structure

The following table describe the structure of the `build` section of the response:

Path | Type | Description  
---|---|---  
`artifact` | `String` | Artifact ID of the application, if any.  
`group` | `String` | Group ID of the application, if any.  
`name` | `String` | Name of the application, if any.  
`version` | `String` | Version of the application, if any.  
`time` | `Varies` | Timestamp of when the application was built, if any.  
  
#### Git Response Structure

The following table describes the structure of the `git` section of the response:

Path | Type | Description  
---|---|---  
`branch` | `String` | Name of the Git branch, if any.  
`commit` | `Object` | Details of the Git commit, if any.  
`commit.time` | `Varies` | Timestamp of the commit, if any.  
`commit.id` | `String` | ID of the commit, if any.  
  
__ |  This is the "simple" output. The contributor can also be configured to output all available data.   
---|---  
  
#### OS Response Structure

The following table describes the structure of the `os` section of the response:

Path | Type | Description  
---|---|---  
`name` | `String` | Name of the operating system (as obtained from the 'os.name' system property).  
`version` | `String` | Version of the operating system (as obtained from the 'os.version' system property).  
`arch` | `String` | Architecture of the operating system (as obtained from the 'os.arch' system property).  
  
#### Process Response Structure

The following table describes the structure of the `process` section of the response:

Path | Type | Description  
---|---|---  
`pid` | `Number` | Process ID.  
`parentPid` | `Number` | Parent Process ID (or -1).  
`owner` | `String` | Process owner.  
`cpus` | `Number` | Number of CPUs available to the process.  
`memory` | `Object` | Memory information.  
`memory.heap` | `Object` | Heap memory.  
`memory.heap.init` | `Number` | Number of bytes initially requested by the JVM.  
`memory.heap.used` | `Number` | Number of bytes currently being used.  
`memory.heap.committed` | `Number` | Number of bytes committed for JVM use.  
`memory.heap.max` | `Number` | Maximum number of bytes that can be used by the JVM (or -1).  
`memory.nonHeap` | `Object` | Non-heap memory.  
`memory.nonHeap.init` | `Number` | Number of bytes initially requested by the JVM.  
`memory.nonHeap.used` | `Number` | Number of bytes currently being used.  
`memory.nonHeap.committed` | `Number` | Number of bytes committed for JVM use.  
`memory.nonHeap.max` | `Number` | Maximum number of bytes that can be used by the JVM (or -1).  
  
#### Java Response Structure

The following table describes the structure of the `java` section of the response:

Path | Type | Description  
---|---|---  
`version` | `String` | Java version, if available.  
`vendor` | `Object` | Vendor details.  
`vendor.name` | `String` | Vendor name, if available.  
`vendor.version` | `String` | Vendor version, if available.  
`runtime` | `Object` | Runtime details.  
`runtime.name` | `String` | Runtime name, if available.  
`runtime.version` | `String` | Runtime version, if available.  
`jvm` | `Object` | JVM details.  
`jvm.name` | `String` | JVM name, if available.  
`jvm.vendor` | `String` | JVM vendor, if available.  
`jvm.version` | `String` | JVM version, if available.  
  
[HTTP Exchanges (`httpexchanges`)](httpexchanges.html) [Spring Integration Graph (`integrationgraph`)](integrationgraph.html)
---
