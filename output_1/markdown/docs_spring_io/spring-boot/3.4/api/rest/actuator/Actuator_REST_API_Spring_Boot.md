Title: Actuator REST API :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/index.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Actuator_REST_API_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Actuator_REST_API_Spring_Boot.png
crawled_at: 2025-06-04T15:39:11.523652
---
Search CTRL + k

### Actuator REST API

  * URLs
  * Timestamps



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/index.html)!  
---|---  
  
# Actuator REST API

### Actuator REST API

  * URLs
  * Timestamps



This API documentation describes Spring Boot Actuators web endpoints.

Before you proceed, you should read the following topics:

  * URLs

  * Timestamps




__ |  In order to get the correct JSON responses documented below, Jackson must be available.   
---|---  
  
## URLs

By default, all web endpoints are available beneath the path `/actuator` with URLs of the form `/actuator/{id}`. The `/actuator` base path can be configured by using the `management.endpoints.web.base-path` property, as shown in the following example:
    
    
    management.endpoints.web.base-path=/manage
    
    Copied!

The preceding `application.properties` example changes the form of the endpoint URLs from `/actuator/{id}` to `/manage/{id}`. For example, the URL `info` endpoint would become `/manage/info`.

## Timestamps

All timestamps that are consumed by the endpoints, either as query parameters or in the request body, must be formatted as an offset date and time as specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601).

[Using the CLI](../../../cli/using-the-cli.html) [Audit Events (`auditevents`)](auditevents.html)
---
