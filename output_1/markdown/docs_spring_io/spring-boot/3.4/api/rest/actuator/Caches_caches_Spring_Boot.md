Title: Caches (caches) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/caches.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Caches_caches_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Caches_caches_Spring_Boot.png
crawled_at: 2025-06-04T15:49:43.549752
---
Search CTRL + k

### Caches (caches)

  * Retrieving All Caches
  * Response Structure
  * Retrieving Caches by Name
  * Query Parameters
  * Response Structure
  * Evict All Caches
  * Evict a Cache by Name
  * Request Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/caches.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Caches (`caches`)](caches.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/caches.html)!  
---|---  
  
# Caches (`caches`)

### Caches (caches)

  * Retrieving All Caches
  * Response Structure
  * Retrieving Caches by Name
  * Query Parameters
  * Response Structure
  * Evict All Caches
  * Evict a Cache by Name
  * Request Structure



The `caches` endpoint provides access to the application’s caches.

## Retrieving All Caches

To retrieve the application’s caches, make a `GET` request to `/actuator/caches`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/caches' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 435
    
    {
      "cacheManagers" : {
        "anotherCacheManager" : {
          "caches" : {
            "countries" : {
              "target" : "java.util.concurrent.ConcurrentHashMap"
            }
          }
        },
        "cacheManager" : {
          "caches" : {
            "cities" : {
              "target" : "java.util.concurrent.ConcurrentHashMap"
            },
            "countries" : {
              "target" : "java.util.concurrent.ConcurrentHashMap"
            }
          }
        }
      }
    }
    
    Copied!

### Response Structure

The response contains details of the application’s caches. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`cacheManagers` | `Object` | Cache managers keyed by id.  
`cacheManagers.*.caches` | `Object` | Caches in the application context keyed by name.  
`cacheManagers.*.caches.*.target` | `String` | Fully qualified name of the native cache.  
  
## Retrieving Caches by Name

To retrieve a cache by name, make a `GET` request to `/actuator/caches/{name}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/caches/cities' -i -X GET
    
    Copied!

The preceding example retrieves information about the cache named `cities`. The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 113
    
    {
      "target" : "java.util.concurrent.ConcurrentHashMap",
      "name" : "cities",
      "cacheManager" : "cacheManager"
    }
    
    Copied!

### Query Parameters

If the requested name is specific enough to identify a single cache, no extra parameter is required. Otherwise, the `cacheManager` must be specified. The following table shows the supported query parameters:

Parameter | Description  
---|---  
`cacheManager` | Name of the cacheManager to qualify the cache. May be omitted if the cache name is unique.  
  
### Response Structure

The response contains details of the requested cache. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`name` | `String` | Cache name.  
`cacheManager` | `String` | Cache manager name.  
`target` | `String` | Fully qualified name of the native cache.  
  
## Evict All Caches

To clear all available caches, make a `DELETE` request to `/actuator/caches` as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/caches' -i -X DELETE
    
    Copied!

## Evict a Cache by Name

To evict a particular cache, make a `DELETE` request to `/actuator/caches/{name}` as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/caches/countries?cacheManager=anotherCacheManager' -i -X DELETE \
        -H 'Content-Type: application/x-www-form-urlencoded'
    
    Copied!

__ |  As there are two caches named `countries`, the `cacheManager` has to be provided to specify which `Cache` should be cleared.   
---|---  
  
### Request Structure

If the requested name is specific enough to identify a single cache, no extra parameter is required. Otherwise, the `cacheManager` must be specified. The following table shows the supported query parameters:

Parameter | Description  
---|---  
`cacheManager` | Name of the cacheManager to qualify the cache. May be omitted if the cache name is unique.  
  
[Beans (`beans`)](beans.html) [Conditions Evaluation Report (`conditions`)](conditions.html)
---
