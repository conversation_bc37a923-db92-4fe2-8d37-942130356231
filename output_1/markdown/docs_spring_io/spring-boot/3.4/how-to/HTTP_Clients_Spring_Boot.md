Title: HTTP Clients :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/http-clients.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/HTTP_Clients_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/HTTP_Clients_Spring_Boot.png
crawled_at: 2025-06-04T15:41:14.206967
---
Search CTRL + k

### HTTP Clients

  * Configure RestTemplate to Use a Proxy
  * Configure the TcpClient used by a Reactor Netty-based WebClient



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/http-clients.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [How-to Guides](index.html)
  * [HTTP Clients](http-clients.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../how-to/http-clients.html)!  
---|---  
  
# HTTP Clients

### HTTP Clients

  * Configure RestTemplate to Use a Proxy
  * Configure the TcpClient used by a Reactor Netty-based WebClient



Spring Boot offers a number of starters that work with HTTP clients. This section answers questions related to using them.

## Configure RestTemplate to Use a Proxy

As described in [RestTemplate Customization](../reference/io/rest-client.html#io.rest-client.resttemplate.customization), you can use a [`RestTemplateCustomizer`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateCustomizer.html) with [`RestTemplateBuilder`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html) to build a customized [`RestTemplate`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html). This is the recommended approach for creating a [`RestTemplate`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html) configured to use a proxy.

The exact details of the proxy configuration depend on the underlying client request factory that is being used.

## Configure the TcpClient used by a Reactor Netty-based WebClient

When Reactor Netty is on the classpath a Reactor Netty-based [`WebClient`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html) is auto-configured. To customize the client’s handling of network connections, provide a [`ClientHttpConnector`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/reactive/ClientHttpConnector.html) bean. The following example configures a 60 second connect timeout and adds a [`ReadTimeoutHandler`](https://netty.io/4.1/api/io/netty/handler/timeout/ReadTimeoutHandler.html):

  * Java

  * Kotlin



    
    
    import io.netty.channel.ChannelOption;
    import io.netty.handler.timeout.ReadTimeoutHandler;
    import reactor.netty.http.client.HttpClient;
    
    import org.springframework.context.annotation.Bean;
    import org.springframework.context.annotation.Configuration;
    import org.springframework.http.client.ReactorResourceFactory;
    import org.springframework.http.client.reactive.ClientHttpConnector;
    import org.springframework.http.client.reactive.ReactorClientHttpConnector;
    
    @Configuration(proxyBeanMethods = false)
    public class MyReactorNettyClientConfiguration {
    
    	@Bean
    	ClientHttpConnector clientHttpConnector(ReactorResourceFactory resourceFactory) {
    		HttpClient httpClient = HttpClient.create(resourceFactory.getConnectionProvider())
    				.runOn(resourceFactory.getLoopResources())
    				.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 60000)
    				.doOnConnected((connection) -> connection.addHandlerLast(new ReadTimeoutHandler(60)));
    		return new ReactorClientHttpConnector(httpClient);
    	}
    
    }
    
    Copied!
    
    
    import io.netty.channel.ChannelOption
    import io.netty.handler.timeout.ReadTimeoutHandler
    import org.springframework.context.annotation.Bean
    import org.springframework.context.annotation.Configuration
    import org.springframework.http.client.reactive.ClientHttpConnector
    import org.springframework.http.client.reactive.ReactorClientHttpConnector
    import org.springframework.http.client.ReactorResourceFactory
    import reactor.netty.http.client.HttpClient
    
    @Configuration(proxyBeanMethods = false)
    class MyReactorNettyClientConfiguration {
    
    	@Bean
    	fun clientHttpConnector(resourceFactory: ReactorResourceFactory): ClientHttpConnector {
    		val httpClient = HttpClient.create(resourceFactory.connectionProvider)
    			.runOn(resourceFactory.loopResources)
    			.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 60000)
    			.doOnConnected { connection ->
    				connection.addHandlerLast(ReadTimeoutHandler(60))
    			}
    		return ReactorClientHttpConnector(httpClient)
    	}
    
    }
    
    Copied!

__ |  Note the use of [`ReactorResourceFactory`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/ReactorResourceFactory.html) for the connection provider and event loop resources. This ensures efficient sharing of resources for the server receiving requests and the client making requests.   
---|---  
  
[Jersey](jersey.html) [Logging](logging.html)
---
