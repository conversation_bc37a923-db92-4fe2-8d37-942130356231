Title: Batch Applications :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/batch.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/Batch_Applications_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/Batch_Applications_Spring_Boot.png
crawled_at: 2025-06-04T15:46:06.556717
---
Search CTRL + k

### Batch Applications

  * Specifying a Batch Data Source
  * Specifying a Batch Transaction Manager
  * Specifying a Batch Task Executor
  * Running Spring Batch Jobs on Startup
  * Running From the Command Line
  * Restarting a Stopped or Failed Job
  * Storing the Job Repository



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/batch.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [How-to Guides](index.html)
  * [Batch Applications](batch.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../how-to/batch.html)!  
---|---  
  
# Batch Applications

### Batch Applications

  * Specifying a Batch Data Source
  * Specifying a Batch Transaction Manager
  * Specifying a Batch Task Executor
  * Running Spring Batch Jobs on Startup
  * Running From the Command Line
  * Restarting a Stopped or Failed Job
  * Storing the Job Repository



A number of questions often arise when people use Spring Batch from within a Spring Boot application. This section addresses those questions.

## Specifying a Batch Data Source

By default, batch applications require a [`DataSource`](https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html) to store job details. Spring Batch expects a single [`DataSource`](https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html) by default. To have it use a [`DataSource`](https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html) other than the application’s main [`DataSource`](https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html), declare a [`DataSource`](https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html) bean, annotating its [`@Bean`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html) method with [`@BatchDataSource`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/batch/BatchDataSource.html). If you do so and want two data sources (for example by retaining the main auto-configured [`DataSource`](https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html)), set the `defaultCandidate` attribute of the [`@Bean`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html) annotation to `false`. To take greater control, add [`@EnableBatchProcessing`](https://docs.spring.io/spring-batch/docs/5.2.x/api/org/springframework/batch/core/configuration/annotation/EnableBatchProcessing.html) to one of your [`@Configuration`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html) classes or extend [`DefaultBatchConfiguration`](https://docs.spring.io/spring-batch/docs/5.2.x/api/org/springframework/batch/core/configuration/support/DefaultBatchConfiguration.html). See the API documentation of [`@EnableBatchProcessing`](https://docs.spring.io/spring-batch/docs/5.2.x/api/org/springframework/batch/core/configuration/annotation/EnableBatchProcessing.html) and [`DefaultBatchConfiguration`](https://docs.spring.io/spring-batch/docs/5.2.x/api/org/springframework/batch/core/configuration/support/DefaultBatchConfiguration.html) for more details.

For more info about Spring Batch, see the [Spring Batch project page](https://spring.io/projects/spring-batch).

## Specifying a Batch Transaction Manager

Similar to Specifying a Batch Data Source, you can define a [`PlatformTransactionManager`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/transaction/PlatformTransactionManager.html) for use in batch processing by annotating its [`@Bean`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html) method with [`@BatchTransactionManager`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/batch/BatchTransactionManager.html). If you do so and want two transaction managers (for example by retaining the auto-configured [`PlatformTransactionManager`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/transaction/PlatformTransactionManager.html)), set the `defaultCandidate` attribute of the [`@Bean`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html) annotation to `false`.

## Specifying a Batch Task Executor

Similar to Specifying a Batch Data Source, you can define a [`TaskExecutor`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/task/TaskExecutor.html) for use in batch processing by annotating its [`@Bean`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html) method with [`@BatchTaskExecutor`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/batch/BatchTaskExecutor.html). If you do so and want two task executors (for example by retaining the auto-configured [`TaskExecutor`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/task/TaskExecutor.html)), set the `defaultCandidate` attribute of the [`@Bean`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html) annotation to `false`.

## Running Spring Batch Jobs on Startup

Spring Batch auto-configuration is enabled by adding `spring-boot-starter-batch` to your application’s classpath.

If a single [`Job`](https://docs.spring.io/spring-batch/docs/5.2.x/api/org/springframework/batch/core/Job.html) bean is found in the application context, it is executed on startup (see [`JobLauncherApplicationRunner`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/batch/JobLauncherApplicationRunner.html) for details). If multiple [`Job`](https://docs.spring.io/spring-batch/docs/5.2.x/api/org/springframework/batch/core/Job.html) beans are found, the job that should be executed must be specified using `spring.batch.job.name`.

To disable running a [`Job`](https://docs.spring.io/spring-batch/docs/5.2.x/api/org/springframework/batch/core/Job.html) found in the application context, set the `spring.batch.job.enabled` to `false`.

See [`BatchAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/batch/BatchAutoConfiguration.java) for more details.

## Running From the Command Line

Spring Boot converts any command line argument starting with `--` to a property to add to the [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html), see [accessing command line properties](../reference/features/external-config.html#features.external-config.command-line-args). This should not be used to pass arguments to batch jobs. To specify batch arguments on the command line, use the regular format (that is without `--`), as shown in the following example:
    
    
    $ java -jar myapp.jar someParameter=someValue anotherParameter=anotherValue
    
    Copied!

If you specify a property of the [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html) on the command line, it is ignored by the job. Consider the following command:
    
    
    $ java -jar myapp.jar --server.port=7070 someParameter=someValue
    
    Copied!

This provides only one argument to the batch job: `someParameter=someValue`.

## Restarting a Stopped or Failed Job

To restart a failed [`Job`](https://docs.spring.io/spring-batch/docs/5.2.x/api/org/springframework/batch/core/Job.html), all parameters (identifying and non-identifying) must be re-specified on the command line. Non-identifying parameters are **not** copied from the previous execution. This allows them to be modified or removed.

__ |  When you’re using a custom [`JobParametersIncrementer`](https://docs.spring.io/spring-batch/docs/5.2.x/api/org/springframework/batch/core/JobParametersIncrementer.html), you have to gather all parameters managed by the incrementer to restart a failed execution.   
---|---  
  
## Storing the Job Repository

Spring Batch requires a data store for the [`Job`](https://docs.spring.io/spring-batch/docs/5.2.x/api/org/springframework/batch/core/Job.html) repository. If you use Spring Boot, you must use an actual database. Note that it can be an in-memory database, see [Configuring a Job Repository](https://docs.spring.io/spring-batch/reference/5.2/job.html#configuringJobRepository).

[Messaging](messaging.html) [Actuator](actuator.html)
---
