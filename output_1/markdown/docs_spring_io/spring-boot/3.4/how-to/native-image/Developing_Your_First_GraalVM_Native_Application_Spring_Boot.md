Title: Developing Your First GraalVM Native Application :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/native-image/developing-your-first-application.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/native-image/Developing_Your_First_GraalVM_Native_Application_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/native-image/Developing_Your_First_GraalVM_Native_Application_Spring_Boot.png
crawled_at: 2025-06-04T15:48:41.553723
---
Search CTRL + k

### Developing Your First GraalVM Native Application

  * Sample Application
  * Building a Native Image Using Buildpacks
  * System Requirements
  * Using Maven
  * Using Gradle
  * Running the example
  * Building a Native Image using Native Build Tools
  * Prerequisites
  * Using Maven
  * Using Gradle
  * Running the Example



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/native-image/developing-your-first-application.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [How-to Guides](../index.html)
  * [GraalVM Native Applications](index.html)
  * [Developing Your First GraalVM Native Application](developing-your-first-application.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../how-to/native-image/developing-your-first-application.html)!  
---|---  
  
# Developing Your First GraalVM Native Application

### Developing Your First GraalVM Native Application

  * Sample Application
  * Building a Native Image Using Buildpacks
  * System Requirements
  * Using Maven
  * Using Gradle
  * Running the example
  * Building a Native Image using Native Build Tools
  * Prerequisites
  * Using Maven
  * Using Gradle
  * Running the Example



There are two main ways to build a Spring Boot native image application:

  * Using Spring Boot [support for Cloud Native Buildpacks](../../reference/packaging/container-images/cloud-native-buildpacks.html) with the [Paketo Java Native Image buildpack](https://paketo.io/docs/reference/java-native-image-reference/) to generate a lightweight container containing a native executable.

  * Using GraalVM Native Build Tools to generate a native executable.




__ |  The easiest way to start a new native Spring Boot project is to go to [start.spring.io](https://start.spring.io), add the `GraalVM Native Support` dependency and generate the project. The included `HELP.md` file will provide getting started hints.   
---|---  
  
## Sample Application

We need an example application that we can use to create our native image. For our purposes, the simple "Hello World!" web application that’s covered in the [Developing Your First Spring Boot Application](../../tutorial/first-application/index.html) section will suffice.

To recap, our main application code looks like this:
    
    
    import org.springframework.boot.SpringApplication;
    import org.springframework.boot.autoconfigure.SpringBootApplication;
    import org.springframework.web.bind.annotation.RequestMapping;
    import org.springframework.web.bind.annotation.RestController;
    
    @RestController
    @SpringBootApplication
    public class MyApplication {
    
    	@RequestMapping("/")
    	String home() {
    		return "Hello World!";
    	}
    
    	public static void main(String[] args) {
    		SpringApplication.run(MyApplication.class, args);
    	}
    
    }
    
    Copied!

This application uses Spring MVC and embedded Tomcat, both of which have been tested and verified to work with GraalVM native images.

## Building a Native Image Using Buildpacks

Spring Boot supports building Docker images containing native executables, using Cloud Native Buildpacks (CNB) integration with both Maven and Gradle and the [Paketo Java Native Image buildpack](https://paketo.io/docs/reference/java-native-image-reference/). This means you can just type a single command and quickly get a sensible image into your locally running Docker daemon. The resulting image doesn’t contain a JVM, instead the native image is compiled statically. This leads to smaller images.

__ |  The CNB builder used for the images is `paketobuildpacks/builder-jammy-java-tiny:latest`. It has a small footprint and reduced attack surface. It does not include a shell and contains a reduced set of system libraries. Use `paketobuildpacks/builder-jammy-base:latest` or `paketobuildpacks/builder-jammy-full:latest` to have more tools available in the image if required.   
---|---  
  
### System Requirements

Docker should be installed. See [Get Docker](https://docs.docker.com/installation/#installation) for more details. [Configure it to allow non-root user](https://docs.docker.com/engine/install/linux-postinstall/#manage-docker-as-a-non-root-user) if you are on Linux.

__ |  You can run `docker run hello-world` (without `sudo`) to check the Docker daemon is reachable as expected. Check the [Maven](../../maven-plugin/build-image.html#build-image.docker-daemon) or [Gradle](../../gradle-plugin/packaging-oci-image.html#build-image.docker-daemon) Spring Boot plugin documentation for more details.   
---|---  
  
__ |  On macOS, it is recommended to increase the memory allocated to Docker to at least `8GB`, and potentially add more CPUs as well. See this [Stack Overflow answer](https://stackoverflow.com/questions/44533319/how-to-assign-more-memory-to-docker-container/44533437#44533437) for more details. On Microsoft Windows, make sure to enable the [Docker WSL 2 backend](https://docs.docker.com/docker-for-windows/wsl/) for better performance.   
---|---  
  
### Using Maven

To build a native image container using Maven you should ensure that your `pom.xml` file uses the `spring-boot-starter-parent` and the `org.graalvm.buildtools:native-maven-plugin`. You should have a `<parent>` section that looks like this:
    
    
    <parent>
    	<groupId>org.springframework.boot</groupId>
    	<artifactId>spring-boot-starter-parent</artifactId>
    	<version>3.4.6</version>
    </parent>
    
    Copied!

You additionally should have this in the `<build> <plugins>` section:
    
    
    <plugin>
    	<groupId>org.graalvm.buildtools</groupId>
    	<artifactId>native-maven-plugin</artifactId>
    </plugin>
    
    Copied!

The `spring-boot-starter-parent` declares a `native` profile that configures the executions that need to run in order to create a native image. You can activate profiles using the `-P` flag on the command line.

__ |  If you don’t want to use `spring-boot-starter-parent` you’ll need to configure executions for the `process-aot` goal from Spring Boot’s plugin and the `add-reachability-metadata` goal from the Native Build Tools plugin.   
---|---  
  
To build the image, you can run the `spring-boot:build-image` goal with the `native` profile active:
    
    
    $ mvn -Pnative spring-boot:build-image
    
    Copied!

### Using Gradle

The Spring Boot Gradle plugin automatically configures AOT tasks when the GraalVM Native Image plugin is applied. You should check that your Gradle build contains a `plugins` block that includes `org.graalvm.buildtools.native`.

As long as the `org.graalvm.buildtools.native` plugin is applied, the `bootBuildImage` task will generate a native image rather than a JVM one. You can run the task using:
    
    
    $ gradle bootBuildImage
    
    Copied!

### Running the example

Once you have run the appropriate build command, a Docker image should be available. You can start your application using `docker run`:
    
    
    $ docker run --rm -p 8080:8080 docker.io/library/myproject:0.0.1-SNAPSHOT
    
    Copied!

You should see output similar to the following:
    
    
      .   ____          _            __ _ _
     /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
    ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
     \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
      '  |____| .__|_| |_|_| |_\__, | / / / /
     =========|_|==============|___/=/_/_/_/
     :: Spring Boot ::  (v{version-spring-boot})
    ....... . . .
    ....... . . . (log output here)
    ....... . . .
    ........ Started MyApplication in 0.08 seconds (process running for 0.095)
    
    Copied!

__ |  The startup time differs from machine to machine, but it should be much faster than a Spring Boot application running on a JVM.   
---|---  
  
If you open a web browser to `[localhost:8080](http://localhost:8080)`, you should see the following output:
    
    
    Hello World!
    
    Copied!

To gracefully exit the application, press `ctrl-c`.

## Building a Native Image using Native Build Tools

If you want to generate a native executable directly without using Docker, you can use GraalVM Native Build Tools. Native Build Tools are plugins shipped by GraalVM for both Maven and Gradle. You can use them to perform a variety of GraalVM tasks, including generating a native image.

### Prerequisites

To build a native image using the Native Build Tools, you’ll need a GraalVM distribution on your machine. You can either download it manually on the [Liberica Native Image Kit page](https://bell-sw.com/pages/downloads/native-image-kit/#/nik-22-17), or you can use a download manager like SDKMAN!.

#### Linux and macOS

To install the native image compiler on macOS or Linux, we recommend using SDKMAN!. Get SDKMAN! from [sdkman.io](https://sdkman.io) and install the Liberica GraalVM distribution by using the following commands:
    
    
    $ sdk install java 22.3.r17-nik
    $ sdk use java 22.3.r17-nik
    
    Copied!

Verify that the correct version has been configured by checking the output of `java -version`:
    
    
    $ java -version
    openjdk version "17.0.5" 2022-10-18 LTS
    OpenJDK Runtime Environment GraalVM 22.3.0 (build 17.0.5+8-LTS)
    OpenJDK 64-Bit Server VM GraalVM 22.3.0 (build 17.0.5+8-LTS, mixed mode)
    
    Copied!

#### Windows

On Windows, follow [these instructions](https://medium.com/graalvm/using-graalvm-and-native-image-on-windows-10-9954dc071311) to install either [GraalVM](https://www.graalvm.org/downloads/) or [Liberica Native Image Kit](https://bell-sw.com/pages/downloads/native-image-kit/#/nik-22-17) in version 22.3, the Visual Studio Build Tools and the Windows SDK. Due to the [Windows related command-line maximum length](https://docs.microsoft.com/en-US/troubleshoot/windows-client/shell-experience/command-line-string-limitation), make sure to use x64 Native Tools Command Prompt instead of the regular Windows command line to run Maven or Gradle plugins.

### Using Maven

As with the buildpacks support, you need to make sure that you’re using `spring-boot-starter-parent` in order to inherit the `native` profile and that the `org.graalvm.buildtools:native-maven-plugin` plugin is used.

With the `native` profile active, you can invoke the `native:compile` goal to trigger `native-image` compilation:
    
    
    $ mvn -Pnative native:compile
    
    Copied!

The native image executable can be found in the `target` directory.

### Using Gradle

When the Native Build Tools Gradle plugin is applied to your project, the Spring Boot Gradle plugin will automatically trigger the Spring AOT engine. Task dependencies are automatically configured, so you can just run the standard `nativeCompile` task to generate a native image:
    
    
    $ gradle nativeCompile
    
    Copied!

The native image executable can be found in the `build/native/nativeCompile` directory.

### Running the Example

At this point, your application should work. You can now start the application by running it directly:

  * Maven

  * Gradle



    
    
    $ target/myproject
    
    Copied!
    
    
    $ build/native/nativeCompile/myproject
    
    Copied!

You should see output similar to the following:
    
    
      .   ____          _            __ _ _
     /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
    ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
     \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
      '  |____| .__|_| |_|_| |_\__, | / / / /
     =========|_|==============|___/=/_/_/_/
     :: Spring Boot ::  (v3.4.6)
    ....... . . .
    ....... . . . (log output here)
    ....... . . .
    ........ Started MyApplication in 0.08 seconds (process running for 0.095)
    
    Copied!

__ |  The startup time differs from machine to machine, but it should be much faster than a Spring Boot application running on a JVM.   
---|---  
  
If you open a web browser to `[localhost:8080](http://localhost:8080)`, you should see the following output:
    
    
    Hello World!
    
    Copied!

To gracefully exit the application, press `ctrl-c`.

[GraalVM Native Applications](index.html) [Testing GraalVM Native Images](testing-native-applications.html)
---
