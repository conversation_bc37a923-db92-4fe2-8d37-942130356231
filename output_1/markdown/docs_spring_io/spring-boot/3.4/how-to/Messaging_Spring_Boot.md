Title: Messaging :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/messaging.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/Messaging_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/Messaging_Spring_Boot.png
crawled_at: 2025-06-04T15:46:44.558713
---
Search CTRL + k

### Messaging

  * Disable Transacted JMS Session



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/messaging.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [How-to Guides](index.html)
  * [Messaging](messaging.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../how-to/messaging.html)!  
---|---  
  
# Messaging

### Messaging

  * Disable Transacted JMS Session



Spring Boot offers a number of starters to support messaging. This section answers questions that arise from using messaging with Spring Boot.

## Disable Transacted JMS Session

If your JMS broker does not support transacted sessions, you have to disable the support of transactions altogether. If you create your own [`JmsListenerContainerFactory`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jms/config/JmsListenerContainerFactory.html), there is nothing to do, since, by default it cannot be transacted. If you want to use the [`DefaultJmsListenerContainerFactoryConfigurer`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jms/DefaultJmsListenerContainerFactoryConfigurer.html) to reuse Spring Boot’s default, you can disable transacted sessions, as follows:

  * Java

  * Kotlin



    
    
    import jakarta.jms.ConnectionFactory;
    
    import org.springframework.boot.autoconfigure.jms.DefaultJmsListenerContainerFactoryConfigurer;
    import org.springframework.boot.jms.ConnectionFactoryUnwrapper;
    import org.springframework.context.annotation.Bean;
    import org.springframework.context.annotation.Configuration;
    import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
    
    @Configuration(proxyBeanMethods = false)
    public class MyJmsConfiguration {
    
    	@Bean
    	public DefaultJmsListenerContainerFactory jmsListenerContainerFactory(ConnectionFactory connectionFactory,
    			DefaultJmsListenerContainerFactoryConfigurer configurer) {
    		DefaultJmsListenerContainerFactory listenerFactory = new DefaultJmsListenerContainerFactory();
    		configurer.configure(listenerFactory, ConnectionFactoryUnwrapper.unwrapCaching(connectionFactory));
    		listenerFactory.setTransactionManager(null);
    		listenerFactory.setSessionTransacted(false);
    		return listenerFactory;
    	}
    
    }
    
    Copied!
    
    
    import jakarta.jms.ConnectionFactory
    import org.springframework.boot.jms.ConnectionFactoryUnwrapper
    import org.springframework.boot.autoconfigure.jms.DefaultJmsListenerContainerFactoryConfigurer
    import org.springframework.context.annotation.Bean
    import org.springframework.context.annotation.Configuration
    import org.springframework.jms.config.DefaultJmsListenerContainerFactory
    
    @Configuration(proxyBeanMethods = false)
    class MyJmsConfiguration {
    
    	@Bean
    	fun jmsListenerContainerFactory(connectionFactory: ConnectionFactory?,
    			configurer: DefaultJmsListenerContainerFactoryConfigurer): DefaultJmsListenerContainerFactory {
    		val listenerFactory = DefaultJmsListenerContainerFactory()
    		configurer.configure(listenerFactory, ConnectionFactoryUnwrapper.unwrapCaching(connectionFactory))
    		listenerFactory.setTransactionManager(null)
    		listenerFactory.setSessionTransacted(false)
    		return listenerFactory
    	}
    
    }
    
    Copied!

The preceding example overrides the default factory, and it should be applied to any other factory that your application defines, if any.

[NoSQL](nosql.html) [Batch Applications](batch.html)
---
