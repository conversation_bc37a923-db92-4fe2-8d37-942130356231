Title: Community :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/community.html
HTML: html/docs_spring_io/spring-boot/3.4/Community_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/Community_Spring_Boot.png
crawled_at: 2025-06-04T15:39:16.837170
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/ROOT/pages/community.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](index.html)
  * [Community](community.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../community.html)!  
---|---  
  
# Community

If you have trouble with Spring Boot, we would like to help.

  * Try the [How-to documents](how-to/index.html). They provide solutions to the most common questions.

  * Learn the Spring basics. Spring Boot builds on many other Spring projects. Check the [spring.io](https://spring.io) web-site for a wealth of reference documentation. If you are starting out with Spring, try one of the [guides](https://spring.io/guides).

  * Ask a question. We monitor [stackoverflow.com](https://stackoverflow.com) for questions tagged with [`spring-boot`](https://stackoverflow.com/tags/spring-boot).

  * Report bugs with Spring Boot at [github.com/spring-projects/spring-boot/issues](https://github.com/spring-projects/spring-boot/issues).




__ |  All of Spring Boot is open source, including the documentation. If you find problems with the docs or if you want to improve them, please [get involved](https://github.com/spring-projects/spring-boot).   
---|---  
  
[Documentation](documentation.html) [System Requirements](system-requirements.html)
---
