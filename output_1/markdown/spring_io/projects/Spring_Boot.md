Title: Spring Boot
Source: https://spring.io/projects/spring-boot
HTML: html/spring_io/projects/Spring_Boot.html
Screenshot: screenshot/spring_io/projects/Spring_Boot.png
crawled_at: 2025-06-04T15:39:07.035058
---
Spring Boot makes it easy to create stand-alone, production-grade Spring based Applications that you can "just run".

We take an opinionated view of the Spring platform and third-party libraries so you can get started with minimum fuss. Most Spring Boot applications need minimal Spring configuration.

If you’re looking for information about a specific version, or instructions about how to upgrade from an earlier release, check out [the project release notes section](https://github.com/spring-projects/spring-boot/wiki#release-notes) on our wiki.

## Features

  * Create stand-alone Spring applications
  * Embed Tomcat, Jetty or Undertow directly (no need to deploy WAR files)
  * Provide opinionated 'starter' dependencies to simplify your build configuration
  * Automatically configure Spring and 3rd party libraries whenever possible
  * Provide production-ready features such as metrics, health checks, and externalized configuration
  * Absolutely no code generation and no requirement for XML configuration



## Getting Started

  * Super quick — try the [Quickstart Guide](https://spring.io/quickstart).
  * More general — try [Building an Application with Spring Boot](https://spring.io/guides/gs/spring-boot/)
  * More specific — try [Building a RESTful Web Service](https://spring.io/guides/gs/rest-service/).
  * Or search through all our guides on the [Guides](https://spring.io/guides) homepage.



## Talks and videos

  * [Mind the Gap: Jumping from Spring Boot 2.x to 3.x](https://www.youtube.com/watch?v=HrRQExD3xow)
  * [Demystifying Spring Internals](https://www.youtube.com/watch?v=LeoCh7VK9cg)
  * [Ahead Of Time and Native in Spring Boot 3.0](https://www.youtube.com/watch?v=TS4DpYSmfXk)
  * [Improve Your Developer Experience with Spring Boot Dev Services](https://www.youtube.com/watch?v=Yqss7tYP890)



You can also join the [Spring Boot community on Gitter](https://gitter.im/spring-projects/spring-boot)!
---
