Title: Spring Security
Source: https://spring.io/projects/spring-security
HTML: html/spring_io/projects/Spring_Security.html
Screenshot: screenshot/spring_io/projects/Spring_Security.png
crawled_at: 2025-06-04T15:41:26.673768
---
Spring Security is a powerful and highly customizable authentication and access-control framework. It is the de-facto standard for securing Spring-based applications.

Spring Security is a framework that focuses on providing both authentication and authorization to Java applications. Like all Spring projects, the real power of Spring Security is found in how easily it can be extended to meet custom requirements

## Features

  * Comprehensive and extensible support for both Authentication and Authorization

  * Protection against attacks like session fixation, clickjacking, cross site request forgery, etc

  * Servlet API integration

  * Optional integration with Spring Web MVC

  * Much more…




## Resources

  * [Getting Help](https://docs.spring.io/spring-security/reference/community.html#community-help)

  * [Getting Spring Security](https://docs.spring.io/spring-security/reference/getting-spring-security.html)

  * Getting Started

    * [Getting Started (Servlet)](https://docs.spring.io/spring-security/reference/servlet/getting-started.html)

    * [Getting Started (WebFlux)](https://docs.spring.io/spring-security/reference/reactive/getting-started.html)

  * [Contributing](https://docs.spring.io/spring-security/reference/community.html#community-becoming-involved)
---
