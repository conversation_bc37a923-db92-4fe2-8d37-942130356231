Title: Spring Cloud Data Flow
Source: https://spring.io/projects/spring-cloud-dataflow
HTML: html/spring_io/projects/Spring_Cloud_Data_Flow.html
Screenshot: screenshot/spring_io/projects/Spring_Cloud_Data_Flow.png
crawled_at: 2025-06-04T15:49:07.921038
---
# Spring Cloud Data Flow is no longer maintained as an open-source project by Broadcom, Inc.

## For information about extended support or enterprise options for Spring Cloud Data Flow, please read the official blog post [here](https://spring.io/blog/2025/04/21/spring-cloud-data-flow-commercial).

Microservice based Streaming and Batch data processing for Cloud Foundry and Kubernetes.

Spring Cloud Data Flow provides tools to create complex topologies for streaming and batch data pipelines. The data pipelines consist of [Spring Boot](https://projects.spring.io/spring-boot/) apps, built using the [Spring Cloud Stream](https://cloud.spring.io/spring-cloud-stream) or [Spring Cloud Task](https://cloud.spring.io/spring-cloud-task/) microservice frameworks.

Spring Cloud Data Flow supports a range of data processing use cases, from ETL to import/export, event streaming, and predictive analytics.

## Features

The Spring Cloud Data Flow server uses [Spring Cloud Deployer](https://github.com/spring-cloud/spring-cloud-deployer/), to deploy data pipelines made of Spring Cloud Stream or Spring Cloud Task applications onto modern platforms such as Cloud Foundry and Kubernetes.

A selection of pre-built [stream](https://spring.io/projects/spring-cloud-stream-applications/) and [task/batch](https://docs.spring.io/spring-cloud-dataflow/docs/current/reference/htmlsingle/#_out_of_the_box_task_applications) starter apps for various data integration and processing scenarios facilitate learning and experimentation.

[Custom](https://docs.spring.io/spring-cloud-dataflow/docs/current/reference/htmlsingle/#custom-applications) stream and task applications, targeting different middleware or data services, can be built using the familiar Spring Boot style programming model.

A simple [stream pipeline DSL](https://docs.spring.io/spring-cloud-dataflow/docs/current/reference/htmlsingle/#_stream_dsl) makes it easy to specify which apps to deploy and how to connect outputs and inputs. The [composed task DSL](https://docs.spring.io/spring-cloud-dataflow/docs/current/reference/htmlsingle/#_composed_tasks_dsl) is useful for when a series of task apps require to be run as a directed graph.

The [dashboard](https://docs.spring.io/spring-cloud-dataflow/docs/current/reference/htmlsingle/#dashboard-introduction) offers a graphical editor for building data pipelines interactively, as well as views of deployable apps and monitoring them with metrics using [Wavefront](https://www.wavefront.com), [Prometheus](https://prometheus.io), [Influx DB](https://www.influxdata.com), or other monitoring systems.

The Spring Cloud Data Flow server exposes a [REST API](https://docs.spring.io/spring-cloud-dataflow/docs/current/reference/htmlsingle/#api-guide-resources) for composing and deploying data pipelines. A separate [shell](https://docs.spring.io/spring-cloud-dataflow/docs/current/reference/htmlsingle/#shell) makes it easy to work with the API from the command line.

## Getting Started

The [Spring Cloud Data Flow Microsite](https://dataflow.spring.io/getting-started/) is the best place to get started.
---
