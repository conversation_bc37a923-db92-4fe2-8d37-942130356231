Title: Spring | Microservices
Source: https://spring.io/microservices
HTML: html/spring_io/Spring_Microservices.html
Screenshot: screenshot/spring_io/Spring_Microservices.png
crawled_at: 2025-06-04T15:50:20.965865
---
# Microservices

Microservice architectures are the ‘new normal’. Building small, self-contained, ready to run applications can bring great flexibility and added resilience to your code. Spring Boot’s many purpose-built features make it easy to build and run your microservices in production at scale. And don’t forget, no microservice architecture is complete without [Spring Cloud](/cloud) ‒ easing administration and boosting your fault-tolerance. 

### What are microservices?

Microservices are a modern approach to software whereby application code is delivered in small, manageable pieces, independent of others.

### Why build microservices?

Their small scale and relative isolation can lead to many additional benefits, such as easier maintenance, improved productivity, greater fault tolerance, better business alignment, and more.

![](/img/extra/microservices-5.svg)
---
