Title: Spring | Spring Trademark Guidelines
Source: https://spring.io/trademarks
HTML: html/spring_io/Spring_Spring_Trademark_Guidelines.html
Screenshot: screenshot/spring_io/Spring_Spring_Trademark_Guidelines.png
crawled_at: 2025-06-04T15:40:24.002239
---
# Spring Trademark Guidelines

  1. **PURPOSE.** Broadcom Inc. and/or its subsidiaries. (“**Broadcom** ”) owns a number of international trademarks and logos that identify the Spring community and individual Spring projects (“**Spring Marks** ”). These trademarks include, but are not limited to:

     1. **Words: SPRING, SPRING FRAMEWORK, SPRING BOOT, SPRING CLOUD, SPRINGSOURCE, SPRING IO, SPRING IO PLATFORM**
     2. **Logos:** ![Spring](/img/favicon.ico)![Spring](/img/spring-2.svg)![Spring](/img/spring.svg)

This policy outlines Broadcom’s policy and guidelines about the use of the Spring Marks by members of the Spring development and user community.

  2. **WHY HAVE TRADEMARK GUIDELINES?** The Spring Marks are a symbol of the quality and community support associated with the Spring open source software. Trademarks protect not only those using the marks, but the entire community as well. Our community members need to know that they can rely on the quality and capabilities represented by the brand. We also want to provide a level playing field. No one should use the Spring Marks in ways that mislead or take advantage of the community, or make unfair use of the trademarks. Also, use of the Spring Marks should not be in a disparaging manner because we prefer that our marks not be used to be rude about the Spring open source project or its members.

  3. **OPEN SOURCE LICENSE VS. TRADEMARKS.** The Apache 2.0 license gives you the right to use, copy, distribute and modify the Spring software. However, open source licenses like the Apache 2.0 license do not address trademarks. Spring Marks need to be used in a way consistent with trademark law, and that is why we have prepared this policy – to help you understand what branding is allowed or required when using our software under the Apache license.

  4. **PROPER USE OF THE SPRING MARKS.** We want to encourage a robust community for the Spring open source project. Therefore, you may do any of the following, as long as you do so in a way that does not devalue, dilute, or disparage the Spring brand. In other words, when you do these things, you should behave responsibly and reasonably in the interest of the community, but you do not need a trademark license from us to do them.

     1. **Nominative Use.** You may engage in “nominative use” of the Spring word trademarks (the “**Spring Word Marks** ”), but this does not allow you to use the logo. Nominative use is sometimes called “fair use” of a trademark, and does not require a trademark license from us. Here are examples:

        1. You may use the Spring Word Marks in connection with development of tools, add-ons, or utilities that are compatible with bit-for-bit identical copies of official Spring software. For example, if you are developing a Foobar tool for Spring, acceptable project titles would be “Foobar for Spring".

        2. You may use the Spring Word Marks in connection with your noncommercial redistribution of (1) bit-for-bit identical copies of official Spring software, and (2) unmodified copies of official Spring source packages. For example, if your Foobar product included a full redistribution of official Spring Software or source code packages: "Spring-powered Foobar Product". Broadcom does not allow a Spring Word Mark to be used directly with another word, phrase or trademark, such as “Spring Foobar.”

        3. If you offer maintenance, support, or hosting services for Spring software, you may accurately state that in your marketing materials or portfolio, using the Spring Word Marks.

        4. You may modify the Spring software and state that your modified software is “based on the Spring software” or a similar accurate statement, using the Spring Word Marks.

        5. You may engage in community advocacy. The Spring software is developed by and for its community. We will allow the use of the word Spring Word Marks in this context, provided:

           1. The Spring Word Mark is used in a manner consistent with this policy;
           2. There is no commercial purpose behind the use; and
           3. There is no suggestion that your project is approved, sponsored, or affiliated with Broadcom.
           4. User or Development Groups. You may create Spring user or development groups, and publicize meetings or discussions for those groups. Please consider joining our official groups, or contact us if you are interested in creating a Broadcom-sponsored group.
     2. **Attribution.** Identify the trademarks as trademarks of Broadcom, as set forth in Section 7.

     3. **Redistribution of Binaries.** If you redistribute binaries that you have downloaded from the Spring repository, you should retain the Spring Marks. However, if you make any changes to the binaries (other than configuration or installation changes that do not involve changes to the source code), or if you re-build binaries from our source code, you should not use our any Spring Mark logo. Spring Mark represent our quality control, so they should be retained where the product has been built by us, but not otherwise. Our source code does not include logo image files, to remind you to follow this rule.

     4. **Capitalization.** “Spring” should always be capitalized and one word.

     5. **Adjectives.** Use the Spring Marks as an adjective, not a noun or verb.

  5. **IMPROPER USE OF THE TRADEMARKS AND LOGOS.** Use of any Spring Marks logo is reserved solely for use by Broadcom in its unaltered form. Examples of **unauthorized** use of the Spring trademarks include:

     1. **Commercial Use:** You may not use the Spring Marks in connection with commercial redistribution of Spring software (commercial redistribution includes, but is not limited to, redistribution in connection with any commercial business activities or revenue-generating business activities) regardless of whether the Spring software is unmodified.
     2. **Entity Names.** You may not form a company, use a company name, or create a software product name that includes the “Spring” trademark, or implies any foundational or authorship role. If you have a software product that works with Spring, it is suggested you use terms such as ‘ for Spring’ or ‘, Spring Edition.” If you wish to form an entity for a user or developer group, please contact us and we will be glad to discuss a license for a suitable name.
     3. **Class or Quality.** You may not imply that you are providing a class or quality of Spring (e.g., "enterprise-class" or "commercial quality") in a way that implies Spring is not of that class, grade or quality, nor that other parties are not of that class, grade, or quality.
     4. **Combinations.** Use of the Spring Marks to identify software that combines any portion of the Spring software with any other software, unless the combined distribution is an official Spring distribution. For example, you may not distribute a combination of the Spring software with software released by the Foobar project under the name “Spring Foobar Distro”.
     5. **False or Misleading Statements.** You may not make false or misleading statements regarding your use of Spring (e.g., "we wrote the majority of the code" or "we are major contributors" or "we are committers").
     6. **Domain Names.** You must not use Spring or any confusingly similar phrase in a domain name. For instance “[www.springhost.com”](http://www.springhost.com%E2%80%9D) is not allowed. If you wish to use such a domain name for a non-commercial user or developer group to engage in community advocacy, please contact us and we will be glad to discuss a license for a suitable domain name. Because of the many persons who, unfortunately, seek to spoof, swindle or deceive the community by using confusing domain names, we must be very strict about this rule.
     7. **Merchandise.** You must not manufacture, sell or give away merchandise items, such as T-shirts and mugs, bearing the Spring logo, or create any mascot for the project. If you wish to use the logo to do this for a non-commerical user or developer group to engage in community advocacy, please contact us and we will be glad to discuss a license to do this.
     8. **Variations, takeoffs or abbreviations.** You may not use a variation of the Spring Marks for any purpose other than common usage of these in community communications. For example, the following are not acceptable: 
        1. SPRNG
        2. MySpring
        3. SpringDB
        4. SpringHost
        5. SpringGuru
     9. **Endorsement or Sponsorship.** You may not use the Spring Marks in a manner that would imply Broadcom’s affiliation with or endorsement, sponsorship, or support of a product or service.
     10. **Rebranding.** You may not change the trademark on unmodified Spring software to your own brand. You may not hold yourself out as the source of the Spring software, except to the extent you have modified it as allowed under the Apache 2.0 license, and you make it clear that you are the source only of the modification.
     11. **Combination Marks.** Do not use the Spring Marks in combination with any other marks or logos (for example Foobar Spring, or the name of your company or product typeset to look like the Spring logo).
     12. **Web Tags.** Do not use the Spring Marks in a title or meta tag of a web page to influence search engine rankings or result listings, rather than for discussion or advocacy of the Spring project.
  6. **PROPER NOTICE AND ATTRIBUTION.** The appropriate trademark symbol (i.e., ®) should appear at least with the first use of the Spring trademarks (for example, SPRING®). When you use a Spring trademark you should include a statement attributing the trademark to Broadcom. For example, "Spring is a trademark of Broadcom Inc. and/or its subsidiaries."

  7. **MORE QUESTIONS?** If you have questions about this policy, please contact us at [<EMAIL>](mailto:<EMAIL>).
---
