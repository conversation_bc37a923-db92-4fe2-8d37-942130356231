Title: Spring | Reactive
Source: https://spring.io/reactive
HTML: html/spring_io/Spring_Reactive.html
Screenshot: screenshot/spring_io/Spring_Reactive.png
crawled_at: 2025-06-04T15:50:35.871277
---
# Reactive

Reactive systems have certain characteristics that make them ideal for low-latency, high-throughput workloads. Project Reactor and the Spring portfolio work together to enable developers to build enterprise-grade reactive systems that are responsive, resilient, elastic, and message-driven. 

### What is reactive processing?

Reactive processing is a paradigm that enables developers build non-blocking, asynchronous applications that can handle back-pressure (flow control).

### Why use reactive processing?

Reactive systems better utilize modern processors. Also, the inclusion of back-pressure in reactive programming ensures better resilience between decoupled components.

![](/img/extra/reactive-2.svg)![](/img/extra/reactive-2-dark.svg)
---
