Title: Spring | Event Driven
Source: https://spring.io/event-driven
HTML: html/spring_io/Spring_Event_Driven.html
Screenshot: screenshot/spring_io/Spring_Event_Driven.png
crawled_at: 2025-06-04T15:50:38.385781
---
# Event Driven

Event-driven systems reflect how modern businesses actually work–thousands of small changes happening all day, every day. Spring’s ability to handle events and enable developers to build applications around them, means your apps will stay in sync with your business. Spring has a number of event-driven options to choose from, from integration and streaming all the way to cloud functions and data flows. 

### Event-driven microservices

When combined with microservices, event streaming opens up exciting opportunities—event-driven architecture being one common example. Spring simplifies the production, processing, and consumption of events, providing several useful abstractions.

### Streaming data

Streaming data represents a constant flow of events. One example might be a stock ticker. Every time a stock price changes, a new event is created. It’s called “streaming data” because there are thousands of these events resulting in a constant stream of data.

### Integration

The bedrock of any event-driven system is message handling. Connecting to message platforms, routing messages, transforming messages, processing messages. With Spring you can solve these integration challenges quickly.

![](/img/extra/event-driven-1.svg)
---
