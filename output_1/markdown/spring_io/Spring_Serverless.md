Title: Spring | Serverless
Source: https://spring.io/serverless
HTML: html/spring_io/Spring_Serverless.html
Screenshot: screenshot/spring_io/Spring_Serverless.png
crawled_at: 2025-06-04T15:49:40.462542
---
# Serverless

Serverless applications take advantage of modern cloud computing capabilities and abstractions to let you focus on logic rather than on infrastructure. In a serverless environment, you can concentrate on writing application code while the underlying platform takes care of scaling, runtimes, resource allocation, security, and other “server” specifics. 

### What is serverless?

Serverless workloads are “event-driven workloads that aren’t concerned with aspects normally handled by server infrastructure.” Concerns like “how many instances to run” and “what operating system to use” are all managed by a Function as a Service platform (or FaaS), leaving developers free to focus on business logic.

### Serverless characteristics?

Serverless applications have a number of specific characteristics, including:

  * Event-driven code execution with triggers
  * Platform handles all the starting, stopping, and scaling chores
  * Scales to zero, with low to no cost when idle
  * Stateless



![](/img/extra/serverless-4.svg)![](/img/extra/serverless-4-dark.svg)
---
