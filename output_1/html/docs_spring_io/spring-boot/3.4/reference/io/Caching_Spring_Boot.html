<!DOCTYPE html>
<html><head><title>Caching :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/reference/io/caching.html"/><meta content="2025-06-04T15:40:58.057071" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">IO</a>
<ul class="nav-list">
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Caching">
<div class="toc-menu"><h3>Caching</h3><ul><li data-level="1"><a href="#io.caching.provider">Supported Cache Providers</a></li><li data-level="2"><a href="#io.caching.provider.generic">Generic</a></li><li data-level="2"><a href="#io.caching.provider.jcache">JCache (JSR-107)</a></li><li data-level="2"><a href="#io.caching.provider.hazelcast">Hazelcast</a></li><li data-level="2"><a href="#io.caching.provider.infinispan">Infinispan</a></li><li data-level="2"><a href="#io.caching.provider.couchbase">Couchbase</a></li><li data-level="2"><a href="#io.caching.provider.redis">Redis</a></li><li data-level="2"><a href="#io.caching.provider.caffeine">Caffeine</a></li><li data-level="2"><a href="#io.caching.provider.cache2k">Cache2k</a></li><li data-level="2"><a href="#io.caching.provider.simple">Simple</a></li><li data-level="2"><a href="#io.caching.provider.none">None</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/io/caching.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../index.html">Spring Boot</a></li>
<li><a href="../index.html">Reference</a></li>
<li><a href="index.html">IO</a></li>
<li><a href="caching.html">Caching</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../reference/io/caching.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Caching</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Caching</h3><ul><li data-level="1"><a href="#io.caching.provider">Supported Cache Providers</a></li><li data-level="2"><a href="#io.caching.provider.generic">Generic</a></li><li data-level="2"><a href="#io.caching.provider.jcache">JCache (JSR-107)</a></li><li data-level="2"><a href="#io.caching.provider.hazelcast">Hazelcast</a></li><li data-level="2"><a href="#io.caching.provider.infinispan">Infinispan</a></li><li data-level="2"><a href="#io.caching.provider.couchbase">Couchbase</a></li><li data-level="2"><a href="#io.caching.provider.redis">Redis</a></li><li data-level="2"><a href="#io.caching.provider.caffeine">Caffeine</a></li><li data-level="2"><a href="#io.caching.provider.cache2k">Cache2k</a></li><li data-level="2"><a href="#io.caching.provider.simple">Simple</a></li><li data-level="2"><a href="#io.caching.provider.none">None</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>The Spring Framework provides support for transparently adding caching to an application.
At its core, the abstraction applies caching to methods, thus reducing the number of executions based on the information available in the cache.
The caching logic is applied transparently, without any interference to the invoker.
Spring Boot auto-configures the cache infrastructure as long as caching support is enabled by using the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/annotation/EnableCaching.html"><code>@EnableCaching</code></a> annotation.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Check the <a href="https://docs.spring.io/spring-framework/reference/6.2/integration/cache.html">relevant section</a> of the Spring Framework reference for more details.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>In a nutshell, to add caching to an operation of your service add the relevant annotation to its method, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_1_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_1_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_1_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_java" class="tabpanel" id="_tabs_1_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.cache.annotation.Cacheable;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMathService</span> </span>{

	<span class="hljs-meta">@Cacheable</span>(<span class="hljs-string">"piDecimals"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">int</span> <span class="hljs-title">computePiDecimal</span><span class="hljs-params">(<span class="hljs-keyword">int</span> precision)</span> </span>{
		...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_1_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.cache.<span class="hljs-keyword">annotation</span>.Cacheable
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMathService</span> </span>{

	<span class="hljs-meta">@Cacheable(<span class="hljs-meta-string">"piDecimals"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">computePiDecimal</span><span class="hljs-params">(precision: <span class="hljs-type">Int</span>)</span></span>: <span class="hljs-built_in">Int</span> {
		...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>This example demonstrates the use of caching on a potentially costly operation.
Before invoking <code>computePiDecimal</code>, the abstraction looks for an entry in the <code>piDecimals</code> cache that matches the <code>precision</code> argument.
If an entry is found, the content in the cache is immediately returned to the caller, and the method is not invoked.
Otherwise, the method is invoked, and the cache is updated before returning the value.</p>
</div>
<div class="admonitionblock caution">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-caution" title="Caution"></i>
</td>
<td class="content">
You can also use the standard JSR-107 (JCache) annotations (such as <a class="apiref external" href="https://javadoc.io/doc/javax.cache/cache-api/1.1.1/javax/cache/annotation/CacheResult.html" target="_blank"><code>@CacheResult</code></a>) transparently.
However, we strongly advise you to not mix and match the Spring Cache and JCache annotations.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If you do not add any specific cache library, Spring Boot auto-configures a <a href="#io.caching.provider.simple">simple provider</a> that uses concurrent maps in memory.
When a cache is required (such as <code>piDecimals</code> in the preceding example), this provider creates it for you.
The simple provider is not really recommended for production usage, but it is great for getting started and making sure that you understand the features.
When you have made up your mind about the cache provider to use, please make sure to read its documentation to figure out how to configure the caches that your application uses.
Nearly all providers require you to explicitly configure every cache that you use in the application.
Some offer a way to customize the default caches defined by the <code>spring.cache.cache-names</code> property.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
It is also possible to transparently <a href="https://docs.spring.io/spring-framework/reference/6.2/integration/cache/annotations.html#cache-annotations-put">update</a> or <a href="https://docs.spring.io/spring-framework/reference/6.2/integration/cache/annotations.html#cache-annotations-evict">evict</a> data from the cache.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="io.caching.provider"><a class="anchor" href="#io.caching.provider"></a>Supported Cache Providers</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The cache abstraction does not provide an actual store and relies on abstraction materialized by the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/Cache.html"><code>Cache</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/CacheManager.html"><code>CacheManager</code></a> interfaces.</p>
</div>
<div class="paragraph">
<p>If you have not defined a bean of type <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/CacheManager.html"><code>CacheManager</code></a> or a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/interceptor/CacheResolver.html"><code>CacheResolver</code></a> named <code>cacheResolver</code> (see <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/annotation/CachingConfigurer.html"><code>CachingConfigurer</code></a>), Spring Boot tries to detect the following providers (in the indicated order):</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p><a href="#io.caching.provider.generic">Generic</a></p>
</li>
<li>
<p><a href="#io.caching.provider.jcache">JCache (JSR-107)</a> (EhCache 3, Hazelcast, Infinispan, and others)</p>
</li>
<li>
<p><a href="#io.caching.provider.hazelcast">Hazelcast</a></p>
</li>
<li>
<p><a href="#io.caching.provider.infinispan">Infinispan</a></p>
</li>
<li>
<p><a href="#io.caching.provider.couchbase">Couchbase</a></p>
</li>
<li>
<p><a href="#io.caching.provider.redis">Redis</a></p>
</li>
<li>
<p><a href="#io.caching.provider.caffeine">Caffeine</a></p>
</li>
<li>
<p><a href="#io.caching.provider.cache2k">Cache2k</a></p>
</li>
<li>
<p><a href="#io.caching.provider.simple">Simple</a></p>
</li>
</ol>
</div>
<div class="paragraph">
<p>Additionally, <a class="external" href="https://github.com/spring-projects/spring-boot-data-geode" target="_blank">Spring Boot for Apache Geode</a> provides <a href="https://docs.spring.io/spring-boot-data-geode-build/2.0.x/reference/html5#geode-caching-provider">auto-configuration for using Apache Geode as a cache provider</a>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/CacheManager.html"><code>CacheManager</code></a> is auto-configured by Spring Boot, it is possible to <em>force</em> a particular cache provider by setting the <code>spring.cache.type</code> property.
Use this property if you need to <a href="#io.caching.provider.none">use no-op caches</a> in certain environments (such as tests).
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Use the <code>spring-boot-starter-cache</code> starter to quickly add basic caching dependencies.
The starter brings in <code>spring-context-support</code>.
If you add dependencies manually, you must include <code>spring-context-support</code> in order to use the JCache or Caffeine support.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/CacheManager.html"><code>CacheManager</code></a> is auto-configured by Spring Boot, you can further tune its configuration before it is fully initialized by exposing a bean that implements the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/cache/CacheManagerCustomizer.html"><code>CacheManagerCustomizer</code></a> interface.
The following example sets a flag to say that <code>null</code> values should not be passed down to the underlying map:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_2_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_2_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_2_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_java" class="tabpanel" id="_tabs_2_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.cache.CacheManagerCustomizer;
<span class="hljs-keyword">import</span> org.springframework.cache.concurrent.ConcurrentMapCacheManager;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCacheManagerConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> CacheManagerCustomizer&lt;ConcurrentMapCacheManager&gt; <span class="hljs-title">cacheManagerCustomizer</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> (cacheManager) -&gt; cacheManager.setAllowNullValues(<span class="hljs-keyword">false</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_2_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.cache.CacheManagerCustomizer
<span class="hljs-keyword">import</span> org.springframework.cache.concurrent.ConcurrentMapCacheManager
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCacheManagerConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">cacheManagerCustomizer</span><span class="hljs-params">()</span></span>: CacheManagerCustomizer&lt;ConcurrentMapCacheManager&gt; {
		<span class="hljs-keyword">return</span> CacheManagerCustomizer { cacheManager -&gt;
			cacheManager.isAllowNullValues = <span class="hljs-literal">false</span>
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
In the preceding example, an auto-configured <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/concurrent/ConcurrentMapCacheManager.html"><code>ConcurrentMapCacheManager</code></a> is expected.
If that is not the case (either you provided your own config or a different cache provider was auto-configured), the customizer is not invoked at all.
You can have as many customizers as you want, and you can also order them by using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/annotation/Order.html"><code>@Order</code></a> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/Ordered.html"><code>Ordered</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="io.caching.provider.generic"><a class="anchor" href="#io.caching.provider.generic"></a>Generic</h3>
<div class="paragraph">
<p>Generic caching is used if the context defines <em>at least</em> one <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/Cache.html"><code>Cache</code></a> bean.
A <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/CacheManager.html"><code>CacheManager</code></a> wrapping all beans of that type is created.</p>
</div>
</div>
<div class="sect2">
<h3 id="io.caching.provider.jcache"><a class="anchor" href="#io.caching.provider.jcache"></a>JCache (JSR-107)</h3>
<div class="paragraph">
<p><a class="external" href="https://jcp.org/en/jsr/detail?id=107" target="_blank">JCache</a> is bootstrapped through the presence of a <a class="apiref external" href="https://javadoc.io/doc/javax.cache/cache-api/1.1.1/javax/cache/spi/CachingProvider.html" target="_blank"><code>CachingProvider</code></a> on the classpath (that is, a JSR-107 compliant caching library exists on the classpath), and the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/jcache/JCacheCacheManager.html"><code>JCacheCacheManager</code></a> is provided by the <code>spring-boot-starter-cache</code> starter.
Various compliant libraries are available, and Spring Boot provides dependency management for Ehcache 3, Hazelcast, and Infinispan.
Any other compliant library can be added as well.</p>
</div>
<div class="paragraph">
<p>It might happen that more than one provider is present, in which case the provider must be explicitly specified.
Even if the JSR-107 standard does not enforce a standardized way to define the location of the configuration file, Spring Boot does its best to accommodate setting a cache with implementation details, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_3_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_3_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_3_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_properties" class="tabpanel" id="_tabs_3_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.cache.jcache.provider</span>=<span class="hljs-string">com.example.MyCachingProvider</span>
<span class="hljs-meta">spring.cache.jcache.config</span>=<span class="hljs-string">classpath:example.xml</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_3_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-comment"># Only necessary if more than one provider is present</span>
<span class="hljs-attr">spring:</span>
  <span class="hljs-attr">cache:</span>
    <span class="hljs-attr">jcache:</span>
      <span class="hljs-attr">provider:</span> <span class="hljs-string">"com.example.MyCachingProvider"</span>
      <span class="hljs-attr">config:</span> <span class="hljs-string">"classpath:example.xml"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
When a cache library offers both a native implementation and JSR-107 support, Spring Boot prefers the JSR-107 support, so that the same features are available if you switch to a different JSR-107 implementation.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Spring Boot has <a class="xref page" href="hazelcast.html">general support for Hazelcast</a>.
If a single <a class="apiref external" href="https://docs.hazelcast.org/docs/5.5.0/javadoc/com/hazelcast/core/HazelcastInstance.html" target="_blank"><code>HazelcastInstance</code></a> is available, it is automatically reused for the <a class="apiref external" href="https://javadoc.io/doc/javax.cache/cache-api/1.1.1/javax/cache/CacheManager.html" target="_blank"><code>CacheManager</code></a> as well, unless the <code>spring.cache.jcache.config</code> property is specified.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>There are two ways to customize the underlying <a class="apiref external" href="https://javadoc.io/doc/javax.cache/cache-api/1.1.1/javax/cache/CacheManager.html" target="_blank"><code>CacheManager</code></a>:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Caches can be created on startup by setting the <code>spring.cache.cache-names</code> property.
If a custom <a class="apiref external" href="https://javadoc.io/doc/javax.cache/cache-api/1.1.1/javax/cache/configuration/Configuration.html" target="_blank"><code>Configuration</code></a> bean is defined, it is used to customize them.</p>
</li>
<li>
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/cache/JCacheManagerCustomizer.html"><code>JCacheManagerCustomizer</code></a> beans are invoked with the reference of the <a class="apiref external" href="https://javadoc.io/doc/javax.cache/cache-api/1.1.1/javax/cache/CacheManager.html" target="_blank"><code>CacheManager</code></a> for full customization.</p>
</li>
</ul>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If a standard <a class="apiref external" href="https://javadoc.io/doc/javax.cache/cache-api/1.1.1/javax/cache/CacheManager.html" target="_blank"><code>CacheManager</code></a> bean is defined, it is wrapped automatically in an <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/CacheManager.html"><code>CacheManager</code></a> implementation that the abstraction expects.
No further customization is applied to it.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="io.caching.provider.hazelcast"><a class="anchor" href="#io.caching.provider.hazelcast"></a>Hazelcast</h3>
<div class="paragraph">
<p>Spring Boot has <a class="xref page" href="hazelcast.html">general support for Hazelcast</a>.
If a <a class="apiref external" href="https://docs.hazelcast.org/docs/5.5.0/javadoc/com/hazelcast/core/HazelcastInstance.html" target="_blank"><code>HazelcastInstance</code></a> has been auto-configured and <code>com.hazelcast:hazelcast-spring</code> is on the classpath, it is automatically wrapped in a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/CacheManager.html"><code>CacheManager</code></a>.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Hazelcast can be used as a JCache compliant cache or as a Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/CacheManager.html"><code>CacheManager</code></a> compliant cache.
When setting <code>spring.cache.type</code> to <code>hazelcast</code>, Spring Boot will use the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/CacheManager.html"><code>CacheManager</code></a> based implementation.
If you want to use Hazelcast as a JCache compliant cache, set <code>spring.cache.type</code> to <code>jcache</code>.
If you have multiple JCache compliant cache providers and want to force the use of Hazelcast, you have to <a href="#io.caching.provider.jcache">explicitly set the JCache provider</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="io.caching.provider.infinispan"><a class="anchor" href="#io.caching.provider.infinispan"></a>Infinispan</h3>
<div class="paragraph">
<p><a class="external" href="https://infinispan.org/" target="_blank">Infinispan</a> has no default configuration file location, so it must be specified explicitly.
Otherwise, the default bootstrap is used.</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_4_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_4_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_4_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_properties" class="tabpanel" id="_tabs_4_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.cache.infinispan.config</span>=<span class="hljs-string">infinispan.xml</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_4_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">cache:</span>
    <span class="hljs-attr">infinispan:</span>
      <span class="hljs-attr">config:</span> <span class="hljs-string">"infinispan.xml"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Caches can be created on startup by setting the <code>spring.cache.cache-names</code> property.
If a custom <a class="apiref external" href="https://docs.jboss.org/infinispan/15.0/apidocs/org/infinispan/configuration/cache/ConfigurationBuilder.html" target="_blank"><code>ConfigurationBuilder</code></a> bean is defined, it is used to customize the caches.</p>
</div>
<div class="paragraph">
<p>To be compatible with Spring Boot’s Jakarta EE 9 baseline, Infinispan’s <code>-jakarta</code> modules must be used.
For every module with a <code>-jakarta</code> variant, the variant must be used in place of the standard module.
For example, <code>infinispan-core-jakarta</code> and <code>infinispan-commons-jakarta</code> must be used in place of <code>infinispan-core</code> and <code>infinispan-commons</code> respectively.</p>
</div>
</div>
<div class="sect2">
<h3 id="io.caching.provider.couchbase"><a class="anchor" href="#io.caching.provider.couchbase"></a>Couchbase</h3>
<div class="paragraph">
<p>If Spring Data Couchbase is available and Couchbase is <a class="xref page" href="../data/nosql.html#data.nosql.couchbase">configured</a>, a <a class="apiref" href="https://docs.spring.io/spring-data/couchbase/docs/5.4.x/api/org/springframework/data/couchbase/cache/CouchbaseCacheManager.html"><code>CouchbaseCacheManager</code></a> is auto-configured.
It is possible to create additional caches on startup by setting the <code>spring.cache.cache-names</code> property and cache defaults can be configured by using <code>spring.cache.couchbase.*</code> properties.
For instance, the following configuration creates <code>cache1</code> and <code>cache2</code> caches with an entry <em>expiration</em> of 10 minutes:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_5_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_5_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_5_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_properties" class="tabpanel" id="_tabs_5_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.cache.cache-names</span>=<span class="hljs-string">cache1,cache2</span>
<span class="hljs-meta">spring.cache.couchbase.expiration</span>=<span class="hljs-string">10m</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_5_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">cache:</span>
    <span class="hljs-attr">cache-names:</span> <span class="hljs-string">"cache1,cache2"</span>
    <span class="hljs-attr">couchbase:</span>
      <span class="hljs-attr">expiration:</span> <span class="hljs-string">"10m"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you need more control over the configuration, consider registering a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/cache/CouchbaseCacheManagerBuilderCustomizer.html"><code>CouchbaseCacheManagerBuilderCustomizer</code></a> bean.
The following example shows a customizer that configures a specific entry expiration for <code>cache1</code> and <code>cache2</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_6_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_6_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_6_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_java" class="tabpanel" id="_tabs_6_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.time.Duration;

<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.cache.CouchbaseCacheManagerBuilderCustomizer;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.data.couchbase.cache.CouchbaseCacheConfiguration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCouchbaseCacheManagerConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> CouchbaseCacheManagerBuilderCustomizer <span class="hljs-title">myCouchbaseCacheManagerBuilderCustomizer</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> (builder) -&gt; builder
				.withCacheConfiguration(<span class="hljs-string">"cache1"</span>, CouchbaseCacheConfiguration
						.defaultCacheConfig().entryExpiry(Duration.ofSeconds(<span class="hljs-number">10</span>)))
				.withCacheConfiguration(<span class="hljs-string">"cache2"</span>, CouchbaseCacheConfiguration
						.defaultCacheConfig().entryExpiry(Duration.ofMinutes(<span class="hljs-number">1</span>)));

	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_6_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.cache.CouchbaseCacheManagerBuilderCustomizer
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.couchbase.cache.CouchbaseCacheConfiguration
<span class="hljs-keyword">import</span> java.time.Duration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCouchbaseCacheManagerConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">myCouchbaseCacheManagerBuilderCustomizer</span><span class="hljs-params">()</span></span>: CouchbaseCacheManagerBuilderCustomizer {
		<span class="hljs-keyword">return</span> CouchbaseCacheManagerBuilderCustomizer { builder -&gt;
			builder
				.withCacheConfiguration(
					<span class="hljs-string">"cache1"</span>, CouchbaseCacheConfiguration
						.defaultCacheConfig().entryExpiry(Duration.ofSeconds(<span class="hljs-number">10</span>))
				)
				.withCacheConfiguration(
					<span class="hljs-string">"cache2"</span>, CouchbaseCacheConfiguration
						.defaultCacheConfig().entryExpiry(Duration.ofMinutes(<span class="hljs-number">1</span>))
				)
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="io.caching.provider.redis"><a class="anchor" href="#io.caching.provider.redis"></a>Redis</h3>
<div class="paragraph">
<p>If <a class="external" href="https://redis.io/" target="_blank">Redis</a> is available and configured, a <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/cache/RedisCacheManager.html"><code>RedisCacheManager</code></a> is auto-configured.
It is possible to create additional caches on startup by setting the <code>spring.cache.cache-names</code> property and cache defaults can be configured by using <code>spring.cache.redis.*</code> properties.
For instance, the following configuration creates <code>cache1</code> and <code>cache2</code> caches with a <em>time to live</em> of 10 minutes:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_7_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_7_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_7_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_properties" class="tabpanel" id="_tabs_7_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.cache.cache-names</span>=<span class="hljs-string">cache1,cache2</span>
<span class="hljs-meta">spring.cache.redis.time-to-live</span>=<span class="hljs-string">10m</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_7_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">cache:</span>
    <span class="hljs-attr">cache-names:</span> <span class="hljs-string">"cache1,cache2"</span>
    <span class="hljs-attr">redis:</span>
      <span class="hljs-attr">time-to-live:</span> <span class="hljs-string">"10m"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
By default, a key prefix is added so that, if two separate caches use the same key, Redis does not have overlapping keys and cannot return invalid values.
We strongly recommend keeping this setting enabled if you create your own <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/cache/RedisCacheManager.html"><code>RedisCacheManager</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can take full control of the default configuration by adding a <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/cache/RedisCacheConfiguration.html"><code>RedisCacheConfiguration</code></a> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> of your own.
This can be useful if you need to customize the default serialization strategy.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If you need more control over the configuration, consider registering a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/cache/RedisCacheManagerBuilderCustomizer.html"><code>RedisCacheManagerBuilderCustomizer</code></a> bean.
The following example shows a customizer that configures a specific time to live for <code>cache1</code> and <code>cache2</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_8_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_8_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_8_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_java" class="tabpanel" id="_tabs_8_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.time.Duration;

<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.cache.RedisCacheManagerBuilderCustomizer;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.data.redis.cache.RedisCacheConfiguration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRedisCacheManagerConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> RedisCacheManagerBuilderCustomizer <span class="hljs-title">myRedisCacheManagerBuilderCustomizer</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> (builder) -&gt; builder
				.withCacheConfiguration(<span class="hljs-string">"cache1"</span>, RedisCacheConfiguration
						.defaultCacheConfig().entryTtl(Duration.ofSeconds(<span class="hljs-number">10</span>)))
				.withCacheConfiguration(<span class="hljs-string">"cache2"</span>, RedisCacheConfiguration
						.defaultCacheConfig().entryTtl(Duration.ofMinutes(<span class="hljs-number">1</span>)));

	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_8_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.cache.RedisCacheManagerBuilderCustomizer
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.redis.cache.RedisCacheConfiguration
<span class="hljs-keyword">import</span> java.time.Duration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRedisCacheManagerConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">myRedisCacheManagerBuilderCustomizer</span><span class="hljs-params">()</span></span>: RedisCacheManagerBuilderCustomizer {
		<span class="hljs-keyword">return</span> RedisCacheManagerBuilderCustomizer { builder -&gt;
			builder
				.withCacheConfiguration(
					<span class="hljs-string">"cache1"</span>, RedisCacheConfiguration
						.defaultCacheConfig().entryTtl(Duration.ofSeconds(<span class="hljs-number">10</span>))
				)
				.withCacheConfiguration(
					<span class="hljs-string">"cache2"</span>, RedisCacheConfiguration
						.defaultCacheConfig().entryTtl(Duration.ofMinutes(<span class="hljs-number">1</span>))
				)
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="io.caching.provider.caffeine"><a class="anchor" href="#io.caching.provider.caffeine"></a>Caffeine</h3>
<div class="paragraph">
<p><a class="external" href="https://github.com/ben-manes/caffeine" target="_blank">Caffeine</a> is a Java 8 rewrite of Guava’s cache that supersedes support for Guava.
If Caffeine is present, a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/caffeine/CaffeineCacheManager.html"><code>CaffeineCacheManager</code></a> (provided by the <code>spring-boot-starter-cache</code> starter) is auto-configured.
Caches can be created on startup by setting the <code>spring.cache.cache-names</code> property and can be customized by one of the following (in the indicated order):</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>A cache spec defined by <code>spring.cache.caffeine.spec</code></p>
</li>
<li>
<p>A <a class="apiref external" href="https://javadoc.io/doc/com.github.ben-manes.caffeine/caffeine/3.1.8/com/github/benmanes/caffeine/cache/CaffeineSpec.html" target="_blank"><code>CaffeineSpec</code></a> bean is defined</p>
</li>
<li>
<p>A <a class="apiref external" href="https://javadoc.io/doc/com.github.ben-manes.caffeine/caffeine/3.1.8/com/github/benmanes/caffeine/cache/Caffeine.html" target="_blank"><code>Caffeine</code></a> bean is defined</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>For instance, the following configuration creates <code>cache1</code> and <code>cache2</code> caches with a maximum size of 500 and a <em>time to live</em> of 10 minutes</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_9">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_9_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_9_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_9_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_9_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_9_properties" class="tabpanel" id="_tabs_9_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.cache.cache-names</span>=<span class="hljs-string">cache1,cache2</span>
<span class="hljs-meta">spring.cache.caffeine.spec</span>=<span class="hljs-string">maximumSize=500,expireAfterAccess=600s</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_9_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_9_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">cache:</span>
    <span class="hljs-attr">cache-names:</span> <span class="hljs-string">"cache1,cache2"</span>
    <span class="hljs-attr">caffeine:</span>
      <span class="hljs-attr">spec:</span> <span class="hljs-string">"maximumSize=500,expireAfterAccess=600s"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If a <a class="apiref external" href="https://javadoc.io/doc/com.github.ben-manes.caffeine/caffeine/3.1.8/com/github/benmanes/caffeine/cache/CacheLoader.html" target="_blank"><code>CacheLoader</code></a> bean is defined, it is automatically associated to the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/caffeine/CaffeineCacheManager.html"><code>CaffeineCacheManager</code></a>.
Since the <a class="apiref external" href="https://javadoc.io/doc/com.github.ben-manes.caffeine/caffeine/3.1.8/com/github/benmanes/caffeine/cache/CacheLoader.html" target="_blank"><code>CacheLoader</code></a> is going to be associated with <em>all</em> caches managed by the cache manager, it must be defined as <code>CacheLoader&lt;Object, Object&gt;</code>.
The auto-configuration ignores any other generic type.</p>
</div>
</div>
<div class="sect2">
<h3 id="io.caching.provider.cache2k"><a class="anchor" href="#io.caching.provider.cache2k"></a>Cache2k</h3>
<div class="paragraph">
<p><a class="external" href="https://cache2k.org/" target="_blank">Cache2k</a> is an in-memory cache.
If the Cache2k spring integration is present, a <code>SpringCache2kCacheManager</code> is auto-configured.</p>
</div>
<div class="paragraph">
<p>Caches can be created on startup by setting the <code>spring.cache.cache-names</code> property.
Cache defaults can be customized using a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/cache/Cache2kBuilderCustomizer.html"><code>Cache2kBuilderCustomizer</code></a> bean.
The following example shows a customizer that configures the capacity of the cache to 200 entries, with an expiration of 5 minutes:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_10">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_10_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_10_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_10_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_10_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_10_java" class="tabpanel" id="_tabs_10_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.concurrent.TimeUnit;

<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.cache.Cache2kBuilderCustomizer;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCache2kDefaultsConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> Cache2kBuilderCustomizer <span class="hljs-title">myCache2kDefaultsCustomizer</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> (builder) -&gt; builder.entryCapacity(<span class="hljs-number">200</span>)
				.expireAfterWrite(<span class="hljs-number">5</span>, TimeUnit.MINUTES);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_10_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_10_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.cache.Cache2kBuilderCustomizer
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> java.util.concurrent.TimeUnit

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCache2kDefaultsConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">myCache2kDefaultsCustomizer</span><span class="hljs-params">()</span></span>: Cache2kBuilderCustomizer {
		<span class="hljs-keyword">return</span> Cache2kBuilderCustomizer { builder -&gt;
			builder.entryCapacity(<span class="hljs-number">200</span>)
				.expireAfterWrite(<span class="hljs-number">5</span>, TimeUnit.MINUTES)
		}
	}
}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="io.caching.provider.simple"><a class="anchor" href="#io.caching.provider.simple"></a>Simple</h3>
<div class="paragraph">
<p>If none of the other providers can be found, a simple implementation using a <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/ConcurrentHashMap.html" target="_blank"><code>ConcurrentHashMap</code></a> as the cache store is configured.
This is the default if no caching library is present in your application.
By default, caches are created as needed, but you can restrict the list of available caches by setting the <code>cache-names</code> property.
For instance, if you want only <code>cache1</code> and <code>cache2</code> caches, set the <code>cache-names</code> property as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_11">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_11_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_11_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_11_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_11_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_11_properties" class="tabpanel" id="_tabs_11_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.cache.cache-names</span>=<span class="hljs-string">cache1,cache2</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_11_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_11_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">cache:</span>
    <span class="hljs-attr">cache-names:</span> <span class="hljs-string">"cache1,cache2"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you do so and your application uses a cache not listed, then it fails at runtime when the cache is needed, but not on startup.
This is similar to the way the "real" cache providers behave if you use an undeclared cache.</p>
</div>
</div>
<div class="sect2">
<h3 id="io.caching.provider.none"><a class="anchor" href="#io.caching.provider.none"></a>None</h3>
<div class="paragraph">
<p>When <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/annotation/EnableCaching.html"><code>@EnableCaching</code></a> is present in your configuration, a suitable cache configuration is expected as well.
If you have a custom ` org.springframework.cache.CacheManager`, consider defining it in a separate <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> class so that you can override it if necessary.
None uses a no-op implementation that is useful in tests, and slice tests use that by default via <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/core/AutoConfigureCache.html"><code>@AutoConfigureCache</code></a>.</p>
</div>
<div class="paragraph">
<p>If you need to use a no-op cache rather than the auto-configured cache manager in a certain environment, set the cache type to <code>none</code>, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_12">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_12_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_12_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_12_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_12_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_12_properties" class="tabpanel" id="_tabs_12_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.cache.type</span>=<span class="hljs-string">none</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_12_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_12_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">cache:</span>
    <span class="hljs-attr">type:</span> <span class="hljs-string">"none"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="index.html">IO</a></span>
<span class="next"><a href="hazelcast.html">Hazelcast</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../reference/io/caching.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="caching.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../3.3/reference/io/caching.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../4.0-SNAPSHOT/reference/io/caching.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.5-SNAPSHOT/reference/io/caching.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.4-SNAPSHOT/reference/io/caching.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.3-SNAPSHOT/reference/io/caching.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>