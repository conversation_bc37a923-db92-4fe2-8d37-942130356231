<!DOCTYPE html>
<html><head><title>Calling REST Services :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/reference/io/rest-client.html"/><meta content="2025-06-04T15:38:32.616720" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="validation.html">Validation</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Calling REST Services">
<div class="toc-menu"><h3>Calling REST Services</h3><ul><li data-level="1"><a href="#io.rest-client.webclient">WebClient</a></li><li data-level="2"><a href="#io.rest-client.webclient.runtime">WebClient Runtime</a></li><li data-level="2"><a href="#io.rest-client.webclient.customization">WebClient Customization</a></li><li data-level="2"><a href="#io.rest-client.webclient.ssl">WebClient SSL Support</a></li><li data-level="1"><a href="#io.rest-client.restclient">RestClient</a></li><li data-level="2"><a href="#io.rest-client.restclient.customization">RestClient Customization</a></li><li data-level="2"><a href="#io.rest-client.restclient.ssl">RestClient SSL Support</a></li><li data-level="1"><a href="#io.rest-client.resttemplate">RestTemplate</a></li><li data-level="2"><a href="#io.rest-client.resttemplate.customization">RestTemplate Customization</a></li><li data-level="2"><a href="#io.rest-client.resttemplate.ssl">RestTemplate SSL Support</a></li><li data-level="1"><a href="#io.rest-client.clienthttprequestfactory">HTTP Client Detection for RestClient and RestTemplate</a></li><li data-level="2"><a href="#io.rest-client.clienthttprequestfactory.configuration">Global HTTP Client Configuration</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/io/rest-client.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../index.html">Spring Boot</a></li>
<li><a href="../index.html">Reference</a></li>
<li><a href="index.html">IO</a></li>
<li><a href="rest-client.html">Calling REST Services</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../reference/io/rest-client.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Calling REST Services</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Calling REST Services</h3><ul><li data-level="1"><a href="#io.rest-client.webclient">WebClient</a></li><li data-level="2"><a href="#io.rest-client.webclient.runtime">WebClient Runtime</a></li><li data-level="2"><a href="#io.rest-client.webclient.customization">WebClient Customization</a></li><li data-level="2"><a href="#io.rest-client.webclient.ssl">WebClient SSL Support</a></li><li data-level="1"><a href="#io.rest-client.restclient">RestClient</a></li><li data-level="2"><a href="#io.rest-client.restclient.customization">RestClient Customization</a></li><li data-level="2"><a href="#io.rest-client.restclient.ssl">RestClient SSL Support</a></li><li data-level="1"><a href="#io.rest-client.resttemplate">RestTemplate</a></li><li data-level="2"><a href="#io.rest-client.resttemplate.customization">RestTemplate Customization</a></li><li data-level="2"><a href="#io.rest-client.resttemplate.ssl">RestTemplate SSL Support</a></li><li data-level="1"><a href="#io.rest-client.clienthttprequestfactory">HTTP Client Detection for RestClient and RestTemplate</a></li><li data-level="2"><a href="#io.rest-client.clienthttprequestfactory.configuration">Global HTTP Client Configuration</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot provides various convenient ways to call remote REST services.
If you are developing a non-blocking reactive application and you’re using Spring WebFlux, then you can use <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a>.
If you prefer blocking APIs then you can use <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="io.rest-client.webclient"><a class="anchor" href="#io.rest-client.webclient"></a>WebClient</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you have Spring WebFlux on your classpath we recommend that you use <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a> to call remote REST services.
The <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a> interface provides a functional style API and is fully reactive.
You can learn more about the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a> in the dedicated <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webflux-webclient.html">section in the Spring Framework docs</a>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you are not writing a reactive Spring WebFlux application you can use the <a href="#io.rest-client.restclient"><code>RestClient</code></a> instead of a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a>.
This provides a similar functional API, but is blocking rather than reactive.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Spring Boot creates and pre-configures a prototype <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.Builder.html"><code>WebClient.Builder</code></a> bean for you.
It is strongly advised to inject it in your components and use it to create <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a> instances.
Spring Boot is configuring that builder to share HTTP resources and reflect codecs setup in the same fashion as the server ones (see <a class="xref page" href="../web/reactive.html#web.reactive.webflux.httpcodecs">WebFlux HTTP codecs auto-configuration</a>), and more.</p>
</div>
<div class="paragraph">
<p>The following code shows a typical example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_1_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_1_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_1_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_java" class="tabpanel" id="_tabs_1_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> reactor.core.publisher.Mono;

<span class="hljs-keyword">import</span> org.springframework.stereotype.Service;
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.client.WebClient;

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> WebClient webClient;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyService</span><span class="hljs-params">(WebClient.Builder webClientBuilder)</span> </span>{
		<span class="hljs-keyword">this</span>.webClient = webClientBuilder.baseUrl(<span class="hljs-string">"https://example.org"</span>).build();
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Mono&lt;Details&gt; <span class="hljs-title">someRestCall</span><span class="hljs-params">(String name)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.webClient.get().uri(<span class="hljs-string">"/{name}/details"</span>, name).retrieve().bodyToMono(Details<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_1_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.stereotype.Service
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.client.WebClient
<span class="hljs-keyword">import</span> reactor.core.publisher.Mono

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span></span>(webClientBuilder: WebClient.Builder) {

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> webClient: WebClient

	<span class="hljs-keyword">init</span> {
		webClient = webClientBuilder.baseUrl(<span class="hljs-string">"https://example.org"</span>).build()
	}

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someRestCall</span><span class="hljs-params">(name: <span class="hljs-type">String</span>?)</span></span>: Mono&lt;Details&gt; {
		<span class="hljs-keyword">return</span> webClient.<span class="hljs-keyword">get</span>().uri(<span class="hljs-string">"/{name}/details"</span>, name)
				.retrieve().bodyToMono(Details::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>)</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="io.rest-client.webclient.runtime"><a class="anchor" href="#io.rest-client.webclient.runtime"></a>WebClient Runtime</h3>
<div class="paragraph">
<p>Spring Boot will auto-detect which <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/reactive/ClientHttpConnector.html"><code>ClientHttpConnector</code></a> to use to drive <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a> depending on the libraries available on the application classpath.
In order of preference, the following clients are supported:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Reactor Netty</p>
</li>
<li>
<p>Jetty RS client</p>
</li>
<li>
<p>Apache HttpClient</p>
</li>
<li>
<p>JDK HttpClient</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>If multiple clients are available on the classpath, the most preferred client will be used.</p>
</div>
<div class="paragraph">
<p>The <code>spring-boot-starter-webflux</code> starter depends on <code>io.projectreactor.netty:reactor-netty</code> by default, which brings both server and client implementations.
If you choose to use Jetty as a reactive server instead, you should add a dependency on the Jetty Reactive HTTP client library, <code>org.eclipse.jetty:jetty-reactive-httpclient</code>.
Using the same technology for server and client has its advantages, as it will automatically share HTTP resources between client and server.</p>
</div>
<div class="paragraph">
<p>Developers can override the resource configuration for Jetty and Reactor Netty by providing a custom <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/ReactorResourceFactory.html"><code>ReactorResourceFactory</code></a> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/reactive/JettyResourceFactory.html"><code>JettyResourceFactory</code></a> bean - this will be applied to both clients and servers.</p>
</div>
<div class="paragraph">
<p>If you wish to override that choice for the client, you can define your own <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/reactive/ClientHttpConnector.html"><code>ClientHttpConnector</code></a> bean and have full control over the client configuration.</p>
</div>
<div class="paragraph">
<p>You can learn more about the <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webflux-webclient/client-builder.html"><code>WebClient</code> configuration options in the Spring Framework reference documentation</a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="io.rest-client.webclient.customization"><a class="anchor" href="#io.rest-client.webclient.customization"></a>WebClient Customization</h3>
<div class="paragraph">
<p>There are three main approaches to <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a> customization, depending on how broadly you want the customizations to apply.</p>
</div>
<div class="paragraph">
<p>To make the scope of any customizations as narrow as possible, inject the auto-configured <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.Builder.html"><code>WebClient.Builder</code></a> and then call its methods as required.
<a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.Builder.html"><code>WebClient.Builder</code></a> instances are stateful: Any change on the builder is reflected in all clients subsequently created with it.
If you want to create several clients with the same builder, you can also consider cloning the builder with <code>WebClient.Builder other = builder.clone();</code>.</p>
</div>
<div class="paragraph">
<p>To make an application-wide, additive customization to all <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.Builder.html"><code>WebClient.Builder</code></a> instances, you can declare <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/reactive/function/client/WebClientCustomizer.html"><code>WebClientCustomizer</code></a> beans and change the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.Builder.html"><code>WebClient.Builder</code></a> locally at the point of injection.</p>
</div>
<div class="paragraph">
<p>Finally, you can fall back to the original API and use <code>WebClient.create()</code>.
In that case, no auto-configuration or <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/reactive/function/client/WebClientCustomizer.html"><code>WebClientCustomizer</code></a> is applied.</p>
</div>
</div>
<div class="sect2">
<h3 id="io.rest-client.webclient.ssl"><a class="anchor" href="#io.rest-client.webclient.ssl"></a>WebClient SSL Support</h3>
<div class="paragraph">
<p>If you need custom SSL configuration on the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/reactive/ClientHttpConnector.html"><code>ClientHttpConnector</code></a> used by the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a>, you can inject a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/reactive/function/client/WebClientSsl.html"><code>WebClientSsl</code></a> instance that can be used with the builder’s <code>apply</code> method.</p>
</div>
<div class="paragraph">
<p>The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/reactive/function/client/WebClientSsl.html"><code>WebClientSsl</code></a> interface provides access to any <a class="xref page" href="../features/ssl.html#features.ssl.bundles">SSL bundles</a> that you have defined in your <code>application.properties</code> or <code>application.yaml</code> file.</p>
</div>
<div class="paragraph">
<p>The following code shows a typical example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_2_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_2_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_2_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_java" class="tabpanel" id="_tabs_2_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> reactor.core.publisher.Mono;

<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientSsl;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Service;
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.client.WebClient;

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> WebClient webClient;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyService</span><span class="hljs-params">(WebClient.Builder webClientBuilder, WebClientSsl ssl)</span> </span>{
		<span class="hljs-keyword">this</span>.webClient = webClientBuilder.baseUrl(<span class="hljs-string">"https://example.org"</span>).apply(ssl.fromBundle(<span class="hljs-string">"mybundle"</span>)).build();
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Mono&lt;Details&gt; <span class="hljs-title">someRestCall</span><span class="hljs-params">(String name)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.webClient.get().uri(<span class="hljs-string">"/{name}/details"</span>, name).retrieve().bodyToMono(Details<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_2_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientSsl
<span class="hljs-keyword">import</span> org.springframework.stereotype.Service
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.client.WebClient
<span class="hljs-keyword">import</span> reactor.core.publisher.Mono

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span></span>(webClientBuilder: WebClient.Builder, ssl: WebClientSsl) {

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> webClient: WebClient

	<span class="hljs-keyword">init</span> {
		webClient = webClientBuilder.baseUrl(<span class="hljs-string">"https://example.org"</span>)
				.apply(ssl.fromBundle(<span class="hljs-string">"mybundle"</span>)).build()
	}

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someRestCall</span><span class="hljs-params">(name: <span class="hljs-type">String</span>?)</span></span>: Mono&lt;Details&gt; {
		<span class="hljs-keyword">return</span> webClient.<span class="hljs-keyword">get</span>().uri(<span class="hljs-string">"/{name}/details"</span>, name)
				.retrieve().bodyToMono(Details::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>)</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="io.rest-client.restclient"><a class="anchor" href="#io.rest-client.restclient"></a>RestClient</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you are not using Spring WebFlux or Project Reactor in your application we recommend that you use <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a> to call remote REST services.</p>
</div>
<div class="paragraph">
<p>The <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a> interface provides a functional style blocking API.</p>
</div>
<div class="paragraph">
<p>Spring Boot creates and pre-configures a prototype <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.Builder.html"><code>RestClient.Builder</code></a> bean for you.
It is strongly advised to inject it in your components and use it to create <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a> instances.
Spring Boot is configuring that builder with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/http/HttpMessageConverters.html"><code>HttpMessageConverters</code></a> and an appropriate <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/ClientHttpRequestFactory.html"><code>ClientHttpRequestFactory</code></a>.</p>
</div>
<div class="paragraph">
<p>The following code shows a typical example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_3_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_3_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_3_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_java" class="tabpanel" id="_tabs_3_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.stereotype.Service;
<span class="hljs-keyword">import</span> org.springframework.web.client.RestClient;

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> RestClient restClient;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyService</span><span class="hljs-params">(RestClient.Builder restClientBuilder)</span> </span>{
		<span class="hljs-keyword">this</span>.restClient = restClientBuilder.baseUrl(<span class="hljs-string">"https://example.org"</span>).build();
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Details <span class="hljs-title">someRestCall</span><span class="hljs-params">(String name)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.restClient.get().uri(<span class="hljs-string">"/{name}/details"</span>, name).retrieve().body(Details<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_3_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.docs.io.restclient.restclient.ssl.Details
<span class="hljs-keyword">import</span> org.springframework.stereotype.Service
<span class="hljs-keyword">import</span> org.springframework.web.client.RestClient

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span></span>(restClientBuilder: RestClient.Builder) {

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> restClient: RestClient

	<span class="hljs-keyword">init</span> {
		restClient = restClientBuilder.baseUrl(<span class="hljs-string">"https://example.org"</span>).build()
	}

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someRestCall</span><span class="hljs-params">(name: <span class="hljs-type">String</span>?)</span></span>: Details {
		<span class="hljs-keyword">return</span> restClient.<span class="hljs-keyword">get</span>().uri(<span class="hljs-string">"/{name}/details"</span>, name)
				.retrieve().body(Details::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>)!!</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="io.rest-client.restclient.customization"><a class="anchor" href="#io.rest-client.restclient.customization"></a>RestClient Customization</h3>
<div class="paragraph">
<p>There are three main approaches to <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a> customization, depending on how broadly you want the customizations to apply.</p>
</div>
<div class="paragraph">
<p>To make the scope of any customizations as narrow as possible, inject the auto-configured <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.Builder.html"><code>RestClient.Builder</code></a> and then call its methods as required.
<a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.Builder.html"><code>RestClient.Builder</code></a> instances are stateful: Any change on the builder is reflected in all clients subsequently created with it.
If you want to create several clients with the same builder, you can also consider cloning the builder with <code>RestClient.Builder other = builder.clone();</code>.</p>
</div>
<div class="paragraph">
<p>To make an application-wide, additive customization to all <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.Builder.html"><code>RestClient.Builder</code></a> instances, you can declare <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestClientCustomizer.html"><code>RestClientCustomizer</code></a> beans and change the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.Builder.html"><code>RestClient.Builder</code></a> locally at the point of injection.</p>
</div>
<div class="paragraph">
<p>Finally, you can fall back to the original API and use <code>RestClient.create()</code>.
In that case, no auto-configuration or <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestClientCustomizer.html"><code>RestClientCustomizer</code></a> is applied.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can also change the <a href="#io.rest-client.clienthttprequestfactory.configuration">global HTTP client configuration</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="io.rest-client.restclient.ssl"><a class="anchor" href="#io.rest-client.restclient.ssl"></a>RestClient SSL Support</h3>
<div class="paragraph">
<p>If you need custom SSL configuration on the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/ClientHttpRequestFactory.html"><code>ClientHttpRequestFactory</code></a> used by the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a>, you can inject a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/client/RestClientSsl.html"><code>RestClientSsl</code></a> instance that can be used with the builder’s <code>apply</code> method.</p>
</div>
<div class="paragraph">
<p>The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/client/RestClientSsl.html"><code>RestClientSsl</code></a> interface provides access to any <a class="xref page" href="../features/ssl.html#features.ssl.bundles">SSL bundles</a> that you have defined in your <code>application.properties</code> or <code>application.yaml</code> file.</p>
</div>
<div class="paragraph">
<p>The following code shows a typical example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_4_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_4_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_4_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_java" class="tabpanel" id="_tabs_4_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.web.client.RestClientSsl;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Service;
<span class="hljs-keyword">import</span> org.springframework.web.client.RestClient;

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> RestClient restClient;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyService</span><span class="hljs-params">(RestClient.Builder restClientBuilder, RestClientSsl ssl)</span> </span>{
		<span class="hljs-keyword">this</span>.restClient = restClientBuilder.baseUrl(<span class="hljs-string">"https://example.org"</span>).apply(ssl.fromBundle(<span class="hljs-string">"mybundle"</span>)).build();
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Details <span class="hljs-title">someRestCall</span><span class="hljs-params">(String name)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.restClient.get().uri(<span class="hljs-string">"/{name}/details"</span>, name).retrieve().body(Details<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_4_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.web.client.RestClientSsl
<span class="hljs-keyword">import</span> org.springframework.boot.docs.io.restclient.restclient.ssl.settings.Details
<span class="hljs-keyword">import</span> org.springframework.stereotype.Service
<span class="hljs-keyword">import</span> org.springframework.web.client.RestClient

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span></span>(restClientBuilder: RestClient.Builder, ssl: RestClientSsl) {

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> restClient: RestClient

	<span class="hljs-keyword">init</span> {
		restClient = restClientBuilder.baseUrl(<span class="hljs-string">"https://example.org"</span>)
				.apply(ssl.fromBundle(<span class="hljs-string">"mybundle"</span>)).build()
	}

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someRestCall</span><span class="hljs-params">(name: <span class="hljs-type">String</span>?)</span></span>: Details {
		<span class="hljs-keyword">return</span> restClient.<span class="hljs-keyword">get</span>().uri(<span class="hljs-string">"/{name}/details"</span>, name)
				.retrieve().body(Details::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>)!!</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you need to apply other customization in addition to an SSL bundle, you can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/http/client/ClientHttpRequestFactorySettings.html"><code>ClientHttpRequestFactorySettings</code></a> class with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/http/client/ClientHttpRequestFactoryBuilder.html"><code>ClientHttpRequestFactoryBuilder</code></a>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_5_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_5_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_5_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_java" class="tabpanel" id="_tabs_5_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.time.Duration;

<span class="hljs-keyword">import</span> org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder;
<span class="hljs-keyword">import</span> org.springframework.boot.http.client.ClientHttpRequestFactorySettings;
<span class="hljs-keyword">import</span> org.springframework.boot.ssl.SslBundles;
<span class="hljs-keyword">import</span> org.springframework.http.client.ClientHttpRequestFactory;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Service;
<span class="hljs-keyword">import</span> org.springframework.web.client.RestClient;

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> RestClient restClient;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyService</span><span class="hljs-params">(RestClient.Builder restClientBuilder, SslBundles sslBundles)</span> </span>{
		ClientHttpRequestFactorySettings settings = ClientHttpRequestFactorySettings
			.ofSslBundle(sslBundles.getBundle(<span class="hljs-string">"mybundle"</span>))
			.withReadTimeout(Duration.ofMinutes(<span class="hljs-number">2</span>));
		ClientHttpRequestFactory requestFactory = ClientHttpRequestFactoryBuilder.detect().build(settings);
		<span class="hljs-keyword">this</span>.restClient = restClientBuilder.baseUrl(<span class="hljs-string">"https://example.org"</span>).requestFactory(requestFactory).build();
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Details <span class="hljs-title">someRestCall</span><span class="hljs-params">(String name)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.restClient.get().uri(<span class="hljs-string">"/{name}/details"</span>, name).retrieve().body(Details<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_5_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder;
<span class="hljs-keyword">import</span> org.springframework.boot.http.client.ClientHttpRequestFactorySettings;
<span class="hljs-keyword">import</span> org.springframework.boot.ssl.SslBundles
<span class="hljs-keyword">import</span> org.springframework.stereotype.Service
<span class="hljs-keyword">import</span> org.springframework.web.client.RestClient
<span class="hljs-keyword">import</span> java.time.Duration

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span></span>(restClientBuilder: RestClient.Builder, sslBundles: SslBundles) {

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> restClient: RestClient

	<span class="hljs-keyword">init</span> {
		<span class="hljs-keyword">val</span> settings = ClientHttpRequestFactorySettings.defaults()
				.withReadTimeout(Duration.ofMinutes(<span class="hljs-number">2</span>))
				.withSslBundle(sslBundles.getBundle(<span class="hljs-string">"mybundle"</span>))
		<span class="hljs-keyword">val</span> requestFactory = ClientHttpRequestFactoryBuilder.detect().build(settings);
		restClient = restClientBuilder
				.baseUrl(<span class="hljs-string">"https://example.org"</span>)
				.requestFactory(requestFactory).build()
	}

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someRestCall</span><span class="hljs-params">(name: <span class="hljs-type">String</span>?)</span></span>: Details {
		<span class="hljs-keyword">return</span> restClient.<span class="hljs-keyword">get</span>().uri(<span class="hljs-string">"/{name}/details"</span>, name).retrieve().body(Details::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>)!!</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="io.rest-client.resttemplate"><a class="anchor" href="#io.rest-client.resttemplate"></a>RestTemplate</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Framework’s <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a> class predates <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a> and is the classic way that many applications use to call remote REST services.
You might choose to use <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a> when you have existing code that you don’t want to migrate to <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a>, or because you’re already familiar with the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a> API.</p>
</div>
<div class="paragraph">
<p>Since <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a> instances often need to be customized before being used, Spring Boot does not provide any single auto-configured <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a> bean.
It does, however, auto-configure a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a>, which can be used to create <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a> instances when needed.
The auto-configured <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> ensures that sensible <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/http/HttpMessageConverters.html"><code>HttpMessageConverters</code></a> and an appropriate <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/ClientHttpRequestFactory.html"><code>ClientHttpRequestFactory</code></a> are applied to <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a> instances.</p>
</div>
<div class="paragraph">
<p>The following code shows a typical example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_6_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_6_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_6_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_java" class="tabpanel" id="_tabs_6_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.client.RestTemplateBuilder;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Service;
<span class="hljs-keyword">import</span> org.springframework.web.client.RestTemplate;

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> RestTemplate restTemplate;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyService</span><span class="hljs-params">(RestTemplateBuilder restTemplateBuilder)</span> </span>{
		<span class="hljs-keyword">this</span>.restTemplate = restTemplateBuilder.build();
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Details <span class="hljs-title">someRestCall</span><span class="hljs-params">(String name)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.restTemplate.getForObject(<span class="hljs-string">"/{name}/details"</span>, Details<span class="hljs-class">.<span class="hljs-keyword">class</span>, <span class="hljs-title">name</span>)</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_6_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.client.RestTemplateBuilder
<span class="hljs-keyword">import</span> org.springframework.stereotype.Service
<span class="hljs-keyword">import</span> org.springframework.web.client.RestTemplate

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span></span>(restTemplateBuilder: RestTemplateBuilder) {

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> restTemplate: RestTemplate

	<span class="hljs-keyword">init</span> {
		restTemplate = restTemplateBuilder.build()
	}

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someRestCall</span><span class="hljs-params">(name: <span class="hljs-type">String</span>)</span></span>: Details {
		<span class="hljs-keyword">return</span> restTemplate.getForObject(<span class="hljs-string">"/{name}/details"</span>, Details::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>, <span class="hljs-type">name)!!</span></span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> includes a number of useful methods that can be used to quickly configure a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a>.
For example, to add BASIC authentication support, you can use <code>builder.basicAuthentication("user", "password").build()</code>.</p>
</div>
<div class="sect2">
<h3 id="io.rest-client.resttemplate.customization"><a class="anchor" href="#io.rest-client.resttemplate.customization"></a>RestTemplate Customization</h3>
<div class="paragraph">
<p>There are three main approaches to <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a> customization, depending on how broadly you want the customizations to apply.</p>
</div>
<div class="paragraph">
<p>To make the scope of any customizations as narrow as possible, inject the auto-configured <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> and then call its methods as required.
Each method call returns a new <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> instance, so the customizations only affect this use of the builder.</p>
</div>
<div class="paragraph">
<p>To make an application-wide, additive customization, use a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateCustomizer.html"><code>RestTemplateCustomizer</code></a> bean.
All such beans are automatically registered with the auto-configured <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> and are applied to any templates that are built with it.</p>
</div>
<div class="paragraph">
<p>The following example shows a customizer that configures the use of a proxy for all hosts except <code>***********</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_7_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_7_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_7_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_java" class="tabpanel" id="_tabs_7_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.apache.hc.client5.http.classic.HttpClient;
<span class="hljs-keyword">import</span> org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
<span class="hljs-keyword">import</span> org.apache.hc.client5.http.impl.routing.DefaultProxyRoutePlanner;
<span class="hljs-keyword">import</span> org.apache.hc.client5.http.routing.HttpRoutePlanner;
<span class="hljs-keyword">import</span> org.apache.hc.core5.http.HttpException;
<span class="hljs-keyword">import</span> org.apache.hc.core5.http.HttpHost;
<span class="hljs-keyword">import</span> org.apache.hc.core5.http.protocol.HttpContext;

<span class="hljs-keyword">import</span> org.springframework.boot.web.client.RestTemplateCustomizer;
<span class="hljs-keyword">import</span> org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
<span class="hljs-keyword">import</span> org.springframework.web.client.RestTemplate;

</span><span class="fold-block"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestTemplateCustomizer</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">RestTemplateCustomizer</span> </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">customize</span><span class="hljs-params">(RestTemplate restTemplate)</span> </span>{
		HttpRoutePlanner routePlanner = <span class="hljs-keyword">new</span> CustomRoutePlanner(<span class="hljs-keyword">new</span> HttpHost(<span class="hljs-string">"proxy.example.com"</span>));
		HttpClient httpClient = HttpClientBuilder.create().setRoutePlanner(routePlanner).build();
		restTemplate.setRequestFactory(<span class="hljs-keyword">new</span> HttpComponentsClientHttpRequestFactory(httpClient));
	}

	<span class="hljs-keyword">static</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">CustomRoutePlanner</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">DefaultProxyRoutePlanner</span> </span>{

		CustomRoutePlanner(HttpHost proxy) {
			<span class="hljs-keyword">super</span>(proxy);
		}

		<span class="hljs-meta">@Override</span>
		<span class="hljs-function"><span class="hljs-keyword">protected</span> HttpHost <span class="hljs-title">determineProxy</span><span class="hljs-params">(HttpHost target, HttpContext context)</span> <span class="hljs-keyword">throws</span> HttpException </span>{
			<span class="hljs-keyword">if</span> (target.getHostName().equals(<span class="hljs-string">"***********"</span>)) {
				<span class="hljs-keyword">return</span> <span class="hljs-keyword">null</span>;
			}
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">super</span>.determineProxy(target, context);
		}

	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_7_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.apache.hc.client5.http.classic.HttpClient
<span class="hljs-keyword">import</span> org.apache.hc.client5.http.impl.classic.HttpClientBuilder
<span class="hljs-keyword">import</span> org.apache.hc.client5.http.impl.routing.DefaultProxyRoutePlanner
<span class="hljs-keyword">import</span> org.apache.hc.client5.http.routing.HttpRoutePlanner
<span class="hljs-keyword">import</span> org.apache.hc.core5.http.HttpException
<span class="hljs-keyword">import</span> org.apache.hc.core5.http.HttpHost
<span class="hljs-keyword">import</span> org.apache.hc.core5.http.protocol.HttpContext
<span class="hljs-keyword">import</span> org.springframework.boot.web.client.RestTemplateCustomizer
<span class="hljs-keyword">import</span> org.springframework.http.client.HttpComponentsClientHttpRequestFactory
<span class="hljs-keyword">import</span> org.springframework.web.client.RestTemplate

</span><span class="fold-block"><span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestTemplateCustomizer</span> : <span class="hljs-type">RestTemplateCustomizer {</span></span>

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">customize</span><span class="hljs-params">(restTemplate: <span class="hljs-type">RestTemplate</span>)</span></span> {
		<span class="hljs-keyword">val</span> routePlanner: HttpRoutePlanner = CustomRoutePlanner(HttpHost(<span class="hljs-string">"proxy.example.com"</span>))
		<span class="hljs-keyword">val</span> httpClient: HttpClient = HttpClientBuilder.create().setRoutePlanner(routePlanner).build()
		restTemplate.requestFactory = HttpComponentsClientHttpRequestFactory(httpClient)
	}

	<span class="hljs-keyword">internal</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">CustomRoutePlanner</span></span>(proxy: HttpHost?) : DefaultProxyRoutePlanner(proxy) {

		<span class="hljs-meta">@Throws(HttpException::class)</span>
		<span class="hljs-keyword">public</span> <span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">determineProxy</span><span class="hljs-params">(target: <span class="hljs-type">HttpHost</span>, context: <span class="hljs-type">HttpContext</span>)</span></span>: HttpHost? {
			<span class="hljs-keyword">if</span> (target.hostName == <span class="hljs-string">"***********"</span>) {
				<span class="hljs-keyword">return</span> <span class="hljs-literal">null</span>
			}
			<span class="hljs-keyword">return</span>  <span class="hljs-keyword">super</span>.determineProxy(target, context)
		}

	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Finally, you can define your own <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> bean.
Doing so will replace the auto-configured builder.
If you want any <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateCustomizer.html"><code>RestTemplateCustomizer</code></a> beans to be applied to your custom builder, as the auto-configuration would have done, configure it using a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/client/RestTemplateBuilderConfigurer.html"><code>RestTemplateBuilderConfigurer</code></a>.
The following example exposes a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> that matches what Spring Boot’s auto-configuration would have done, except that custom connect and read timeouts are also specified:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_8_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_8_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_8_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_java" class="tabpanel" id="_tabs_8_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.time.Duration;

<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.web.client.RestTemplateBuilderConfigurer;
<span class="hljs-keyword">import</span> org.springframework.boot.web.client.RestTemplateBuilder;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestTemplateBuilderConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> RestTemplateBuilder <span class="hljs-title">restTemplateBuilder</span><span class="hljs-params">(RestTemplateBuilderConfigurer configurer)</span> </span>{
		<span class="hljs-keyword">return</span> configurer.configure(<span class="hljs-keyword">new</span> RestTemplateBuilder())
			.connectTimeout(Duration.ofSeconds(<span class="hljs-number">5</span>))
			.readTimeout(Duration.ofSeconds(<span class="hljs-number">2</span>));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_8_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.web.client.RestTemplateBuilderConfigurer
<span class="hljs-keyword">import</span> org.springframework.boot.web.client.RestTemplateBuilder
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> java.time.Duration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestTemplateBuilderConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">restTemplateBuilder</span><span class="hljs-params">(configurer: <span class="hljs-type">RestTemplateBuilderConfigurer</span>)</span></span>: RestTemplateBuilder {
		<span class="hljs-keyword">return</span> configurer.configure(RestTemplateBuilder()).connectTimeout(Duration.ofSeconds(<span class="hljs-number">5</span>))
			.readTimeout(Duration.ofSeconds(<span class="hljs-number">2</span>))
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The most extreme (and rarely used) option is to create your own <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> bean without using a configurer.
In addition to replacing the auto-configured builder, this also prevents any <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateCustomizer.html"><code>RestTemplateCustomizer</code></a> beans from being used.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can also change the <a href="#io.rest-client.clienthttprequestfactory.configuration">global HTTP client configuration</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="io.rest-client.resttemplate.ssl"><a class="anchor" href="#io.rest-client.resttemplate.ssl"></a>RestTemplate SSL Support</h3>
<div class="paragraph">
<p>If you need custom SSL configuration on the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a>, you can apply an <a class="xref page" href="../features/ssl.html#features.ssl.bundles">SSL bundle</a> to the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> as shown in this example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_9">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_9_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_9_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_9_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_9_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_9_java" class="tabpanel" id="_tabs_9_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.docs.io.restclient.resttemplate.Details;
<span class="hljs-keyword">import</span> org.springframework.boot.ssl.SslBundles;
<span class="hljs-keyword">import</span> org.springframework.boot.web.client.RestTemplateBuilder;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Service;
<span class="hljs-keyword">import</span> org.springframework.web.client.RestTemplate;

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> RestTemplate restTemplate;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyService</span><span class="hljs-params">(RestTemplateBuilder restTemplateBuilder, SslBundles sslBundles)</span> </span>{
		<span class="hljs-keyword">this</span>.restTemplate = restTemplateBuilder.sslBundle(sslBundles.getBundle(<span class="hljs-string">"mybundle"</span>)).build();
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Details <span class="hljs-title">someRestCall</span><span class="hljs-params">(String name)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.restTemplate.getForObject(<span class="hljs-string">"/{name}/details"</span>, Details<span class="hljs-class">.<span class="hljs-keyword">class</span>, <span class="hljs-title">name</span>)</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_9_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_9_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.docs.io.restclient.resttemplate.Details
<span class="hljs-keyword">import</span> org.springframework.boot.ssl.SslBundles
<span class="hljs-keyword">import</span> org.springframework.boot.web.client.RestTemplateBuilder
<span class="hljs-keyword">import</span> org.springframework.stereotype.Service
<span class="hljs-keyword">import</span> org.springframework.web.client.RestTemplate

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span></span>(restTemplateBuilder: RestTemplateBuilder, sslBundles: SslBundles) {

    <span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> restTemplate: RestTemplate

    <span class="hljs-keyword">init</span> {
        restTemplate = restTemplateBuilder.sslBundle(sslBundles.getBundle(<span class="hljs-string">"mybundle"</span>)).build()
    }

    <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someRestCall</span><span class="hljs-params">(name: <span class="hljs-type">String</span>)</span></span>: Details {
        <span class="hljs-keyword">return</span> restTemplate.getForObject(<span class="hljs-string">"/{name}/details"</span>, Details::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>, <span class="hljs-type">name)!!</span></span>
    }

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="io.rest-client.clienthttprequestfactory"><a class="anchor" href="#io.rest-client.clienthttprequestfactory"></a>HTTP Client Detection for RestClient and RestTemplate</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot will auto-detect which HTTP client to use with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a> depending on the libraries available on the application classpath.
In order of preference, the following clients are supported:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Apache HttpClient</p>
</li>
<li>
<p>Jetty HttpClient</p>
</li>
<li>
<p>Reactor Netty HttpClient</p>
</li>
<li>
<p>JDK client (<code>java.net.http.HttpClient</code>)</p>
</li>
<li>
<p>Simple JDK client (<code>java.net.HttpURLConnection</code>)</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>If multiple clients are available on the classpath, and not global configuration is provided, the most preferred client will be used.</p>
</div>
<div class="sect2">
<h3 id="io.rest-client.clienthttprequestfactory.configuration"><a class="anchor" href="#io.rest-client.clienthttprequestfactory.configuration"></a>Global HTTP Client Configuration</h3>
<div class="paragraph">
<p>If the auto-detected HTTP client does not meet your needs, you can use the <code>spring.http.client.factory</code> property to pick a specific factory.
For example, if you have Apache HttpClient on your classpath, but you prefer Jetty’s <a class="apiref external" href="https://javadoc.jetty.org/jetty-12/org/eclipse/jetty/client/HttpClient.html" target="_blank"><code>HttpClient</code></a> you can add the following:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_10">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_10_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_10_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_10_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_10_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_10_properties" class="tabpanel" id="_tabs_10_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.http.client.factory</span>=<span class="hljs-string">jetty</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_10_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_10_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">http:</span>
    <span class="hljs-attr">client:</span>
      <span class="hljs-attr">factory:</span> <span class="hljs-string">jetty</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also set properties to change defaults that will be applied to all clients.
For example, you may want to change timeouts and if redirects are followed:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_11">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_11_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_11_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_11_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_11_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_11_properties" class="tabpanel" id="_tabs_11_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.http.client.connect-timeout</span>=<span class="hljs-string">2s</span>
<span class="hljs-meta">spring.http.client.read-timeout</span>=<span class="hljs-string">1s</span>
<span class="hljs-meta">spring.http.client.redirects</span>=<span class="hljs-string">dont-follow</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_11_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_11_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">http:</span>
    <span class="hljs-attr">client:</span>
      <span class="hljs-attr">connect-timeout:</span> <span class="hljs-string">2s</span>
      <span class="hljs-attr">read-timeout:</span> <span class="hljs-string">1s</span>
      <span class="hljs-attr">redirects:</span> <span class="hljs-string">dont-follow</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>For more complex customizations, you can declare your own <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/http/client/ClientHttpRequestFactoryBuilder.html"><code>ClientHttpRequestFactoryBuilder</code></a> bean which will cause auto-configuration to back off.
This can be useful when you need to customize some of the internals of the underlying HTTP library.</p>
</div>
<div class="paragraph">
<p>For example, the following will use a JDK client configured with a specific <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/net/ProxySelector.html" target="_blank"><code>ProxySelector</code></a>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_12">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_12_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_12_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_12_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_12_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_12_java" class="tabpanel" id="_tabs_12_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.net.ProxySelector;

<span class="hljs-keyword">import</span> org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyClientHttpConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	ClientHttpRequestFactoryBuilder&lt;?&gt; clientHttpRequestFactoryBuilder(ProxySelector proxySelector) {
		<span class="hljs-keyword">return</span> ClientHttpRequestFactoryBuilder.jdk()
			.withHttpClientCustomizer((builder) -&gt; builder.proxy(proxySelector));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_12_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_12_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> java.net.ProxySelector
<span class="hljs-keyword">import</span> java.net.http.HttpClient

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyClientHttpConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">clientHttpRequestFactoryBuilder</span><span class="hljs-params">(proxySelector: <span class="hljs-type">ProxySelector</span>)</span></span>: ClientHttpRequestFactoryBuilder&lt;*&gt; {
		<span class="hljs-keyword">return</span> ClientHttpRequestFactoryBuilder.jdk()
				.withHttpClientCustomizer { builder -&gt; builder.proxy(proxySelector) }
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="validation.html">Validation</a></span>
<span class="next"><a href="webservices.html">Web Services</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../reference/io/rest-client.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="rest-client.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../3.3/reference/io/rest-client.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../4.0-SNAPSHOT/reference/io/rest-client.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.5-SNAPSHOT/reference/io/rest-client.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.4-SNAPSHOT/reference/io/rest-client.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.3-SNAPSHOT/reference/io/rest-client.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>