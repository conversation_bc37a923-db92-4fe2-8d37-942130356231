<!DOCTYPE html>
<html><head><title>Spring Security :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/reference/web/spring-security.html"/><meta content="2025-06-04T15:39:47.333969" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Spring Security">
<div class="toc-menu"><h3>Spring Security</h3><ul><li data-level="1"><a href="#web.security.spring-mvc">MVC Security</a></li><li data-level="1"><a href="#web.security.spring-webflux">WebFlux Security</a></li><li data-level="1"><a href="#web.security.oauth2">OAuth2</a></li><li data-level="2"><a href="#web.security.oauth2.client">Client</a></li><li data-level="2"><a href="#web.security.oauth2.server">Resource Server</a></li><li data-level="2"><a href="#web.security.oauth2.authorization-server">Authorization Server</a></li><li data-level="1"><a href="#web.security.saml2">SAML 2.0</a></li><li data-level="2"><a href="#web.security.saml2.relying-party">Relying Party</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/web/spring-security.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../index.html">Spring Boot</a></li>
<li><a href="../index.html">Reference</a></li>
<li><a href="index.html">Web</a></li>
<li><a href="spring-security.html">Spring Security</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../reference/web/spring-security.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Spring Security</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Spring Security</h3><ul><li data-level="1"><a href="#web.security.spring-mvc">MVC Security</a></li><li data-level="1"><a href="#web.security.spring-webflux">WebFlux Security</a></li><li data-level="1"><a href="#web.security.oauth2">OAuth2</a></li><li data-level="2"><a href="#web.security.oauth2.client">Client</a></li><li data-level="2"><a href="#web.security.oauth2.server">Resource Server</a></li><li data-level="2"><a href="#web.security.oauth2.authorization-server">Authorization Server</a></li><li data-level="1"><a href="#web.security.saml2">SAML 2.0</a></li><li data-level="2"><a href="#web.security.saml2.relying-party">Relying Party</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>If <a class="external" href="https://spring.io/projects/spring-security" target="_blank">Spring Security</a> is on the classpath, then web applications are secured by default.
Spring Boot relies on Spring Security’s content-negotiation strategy to determine whether to use <code>httpBasic</code> or <code>formLogin</code>.
To add method-level security to a web application, you can also add <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/config/annotation/method/configuration/EnableMethodSecurity.html"><code>@EnableMethodSecurity</code></a> with your desired settings.
Additional information can be found in the <a href="https://docs.spring.io/spring-security/reference/6.4/servlet/authorization/method-security.html">Spring Security Reference Guide</a>.</p>
</div>
<div class="paragraph">
<p>The default <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/UserDetailsService.html"><code>UserDetailsService</code></a> has a single user.
The user name is <code>user</code>, and the password is random and is printed at WARN level when the application starts, as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-none hljs">Using generated security password: 78fa095d-3f4c-48b1-ad50-e24c31d5cf35

This generated password is for development use only. Your security configuration must be updated before running your application in production.</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If you fine-tune your logging configuration, ensure that the <code>org.springframework.boot.autoconfigure.security</code> category is set to log <code>WARN</code>-level messages.
Otherwise, the default password is not printed.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>You can change the username and password by providing a <code>spring.security.user.name</code> and <code>spring.security.user.password</code>.</p>
</div>
<div class="paragraph">
<p>The basic features you get by default in a web application are:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>A <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/UserDetailsService.html"><code>UserDetailsService</code></a> (or <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/ReactiveUserDetailsService.html"><code>ReactiveUserDetailsService</code></a> in case of a WebFlux application) bean with in-memory store and a single user with a generated password (see <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/SecurityProperties.User.html"><code>SecurityProperties.User</code></a> for the properties of the user).</p>
</li>
<li>
<p>Form-based login or HTTP Basic security (depending on the <code>Accept</code> header in the request) for the entire application (including actuator endpoints if actuator is on the classpath).</p>
</li>
<li>
<p>A <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/authentication/DefaultAuthenticationEventPublisher.html"><code>DefaultAuthenticationEventPublisher</code></a> for publishing authentication events.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>You can provide a different <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/authentication/AuthenticationEventPublisher.html"><code>AuthenticationEventPublisher</code></a> by adding a bean for it.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="web.security.spring-mvc"><a class="anchor" href="#web.security.spring-mvc"></a>MVC Security</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The default security configuration is implemented in <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/servlet/SecurityAutoConfiguration.html"><code>SecurityAutoConfiguration</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/servlet/UserDetailsServiceAutoConfiguration.html"><code>UserDetailsServiceAutoConfiguration</code></a>.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/servlet/SecurityAutoConfiguration.html"><code>SecurityAutoConfiguration</code></a> imports <code>SpringBootWebSecurityConfiguration</code> for web security and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/servlet/UserDetailsServiceAutoConfiguration.html"><code>UserDetailsServiceAutoConfiguration</code></a> for authentication.</p>
</div>
<div class="paragraph">
<p>To completely switch off the default web application security configuration, including Actuator security, or to combine multiple Spring Security components such as OAuth2 Client and Resource Server, add a bean of type <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/SecurityFilterChain.html"><code>SecurityFilterChain</code></a> (doing so does not disable the <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/UserDetailsService.html"><code>UserDetailsService</code></a> configuration).
To also switch off the <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/UserDetailsService.html"><code>UserDetailsService</code></a> configuration, add a bean of type <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/UserDetailsService.html"><code>UserDetailsService</code></a>, <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/authentication/AuthenticationProvider.html"><code>AuthenticationProvider</code></a>, or <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/authentication/AuthenticationManager.html"><code>AuthenticationManager</code></a>.</p>
</div>
<div class="paragraph">
<p>The auto-configuration of a <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/UserDetailsService.html"><code>UserDetailsService</code></a> will also back off when any of the following Spring Security modules is on the classpath:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>spring-security-oauth2-client</code></p>
</li>
<li>
<p><code>spring-security-oauth2-resource-server</code></p>
</li>
<li>
<p><code>spring-security-saml2-service-provider</code></p>
</li>
</ul>
</div>
<div class="paragraph">
<p>To use <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/UserDetailsService.html"><code>UserDetailsService</code></a> in addition to one or more of these dependencies, define your own <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/provisioning/InMemoryUserDetailsManager.html"><code>InMemoryUserDetailsManager</code></a> bean.</p>
</div>
<div class="paragraph">
<p>Access rules can be overridden by adding a custom <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/SecurityFilterChain.html"><code>SecurityFilterChain</code></a> bean.
Spring Boot provides convenience methods that can be used to override access rules for actuator endpoints and static resources.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/autoconfigure/security/servlet/EndpointRequest.html"><code>EndpointRequest</code></a> can be used to create a <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/util/matcher/RequestMatcher.html"><code>RequestMatcher</code></a> that is based on the <code>management.endpoints.web.base-path</code> property.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/servlet/PathRequest.html"><code>PathRequest</code></a> can be used to create a <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/util/matcher/RequestMatcher.html"><code>RequestMatcher</code></a> for resources in commonly used locations.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="web.security.spring-webflux"><a class="anchor" href="#web.security.spring-webflux"></a>WebFlux Security</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Similar to Spring MVC applications, you can secure your WebFlux applications by adding the <code>spring-boot-starter-security</code> dependency.
The default security configuration is implemented in <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/reactive/ReactiveSecurityAutoConfiguration.html"><code>ReactiveSecurityAutoConfiguration</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/reactive/ReactiveUserDetailsServiceAutoConfiguration.html"><code>ReactiveUserDetailsServiceAutoConfiguration</code></a>.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/reactive/ReactiveSecurityAutoConfiguration.html"><code>ReactiveSecurityAutoConfiguration</code></a> imports <code>WebFluxSecurityConfiguration</code> for web security and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/reactive/UserDetailsServiceAutoConfiguration.html"><code>UserDetailsServiceAutoConfiguration</code></a> for authentication.
In addition to reactive web applications, the latter is also auto-configured when RSocket is in use.</p>
</div>
<div class="paragraph">
<p>To completely switch off the default web application security configuration, including Actuator security, add a bean of type <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/server/WebFilterChainProxy.html"><code>WebFilterChainProxy</code></a> (doing so does not disable the <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/ReactiveUserDetailsService.html"><code>ReactiveUserDetailsService</code></a> configuration).
To also switch off the <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/ReactiveUserDetailsService.html"><code>ReactiveUserDetailsService</code></a> configuration, add a bean of type <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/ReactiveUserDetailsService.html"><code>ReactiveUserDetailsService</code></a> or <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/authentication/ReactiveAuthenticationManager.html"><code>ReactiveAuthenticationManager</code></a>.</p>
</div>
<div class="paragraph">
<p>The auto-configuration will also back off when any of the following Spring Security modules is on the classpath:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>spring-security-oauth2-client</code></p>
</li>
<li>
<p><code>spring-security-oauth2-resource-server</code></p>
</li>
</ul>
</div>
<div class="paragraph">
<p>To use <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/ReactiveUserDetailsService.html"><code>ReactiveUserDetailsService</code></a> in addition to one or more of these dependencies, define your own <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/core/userdetails/MapReactiveUserDetailsService.html"><code>MapReactiveUserDetailsService</code></a> bean.</p>
</div>
<div class="paragraph">
<p>Access rules and the use of multiple Spring Security components such as OAuth 2 Client and Resource Server can be configured by adding a custom <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/server/SecurityWebFilterChain.html"><code>SecurityWebFilterChain</code></a> bean.
Spring Boot provides convenience methods that can be used to override access rules for actuator endpoints and static resources.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/autoconfigure/security/reactive/EndpointRequest.html"><code>EndpointRequest</code></a> can be used to create a <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/server/util/matcher/ServerWebExchangeMatcher.html"><code>ServerWebExchangeMatcher</code></a> that is based on the <code>management.endpoints.web.base-path</code> property.</p>
</div>
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/reactive/PathRequest.html"><code>PathRequest</code></a> can be used to create a <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/server/util/matcher/ServerWebExchangeMatcher.html"><code>ServerWebExchangeMatcher</code></a> for resources in commonly used locations.</p>
</div>
<div class="paragraph">
<p>For example, you can customize your security configuration by adding something like:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_1_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_1_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_1_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_java" class="tabpanel" id="_tabs_1_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.security.reactive.PathRequest;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.security.config.web.server.ServerHttpSecurity;
<span class="hljs-keyword">import</span> org.springframework.security.web.server.SecurityWebFilterChain;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.security.config.Customizer.withDefaults;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebFluxSecurityConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> SecurityWebFilterChain <span class="hljs-title">springSecurityFilterChain</span><span class="hljs-params">(ServerHttpSecurity http)</span> </span>{
		http.authorizeExchange((exchange) -&gt; {
			exchange.matchers(PathRequest.toStaticResources().atCommonLocations()).permitAll();
			exchange.pathMatchers(<span class="hljs-string">"/foo"</span>, <span class="hljs-string">"/bar"</span>).authenticated();
		});
		http.formLogin(withDefaults());
		<span class="hljs-keyword">return</span> http.build();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_1_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.security.reactive.PathRequest
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.security.config.Customizer.withDefaults
<span class="hljs-keyword">import</span> org.springframework.security.config.web.server.ServerHttpSecurity
<span class="hljs-keyword">import</span> org.springframework.security.web.server.SecurityWebFilterChain

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebFluxSecurityConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">springSecurityFilterChain</span><span class="hljs-params">(http: <span class="hljs-type">ServerHttpSecurity</span>)</span></span>: SecurityWebFilterChain {
		http.authorizeExchange { spec -&gt;
			spec.matchers(PathRequest.toStaticResources().atCommonLocations()).permitAll()
			spec.pathMatchers(<span class="hljs-string">"/foo"</span>, <span class="hljs-string">"/bar"</span>).authenticated()
		}
		http.formLogin(withDefaults())
		<span class="hljs-keyword">return</span> http.build()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="web.security.oauth2"><a class="anchor" href="#web.security.oauth2"></a>OAuth2</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="external" href="https://oauth.net/2/" target="_blank">OAuth2</a> is a widely used authorization framework that is supported by Spring.</p>
</div>
<div class="sect2">
<h3 id="web.security.oauth2.client"><a class="anchor" href="#web.security.oauth2.client"></a>Client</h3>
<div class="paragraph">
<p>If you have <code>spring-security-oauth2-client</code> on your classpath, you can take advantage of some auto-configuration to set up OAuth2/Open ID Connect clients.
This configuration makes use of the properties under <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/oauth2/client/OAuth2ClientProperties.html"><code>OAuth2ClientProperties</code></a>.
The same properties are applicable to both servlet and reactive applications.</p>
</div>
<div class="paragraph">
<p>You can register multiple OAuth2 clients and providers under the <code>spring.security.oauth2.client</code> prefix, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_2_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_2_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_2_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_properties" class="tabpanel" id="_tabs_2_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.security.oauth2.client.registration.my-login-client.client-id</span>=<span class="hljs-string">abcd</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-login-client.client-secret</span>=<span class="hljs-string">password</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-login-client.client-name</span>=<span class="hljs-string">Client for OpenID Connect</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-login-client.provider</span>=<span class="hljs-string">my-oauth-provider</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-login-client.scope</span>=<span class="hljs-string">openid,profile,email,phone,address</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-login-client.redirect-uri</span>=<span class="hljs-string">{baseUrl}/login/oauth2/code/{registrationId}</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-login-client.client-authentication-method</span>=<span class="hljs-string">client_secret_basic</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-login-client.authorization-grant-type</span>=<span class="hljs-string">authorization_code</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-1.client-id</span>=<span class="hljs-string">abcd</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-1.client-secret</span>=<span class="hljs-string">password</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-1.client-name</span>=<span class="hljs-string">Client for user scope</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-1.provider</span>=<span class="hljs-string">my-oauth-provider</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-1.scope</span>=<span class="hljs-string">user</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-1.redirect-uri</span>=<span class="hljs-string">{baseUrl}/authorized/user</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-1.client-authentication-method</span>=<span class="hljs-string">client_secret_basic</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-1.authorization-grant-type</span>=<span class="hljs-string">authorization_code</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-2.client-id</span>=<span class="hljs-string">abcd</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-2.client-secret</span>=<span class="hljs-string">password</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-2.client-name</span>=<span class="hljs-string">Client for email scope</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-2.provider</span>=<span class="hljs-string">my-oauth-provider</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-2.scope</span>=<span class="hljs-string">email</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-2.redirect-uri</span>=<span class="hljs-string">{baseUrl}/authorized/email</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-2.client-authentication-method</span>=<span class="hljs-string">client_secret_basic</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client-2.authorization-grant-type</span>=<span class="hljs-string">authorization_code</span>
<span class="hljs-meta">spring.security.oauth2.client.provider.my-oauth-provider.authorization-uri</span>=<span class="hljs-string">https://my-auth-server.com/oauth2/authorize</span>
<span class="hljs-meta">spring.security.oauth2.client.provider.my-oauth-provider.token-uri</span>=<span class="hljs-string">https://my-auth-server.com/oauth2/token</span>
<span class="hljs-meta">spring.security.oauth2.client.provider.my-oauth-provider.user-info-uri</span>=<span class="hljs-string">https://my-auth-server.com/userinfo</span>
<span class="hljs-meta">spring.security.oauth2.client.provider.my-oauth-provider.user-info-authentication-method</span>=<span class="hljs-string">header</span>
<span class="hljs-meta">spring.security.oauth2.client.provider.my-oauth-provider.jwk-set-uri</span>=<span class="hljs-string">https://my-auth-server.com/oauth2/jwks</span>
<span class="hljs-meta">spring.security.oauth2.client.provider.my-oauth-provider.user-name-attribute</span>=<span class="hljs-string">name</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_2_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">security:</span>
    <span class="hljs-attr">oauth2:</span>
      <span class="hljs-attr">client:</span>
        <span class="hljs-attr">registration:</span>
          <span class="hljs-attr">my-login-client:</span>
            <span class="hljs-attr">client-id:</span> <span class="hljs-string">"abcd"</span>
            <span class="hljs-attr">client-secret:</span> <span class="hljs-string">"password"</span>
            <span class="hljs-attr">client-name:</span> <span class="hljs-string">"Client for OpenID Connect"</span>
            <span class="hljs-attr">provider:</span> <span class="hljs-string">"my-oauth-provider"</span>
            <span class="hljs-attr">scope:</span> <span class="hljs-string">"openid,profile,email,phone,address"</span>
            <span class="hljs-attr">redirect-uri:</span> <span class="hljs-string">"{baseUrl}/login/oauth2/code/{registrationId}"</span>
            <span class="hljs-attr">client-authentication-method:</span> <span class="hljs-string">"client_secret_basic"</span>
            <span class="hljs-attr">authorization-grant-type:</span> <span class="hljs-string">"authorization_code"</span>

          <span class="hljs-attr">my-client-1:</span>
            <span class="hljs-attr">client-id:</span> <span class="hljs-string">"abcd"</span>
            <span class="hljs-attr">client-secret:</span> <span class="hljs-string">"password"</span>
            <span class="hljs-attr">client-name:</span> <span class="hljs-string">"Client for user scope"</span>
            <span class="hljs-attr">provider:</span> <span class="hljs-string">"my-oauth-provider"</span>
            <span class="hljs-attr">scope:</span> <span class="hljs-string">"user"</span>
            <span class="hljs-attr">redirect-uri:</span> <span class="hljs-string">"{baseUrl}/authorized/user"</span>
            <span class="hljs-attr">client-authentication-method:</span> <span class="hljs-string">"client_secret_basic"</span>
            <span class="hljs-attr">authorization-grant-type:</span> <span class="hljs-string">"authorization_code"</span>

          <span class="hljs-attr">my-client-2:</span>
            <span class="hljs-attr">client-id:</span> <span class="hljs-string">"abcd"</span>
            <span class="hljs-attr">client-secret:</span> <span class="hljs-string">"password"</span>
            <span class="hljs-attr">client-name:</span> <span class="hljs-string">"Client for email scope"</span>
            <span class="hljs-attr">provider:</span> <span class="hljs-string">"my-oauth-provider"</span>
            <span class="hljs-attr">scope:</span> <span class="hljs-string">"email"</span>
            <span class="hljs-attr">redirect-uri:</span> <span class="hljs-string">"{baseUrl}/authorized/email"</span>
            <span class="hljs-attr">client-authentication-method:</span> <span class="hljs-string">"client_secret_basic"</span>
            <span class="hljs-attr">authorization-grant-type:</span> <span class="hljs-string">"authorization_code"</span>

        <span class="hljs-attr">provider:</span>
          <span class="hljs-attr">my-oauth-provider:</span>
            <span class="hljs-attr">authorization-uri:</span> <span class="hljs-string">"https://my-auth-server.com/oauth2/authorize"</span>
            <span class="hljs-attr">token-uri:</span> <span class="hljs-string">"https://my-auth-server.com/oauth2/token"</span>
            <span class="hljs-attr">user-info-uri:</span> <span class="hljs-string">"https://my-auth-server.com/userinfo"</span>
            <span class="hljs-attr">user-info-authentication-method:</span> <span class="hljs-string">"header"</span>
            <span class="hljs-attr">jwk-set-uri:</span> <span class="hljs-string">"https://my-auth-server.com/oauth2/jwks"</span>
            <span class="hljs-attr">user-name-attribute:</span> <span class="hljs-string">"name"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>For OpenID Connect providers that support <a class="external" href="https://openid.net/specs/openid-connect-discovery-1_0.html" target="_blank">OpenID Connect discovery</a>, the configuration can be further simplified.
The provider needs to be configured with an <code>issuer-uri</code> which is the URI that it asserts as its Issuer Identifier.
For example, if the <code>issuer-uri</code> provided is "https://example.com", then an "OpenID Provider Configuration Request" will be made to "https://example.com/.well-known/openid-configuration".
The result is expected to be an "OpenID Provider Configuration Response".
The following example shows how an OpenID Connect Provider can be configured with the <code>issuer-uri</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_3_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_3_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_3_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_properties" class="tabpanel" id="_tabs_3_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.security.oauth2.client.provider.oidc-provider.issuer-uri</span>=<span class="hljs-string">https://dev-123456.oktapreview.com/oauth2/default/</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_3_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">security:</span>
    <span class="hljs-attr">oauth2:</span>
      <span class="hljs-attr">client:</span>
        <span class="hljs-attr">provider:</span>
          <span class="hljs-attr">oidc-provider:</span>
            <span class="hljs-attr">issuer-uri:</span> <span class="hljs-string">"https://dev-123456.oktapreview.com/oauth2/default/"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>By default, Spring Security’s <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/oauth2/client/web/OAuth2LoginAuthenticationFilter.html"><code>OAuth2LoginAuthenticationFilter</code></a> only processes URLs matching <code>/login/oauth2/code/*</code>.
If you want to customize the <code>redirect-uri</code> to use a different pattern, you need to provide configuration to process that custom pattern.
For example, for servlet applications, you can add your own <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/SecurityFilterChain.html"><code>SecurityFilterChain</code></a> that resembles the following:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_4_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_4_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_4_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_java" class="tabpanel" id="_tabs_4_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.security.config.annotation.web.builders.HttpSecurity;
<span class="hljs-keyword">import</span> org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
<span class="hljs-keyword">import</span> org.springframework.security.web.SecurityFilterChain;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-meta">@EnableWebSecurity</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyOAuthClientConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> SecurityFilterChain <span class="hljs-title">securityFilterChain</span><span class="hljs-params">(HttpSecurity http)</span> <span class="hljs-keyword">throws</span> Exception </span>{
		http
			.authorizeHttpRequests((requests) -&gt; requests
				.anyRequest().authenticated()
			)
			.oauth2Login((login) -&gt; login
				.redirectionEndpoint((endpoint) -&gt; endpoint
					.baseUri(<span class="hljs-string">"/login/oauth2/callback/*"</span>)
				)
			);
		<span class="hljs-keyword">return</span> http.build();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_4_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.security.config.<span class="hljs-keyword">annotation</span>.web.builders.HttpSecurity
<span class="hljs-keyword">import</span> org.springframework.security.config.<span class="hljs-keyword">annotation</span>.web.configuration.EnableWebSecurity
<span class="hljs-keyword">import</span> org.springframework.security.config.<span class="hljs-keyword">annotation</span>.web.invoke
<span class="hljs-keyword">import</span> org.springframework.security.web.SecurityFilterChain

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-meta">@EnableWebSecurity</span>
<span class="hljs-keyword">open</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyOAuthClientConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-keyword">open</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">securityFilterChain</span><span class="hljs-params">(http: <span class="hljs-type">HttpSecurity</span>)</span></span>: SecurityFilterChain {
		http {
			authorizeHttpRequests {
				authorize(anyRequest, authenticated)
			}
			oauth2Login {
				redirectionEndpoint {
					baseUri = <span class="hljs-string">"/login/oauth2/callback/*"</span>
				}
			}
		}
		<span class="hljs-keyword">return</span> http.build()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Spring Boot auto-configures an <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/oauth2/client/InMemoryOAuth2AuthorizedClientService.html"><code>InMemoryOAuth2AuthorizedClientService</code></a> which is used by Spring Security for the management of client registrations.
The <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/oauth2/client/InMemoryOAuth2AuthorizedClientService.html"><code>InMemoryOAuth2AuthorizedClientService</code></a> has limited capabilities and we recommend using it only for development environments.
For production environments, consider using a <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/oauth2/client/JdbcOAuth2AuthorizedClientService.html"><code>JdbcOAuth2AuthorizedClientService</code></a> or creating your own implementation of <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/oauth2/client/OAuth2AuthorizedClientService.html"><code>OAuth2AuthorizedClientService</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="sect3">
<h4 id="web.security.oauth2.client.common-providers"><a class="anchor" href="#web.security.oauth2.client.common-providers"></a>OAuth2 Client Registration for Common Providers</h4>
<div class="paragraph">
<p>For common OAuth2 and OpenID providers, including Google, Github, Facebook, and Okta, we provide a set of provider defaults (<code>google</code>, <code>github</code>, <code>facebook</code>, and <code>okta</code>, respectively).</p>
</div>
<div class="paragraph">
<p>If you do not need to customize these providers, you can set the <code>provider</code> attribute to the one for which you need to infer defaults.
Also, if the key for the client registration matches a default supported provider, Spring Boot infers that as well.</p>
</div>
<div class="paragraph">
<p>In other words, the two configurations in the following example use the Google provider:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_5_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_5_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_5_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_properties" class="tabpanel" id="_tabs_5_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.security.oauth2.client.registration.my-client.client-id</span>=<span class="hljs-string">abcd</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client.client-secret</span>=<span class="hljs-string">password</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.my-client.provider</span>=<span class="hljs-string">google</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.google.client-id</span>=<span class="hljs-string">abcd</span>
<span class="hljs-meta">spring.security.oauth2.client.registration.google.client-secret</span>=<span class="hljs-string">password</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_5_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">security:</span>
    <span class="hljs-attr">oauth2:</span>
      <span class="hljs-attr">client:</span>
        <span class="hljs-attr">registration:</span>
          <span class="hljs-attr">my-client:</span>
            <span class="hljs-attr">client-id:</span> <span class="hljs-string">"abcd"</span>
            <span class="hljs-attr">client-secret:</span> <span class="hljs-string">"password"</span>
            <span class="hljs-attr">provider:</span> <span class="hljs-string">"google"</span>
          <span class="hljs-attr">google:</span>
            <span class="hljs-attr">client-id:</span> <span class="hljs-string">"abcd"</span>
            <span class="hljs-attr">client-secret:</span> <span class="hljs-string">"password"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="web.security.oauth2.server"><a class="anchor" href="#web.security.oauth2.server"></a>Resource Server</h3>
<div class="paragraph">
<p>If you have <code>spring-security-oauth2-resource-server</code> on your classpath, Spring Boot can set up an OAuth2 Resource Server.
For JWT configuration, a JWK Set URI or OIDC Issuer URI needs to be specified, as shown in the following examples:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_6_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_6_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_6_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_properties" class="tabpanel" id="_tabs_6_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.security.oauth2.resourceserver.jwt.jwk-set-uri</span>=<span class="hljs-string">https://example.com/oauth2/default/v1/keys</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_6_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">security:</span>
    <span class="hljs-attr">oauth2:</span>
      <span class="hljs-attr">resourceserver:</span>
        <span class="hljs-attr">jwt:</span>
          <span class="hljs-attr">jwk-set-uri:</span> <span class="hljs-string">"https://example.com/oauth2/default/v1/keys"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_7_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_7_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_7_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_properties" class="tabpanel" id="_tabs_7_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.security.oauth2.resourceserver.jwt.issuer-uri</span>=<span class="hljs-string">https://dev-123456.oktapreview.com/oauth2/default/</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_7_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">security:</span>
    <span class="hljs-attr">oauth2:</span>
      <span class="hljs-attr">resourceserver:</span>
        <span class="hljs-attr">jwt:</span>
          <span class="hljs-attr">issuer-uri:</span> <span class="hljs-string">"https://dev-123456.oktapreview.com/oauth2/default/"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If the authorization server does not support a JWK Set URI, you can configure the resource server with the Public Key used for verifying the signature of the JWT.
This can be done using the <code>spring.security.oauth2.resourceserver.jwt.public-key-location</code> property, where the value needs to point to a file containing the public key in the PEM-encoded x509 format.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The <code>spring.security.oauth2.resourceserver.jwt.audiences</code> property can be used to specify the expected values of the aud claim in JWTs.
For example, to require JWTs to contain an aud claim with the value <code>my-audience</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_8_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_8_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_8_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_properties" class="tabpanel" id="_tabs_8_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.security.oauth2.resourceserver.jwt.audiences[0]</span>=<span class="hljs-string">my-audience</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_8_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">security:</span>
    <span class="hljs-attr">oauth2:</span>
      <span class="hljs-attr">resourceserver:</span>
        <span class="hljs-attr">jwt:</span>
          <span class="hljs-attr">audiences:</span>
            <span class="hljs-bullet">-</span> <span class="hljs-string">"my-audience"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The same properties are applicable for both servlet and reactive applications.
Alternatively, you can define your own <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/oauth2/jwt/JwtDecoder.html"><code>JwtDecoder</code></a> bean for servlet applications or a <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/oauth2/jwt/ReactiveJwtDecoder.html"><code>ReactiveJwtDecoder</code></a> for reactive applications.</p>
</div>
<div class="paragraph">
<p>In cases where opaque tokens are used instead of JWTs, you can configure the following properties to validate tokens through introspection:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_9">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_9_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_9_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_9_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_9_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_9_properties" class="tabpanel" id="_tabs_9_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.security.oauth2.resourceserver.opaquetoken.introspection-uri</span>=<span class="hljs-string">https://example.com/check-token</span>
<span class="hljs-meta">spring.security.oauth2.resourceserver.opaquetoken.client-id</span>=<span class="hljs-string">my-client-id</span>
<span class="hljs-meta">spring.security.oauth2.resourceserver.opaquetoken.client-secret</span>=<span class="hljs-string">my-client-secret</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_9_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_9_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">security:</span>
    <span class="hljs-attr">oauth2:</span>
      <span class="hljs-attr">resourceserver:</span>
        <span class="hljs-attr">opaquetoken:</span>
          <span class="hljs-attr">introspection-uri:</span> <span class="hljs-string">"https://example.com/check-token"</span>
          <span class="hljs-attr">client-id:</span> <span class="hljs-string">"my-client-id"</span>
          <span class="hljs-attr">client-secret:</span> <span class="hljs-string">"my-client-secret"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Again, the same properties are applicable for both servlet and reactive applications.
Alternatively, you can define your own <a class="apiref" href="https://docs.spring.io/spring-authorization-server/docs/1.4.x/api/org/springframework/security/oauth2/server/resource/introspection/OpaqueTokenIntrospector.html"><code>OpaqueTokenIntrospector</code></a> bean for servlet applications or a <a class="apiref" href="https://docs.spring.io/spring-authorization-server/docs/1.4.x/api/org/springframework/security/oauth2/server/resource/introspection/ReactiveOpaqueTokenIntrospector.html"><code>ReactiveOpaqueTokenIntrospector</code></a> for reactive applications.</p>
</div>
</div>
<div class="sect2">
<h3 id="web.security.oauth2.authorization-server"><a class="anchor" href="#web.security.oauth2.authorization-server"></a>Authorization Server</h3>
<div class="paragraph">
<p>If you have <code>spring-security-oauth2-authorization-server</code> on your classpath, you can take advantage of some auto-configuration to set up a Servlet-based OAuth2 Authorization Server.</p>
</div>
<div class="paragraph">
<p>You can register multiple OAuth2 clients under the <code>spring.security.oauth2.authorizationserver.client</code> prefix, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_10">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_10_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_10_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_10_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_10_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_10_properties" class="tabpanel" id="_tabs_10_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.registration.client-id</span>=<span class="hljs-string">abcd</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.registration.client-secret</span>=<span class="hljs-string">{noop}secret1</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.registration.client-authentication-methods[0]</span>=<span class="hljs-string">client_secret_basic</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.registration.authorization-grant-types[0]</span>=<span class="hljs-string">authorization_code</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.registration.authorization-grant-types[1]</span>=<span class="hljs-string">refresh_token</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.registration.redirect-uris[0]</span>=<span class="hljs-string">https://my-client-1.com/login/oauth2/code/abcd</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.registration.redirect-uris[1]</span>=<span class="hljs-string">https://my-client-1.com/authorized</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.registration.scopes[0]</span>=<span class="hljs-string">openid</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.registration.scopes[1]</span>=<span class="hljs-string">profile</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.registration.scopes[2]</span>=<span class="hljs-string">email</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.registration.scopes[3]</span>=<span class="hljs-string">phone</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.registration.scopes[4]</span>=<span class="hljs-string">address</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.require-authorization-consent</span>=<span class="hljs-string">true</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.token.authorization-code-time-to-live</span>=<span class="hljs-string">5m</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.token.access-token-time-to-live</span>=<span class="hljs-string">10m</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.token.access-token-format</span>=<span class="hljs-string">reference</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.token.reuse-refresh-tokens</span>=<span class="hljs-string">false</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-1.token.refresh-token-time-to-live</span>=<span class="hljs-string">30m</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-2.registration.client-id</span>=<span class="hljs-string">efgh</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-2.registration.client-secret</span>=<span class="hljs-string">{noop}secret2</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-2.registration.client-authentication-methods[0]</span>=<span class="hljs-string">client_secret_jwt</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-2.registration.authorization-grant-types[0]</span>=<span class="hljs-string">client_credentials</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-2.registration.scopes[0]</span>=<span class="hljs-string">user.read</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-2.registration.scopes[1]</span>=<span class="hljs-string">user.write</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-2.jwk-set-uri</span>=<span class="hljs-string">https://my-client-2.com/jwks</span>
<span class="hljs-meta">spring.security.oauth2.authorizationserver.client.my-client-2.token-endpoint-authentication-signing-algorithm</span>=<span class="hljs-string">RS256</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_10_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_10_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">security:</span>
    <span class="hljs-attr">oauth2:</span>
      <span class="hljs-attr">authorizationserver:</span>
        <span class="hljs-attr">client:</span>
          <span class="hljs-attr">my-client-1:</span>
            <span class="hljs-attr">registration:</span>
              <span class="hljs-attr">client-id:</span> <span class="hljs-string">"abcd"</span>
              <span class="hljs-attr">client-secret:</span> <span class="hljs-string">"{noop}secret1"</span>
              <span class="hljs-attr">client-authentication-methods:</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"client_secret_basic"</span>
              <span class="hljs-attr">authorization-grant-types:</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"authorization_code"</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"refresh_token"</span>
              <span class="hljs-attr">redirect-uris:</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"https://my-client-1.com/login/oauth2/code/abcd"</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"https://my-client-1.com/authorized"</span>
              <span class="hljs-attr">scopes:</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"openid"</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"profile"</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"email"</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"phone"</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"address"</span>
            <span class="hljs-attr">require-authorization-consent:</span> <span class="hljs-literal">true</span>
            <span class="hljs-attr">token:</span>
              <span class="hljs-attr">authorization-code-time-to-live:</span> <span class="hljs-string">5m</span>
              <span class="hljs-attr">access-token-time-to-live:</span> <span class="hljs-string">10m</span>
              <span class="hljs-attr">access-token-format:</span> <span class="hljs-string">"reference"</span>
              <span class="hljs-attr">reuse-refresh-tokens:</span> <span class="hljs-literal">false</span>
              <span class="hljs-attr">refresh-token-time-to-live:</span> <span class="hljs-string">30m</span>
          <span class="hljs-attr">my-client-2:</span>
            <span class="hljs-attr">registration:</span>
              <span class="hljs-attr">client-id:</span> <span class="hljs-string">"efgh"</span>
              <span class="hljs-attr">client-secret:</span> <span class="hljs-string">"{noop}secret2"</span>
              <span class="hljs-attr">client-authentication-methods:</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"client_secret_jwt"</span>
              <span class="hljs-attr">authorization-grant-types:</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"client_credentials"</span>
              <span class="hljs-attr">scopes:</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"user.read"</span>
                <span class="hljs-bullet">-</span> <span class="hljs-string">"user.write"</span>
            <span class="hljs-attr">jwk-set-uri:</span> <span class="hljs-string">"https://my-client-2.com/jwks"</span>
            <span class="hljs-attr">token-endpoint-authentication-signing-algorithm:</span> <span class="hljs-string">"RS256"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <code>client-secret</code> property must be in a format that can be matched by the configured <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/crypto/password/PasswordEncoder.html"><code>PasswordEncoder</code></a>.
The default instance of <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/crypto/password/PasswordEncoder.html"><code>PasswordEncoder</code></a> is created via <code>PasswordEncoderFactories.createDelegatingPasswordEncoder()</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The auto-configuration Spring Boot provides for Spring Authorization Server is designed for getting started quickly.
Most applications will require customization and will want to define several beans to override auto-configuration.</p>
</div>
<div class="paragraph">
<p>The following components can be defined as beans to override auto-configuration specific to Spring Authorization Server:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a class="apiref" href="https://docs.spring.io/spring-authorization-server/docs/1.4.x/api/org/springframework/security/oauth2/server/authorization/client/RegisteredClientRepository.html"><code>RegisteredClientRepository</code></a></p>
</li>
<li>
<p><a class="apiref" href="https://docs.spring.io/spring-authorization-server/docs/1.4.x/api/org/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings.html"><code>AuthorizationServerSettings</code></a></p>
</li>
<li>
<p><a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/SecurityFilterChain.html"><code>SecurityFilterChain</code></a></p>
</li>
<li>
<p><code>com.nimbusds.jose.jwk.source.JWKSource&lt;com.nimbusds.jose.proc.SecurityContext&gt;</code></p>
</li>
<li>
<p><a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/oauth2/jwt/JwtDecoder.html"><code>JwtDecoder</code></a></p>
</li>
</ul>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Spring Boot auto-configures an <a class="apiref" href="https://docs.spring.io/spring-authorization-server/docs/1.4.x/api/org/springframework/security/oauth2/server/authorization/client/InMemoryRegisteredClientRepository.html"><code>InMemoryRegisteredClientRepository</code></a> which is used by Spring Authorization Server for the management of registered clients.
The <a class="apiref" href="https://docs.spring.io/spring-authorization-server/docs/1.4.x/api/org/springframework/security/oauth2/server/authorization/client/InMemoryRegisteredClientRepository.html"><code>InMemoryRegisteredClientRepository</code></a> has limited capabilities and we recommend using it only for development environments.
For production environments, consider using a <a class="apiref" href="https://docs.spring.io/spring-authorization-server/docs/1.4.x/api/org/springframework/security/oauth2/server/authorization/client/JdbcRegisteredClientRepository.html"><code>JdbcRegisteredClientRepository</code></a> or creating your own implementation of <a class="apiref" href="https://docs.spring.io/spring-authorization-server/docs/1.4.x/api/org/springframework/security/oauth2/server/authorization/client/RegisteredClientRepository.html"><code>RegisteredClientRepository</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Additional information can be found in the <a href="https://docs.spring.io/spring-authorization-server/reference/1.4/getting-started.html">Getting Started</a> chapter of the <a href="https://docs.spring.io/spring-authorization-server/reference/1.4">Spring Authorization Server Reference Guide</a>.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="web.security.saml2"><a class="anchor" href="#web.security.saml2"></a>SAML 2.0</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="web.security.saml2.relying-party"><a class="anchor" href="#web.security.saml2.relying-party"></a>Relying Party</h3>
<div class="paragraph">
<p>If you have <code>spring-security-saml2-service-provider</code> on your classpath, you can take advantage of some auto-configuration to set up a SAML 2.0 Relying Party.
This configuration makes use of the properties under <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/saml2/Saml2RelyingPartyProperties.html"><code>Saml2RelyingPartyProperties</code></a>.</p>
</div>
<div class="paragraph">
<p>A relying party registration represents a paired configuration between an Identity Provider, IDP, and a Service Provider, SP.
You can register multiple relying parties under the <code>spring.security.saml2.relyingparty</code> prefix, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_11">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_11_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_11_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_11_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_11_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_11_properties" class="tabpanel" id="_tabs_11_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party1.signing.credentials[0].private-key-location</span>=<span class="hljs-string">path-to-private-key</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party1.signing.credentials[0].certificate-location</span>=<span class="hljs-string">path-to-certificate</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party1.decryption.credentials[0].private-key-location</span>=<span class="hljs-string">path-to-private-key</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party1.decryption.credentials[0].certificate-location</span>=<span class="hljs-string">path-to-certificate</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party1.singlelogout.url</span>=<span class="hljs-string">https://myapp/logout/saml2/slo</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party1.singlelogout.response-url</span>=<span class="hljs-string">https://remoteidp2.slo.url</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party1.singlelogout.binding</span>=<span class="hljs-string">POST</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party1.assertingparty.verification.credentials[0].certificate-location</span>=<span class="hljs-string">path-to-verification-cert</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party1.assertingparty.entity-id</span>=<span class="hljs-string">remote-idp-entity-id1</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party1.assertingparty.sso-url</span>=<span class="hljs-string">https://remoteidp1.sso.url</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party2.signing.credentials[0].private-key-location</span>=<span class="hljs-string">path-to-private-key</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party2.signing.credentials[0].certificate-location</span>=<span class="hljs-string">path-to-certificate</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party2.decryption.credentials[0].private-key-location</span>=<span class="hljs-string">path-to-private-key</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party2.decryption.credentials[0].certificate-location</span>=<span class="hljs-string">path-to-certificate</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party2.assertingparty.verification.credentials[0].certificate-location</span>=<span class="hljs-string">path-to-other-verification-cert</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party2.assertingparty.entity-id</span>=<span class="hljs-string">remote-idp-entity-id2</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party2.assertingparty.sso-url</span>=<span class="hljs-string">https://remoteidp2.sso.url</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party2.assertingparty.singlelogout.url</span>=<span class="hljs-string">https://remoteidp2.slo.url</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party2.assertingparty.singlelogout.response-url</span>=<span class="hljs-string">https://myapp/logout/saml2/slo</span>
<span class="hljs-meta">spring.security.saml2.relyingparty.registration.my-relying-party2.assertingparty.singlelogout.binding</span>=<span class="hljs-string">POST</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_11_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_11_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">security:</span>
    <span class="hljs-attr">saml2:</span>
      <span class="hljs-attr">relyingparty:</span>
        <span class="hljs-attr">registration:</span>
          <span class="hljs-attr">my-relying-party1:</span>
            <span class="hljs-attr">signing:</span>
              <span class="hljs-attr">credentials:</span>
              <span class="hljs-bullet">-</span> <span class="hljs-attr">private-key-location:</span> <span class="hljs-string">"path-to-private-key"</span>
                <span class="hljs-attr">certificate-location:</span> <span class="hljs-string">"path-to-certificate"</span>
            <span class="hljs-attr">decryption:</span>
              <span class="hljs-attr">credentials:</span>
              <span class="hljs-bullet">-</span> <span class="hljs-attr">private-key-location:</span> <span class="hljs-string">"path-to-private-key"</span>
                <span class="hljs-attr">certificate-location:</span> <span class="hljs-string">"path-to-certificate"</span>
            <span class="hljs-attr">singlelogout:</span>
               <span class="hljs-attr">url:</span> <span class="hljs-string">"https://myapp/logout/saml2/slo"</span>
               <span class="hljs-attr">response-url:</span> <span class="hljs-string">"https://remoteidp2.slo.url"</span>
               <span class="hljs-attr">binding:</span> <span class="hljs-string">"POST"</span>
            <span class="hljs-attr">assertingparty:</span>
              <span class="hljs-attr">verification:</span>
                <span class="hljs-attr">credentials:</span>
                <span class="hljs-bullet">-</span> <span class="hljs-attr">certificate-location:</span> <span class="hljs-string">"path-to-verification-cert"</span>
              <span class="hljs-attr">entity-id:</span> <span class="hljs-string">"remote-idp-entity-id1"</span>
              <span class="hljs-attr">sso-url:</span> <span class="hljs-string">"https://remoteidp1.sso.url"</span>

          <span class="hljs-attr">my-relying-party2:</span>
            <span class="hljs-attr">signing:</span>
              <span class="hljs-attr">credentials:</span>
              <span class="hljs-bullet">-</span> <span class="hljs-attr">private-key-location:</span> <span class="hljs-string">"path-to-private-key"</span>
                <span class="hljs-attr">certificate-location:</span> <span class="hljs-string">"path-to-certificate"</span>
            <span class="hljs-attr">decryption:</span>
              <span class="hljs-attr">credentials:</span>
              <span class="hljs-bullet">-</span> <span class="hljs-attr">private-key-location:</span> <span class="hljs-string">"path-to-private-key"</span>
                <span class="hljs-attr">certificate-location:</span> <span class="hljs-string">"path-to-certificate"</span>
            <span class="hljs-attr">assertingparty:</span>
              <span class="hljs-attr">verification:</span>
                <span class="hljs-attr">credentials:</span>
                <span class="hljs-bullet">-</span> <span class="hljs-attr">certificate-location:</span> <span class="hljs-string">"path-to-other-verification-cert"</span>
              <span class="hljs-attr">entity-id:</span> <span class="hljs-string">"remote-idp-entity-id2"</span>
              <span class="hljs-attr">sso-url:</span> <span class="hljs-string">"https://remoteidp2.sso.url"</span>
              <span class="hljs-attr">singlelogout:</span>
                <span class="hljs-attr">url:</span> <span class="hljs-string">"https://remoteidp2.slo.url"</span>
                <span class="hljs-attr">response-url:</span> <span class="hljs-string">"https://myapp/logout/saml2/slo"</span>
                <span class="hljs-attr">binding:</span> <span class="hljs-string">"POST"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>For SAML2 logout, by default, Spring Security’s <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/saml2/provider/service/web/authentication/logout/Saml2LogoutRequestFilter.html"><code>Saml2LogoutRequestFilter</code></a> and <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/saml2/provider/service/web/authentication/logout/Saml2LogoutResponseFilter.html"><code>Saml2LogoutResponseFilter</code></a> only process URLs matching <code>/logout/saml2/slo</code>.
If you want to customize the <code>url</code> to which AP-initiated logout requests get sent to or the <code>response-url</code> to which an AP sends logout responses to, to use a different pattern, you need to provide configuration to process that custom pattern.
For example, for servlet applications, you can add your own <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/SecurityFilterChain.html"><code>SecurityFilterChain</code></a> that resembles the following:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.security.config.annotation.web.builders.HttpSecurity;
<span class="hljs-keyword">import</span> org.springframework.security.web.SecurityFilterChain;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.security.config.Customizer.withDefaults;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MySamlRelyingPartyConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> SecurityFilterChain <span class="hljs-title">securityFilterChain</span><span class="hljs-params">(HttpSecurity http)</span> <span class="hljs-keyword">throws</span> Exception </span>{
		http.authorizeHttpRequests((requests) -&gt; requests.anyRequest().authenticated());
		http.saml2Login(withDefaults());
		http.saml2Logout((saml2) -&gt; saml2.logoutRequest((request) -&gt; request.logoutUrl(<span class="hljs-string">"/SLOService.saml2"</span>))
			.logoutResponse((response) -&gt; response.logoutUrl(<span class="hljs-string">"/SLOService.saml2"</span>)));
		<span class="hljs-keyword">return</span> http.build();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="graceful-shutdown.html">Graceful Shutdown</a></span>
<span class="next"><a href="spring-session.html">Spring Session</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../reference/web/spring-security.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="spring-security.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../3.3/reference/web/spring-security.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../4.0-SNAPSHOT/reference/web/spring-security.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.5-SNAPSHOT/reference/web/spring-security.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.4-SNAPSHOT/reference/web/spring-security.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.3-SNAPSHOT/reference/web/spring-security.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>