<!DOCTYPE html>
<html><head><title>SQL Databases :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/reference/data/sql.html"/><meta content="2025-06-04T15:41:45.962065" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Data</a>
<ul class="nav-list">
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="SQL Databases">
<div class="toc-menu"><h3>SQL Databases</h3><ul><li data-level="1"><a href="#data.sql.datasource">Configure a DataSource</a></li><li data-level="2"><a href="#data.sql.datasource.embedded">Embedded Database Support</a></li><li data-level="2"><a href="#data.sql.datasource.production">Connection to a Production Database</a></li><li data-level="2"><a href="#data.sql.datasource.configuration">DataSource Configuration</a></li><li data-level="2"><a href="#data.sql.datasource.connection-pool">Supported Connection Pools</a></li><li data-level="2"><a href="#data.sql.datasource.jndi">Connection to a JNDI DataSource</a></li><li data-level="1"><a href="#data.sql.jdbc-template">Using JdbcTemplate</a></li><li data-level="1"><a href="#data.sql.jdbc-client">Using JdbcClient</a></li><li data-level="1"><a href="#data.sql.jpa-and-spring-data">JPA and Spring Data JPA</a></li><li data-level="2"><a href="#data.sql.jpa-and-spring-data.entity-classes">Entity Classes</a></li><li data-level="2"><a href="#data.sql.jpa-and-spring-data.repositories">Spring Data JPA Repositories</a></li><li data-level="2"><a href="#data.sql.jpa-and-spring-data.envers-repositories">Spring Data Envers Repositories</a></li><li data-level="2"><a href="#data.sql.jpa-and-spring-data.creating-and-dropping">Creating and Dropping JPA Databases</a></li><li data-level="2"><a href="#data.sql.jpa-and-spring-data.open-entity-manager-in-view">Open EntityManager in View</a></li><li data-level="1"><a href="#data.sql.jdbc">Spring Data JDBC</a></li><li data-level="1"><a href="#data.sql.h2-web-console">Using H2’s Web Console</a></li><li data-level="2"><a href="#data.sql.h2-web-console.custom-path">Changing the H2 Console’s Path</a></li><li data-level="2"><a href="#data.sql.h2-web-console.spring-security">Accessing the H2 Console in a Secured Application</a></li><li data-level="1"><a href="#data.sql.jooq">Using jOOQ</a></li><li data-level="2"><a href="#data.sql.jooq.codegen">Code Generation</a></li><li data-level="2"><a href="#data.sql.jooq.dslcontext">Using DSLContext</a></li><li data-level="2"><a href="#data.sql.jooq.sqldialect">jOOQ SQL Dialect</a></li><li data-level="2"><a href="#data.sql.jooq.customizing">Customizing jOOQ</a></li><li data-level="1"><a href="#data.sql.r2dbc">Using R2DBC</a></li><li data-level="2"><a href="#data.sql.r2dbc.embedded">Embedded Database Support</a></li><li data-level="2"><a href="#data.sql.r2dbc.using-database-client">Using DatabaseClient</a></li><li data-level="2"><a href="#data.sql.r2dbc.repositories">Spring Data R2DBC Repositories</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/data/sql.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../index.html">Spring Boot</a></li>
<li><a href="../index.html">Reference</a></li>
<li><a href="index.html">Data</a></li>
<li><a href="sql.html">SQL Databases</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../reference/data/sql.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">SQL Databases</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>SQL Databases</h3><ul><li data-level="1"><a href="#data.sql.datasource">Configure a DataSource</a></li><li data-level="2"><a href="#data.sql.datasource.embedded">Embedded Database Support</a></li><li data-level="2"><a href="#data.sql.datasource.production">Connection to a Production Database</a></li><li data-level="2"><a href="#data.sql.datasource.configuration">DataSource Configuration</a></li><li data-level="2"><a href="#data.sql.datasource.connection-pool">Supported Connection Pools</a></li><li data-level="2"><a href="#data.sql.datasource.jndi">Connection to a JNDI DataSource</a></li><li data-level="1"><a href="#data.sql.jdbc-template">Using JdbcTemplate</a></li><li data-level="1"><a href="#data.sql.jdbc-client">Using JdbcClient</a></li><li data-level="1"><a href="#data.sql.jpa-and-spring-data">JPA and Spring Data JPA</a></li><li data-level="2"><a href="#data.sql.jpa-and-spring-data.entity-classes">Entity Classes</a></li><li data-level="2"><a href="#data.sql.jpa-and-spring-data.repositories">Spring Data JPA Repositories</a></li><li data-level="2"><a href="#data.sql.jpa-and-spring-data.envers-repositories">Spring Data Envers Repositories</a></li><li data-level="2"><a href="#data.sql.jpa-and-spring-data.creating-and-dropping">Creating and Dropping JPA Databases</a></li><li data-level="2"><a href="#data.sql.jpa-and-spring-data.open-entity-manager-in-view">Open EntityManager in View</a></li><li data-level="1"><a href="#data.sql.jdbc">Spring Data JDBC</a></li><li data-level="1"><a href="#data.sql.h2-web-console">Using H2’s Web Console</a></li><li data-level="2"><a href="#data.sql.h2-web-console.custom-path">Changing the H2 Console’s Path</a></li><li data-level="2"><a href="#data.sql.h2-web-console.spring-security">Accessing the H2 Console in a Secured Application</a></li><li data-level="1"><a href="#data.sql.jooq">Using jOOQ</a></li><li data-level="2"><a href="#data.sql.jooq.codegen">Code Generation</a></li><li data-level="2"><a href="#data.sql.jooq.dslcontext">Using DSLContext</a></li><li data-level="2"><a href="#data.sql.jooq.sqldialect">jOOQ SQL Dialect</a></li><li data-level="2"><a href="#data.sql.jooq.customizing">Customizing jOOQ</a></li><li data-level="1"><a href="#data.sql.r2dbc">Using R2DBC</a></li><li data-level="2"><a href="#data.sql.r2dbc.embedded">Embedded Database Support</a></li><li data-level="2"><a href="#data.sql.r2dbc.using-database-client">Using DatabaseClient</a></li><li data-level="2"><a href="#data.sql.r2dbc.repositories">Spring Data R2DBC Repositories</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>The <a class="external" href="https://spring.io/projects/spring-framework" target="_blank">Spring Framework</a> provides extensive support for working with SQL databases, from direct JDBC access using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/simple/JdbcClient.html"><code>JdbcClient</code></a> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/JdbcTemplate.html"><code>JdbcTemplate</code></a> to complete “object relational mapping” technologies such as Hibernate.
<a class="external" href="https://spring.io/projects/spring-data" target="_blank">Spring Data</a> provides an additional level of functionality: creating <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> implementations directly from interfaces and using conventions to generate queries from your method names.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.sql.datasource"><a class="anchor" href="#data.sql.datasource"></a>Configure a DataSource</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Java’s <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> interface provides a standard method of working with database connections.
Traditionally, a <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> uses a <code>URL</code> along with some credentials to establish a database connection.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
See the <a class="xref page" href="../../how-to/data-access.html#howto.data-access.configure-custom-datasource">Configure a Custom DataSource</a> section of the “How-to Guides” for more advanced examples, typically to take full control over the configuration of the DataSource.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="data.sql.datasource.embedded"><a class="anchor" href="#data.sql.datasource.embedded"></a>Embedded Database Support</h3>
<div class="paragraph">
<p>It is often convenient to develop applications by using an in-memory embedded database.
Obviously, in-memory databases do not provide persistent storage.
You need to populate your database when your application starts and be prepared to throw away data when your application ends.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The “How-to Guides” section includes a <a class="xref page" href="../../how-to/data-initialization.html">section on how to initialize a database</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Spring Boot can auto-configure embedded <a class="external" href="https://www.h2database.com" target="_blank">H2</a>, <a class="external" href="https://hsqldb.org/" target="_blank">HSQL</a>, and <a class="external" href="https://db.apache.org/derby/" target="_blank">Derby</a> databases.
You need not provide any connection URLs.
You need only include a build dependency to the embedded database that you want to use.
If there are multiple embedded databases on the classpath, set the <code>spring.datasource.embedded-database-connection</code> configuration property to control which one is used.
Setting the property to <code>none</code> disables auto-configuration of an embedded database.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>If you are using this feature in your tests, you may notice that the same database is reused by your whole test suite regardless of the number of application contexts that you use.
If you want to make sure that each context has a separate embedded database, you should set <code>spring.datasource.generate-unique-name</code> to <code>true</code>.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>For example, the typical POM dependencies would be as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-starter-data-jpa<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span>
<span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.hsqldb<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>hsqldb<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">scope</span>&gt;</span>runtime<span class="hljs-tag">&lt;/<span class="hljs-name">scope</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
You need a dependency on <code>spring-jdbc</code> for an embedded database to be auto-configured.
In this example, it is pulled in transitively through <code>spring-boot-starter-data-jpa</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If, for whatever reason, you do configure the connection URL for an embedded database, take care to ensure that the database’s automatic shutdown is disabled.
If you use H2, you should use <code>DB_CLOSE_ON_EXIT=FALSE</code> to do so.
If you use HSQLDB, you should ensure that <code>shutdown=true</code> is not used.
Disabling the database’s automatic shutdown lets Spring Boot control when the database is closed, thereby ensuring that it happens once access to the database is no longer needed.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.datasource.production"><a class="anchor" href="#data.sql.datasource.production"></a>Connection to a Production Database</h3>
<div class="paragraph">
<p>Production database connections can also be auto-configured by using a pooling <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.datasource.configuration"><a class="anchor" href="#data.sql.datasource.configuration"></a>DataSource Configuration</h3>
<div class="paragraph">
<p>DataSource configuration is controlled by external configuration properties in <code>spring.datasource.*</code>.
For example, you might declare the following section in <code>application.properties</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_1_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_1_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_1_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_properties" class="tabpanel" id="_tabs_1_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.datasource.url</span>=<span class="hljs-string">***************************</span>
<span class="hljs-meta">spring.datasource.username</span>=<span class="hljs-string">dbuser</span>
<span class="hljs-meta">spring.datasource.password</span>=<span class="hljs-string">dbpass</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_1_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">datasource:</span>
    <span class="hljs-attr">url:</span> <span class="hljs-string">"***************************"</span>
    <span class="hljs-attr">username:</span> <span class="hljs-string">"dbuser"</span>
    <span class="hljs-attr">password:</span> <span class="hljs-string">"dbpass"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
You should at least specify the URL by setting the <code>spring.datasource.url</code> property.
Otherwise, Spring Boot tries to auto-configure an embedded database.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Spring Boot can deduce the JDBC driver class for most databases from the URL.
If you need to specify a specific class, you can use the <code>spring.datasource.driver-class-name</code> property.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
For a pooling <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> to be created, we need to be able to verify that a valid <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/java/sql/Driver.html" target="_blank"><code>Driver</code></a> class is available, so we check for that before doing anything.
In other words, if you set <code>spring.datasource.driver-class-name=com.mysql.jdbc.Driver</code>, then that class has to be loadable.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>See <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jdbc/DataSourceProperties.html"><code>DataSourceProperties</code></a> API documentation for more of the supported options.
These are the standard options that work regardless of <a href="#data.sql.datasource.connection-pool">the actual implementation</a>.
It is also possible to fine-tune implementation-specific settings by using their respective prefix (<code>spring.datasource.hikari.*</code>, <code>spring.datasource.tomcat.*</code>, <code>spring.datasource.dbcp2.*</code>, and <code>spring.datasource.oracleucp.*</code>).
See the documentation of the connection pool implementation you are using for more details.</p>
</div>
<div class="paragraph">
<p>For instance, if you use the <a class="external" href="https://tomcat.apache.org/tomcat-10.1-doc/jdbc-pool.html#Common_Attributes" target="_blank">Tomcat connection pool</a>, you could customize many additional settings, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_2_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_2_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_2_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_properties" class="tabpanel" id="_tabs_2_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.datasource.tomcat.max-wait</span>=<span class="hljs-string">10000</span>
<span class="hljs-meta">spring.datasource.tomcat.max-active</span>=<span class="hljs-string">50</span>
<span class="hljs-meta">spring.datasource.tomcat.test-on-borrow</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_2_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">datasource:</span>
    <span class="hljs-attr">tomcat:</span>
      <span class="hljs-attr">max-wait:</span> <span class="hljs-number">10000</span>
      <span class="hljs-attr">max-active:</span> <span class="hljs-number">50</span>
      <span class="hljs-attr">test-on-borrow:</span> <span class="hljs-literal">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>This will set the pool to wait 10000ms before throwing an exception if no connection is available, limit the maximum number of connections to 50 and validate the connection before borrowing it from the pool.</p>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.datasource.connection-pool"><a class="anchor" href="#data.sql.datasource.connection-pool"></a>Supported Connection Pools</h3>
<div class="paragraph">
<p>Spring Boot uses the following algorithm for choosing a specific implementation:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>We prefer <a class="external" href="https://github.com/brettwooldridge/HikariCP" target="_blank">HikariCP</a> for its performance and concurrency.
If HikariCP is available, we always choose it.</p>
</li>
<li>
<p>Otherwise, if the Tomcat pooling <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> is available, we use it.</p>
</li>
<li>
<p>Otherwise, if <a class="external" href="https://commons.apache.org/proper/commons-dbcp/" target="_blank">Commons DBCP2</a> is available, we use it.</p>
</li>
<li>
<p>If none of HikariCP, Tomcat, and DBCP2 are available and if Oracle UCP is available, we use it.</p>
</li>
</ol>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If you use the <code>spring-boot-starter-jdbc</code> or <code>spring-boot-starter-data-jpa</code> starters, you automatically get a dependency to HikariCP.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>You can bypass that algorithm completely and specify the connection pool to use by setting the <code>spring.datasource.type</code> property.
This is especially important if you run your application in a Tomcat container, as <code>tomcat-jdbc</code> is provided by default.</p>
</div>
<div class="paragraph">
<p>Additional connection pools can always be configured manually, using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jdbc/DataSourceBuilder.html"><code>DataSourceBuilder</code></a>.
If you define your own <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> bean, auto-configuration does not occur.
The following connection pools are supported by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jdbc/DataSourceBuilder.html"><code>DataSourceBuilder</code></a>:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>HikariCP</p>
</li>
<li>
<p>Tomcat pooling <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a></p>
</li>
<li>
<p>Commons DBCP2</p>
</li>
<li>
<p>Oracle UCP &amp; <code>OracleDataSource</code></p>
</li>
<li>
<p>Spring Framework’s <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/datasource/SimpleDriverDataSource.html"><code>SimpleDriverDataSource</code></a></p>
</li>
<li>
<p>H2 <a class="apiref external" href="https://www.h2database.com/javadoc/org/h2/jdbcx/JdbcDataSource.html" target="_blank"><code>JdbcDataSource</code></a></p>
</li>
<li>
<p>PostgreSQL <a class="apiref external" href="https://jdbc.postgresql.org/documentation/publicapi/org/postgresql/ds/PGSimpleDataSource.html" target="_blank"><code>PGSimpleDataSource</code></a></p>
</li>
<li>
<p>C3P0</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.datasource.jndi"><a class="anchor" href="#data.sql.datasource.jndi"></a>Connection to a JNDI DataSource</h3>
<div class="paragraph">
<p>If you deploy your Spring Boot application to an Application Server, you might want to configure and manage your DataSource by using your Application Server’s built-in features and access it by using JNDI.</p>
</div>
<div class="paragraph">
<p>The <code>spring.datasource.jndi-name</code> property can be used as an alternative to the <code>spring.datasource.url</code>, <code>spring.datasource.username</code>, and <code>spring.datasource.password</code> properties to access the <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> from a specific JNDI location.
For example, the following section in <code>application.properties</code> shows how you can access a JBoss AS defined <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_3_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_3_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_3_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_properties" class="tabpanel" id="_tabs_3_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.datasource.jndi-name</span>=<span class="hljs-string">java:jboss/datasources/customers</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_3_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">datasource:</span>
    <span class="hljs-attr">jndi-name:</span> <span class="hljs-string">"java:jboss/datasources/customers"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.sql.jdbc-template"><a class="anchor" href="#data.sql.jdbc-template"></a>Using JdbcTemplate</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring’s <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/JdbcTemplate.html"><code>JdbcTemplate</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/namedparam/NamedParameterJdbcTemplate.html"><code>NamedParameterJdbcTemplate</code></a> classes are auto-configured, and you can autowire them directly into your own beans, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_4_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_4_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_4_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_java" class="tabpanel" id="_tabs_4_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.jdbc.core.JdbcTemplate;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> JdbcTemplate jdbcTemplate;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(JdbcTemplate jdbcTemplate)</span> </span>{
		<span class="hljs-keyword">this</span>.jdbcTemplate = jdbcTemplate;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">doSomething</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">this</span>.jdbcTemplate ...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_4_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.jdbc.core.JdbcTemplate
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> jdbcTemplate: JdbcTemplate) {

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">doSomething</span><span class="hljs-params">()</span></span> {
		jdbcTemplate.execute(<span class="hljs-string">"delete from customer"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can customize some properties of the template by using the <code>spring.jdbc.template.*</code> properties, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_5_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_5_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_5_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_properties" class="tabpanel" id="_tabs_5_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.jdbc.template.max-rows</span>=<span class="hljs-string">500</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_5_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">jdbc:</span>
    <span class="hljs-attr">template:</span>
      <span class="hljs-attr">max-rows:</span> <span class="hljs-number">500</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/namedparam/NamedParameterJdbcTemplate.html"><code>NamedParameterJdbcTemplate</code></a> reuses the same <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/JdbcTemplate.html"><code>JdbcTemplate</code></a> instance behind the scenes.
If more than one <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/JdbcTemplate.html"><code>JdbcTemplate</code></a> is defined and no primary candidate exists, the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/namedparam/NamedParameterJdbcTemplate.html"><code>NamedParameterJdbcTemplate</code></a> is not auto-configured.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.sql.jdbc-client"><a class="anchor" href="#data.sql.jdbc-client"></a>Using JdbcClient</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring’s <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/simple/JdbcClient.html"><code>JdbcClient</code></a> is auto-configured based on the presence of a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/namedparam/NamedParameterJdbcTemplate.html"><code>NamedParameterJdbcTemplate</code></a>.
You can inject it directly in your own beans as well, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_6_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_6_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_6_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_java" class="tabpanel" id="_tabs_6_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.jdbc.core.simple.JdbcClient;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> JdbcClient jdbcClient;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(JdbcClient jdbcClient)</span> </span>{
		<span class="hljs-keyword">this</span>.jdbcClient = jdbcClient;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">doSomething</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">this</span>.jdbcClient ...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_6_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.jdbc.core.simple.JdbcClient
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> jdbcClient: JdbcClient) {

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">doSomething</span><span class="hljs-params">()</span></span> {
		jdbcClient.sql(<span class="hljs-string">"delete from customer"</span>).update()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you rely on auto-configuration to create the underlying <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/JdbcTemplate.html"><code>JdbcTemplate</code></a>, any customization using <code>spring.jdbc.template.*</code> properties is taken into account in the client as well.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.sql.jpa-and-spring-data"><a class="anchor" href="#data.sql.jpa-and-spring-data"></a>JPA and Spring Data JPA</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The Java Persistence API is a standard technology that lets you “map” objects to relational databases.
The <code>spring-boot-starter-data-jpa</code> POM provides a quick way to get started.
It provides the following key dependencies:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Hibernate: One of the most popular JPA implementations.</p>
</li>
<li>
<p>Spring Data JPA: Helps you to implement JPA-based repositories.</p>
</li>
<li>
<p>Spring ORM: Core ORM support from the Spring Framework.</p>
</li>
</ul>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
We do not go into too many details of JPA or <a class="external" href="https://spring.io/projects/spring-data" target="_blank">Spring Data</a> here.
You can follow the <a class="external" href="https://spring.io/guides/gs/accessing-data-jpa/" target="_blank">Accessing Data with JPA</a> guide from <a class="bare external" href="https://spring.io" target="_blank">spring.io</a> and read the <a class="external" href="https://spring.io/projects/spring-data-jpa" target="_blank">Spring Data JPA</a> and <a class="external" href="https://hibernate.org/orm/documentation/" target="_blank">Hibernate</a> reference documentation.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="data.sql.jpa-and-spring-data.entity-classes"><a class="anchor" href="#data.sql.jpa-and-spring-data.entity-classes"></a>Entity Classes</h3>
<div class="paragraph">
<p>Traditionally, JPA “Entity” classes are specified in a <code>persistence.xml</code> file.
With Spring Boot, this file is not necessary and “Entity Scanning” is used instead.
By default the <a class="xref page" href="../using/auto-configuration.html#using.auto-configuration.packages">auto-configuration packages</a> are scanned.</p>
</div>
<div class="paragraph">
<p>Any classes annotated with <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/Entity.html" target="_blank"><code>@Entity</code></a>, <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/Embeddable.html" target="_blank"><code>@Embeddable</code></a>, or <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/MappedSuperclass.html" target="_blank"><code>@MappedSuperclass</code></a> are considered.
A typical entity class resembles the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_7_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_7_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_7_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_java" class="tabpanel" id="_tabs_7_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.io.Serializable;

<span class="hljs-keyword">import</span> jakarta.persistence.Column;
<span class="hljs-keyword">import</span> jakarta.persistence.Entity;
<span class="hljs-keyword">import</span> jakarta.persistence.GeneratedValue;
<span class="hljs-keyword">import</span> jakarta.persistence.Id;

</span><span class="fold-block"><span class="hljs-meta">@Entity</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">City</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">Serializable</span> </span>{

	<span class="hljs-meta">@Id</span>
	<span class="hljs-meta">@GeneratedValue</span>
	<span class="hljs-keyword">private</span> Long id;

	<span class="hljs-meta">@Column</span>(nullable = <span class="hljs-keyword">false</span>)
	<span class="hljs-keyword">private</span> String name;

	<span class="hljs-meta">@Column</span>(nullable = <span class="hljs-keyword">false</span>)
	<span class="hljs-keyword">private</span> String state;

	<span class="hljs-comment">// ... additional members, often include @OneToMany mappings</span>

	<span class="hljs-function"><span class="hljs-keyword">protected</span> <span class="hljs-title">City</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-comment">// no-args constructor required by JPA spec</span>
		<span class="hljs-comment">// this one is protected since it should not be used directly</span>
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">City</span><span class="hljs-params">(String name, String state)</span> </span>{
		<span class="hljs-keyword">this</span>.name = name;
		<span class="hljs-keyword">this</span>.state = state;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">getName</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.name;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">getState</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.state;
	}

	<span class="hljs-comment">// ... etc</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_7_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> jakarta.persistence.Column
<span class="hljs-keyword">import</span> jakarta.persistence.Entity
<span class="hljs-keyword">import</span> jakarta.persistence.GeneratedValue
<span class="hljs-keyword">import</span> jakarta.persistence.Id
<span class="hljs-keyword">import</span> java.io.Serializable

</span><span class="fold-block"><span class="hljs-meta">@Entity</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">City</span> : <span class="hljs-type">Serializable {</span></span>

	<span class="hljs-meta">@Id</span>
	<span class="hljs-meta">@GeneratedValue</span>
	<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> id: <span class="hljs-built_in">Long</span>? = <span class="hljs-literal">null</span>

	<span class="hljs-meta">@Column(nullable = false)</span>
	<span class="hljs-keyword">var</span> name: String? = <span class="hljs-literal">null</span>
		<span class="hljs-keyword">private</span> <span class="hljs-keyword">set</span>

	<span class="hljs-comment">// ... etc</span>
	<span class="hljs-meta">@Column(nullable = false)</span>
	<span class="hljs-keyword">var</span> state: String? = <span class="hljs-literal">null</span>
		<span class="hljs-keyword">private</span> <span class="hljs-keyword">set</span>

	<span class="hljs-comment">// ... additional members, often include @OneToMany mappings</span>

	<span class="hljs-keyword">protected</span> <span class="hljs-keyword">constructor</span>() {
		<span class="hljs-comment">// no-args constructor required by JPA spec</span>
		<span class="hljs-comment">// this one is protected since it should not be used directly</span>
	}

	<span class="hljs-keyword">constructor</span>(name: String?, state: String?) {
		<span class="hljs-keyword">this</span>.name = name
		<span class="hljs-keyword">this</span>.state = state
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can customize entity scanning locations by using the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/domain/EntityScan.html"><code>@EntityScan</code></a> annotation.
See the <a class="xref page" href="../../how-to/data-access.html#howto.data-access.separate-entity-definitions-from-spring-configuration">Separate @Entity Definitions from Spring Configuration</a> section of the “How-to Guides”.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.jpa-and-spring-data.repositories"><a class="anchor" href="#data.sql.jpa-and-spring-data.repositories"></a>Spring Data JPA Repositories</h3>
<div class="paragraph">
<p><a class="external" href="https://spring.io/projects/spring-data-jpa" target="_blank">Spring Data JPA</a> repositories are interfaces that you can define to access data.
JPA queries are created automatically from your method names.
For example, a <code>CityRepository</code> interface might declare a <code>findAllByState(String state)</code> method to find all the cities in a given state.</p>
</div>
<div class="paragraph">
<p>For more complex queries, you can annotate your method with Spring Data’s <a class="apiref" href="https://docs.spring.io/spring-data/jpa/docs/3.4.x/api/org/springframework/data/jpa/repository/Query.html"><code>Query</code></a> annotation.</p>
</div>
<div class="paragraph">
<p>Spring Data repositories usually extend from the <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> or <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/CrudRepository.html"><code>CrudRepository</code></a> interfaces.
If you use auto-configuration, the <a class="xref page" href="../using/auto-configuration.html#using.auto-configuration.packages">auto-configuration packages</a> are searched for repositories.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can customize the locations to look for repositories using <a class="apiref" href="https://docs.spring.io/spring-data/jpa/docs/3.4.x/api/org/springframework/data/jpa/repository/config/EnableJpaRepositories.html"><code>@EnableJpaRepositories</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following example shows a typical Spring Data repository interface definition:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_8_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_8_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_8_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_java" class="tabpanel" id="_tabs_8_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.docs.data.sql.jpaandspringdata.entityclasses.City;
<span class="hljs-keyword">import</span> org.springframework.data.domain.Page;
<span class="hljs-keyword">import</span> org.springframework.data.domain.Pageable;
<span class="hljs-keyword">import</span> org.springframework.data.repository.Repository;

</span><span class="fold-block"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">CityRepository</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">Repository</span>&lt;<span class="hljs-title">City</span>, <span class="hljs-title">Long</span>&gt; </span>{

	<span class="hljs-function">Page&lt;City&gt; <span class="hljs-title">findAll</span><span class="hljs-params">(Pageable pageable)</span></span>;

	<span class="hljs-function">City <span class="hljs-title">findByNameAndStateAllIgnoringCase</span><span class="hljs-params">(String name, String state)</span></span>;

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_8_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.docs.<span class="hljs-keyword">data</span>.sql.jpaandspringdata.entityclasses.City
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.domain.Page
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.domain.Pageable
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.repository.Repository

</span><span class="fold-block"><span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">CityRepository</span> : <span class="hljs-type">Repository</span>&lt;<span class="hljs-type">City?, Long?</span>&gt; </span>{

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">findAll</span><span class="hljs-params">(pageable: <span class="hljs-type">Pageable</span>?)</span></span>: Page&lt;City?&gt;?

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">findByNameAndStateAllIgnoringCase</span><span class="hljs-params">(name: <span class="hljs-type">String</span>?, state: <span class="hljs-type">String</span>?)</span></span>: City?

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Spring Data JPA repositories support three different modes of bootstrapping: default, deferred, and lazy.
To enable deferred or lazy bootstrapping, set the <code>spring.data.jpa.repositories.bootstrap-mode</code> property to <code>deferred</code> or <code>lazy</code> respectively.
When using deferred or lazy bootstrapping, the auto-configured <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/orm/jpa/EntityManagerFactoryBuilder.html"><code>EntityManagerFactoryBuilder</code></a> will use the context’s <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/task/AsyncTaskExecutor.html"><code>AsyncTaskExecutor</code></a>, if any, as the bootstrap executor.
If more than one exists, the one named <code>applicationTaskExecutor</code> will be used.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>When using deferred or lazy bootstrapping, make sure to defer any access to the JPA infrastructure after the application context bootstrap phase.
You can use <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/SmartInitializingSingleton.html"><code>SmartInitializingSingleton</code></a> to invoke any initialization that requires the JPA infrastructure.
For JPA components (such as converters) that are created as Spring beans, use <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/ObjectProvider.html"><code>ObjectProvider</code></a> to delay the resolution of dependencies, if any.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
We have barely scratched the surface of Spring Data JPA.
For complete details, see the <a href="https://docs.spring.io/spring-data/jpa/reference/3.4">Spring Data JPA reference documentation</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.jpa-and-spring-data.envers-repositories"><a class="anchor" href="#data.sql.jpa-and-spring-data.envers-repositories"></a>Spring Data Envers Repositories</h3>
<div class="paragraph">
<p>If <a class="external" href="https://spring.io/projects/spring-data-envers" target="_blank">Spring Data Envers</a> is available, JPA repositories are auto-configured to support typical Envers queries.</p>
</div>
<div class="paragraph">
<p>To use Spring Data Envers, make sure your repository extends from <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/history/RevisionRepository.html"><code>RevisionRepository</code></a> as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_9">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_9_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_9_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_9_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_9_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_9_java" class="tabpanel" id="_tabs_9_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.docs.data.sql.jpaandspringdata.entityclasses.Country;
<span class="hljs-keyword">import</span> org.springframework.data.domain.Page;
<span class="hljs-keyword">import</span> org.springframework.data.domain.Pageable;
<span class="hljs-keyword">import</span> org.springframework.data.repository.Repository;
<span class="hljs-keyword">import</span> org.springframework.data.repository.history.RevisionRepository;

</span><span class="fold-block"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">CountryRepository</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">RevisionRepository</span>&lt;<span class="hljs-title">Country</span>, <span class="hljs-title">Long</span>, <span class="hljs-title">Integer</span>&gt;, <span class="hljs-title">Repository</span>&lt;<span class="hljs-title">Country</span>, <span class="hljs-title">Long</span>&gt; </span>{

	<span class="hljs-function">Page&lt;Country&gt; <span class="hljs-title">findAll</span><span class="hljs-params">(Pageable pageable)</span></span>;

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_9_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_9_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.docs.<span class="hljs-keyword">data</span>.sql.jpaandspringdata.entityclasses.Country
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.domain.Page
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.domain.Pageable
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.repository.Repository
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.repository.history.RevisionRepository

</span><span class="fold-block"><span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">CountryRepository</span> :
		<span class="hljs-type">RevisionRepository</span>&lt;<span class="hljs-type">Country?, Long?, Int</span>&gt;,
		<span class="hljs-type">Repository</span>&lt;<span class="hljs-type">Country?, Long?</span>&gt; </span>{

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">findAll</span><span class="hljs-params">(pageable: <span class="hljs-type">Pageable</span>?)</span></span>: Page&lt;Country?&gt;?

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
For more details, check the <a href="https://docs.spring.io/spring-data/jpa/reference/3.4/envers.html">Spring Data Envers reference documentation</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.jpa-and-spring-data.creating-and-dropping"><a class="anchor" href="#data.sql.jpa-and-spring-data.creating-and-dropping"></a>Creating and Dropping JPA Databases</h3>
<div class="paragraph">
<p>By default, JPA databases are automatically created <strong>only</strong> if you use an embedded database (H2, HSQL, or Derby).
You can explicitly configure JPA settings by using <code>spring.jpa.*</code> properties.
For example, to create and drop tables you can add the following line to your <code>application.properties</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_10">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_10_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_10_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_10_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_10_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_10_properties" class="tabpanel" id="_tabs_10_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.jpa.hibernate.ddl-auto</span>=<span class="hljs-string">create-drop</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_10_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_10_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">jpa:</span>
    <span class="hljs-attr">hibernate.ddl-auto:</span> <span class="hljs-string">"create-drop"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Hibernate’s own internal property name for this (if you happen to remember it better) is <code>hibernate.hbm2ddl.auto</code>.
You can set it, along with other Hibernate native properties, by using <code>spring.jpa.properties.*</code> (the prefix is stripped before adding them to the entity manager).
The following line shows an example of setting JPA properties for Hibernate:
</td>
</tr>
</tbody></table>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_11">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_11_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_11_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_11_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_11_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_11_properties" class="tabpanel" id="_tabs_11_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.jpa.properties.hibernate.globally_quoted_identifiers</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_11_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_11_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">jpa:</span>
    <span class="hljs-attr">properties:</span>
      <span class="hljs-attr">hibernate:</span>
        <span class="hljs-attr">"globally_quoted_identifiers":</span> <span class="hljs-string">"true"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The line in the preceding example passes a value of <code>true</code> for the <code>hibernate.globally_quoted_identifiers</code> property to the Hibernate entity manager.</p>
</div>
<div class="paragraph">
<p>By default, the DDL execution (or validation) is deferred until the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a> has started.</p>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.jpa-and-spring-data.open-entity-manager-in-view"><a class="anchor" href="#data.sql.jpa-and-spring-data.open-entity-manager-in-view"></a>Open EntityManager in View</h3>
<div class="paragraph">
<p>If you are running a web application, Spring Boot by default registers <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/orm/jpa/support/OpenEntityManagerInViewInterceptor.html"><code>OpenEntityManagerInViewInterceptor</code></a> to apply the “Open EntityManager in View” pattern, to allow for lazy loading in web views.
If you do not want this behavior, you should set <code>spring.jpa.open-in-view</code> to <code>false</code> in your <code>application.properties</code>.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.sql.jdbc"><a class="anchor" href="#data.sql.jdbc"></a>Spring Data JDBC</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Data includes repository support for JDBC and will automatically generate SQL for the methods on <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/CrudRepository.html"><code>CrudRepository</code></a>.
For more advanced queries, a <a class="apiref" href="https://docs.spring.io/spring-data/jdbc/docs/3.4.x/api/org/springframework/data/jdbc/repository/query/Query.html"><code>@Query</code></a> annotation is provided.</p>
</div>
<div class="paragraph">
<p>Spring Boot will auto-configure Spring Data’s JDBC repositories when the necessary dependencies are on the classpath.
They can be added to your project with a single dependency on <code>spring-boot-starter-data-jdbc</code>.
If necessary, you can take control of Spring Data JDBC’s configuration by adding the <a class="apiref" href="https://docs.spring.io/spring-data/jdbc/docs/3.4.x/api/org/springframework/data/jdbc/repository/config/EnableJdbcRepositories.html"><code>@EnableJdbcRepositories</code></a> annotation or an <a class="apiref" href="https://docs.spring.io/spring-data/jdbc/docs/3.4.x/api/org/springframework/data/jdbc/repository/config/AbstractJdbcConfiguration.html"><code>AbstractJdbcConfiguration</code></a> subclass to your application.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
For complete details of Spring Data JDBC, see the <a href="https://docs.spring.io/spring-data/relational/reference/3.4">reference documentation</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.sql.h2-web-console"><a class="anchor" href="#data.sql.h2-web-console"></a>Using H2’s Web Console</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The <a class="external" href="https://www.h2database.com" target="_blank">H2 database</a> provides a <a class="external" href="https://www.h2database.com/html/quickstart.html#h2_console" target="_blank">browser-based console</a> that Spring Boot can auto-configure for you.
The console is auto-configured when the following conditions are met:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>You are developing a servlet-based web application.</p>
</li>
<li>
<p><code>com.h2database:h2</code> is on the classpath.</p>
</li>
<li>
<p>You are using <a class="xref page" href="../using/devtools.html">Spring Boot’s developer tools</a>.</p>
</li>
</ul>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you are not using Spring Boot’s developer tools but would still like to make use of H2’s console, you can configure the <code>spring.h2.console.enabled</code> property with a value of <code>true</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The H2 console is only intended for use during development, so you should take care to ensure that <code>spring.h2.console.enabled</code> is not set to <code>true</code> in production.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="data.sql.h2-web-console.custom-path"><a class="anchor" href="#data.sql.h2-web-console.custom-path"></a>Changing the H2 Console’s Path</h3>
<div class="paragraph">
<p>By default, the console is available at <code>/h2-console</code>.
You can customize the console’s path by using the <code>spring.h2.console.path</code> property.</p>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.h2-web-console.spring-security"><a class="anchor" href="#data.sql.h2-web-console.spring-security"></a>Accessing the H2 Console in a Secured Application</h3>
<div class="paragraph">
<p>H2 Console uses frames and, as it is intended for development only, does not implement CSRF protection measures.
If your application uses Spring Security, you need to configure it to</p>
</div>
<div class="ulist">
<ul>
<li>
<p>disable CSRF protection for requests against the console,</p>
</li>
<li>
<p>set the header <code>X-Frame-Options</code> to <code>SAMEORIGIN</code> on responses from the console.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>More information on <a href="https://docs.spring.io/spring-security/reference/6.4/features/exploits/csrf.html">CSRF</a> and the header <a href="https://docs.spring.io/spring-security/reference/6.4/features/exploits/headers.html#headers-frame-options">X-Frame-Options</a> can be found in the Spring Security Reference Guide.</p>
</div>
<div class="paragraph">
<p>In simple setups, a <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/SecurityFilterChain.html"><code>SecurityFilterChain</code></a> like the following can be used:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_12">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_12_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_12_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_12_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_12_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_12_java" class="tabpanel" id="_tabs_12_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.security.servlet.PathRequest;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Profile;
<span class="hljs-keyword">import</span> org.springframework.core.Ordered;
<span class="hljs-keyword">import</span> org.springframework.core.annotation.Order;
<span class="hljs-keyword">import</span> org.springframework.security.config.Customizer;
<span class="hljs-keyword">import</span> org.springframework.security.config.annotation.web.builders.HttpSecurity;
<span class="hljs-keyword">import</span> org.springframework.security.config.annotation.web.configurers.CsrfConfigurer;
<span class="hljs-keyword">import</span> org.springframework.security.config.annotation.web.configurers.HeadersConfigurer.FrameOptionsConfig;
<span class="hljs-keyword">import</span> org.springframework.security.web.SecurityFilterChain;

</span><span class="fold-block"><span class="hljs-meta">@Profile</span>(<span class="hljs-string">"dev"</span>)
<span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">DevProfileSecurityConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@Order</span>(Ordered.HIGHEST_PRECEDENCE)
	<span class="hljs-function">SecurityFilterChain <span class="hljs-title">h2ConsoleSecurityFilterChain</span><span class="hljs-params">(HttpSecurity http)</span> <span class="hljs-keyword">throws</span> Exception </span>{
		http.securityMatcher(PathRequest.toH2Console());
		http.authorizeHttpRequests(yourCustomAuthorization());
		http.csrf(CsrfConfigurer::disable);
		http.headers((headers) -&gt; headers.frameOptions(FrameOptionsConfig::sameOrigin));
		<span class="hljs-keyword">return</span> http.build();
	}


}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_12_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_12_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Profile
<span class="hljs-keyword">import</span> org.springframework.core.Ordered
<span class="hljs-keyword">import</span> org.springframework.core.<span class="hljs-keyword">annotation</span>.Order
<span class="hljs-keyword">import</span> org.springframework.security.config.Customizer
<span class="hljs-keyword">import</span> org.springframework.security.config.<span class="hljs-keyword">annotation</span>.web.builders.HttpSecurity
<span class="hljs-keyword">import</span> org.springframework.security.web.SecurityFilterChain

</span><span class="fold-block"><span class="hljs-meta">@Profile(<span class="hljs-meta-string">"dev"</span>)</span>
<span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">DevProfileSecurityConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@Order(Ordered.HIGHEST_PRECEDENCE)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">h2ConsoleSecurityFilterChain</span><span class="hljs-params">(http: <span class="hljs-type">HttpSecurity</span>)</span></span>: SecurityFilterChain {
		<span class="hljs-keyword">return</span> http.authorizeHttpRequests(yourCustomAuthorization())
			.csrf { csrf -&gt; csrf.disable() }
			.headers { headers -&gt; headers.frameOptions { frameOptions -&gt; frameOptions.sameOrigin() } }
			.build()
	}


}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock warning">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-warning" title="Warning"></i>
</td>
<td class="content">
The H2 console is only intended for use during development.
In production, disabling CSRF protection or allowing frames for a website may create severe security risks.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<code>PathRequest.toH2Console()</code> returns the correct request matcher also when the console’s path has been customized.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.sql.jooq"><a class="anchor" href="#data.sql.jooq"></a>Using jOOQ</h2>
<div class="sectionbody">
<div class="paragraph">
<p>jOOQ Object Oriented Querying (<a class="external" href="https://www.jooq.org/" target="_blank">jOOQ</a>) is a popular product from <a class="external" href="https://www.datageekery.com/" target="_blank">Data Geekery</a> which generates Java code from your database and lets you build type-safe SQL queries through its fluent API.
Both the commercial and open source editions can be used with Spring Boot.</p>
</div>
<div class="sect2">
<h3 id="data.sql.jooq.codegen"><a class="anchor" href="#data.sql.jooq.codegen"></a>Code Generation</h3>
<div class="paragraph">
<p>In order to use jOOQ type-safe queries, you need to generate Java classes from your database schema.
You can follow the instructions in the <a class="external" href="https://www.jooq.org/doc/3.19.23/manual-single-page/#jooq-in-7-steps-step3" target="_blank">jOOQ user manual</a>.
If you use the <code>jooq-codegen-maven</code> plugin and you also use the <code>spring-boot-starter-parent</code> “parent POM”, you can safely omit the plugin’s <code>&lt;version&gt;</code> tag.
You can also use Spring Boot-defined version variables (such as <code>h2.version</code>) to declare the plugin’s database dependency.
The following listing shows an example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.jooq<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>jooq-codegen-maven<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">executions</span>&gt;</span>
		...
	<span class="hljs-tag">&lt;/<span class="hljs-name">executions</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">dependencies</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>com.h2database<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>h2<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">version</span>&gt;</span>${h2.version}<span class="hljs-tag">&lt;/<span class="hljs-name">version</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">dependencies</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">jdbc</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">driver</span>&gt;</span>org.h2.Driver<span class="hljs-tag">&lt;/<span class="hljs-name">driver</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">url</span>&gt;</span>jdbc:h2:~/yourdatabase<span class="hljs-tag">&lt;/<span class="hljs-name">url</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">jdbc</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">generator</span>&gt;</span>
			...
		<span class="hljs-tag">&lt;/<span class="hljs-name">generator</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.jooq.dslcontext"><a class="anchor" href="#data.sql.jooq.dslcontext"></a>Using DSLContext</h3>
<div class="paragraph">
<p>The fluent API offered by jOOQ is initiated through the <a class="apiref external" href="https://www.jooq.org/javadoc/3.19.23/org/jooq/DSLContext.html" target="_blank"><code>DSLContext</code></a> interface.
Spring Boot auto-configures a <a class="apiref external" href="https://www.jooq.org/javadoc/3.19.23/org/jooq/DSLContext.html" target="_blank"><code>DSLContext</code></a> as a Spring Bean and connects it to your application <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a>.
To use the <a class="apiref external" href="https://www.jooq.org/javadoc/3.19.23/org/jooq/DSLContext.html" target="_blank"><code>DSLContext</code></a>, you can inject it, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_13">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_13_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_13_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_13_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_13_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_13_java" class="tabpanel" id="_tabs_13_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.GregorianCalendar;
<span class="hljs-keyword">import</span> java.util.List;

<span class="hljs-keyword">import</span> org.jooq.DSLContext;

<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.boot.docs.data.sql.jooq.dslcontext.Tables.AUTHOR;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> DSLContext create;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(DSLContext dslContext)</span> </span>{
		<span class="hljs-keyword">this</span>.create = dslContext;
	}


}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_13_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_13_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.jooq.DSLContext
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component
<span class="hljs-keyword">import</span> java.util.GregorianCalendar

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> create: DSLContext) {


}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The jOOQ manual tends to use a variable named <code>create</code> to hold the <a class="apiref external" href="https://www.jooq.org/javadoc/3.19.23/org/jooq/DSLContext.html" target="_blank"><code>DSLContext</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>You can then use the <a class="apiref external" href="https://www.jooq.org/javadoc/3.19.23/org/jooq/DSLContext.html" target="_blank"><code>DSLContext</code></a> to construct your queries, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_14">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_14_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_14_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_14_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_14_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_14_java" class="tabpanel" id="_tabs_14_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">	<span class="hljs-function"><span class="hljs-keyword">public</span> List&lt;GregorianCalendar&gt; <span class="hljs-title">authorsBornAfter1980</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.create.selectFrom(AUTHOR)
			.where(AUTHOR.DATE_OF_BIRTH.greaterThan(<span class="hljs-keyword">new</span> GregorianCalendar(<span class="hljs-number">1980</span>, <span class="hljs-number">0</span>, <span class="hljs-number">1</span>)))
			.fetch(AUTHOR.DATE_OF_BIRTH);</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_14_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_14_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin">	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">authorsBornAfter1980</span><span class="hljs-params">()</span></span>: List&lt;GregorianCalendar&gt; {
		<span class="hljs-keyword">return</span> create.selectFrom&lt;Tables.TAuthorRecord&gt;(Tables.AUTHOR)
			.<span class="hljs-keyword">where</span>(Tables.AUTHOR?.DATE_OF_BIRTH?.greaterThan(GregorianCalendar(<span class="hljs-number">1980</span>, <span class="hljs-number">0</span>, <span class="hljs-number">1</span>)))
			.fetch(Tables.AUTHOR?.DATE_OF_BIRTH)
	}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.jooq.sqldialect"><a class="anchor" href="#data.sql.jooq.sqldialect"></a>jOOQ SQL Dialect</h3>
<div class="paragraph">
<p>Unless the <code>spring.jooq.sql-dialect</code> property has been configured, Spring Boot determines the SQL dialect to use for your datasource.
If Spring Boot could not detect the dialect, it uses <code>DEFAULT</code>.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Spring Boot can only auto-configure dialects supported by the open source version of jOOQ.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.jooq.customizing"><a class="anchor" href="#data.sql.jooq.customizing"></a>Customizing jOOQ</h3>
<div class="paragraph">
<p>More advanced customizations can be achieved by defining your own <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jooq/DefaultConfigurationCustomizer.html"><code>DefaultConfigurationCustomizer</code></a> bean that will be invoked prior to creating the <a class="apiref external" href="https://www.jooq.org/javadoc/3.19.23/org/jooq/Configuration.html" target="_blank"><code>Configuration</code></a> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a>.
This takes precedence to anything that is applied by the auto-configuration.</p>
</div>
<div class="paragraph">
<p>You can also create your own <a class="apiref external" href="https://www.jooq.org/javadoc/3.19.23/org/jooq/Configuration.html" target="_blank"><code>Configuration</code></a> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> if you want to take complete control of the jOOQ configuration.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.sql.r2dbc"><a class="anchor" href="#data.sql.r2dbc"></a>Using R2DBC</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The Reactive Relational Database Connectivity (<a class="external" href="https://r2dbc.io" target="_blank">R2DBC</a>) project brings reactive programming APIs to relational databases.
R2DBC’s <a class="apiref external" href="https://r2dbc.io/spec/1.0.0.RELEASE/api/io/r2dbc/spi/Connection.html" target="_blank"><code>Connection</code></a> provides a standard method of working with non-blocking database connections.
Connections are provided by using a <a class="apiref external" href="https://r2dbc.io/spec/1.0.0.RELEASE/api/io/r2dbc/spi/ConnectionFactory.html" target="_blank"><code>ConnectionFactory</code></a>, similar to a <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> with jdbc.</p>
</div>
<div class="paragraph">
<p><a class="apiref external" href="https://r2dbc.io/spec/1.0.0.RELEASE/api/io/r2dbc/spi/ConnectionFactory.html" target="_blank"><code>ConnectionFactory</code></a> configuration is controlled by external configuration properties in <code>spring.r2dbc.*</code>.
For example, you might declare the following section in <code>application.properties</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_15">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_15_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_15_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_15_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_15_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_15_properties" class="tabpanel" id="_tabs_15_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.r2dbc.url</span>=<span class="hljs-string">r2dbc:postgresql://localhost/test</span>
<span class="hljs-meta">spring.r2dbc.username</span>=<span class="hljs-string">dbuser</span>
<span class="hljs-meta">spring.r2dbc.password</span>=<span class="hljs-string">dbpass</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_15_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_15_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">r2dbc:</span>
    <span class="hljs-attr">url:</span> <span class="hljs-string">"r2dbc:postgresql://localhost/test"</span>
    <span class="hljs-attr">username:</span> <span class="hljs-string">"dbuser"</span>
    <span class="hljs-attr">password:</span> <span class="hljs-string">"dbpass"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You do not need to specify a driver class name, since Spring Boot obtains the driver from R2DBC’s Connection Factory discovery.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
At least the url should be provided.
Information specified in the URL takes precedence over individual properties, that is <code>name</code>, <code>username</code>, <code>password</code> and pooling options.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The “How-to Guides” section includes a <a class="xref page" href="../../how-to/data-initialization.html#howto.data-initialization.using-basic-sql-scripts">section on how to initialize a database</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>To customize the connections created by a <a class="apiref external" href="https://r2dbc.io/spec/1.0.0.RELEASE/api/io/r2dbc/spi/ConnectionFactory.html" target="_blank"><code>ConnectionFactory</code></a>, that is, set specific parameters that you do not want (or cannot) configure in your central database configuration, you can use a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/r2dbc/ConnectionFactoryOptionsBuilderCustomizer.html"><code>ConnectionFactoryOptionsBuilderCustomizer</code></a> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a>.
The following example shows how to manually override the database port while the rest of the options are taken from the application configuration:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_16">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_16_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_16_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_16_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_16_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_16_java" class="tabpanel" id="_tabs_16_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.r2dbc.spi.ConnectionFactoryOptions;

<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.r2dbc.ConnectionFactoryOptionsBuilderCustomizer;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyR2dbcConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> ConnectionFactoryOptionsBuilderCustomizer <span class="hljs-title">connectionFactoryPortCustomizer</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> (builder) -&gt; builder.option(ConnectionFactoryOptions.PORT, <span class="hljs-number">5432</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_16_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_16_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.r2dbc.spi.ConnectionFactoryOptions
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.r2dbc.ConnectionFactoryOptionsBuilderCustomizer
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyR2dbcConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">connectionFactoryPortCustomizer</span><span class="hljs-params">()</span></span>: ConnectionFactoryOptionsBuilderCustomizer {
		<span class="hljs-keyword">return</span> ConnectionFactoryOptionsBuilderCustomizer { builder -&gt;
			builder.option(ConnectionFactoryOptions.PORT, <span class="hljs-number">5432</span>)
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The following examples show how to set some PostgreSQL connection options:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_17">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_17_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_17_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_17_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_17_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_17_java" class="tabpanel" id="_tabs_17_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.HashMap;
<span class="hljs-keyword">import</span> java.util.Map;

<span class="hljs-keyword">import</span> io.r2dbc.postgresql.PostgresqlConnectionFactoryProvider;

<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.r2dbc.ConnectionFactoryOptionsBuilderCustomizer;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyPostgresR2dbcConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> ConnectionFactoryOptionsBuilderCustomizer <span class="hljs-title">postgresCustomizer</span><span class="hljs-params">()</span> </span>{
		Map&lt;String, String&gt; options = <span class="hljs-keyword">new</span> HashMap&lt;&gt;();
		options.put(<span class="hljs-string">"lock_timeout"</span>, <span class="hljs-string">"30s"</span>);
		options.put(<span class="hljs-string">"statement_timeout"</span>, <span class="hljs-string">"60s"</span>);
		<span class="hljs-keyword">return</span> (builder) -&gt; builder.option(PostgresqlConnectionFactoryProvider.OPTIONS, options);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_17_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_17_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.r2dbc.postgresql.PostgresqlConnectionFactoryProvider
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.r2dbc.ConnectionFactoryOptionsBuilderCustomizer
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyPostgresR2dbcConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">postgresCustomizer</span><span class="hljs-params">()</span></span>: ConnectionFactoryOptionsBuilderCustomizer {
		<span class="hljs-keyword">val</span> options: MutableMap&lt;String, String&gt; = HashMap()
		options[<span class="hljs-string">"lock_timeout"</span>] = <span class="hljs-string">"30s"</span>
		options[<span class="hljs-string">"statement_timeout"</span>] = <span class="hljs-string">"60s"</span>
		<span class="hljs-keyword">return</span> ConnectionFactoryOptionsBuilderCustomizer { builder -&gt;
			builder.option(PostgresqlConnectionFactoryProvider.OPTIONS, options)
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>When a <a class="apiref external" href="https://r2dbc.io/spec/1.0.0.RELEASE/api/io/r2dbc/spi/ConnectionFactory.html" target="_blank"><code>ConnectionFactory</code></a> bean is available, the regular JDBC <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> auto-configuration backs off.
If you want to retain the JDBC <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> auto-configuration, and are comfortable with the risk of using the blocking JDBC API in a reactive application, add <code>@Import(DataSourceAutoConfiguration.class)</code> on a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> class in your application to re-enable it.</p>
</div>
<div class="sect2">
<h3 id="data.sql.r2dbc.embedded"><a class="anchor" href="#data.sql.r2dbc.embedded"></a>Embedded Database Support</h3>
<div class="paragraph">
<p>Similarly to <a href="#data.sql.datasource.embedded">the JDBC support</a>, Spring Boot can automatically configure an embedded database for reactive usage.
You need not provide any connection URLs.
You need only include a build dependency to the embedded database that you want to use, as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>io.r2dbc<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>r2dbc-h2<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">scope</span>&gt;</span>runtime<span class="hljs-tag">&lt;/<span class="hljs-name">scope</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>If you are using this feature in your tests, you may notice that the same database is reused by your whole test suite regardless of the number of application contexts that you use.
If you want to make sure that each context has a separate embedded database, you should set <code>spring.r2dbc.generate-unique-name</code> to <code>true</code>.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.r2dbc.using-database-client"><a class="anchor" href="#data.sql.r2dbc.using-database-client"></a>Using DatabaseClient</h3>
<div class="paragraph">
<p>A <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/r2dbc/core/DatabaseClient.html"><code>DatabaseClient</code></a> bean is auto-configured, and you can autowire it directly into your own beans, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_18">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_18_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_18_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_18_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_18_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_18_java" class="tabpanel" id="_tabs_18_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.Map;

<span class="hljs-keyword">import</span> reactor.core.publisher.Flux;

<span class="hljs-keyword">import</span> org.springframework.r2dbc.core.DatabaseClient;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> DatabaseClient databaseClient;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(DatabaseClient databaseClient)</span> </span>{
		<span class="hljs-keyword">this</span>.databaseClient = databaseClient;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-keyword">public</span> Flux&lt;Map&lt;String, Object&gt;&gt; someMethod() {
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.databaseClient.sql(<span class="hljs-string">"select * from user"</span>).fetch().all();
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_18_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_18_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.r2dbc.core.DatabaseClient
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component
<span class="hljs-keyword">import</span> reactor.core.publisher.Flux

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> databaseClient: DatabaseClient) {

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someMethod</span><span class="hljs-params">()</span></span>: Flux&lt;Map&lt;String, Any&gt;&gt; {
		<span class="hljs-keyword">return</span> databaseClient.sql(<span class="hljs-string">"select * from user"</span>).fetch().all()
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="data.sql.r2dbc.repositories"><a class="anchor" href="#data.sql.r2dbc.repositories"></a>Spring Data R2DBC Repositories</h3>
<div class="paragraph">
<p><a class="external" href="https://spring.io/projects/spring-data-r2dbc" target="_blank">Spring Data R2DBC</a> repositories are interfaces that you can define to access data.
Queries are created automatically from your method names.
For example, a <code>CityRepository</code> interface might declare a <code>findAllByState(String state)</code> method to find all the cities in a given state.</p>
</div>
<div class="paragraph">
<p>For more complex queries, you can annotate your method with Spring Data’s <a class="apiref" href="https://docs.spring.io/spring-data/r2dbc/docs/3.4.x/api/org/springframework/data/r2dbc/repository/Query.html"><code>@Query</code></a> annotation.</p>
</div>
<div class="paragraph">
<p>Spring Data repositories usually extend from the <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> or <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/CrudRepository.html"><code>CrudRepository</code></a> interfaces.
If you use auto-configuration, the <a class="xref page" href="../using/auto-configuration.html#using.auto-configuration.packages">auto-configuration packages</a> are searched for repositories.</p>
</div>
<div class="paragraph">
<p>The following example shows a typical Spring Data repository interface definition:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_19">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_19_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_19_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_19_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_19_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_19_java" class="tabpanel" id="_tabs_19_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> reactor.core.publisher.Mono;

<span class="hljs-keyword">import</span> org.springframework.data.repository.Repository;

</span><span class="fold-block"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">CityRepository</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">Repository</span>&lt;<span class="hljs-title">City</span>, <span class="hljs-title">Long</span>&gt; </span>{

	<span class="hljs-function">Mono&lt;City&gt; <span class="hljs-title">findByNameAndStateAllIgnoringCase</span><span class="hljs-params">(String name, String state)</span></span>;

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_19_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_19_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.repository.Repository
<span class="hljs-keyword">import</span> reactor.core.publisher.Mono

</span><span class="fold-block"><span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">CityRepository</span> : <span class="hljs-type">Repository</span>&lt;<span class="hljs-type">City?, Long?</span>&gt; </span>{

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">findByNameAndStateAllIgnoringCase</span><span class="hljs-params">(name: <span class="hljs-type">String</span>?, state: <span class="hljs-type">String</span>?)</span></span>: Mono&lt;City?&gt;?

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
We have barely scratched the surface of Spring Data R2DBC. For complete details, see the <a href="https://docs.spring.io/spring-data/relational/reference/3.4">Spring Data R2DBC reference documentation</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="index.html">Data</a></span>
<span class="next"><a href="nosql.html">Working with NoSQL Technologies</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../reference/data/sql.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="sql.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../3.3/reference/data/sql.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../4.0-SNAPSHOT/reference/data/sql.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.5-SNAPSHOT/reference/data/sql.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.4-SNAPSHOT/reference/data/sql.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.3-SNAPSHOT/reference/data/sql.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>