<!DOCTYPE html>
<html><head><title>Testing Spring Boot Applications :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/reference/testing/spring-boot-applications.html"/><meta content="2025-06-04T15:40:13.876935" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Testing Spring Boot Applications">
<div class="toc-menu"><h3>Testing Spring Boot Applications</h3><ul><li data-level="1"><a href="#testing.spring-boot-applications.detecting-web-app-type">Detecting Web Application Type</a></li><li data-level="1"><a href="#testing.spring-boot-applications.detecting-configuration">Detecting Test Configuration</a></li><li data-level="1"><a href="#testing.spring-boot-applications.using-main">Using the Test Configuration Main Method</a></li><li data-level="1"><a href="#testing.spring-boot-applications.excluding-configuration">Excluding Test Configuration</a></li><li data-level="1"><a href="#testing.spring-boot-applications.using-application-arguments">Using Application Arguments</a></li><li data-level="1"><a href="#testing.spring-boot-applications.with-mock-environment">Testing With a Mock Environment</a></li><li data-level="1"><a href="#testing.spring-boot-applications.with-running-server">Testing With a Running Server</a></li><li data-level="1"><a href="#testing.spring-boot-applications.customizing-web-test-client">Customizing WebTestClient</a></li><li data-level="1"><a href="#testing.spring-boot-applications.jmx">Using JMX</a></li><li data-level="1"><a href="#testing.spring-boot-applications.observations">Using Observations</a></li><li data-level="1"><a href="#testing.spring-boot-applications.metrics">Using Metrics</a></li><li data-level="1"><a href="#testing.spring-boot-applications.tracing">Using Tracing</a></li><li data-level="1"><a href="#testing.spring-boot-applications.mocking-beans">Mocking and Spying Beans</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-tests">Auto-configured Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.json-tests">Auto-configured JSON Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.spring-mvc-tests">Auto-configured Spring MVC Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.spring-webflux-tests">Auto-configured Spring WebFlux Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.spring-graphql-tests">Auto-configured Spring GraphQL Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-cassandra">Auto-configured Data Cassandra Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-couchbase">Auto-configured Data Couchbase Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-elasticsearch">Auto-configured Data Elasticsearch Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-jpa">Auto-configured Data JPA Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-jdbc">Auto-configured JDBC Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-jdbc">Auto-configured Data JDBC Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-r2dbc">Auto-configured Data R2DBC Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-jooq">Auto-configured jOOQ Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-mongodb">Auto-configured Data MongoDB Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-neo4j">Auto-configured Data Neo4j Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-redis">Auto-configured Data Redis Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-ldap">Auto-configured Data LDAP Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-rest-client">Auto-configured REST Clients</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-restdocs">Auto-configured Spring REST Docs Tests</a></li><li data-level="2"><a href="#testing.spring-boot-applications.autoconfigured-spring-restdocs.with-mock-mvc">Auto-configured Spring REST Docs Tests With Mock MVC</a></li><li data-level="2"><a href="#testing.spring-boot-applications.autoconfigured-spring-restdocs.with-web-test-client">Auto-configured Spring REST Docs Tests With WebTestClient</a></li><li data-level="2"><a href="#testing.spring-boot-applications.autoconfigured-spring-restdocs.with-rest-assured">Auto-configured Spring REST Docs Tests With REST Assured</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-webservices">Auto-configured Spring Web Services Tests</a></li><li data-level="2"><a href="#testing.spring-boot-applications.autoconfigured-webservices.client">Auto-configured Spring Web Services Client Tests</a></li><li data-level="2"><a href="#testing.spring-boot-applications.autoconfigured-webservices.server">Auto-configured Spring Web Services Server Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.additional-autoconfiguration-and-slicing">Additional Auto-configuration and Slicing</a></li><li data-level="1"><a href="#testing.spring-boot-applications.user-configuration-and-slicing">User Configuration and Slicing</a></li><li data-level="1"><a href="#testing.spring-boot-applications.spock">Using Spock to Test Spring Boot Applications</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/testing/spring-boot-applications.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../index.html">Spring Boot</a></li>
<li><a href="../index.html">Reference</a></li>
<li><a href="index.html">Testing</a></li>
<li><a href="spring-boot-applications.html">Testing Spring Boot Applications</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../reference/testing/spring-boot-applications.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Testing Spring Boot Applications</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Testing Spring Boot Applications</h3><ul><li data-level="1"><a href="#testing.spring-boot-applications.detecting-web-app-type">Detecting Web Application Type</a></li><li data-level="1"><a href="#testing.spring-boot-applications.detecting-configuration">Detecting Test Configuration</a></li><li data-level="1"><a href="#testing.spring-boot-applications.using-main">Using the Test Configuration Main Method</a></li><li data-level="1"><a href="#testing.spring-boot-applications.excluding-configuration">Excluding Test Configuration</a></li><li data-level="1"><a href="#testing.spring-boot-applications.using-application-arguments">Using Application Arguments</a></li><li data-level="1"><a href="#testing.spring-boot-applications.with-mock-environment">Testing With a Mock Environment</a></li><li data-level="1"><a href="#testing.spring-boot-applications.with-running-server">Testing With a Running Server</a></li><li data-level="1"><a href="#testing.spring-boot-applications.customizing-web-test-client">Customizing WebTestClient</a></li><li data-level="1"><a href="#testing.spring-boot-applications.jmx">Using JMX</a></li><li data-level="1"><a href="#testing.spring-boot-applications.observations">Using Observations</a></li><li data-level="1"><a href="#testing.spring-boot-applications.metrics">Using Metrics</a></li><li data-level="1"><a href="#testing.spring-boot-applications.tracing">Using Tracing</a></li><li data-level="1"><a href="#testing.spring-boot-applications.mocking-beans">Mocking and Spying Beans</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-tests">Auto-configured Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.json-tests">Auto-configured JSON Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.spring-mvc-tests">Auto-configured Spring MVC Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.spring-webflux-tests">Auto-configured Spring WebFlux Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.spring-graphql-tests">Auto-configured Spring GraphQL Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-cassandra">Auto-configured Data Cassandra Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-couchbase">Auto-configured Data Couchbase Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-elasticsearch">Auto-configured Data Elasticsearch Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-jpa">Auto-configured Data JPA Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-jdbc">Auto-configured JDBC Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-jdbc">Auto-configured Data JDBC Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-r2dbc">Auto-configured Data R2DBC Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-jooq">Auto-configured jOOQ Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-mongodb">Auto-configured Data MongoDB Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-neo4j">Auto-configured Data Neo4j Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-redis">Auto-configured Data Redis Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-data-ldap">Auto-configured Data LDAP Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-rest-client">Auto-configured REST Clients</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-spring-restdocs">Auto-configured Spring REST Docs Tests</a></li><li data-level="2"><a href="#testing.spring-boot-applications.autoconfigured-spring-restdocs.with-mock-mvc">Auto-configured Spring REST Docs Tests With Mock MVC</a></li><li data-level="2"><a href="#testing.spring-boot-applications.autoconfigured-spring-restdocs.with-web-test-client">Auto-configured Spring REST Docs Tests With WebTestClient</a></li><li data-level="2"><a href="#testing.spring-boot-applications.autoconfigured-spring-restdocs.with-rest-assured">Auto-configured Spring REST Docs Tests With REST Assured</a></li><li data-level="1"><a href="#testing.spring-boot-applications.autoconfigured-webservices">Auto-configured Spring Web Services Tests</a></li><li data-level="2"><a href="#testing.spring-boot-applications.autoconfigured-webservices.client">Auto-configured Spring Web Services Client Tests</a></li><li data-level="2"><a href="#testing.spring-boot-applications.autoconfigured-webservices.server">Auto-configured Spring Web Services Server Tests</a></li><li data-level="1"><a href="#testing.spring-boot-applications.additional-autoconfiguration-and-slicing">Additional Auto-configuration and Slicing</a></li><li data-level="1"><a href="#testing.spring-boot-applications.user-configuration-and-slicing">User Configuration and Slicing</a></li><li data-level="1"><a href="#testing.spring-boot-applications.spock">Using Spock to Test Spring Boot Applications</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>A Spring Boot application is a Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a>, so nothing very special has to be done to test it beyond what you would normally do with a vanilla Spring context.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
External properties, logging, and other features of Spring Boot are installed in the context by default only if you use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringApplication.html"><code>SpringApplication</code></a> to create it.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Spring Boot provides a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> annotation, which can be used as an alternative to the standard <code>spring-test</code> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/context/ContextConfiguration.html"><code>@ContextConfiguration</code></a> annotation when you need Spring Boot features.
The annotation works by <a href="#testing.spring-boot-applications.detecting-configuration">creating the </a><a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a> used in your tests through <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringApplication.html"><code>SpringApplication</code></a>.
In addition to <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> a number of other annotations are also provided for <a href="#testing.spring-boot-applications.autoconfigured-tests">testing more specific slices</a> of an application.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you are using JUnit 4, do not forget to also add <code>@RunWith(SpringRunner.class)</code> to your test, otherwise the annotations will be ignored.
If you are using JUnit 5, there is no need to add the equivalent <code>@ExtendWith(SpringExtension.class)</code> as <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> and the other <code>@…​Test</code> annotations are already annotated with it.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>By default, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> will not start a server.
You can use the <code>webEnvironment</code> attribute of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> to further refine how your tests run:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>MOCK</code>(Default) : Loads a web <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a> and provides a mock web environment.
Embedded servers are not started when using this annotation.
If a web environment is not available on your classpath, this mode transparently falls back to creating a regular non-web <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a>.
It can be used in conjunction with <a href="#testing.spring-boot-applications.with-mock-environment"><code>@AutoConfigureMockMvc</code> or </a><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/AutoConfigureWebTestClient.html"><code>@AutoConfigureWebTestClient</code></a> for mock-based testing of your web application.</p>
</li>
<li>
<p><code>RANDOM_PORT</code>: Loads a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/context/WebServerApplicationContext.html"><code>WebServerApplicationContext</code></a> and provides a real web environment.
Embedded servers are started and listen on a random port.</p>
</li>
<li>
<p><code>DEFINED_PORT</code>: Loads a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/context/WebServerApplicationContext.html"><code>WebServerApplicationContext</code></a> and provides a real web environment.
Embedded servers are started and listen on a defined port (from your <code>application.properties</code>) or on the default port of <code>8080</code>.</p>
</li>
<li>
<p><code>NONE</code>: Loads an <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a> by using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringApplication.html"><code>SpringApplication</code></a> but does not provide <em>any</em> web environment (mock or otherwise).</p>
</li>
</ul>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If your test is <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/transaction/annotation/Transactional.html"><code>@Transactional</code></a>, it rolls back the transaction at the end of each test method by default.
However, as using this arrangement with either <code>RANDOM_PORT</code> or <code>DEFINED_PORT</code> implicitly provides a real servlet environment, the HTTP client and server run in separate threads and, thus, in separate transactions.
Any transaction initiated on the server does not roll back in this case.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> with <code>webEnvironment = WebEnvironment.RANDOM_PORT</code> will also start the management server on a separate random port if your application uses a different port for the management server.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.detecting-web-app-type"><a class="anchor" href="#testing.spring-boot-applications.detecting-web-app-type"></a>Detecting Web Application Type</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If Spring MVC is available, a regular MVC-based application context is configured.
If you have only Spring WebFlux, we will detect that and configure a WebFlux-based application context instead.</p>
</div>
<div class="paragraph">
<p>If both are present, Spring MVC takes precedence.
If you want to test a reactive web application in this scenario, you must set the <code>spring.main.web-application-type</code> property:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_1_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_1_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_1_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_java" class="tabpanel" id="_tabs_1_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>(properties = <span class="hljs-string">"spring.main.web-application-type=reactive"</span>)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebFluxTests</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_1_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest(properties = [<span class="hljs-meta-string">"spring.main.web-application-type=reactive"</span>])</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebFluxTests</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.detecting-configuration"><a class="anchor" href="#testing.spring-boot-applications.detecting-configuration"></a>Detecting Test Configuration</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you are familiar with the Spring Test Framework, you may be used to using <code>@ContextConfiguration(classes=…​)</code> in order to specify which Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> to load.
Alternatively, you might have often used nested <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> classes within your test.</p>
</div>
<div class="paragraph">
<p>When testing Spring Boot applications, this is often not required.
Spring Boot’s <code>@*Test</code> annotations search for your primary configuration automatically whenever you do not explicitly define one.</p>
</div>
<div class="paragraph">
<p>The search algorithm works up from the package that contains the test until it finds a class annotated with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html"><code>@SpringBootApplication</code></a> or <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringBootConfiguration.html"><code>@SpringBootConfiguration</code></a>.
As long as you <a class="xref page" href="../using/structuring-your-code.html">structured your code</a> in a sensible way, your main configuration is usually found.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>If you use a <a href="#testing.spring-boot-applications.autoconfigured-tests">test annotation to test a more specific slice of your application</a>, you should avoid adding configuration settings that are specific to a particular area on the <a href="#testing.spring-boot-applications.user-configuration-and-slicing">main method’s application class</a>.</p>
</div>
<div class="paragraph">
<p>The underlying component scan configuration of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html"><code>@SpringBootApplication</code></a> defines exclude filters that are used to make sure slicing works as expected.
If you are using an explicit <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/ComponentScan.html"><code>@ComponentScan</code></a> directive on your <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html"><code>@SpringBootApplication</code></a>-annotated class, be aware that those filters will be disabled.
If you are using slicing, you should define them again.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If you want to customize the primary configuration, you can use a nested <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/TestConfiguration.html"><code>@TestConfiguration</code></a> class.
Unlike a nested <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> class, which would be used instead of your application’s primary configuration, a nested <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/TestConfiguration.html"><code>@TestConfiguration</code></a> class is used in addition to your application’s primary configuration.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Spring’s test framework caches application contexts between tests.
Therefore, as long as your tests share the same configuration (no matter how it is discovered), the potentially time-consuming process of loading the context happens only once.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.using-main"><a class="anchor" href="#testing.spring-boot-applications.using-main"></a>Using the Test Configuration Main Method</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Typically the test configuration discovered by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> will be your main <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html"><code>@SpringBootApplication</code></a>.
In most well structured applications, this configuration class will also include the <code>main</code> method used to launch the application.</p>
</div>
<div class="paragraph">
<p>For example, the following is a very common code pattern for a typical Spring Boot application:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_2_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_2_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_2_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_java" class="tabpanel" id="_tabs_2_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.SpringApplication;
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.SpringBootApplication;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootApplication</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplication</span> </span>{

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">static</span> <span class="hljs-keyword">void</span> <span class="hljs-title">main</span><span class="hljs-params">(String[] args)</span> </span>{
		SpringApplication.run(MyApplication<span class="hljs-class">.<span class="hljs-keyword">class</span>, <span class="hljs-title">args</span>)</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_2_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.SpringBootApplication
<span class="hljs-keyword">import</span> org.springframework.boot.docs.using.structuringyourcode.locatingthemainclass.MyApplication
<span class="hljs-keyword">import</span> org.springframework.boot.runApplication

</span><span class="fold-block"><span class="hljs-meta">@SpringBootApplication</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplication</span></span>

<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">main</span><span class="hljs-params">(args: <span class="hljs-type">Array</span>&lt;<span class="hljs-type">String</span>&gt;)</span></span> {
	runApplication&lt;MyApplication&gt;(*args)
}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>In the example above, the <code>main</code> method doesn’t do anything other than delegate to <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringApplication.html#run(java.lang.Class,java.lang.String…​)"><code>SpringApplication.run(Class, String…​)</code></a>.
It is, however, possible to have a more complex <code>main</code> method that applies customizations before calling <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringApplication.html#run(java.lang.Class,java.lang.String…​)"><code>SpringApplication.run(Class, String…​)</code></a>.</p>
</div>
<div class="paragraph">
<p>For example, here is an application that changes the banner mode and sets additional profiles:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_3_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_3_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_3_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_java" class="tabpanel" id="_tabs_3_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.Banner;
<span class="hljs-keyword">import</span> org.springframework.boot.SpringApplication;
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.SpringBootApplication;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootApplication</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplication</span> </span>{

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">static</span> <span class="hljs-keyword">void</span> <span class="hljs-title">main</span><span class="hljs-params">(String[] args)</span> </span>{
		SpringApplication application = <span class="hljs-keyword">new</span> SpringApplication(MyApplication<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;
		application.setBannerMode(Banner.Mode.OFF);
		application.setAdditionalProfiles(<span class="hljs-string">"myprofile"</span>);
		application.run(args);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_3_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.Banner
<span class="hljs-keyword">import</span> org.springframework.boot.runApplication
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.SpringBootApplication

</span><span class="fold-block"><span class="hljs-meta">@SpringBootApplication</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplication</span></span>

<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">main</span><span class="hljs-params">(args: <span class="hljs-type">Array</span>&lt;<span class="hljs-type">String</span>&gt;)</span></span> {
	runApplication&lt;MyApplication&gt;(*args) {
		setBannerMode(Banner.Mode.OFF)
		setAdditionalProfiles(<span class="hljs-string">"myprofile"</span>)
	}
}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Since customizations in the <code>main</code> method can affect the resulting <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a>, it’s possible that you might also want to use the <code>main</code> method to create the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a> used in your tests.
By default, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> will not call your <code>main</code> method, and instead the class itself is used directly to create the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a></p>
</div>
<div class="paragraph">
<p>If you want to change this behavior, you can change the <code>useMainMethod</code> attribute of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> to <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.UseMainMethod.html#ALWAYS"><code>SpringBootTest.UseMainMethod.ALWAYS</code></a> or <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.UseMainMethod.html#WHEN_AVAILABLE"><code>SpringBootTest.UseMainMethod.WHEN_AVAILABLE</code></a>.
When set to <code>ALWAYS</code>, the test will fail if no <code>main</code> method can be found.
When set to <code>WHEN_AVAILABLE</code> the <code>main</code> method will be used if it is available, otherwise the standard loading mechanism will be used.</p>
</div>
<div class="paragraph">
<p>For example, the following test will invoke the <code>main</code> method of <code>MyApplication</code> in order to create the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a>.
If the main method sets additional profiles then those will be active when the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a> starts.</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_4_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_4_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_4_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_java" class="tabpanel" id="_tabs_4_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest.UseMainMethod;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>(useMainMethod = UseMainMethod.ALWAYS)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplicationTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">exampleTest</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-comment">// ...</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_4_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest.UseMainMethod

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest(useMainMethod = UseMainMethod.ALWAYS)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplicationTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">exampleTest</span><span class="hljs-params">()</span></span> {
		<span class="hljs-comment">// ...</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.excluding-configuration"><a class="anchor" href="#testing.spring-boot-applications.excluding-configuration"></a>Excluding Test Configuration</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If your application uses component scanning (for example, if you use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html"><code>@SpringBootApplication</code></a> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/ComponentScan.html"><code>@ComponentScan</code></a>), you may find top-level configuration classes that you created only for specific tests accidentally get picked up everywhere.</p>
</div>
<div class="paragraph">
<p>As we <a href="#testing.spring-boot-applications.detecting-configuration">have seen earlier</a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/TestConfiguration.html"><code>@TestConfiguration</code></a> can be used on an inner class of a test to customize the primary configuration.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/TestConfiguration.html"><code>@TestConfiguration</code></a> can also be used on a top-level class. Doing so indicates that the class should not be picked up by scanning.
You can then import the class explicitly where it is required, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_5_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_5_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_5_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_java" class="tabpanel" id="_tabs_5_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Import;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>
<span class="hljs-meta">@Import</span>(MyTestsConfiguration<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">class</span> <span class="hljs-title">MyTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">exampleTest</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-comment">// ...</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_5_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Import

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>
<span class="hljs-meta">@Import(MyTestsConfiguration::class)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">exampleTest</span><span class="hljs-params">()</span></span> {
		<span class="hljs-comment">// ...</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If you directly use <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/ComponentScan.html"><code>@ComponentScan</code></a> (that is, not through <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html"><code>@SpringBootApplication</code></a>) you need to register the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/TypeExcludeFilter.html"><code>TypeExcludeFilter</code></a> with it.
See the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/TypeExcludeFilter.html"><code>TypeExcludeFilter</code></a> API documentation for details.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
An imported <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/TestConfiguration.html"><code>@TestConfiguration</code></a> is processed earlier than an inner-class <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/TestConfiguration.html"><code>@TestConfiguration</code></a> and an imported <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/TestConfiguration.html"><code>@TestConfiguration</code></a> will be processed before any configuration found through component scanning.
Generally speaking, this difference in ordering has no noticeable effect but it is something to be aware of if you’re relying on bean overriding.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.using-application-arguments"><a class="anchor" href="#testing.spring-boot-applications.using-application-arguments"></a>Using Application Arguments</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If your application expects <a class="xref page" href="../features/spring-application.html#features.spring-application.application-arguments">arguments</a>, you can
have <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> inject them using the <code>args</code> attribute.</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_6_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_6_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_6_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_java" class="tabpanel" id="_tabs_6_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.ApplicationArguments;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>(args = <span class="hljs-string">"--app.test=one"</span>)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplicationArgumentTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">applicationArgumentsPopulated</span><span class="hljs-params">(@Autowired ApplicationArguments args)</span> </span>{
		assertThat(args.getOptionNames()).containsOnly(<span class="hljs-string">"app.test"</span>);
		assertThat(args.getOptionValues(<span class="hljs-string">"app.test"</span>)).containsOnly(<span class="hljs-string">"one"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_6_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.assertj.core.api.Assertions.assertThat
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.ApplicationArguments
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest(args = [<span class="hljs-meta-string">"--app.test=one"</span>])</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplicationArgumentTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">applicationArgumentsPopulated</span><span class="hljs-params">(<span class="hljs-meta">@Autowired</span> args: <span class="hljs-type">ApplicationArguments</span>)</span></span> {
		assertThat(args.optionNames).containsOnly(<span class="hljs-string">"app.test"</span>)
		assertThat(args.getOptionValues(<span class="hljs-string">"app.test"</span>)).containsOnly(<span class="hljs-string">"one"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.with-mock-environment"><a class="anchor" href="#testing.spring-boot-applications.with-mock-environment"></a>Testing With a Mock Environment</h2>
<div class="sectionbody">
<div class="paragraph">
<p>By default, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> does not start the server but instead sets up a mock environment for testing web endpoints.</p>
</div>
<div class="paragraph">
<p>With Spring MVC, we can query our web endpoints using <a href="https://docs.spring.io/spring-framework/reference/6.2/testing/mockmvc.html"><code>MockMvc</code></a>.
Three integrations are available:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>The regular <a href="https://docs.spring.io/spring-framework/reference/6.2/testing/mockmvc/hamcrest.html"><code>MockMvc</code></a> that uses Hamcrest.</p>
</li>
<li>
<p><a href="https://docs.spring.io/spring-framework/reference/6.2/testing/mockmvc/assertj.html"><code>MockMvcTester</code></a> that wraps <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/servlet/MockMvc.html"><code>MockMvc</code></a> and uses AssertJ.</p>
</li>
<li>
<p><a href="https://docs.spring.io/spring-framework/reference/6.2/testing/webtestclient.html"><code>WebTestClient</code></a> where <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/servlet/MockMvc.html"><code>MockMvc</code></a> is plugged in as the server to handle requests with.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The following example showcases the available integrations:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_7_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_7_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_7_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_java" class="tabpanel" id="_tabs_7_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest;
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.WebTestClient;
<span class="hljs-keyword">import</span> org.springframework.test.web.servlet.MockMvc;
<span class="hljs-keyword">import</span> org.springframework.test.web.servlet.assertj.MockMvcTester;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>
<span class="hljs-meta">@AutoConfigureMockMvc</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMockMvcTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">testWithMockMvc</span><span class="hljs-params">(@Autowired MockMvc mvc)</span> <span class="hljs-keyword">throws</span> Exception </span>{
		mvc.perform(get(<span class="hljs-string">"/"</span>)).andExpect(status().isOk()).andExpect(content().string(<span class="hljs-string">"Hello World"</span>));
	}

	<span class="hljs-comment">// If AssertJ is on the classpath, you can use MockMvcTester</span>
	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">testWithMockMvcTester</span><span class="hljs-params">(@Autowired MockMvcTester mvc)</span> </span>{
		assertThat(mvc.get().uri(<span class="hljs-string">"/"</span>)).hasStatusOk().hasBodyTextEqualTo(<span class="hljs-string">"Hello World"</span>);
	}

	<span class="hljs-comment">// If Spring WebFlux is on the classpath, you can drive MVC tests with a WebTestClient</span>
	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">testWithWebTestClient</span><span class="hljs-params">(@Autowired WebTestClient webClient)</span> </span>{
		webClient
				.get().uri(<span class="hljs-string">"/"</span>)
				.exchange()
				.expectStatus().isOk()
				.expectBody(String<span class="hljs-class">.<span class="hljs-keyword">class</span>).<span class="hljs-title">isEqualTo</span>("<span class="hljs-title">Hello</span> <span class="hljs-title">World</span>")</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_7_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.assertj.core.api.Assertions.assertThat
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.WebTestClient
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.expectBody
<span class="hljs-keyword">import</span> org.springframework.test.web.servlet.assertj.MockMvcTester

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>
<span class="hljs-meta">@AutoConfigureMockMvc</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMockMvcTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">testWithMockMvc</span><span class="hljs-params">(<span class="hljs-meta">@Autowired</span> mvc: <span class="hljs-type">MockMvcTester</span>)</span></span> {
		assertThat(mvc.<span class="hljs-keyword">get</span>().uri(<span class="hljs-string">"/"</span>)).hasStatusOk()
				.hasBodyTextEqualTo(<span class="hljs-string">"Hello World"</span>)
	}

	<span class="hljs-comment">// If Spring WebFlux is on the classpath, you can drive MVC tests with a WebTestClient</span>

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">testWithWebTestClient</span><span class="hljs-params">(<span class="hljs-meta">@Autowired</span> webClient: <span class="hljs-type">WebTestClient</span>)</span></span> {
		webClient
				.<span class="hljs-keyword">get</span>().uri(<span class="hljs-string">"/"</span>)
				.exchange()
				.expectStatus().isOk
				.expectBody&lt;String&gt;().isEqualTo(<span class="hljs-string">"Hello World"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you want to focus only on the web layer and not start a complete <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a>, consider <a href="#testing.spring-boot-applications.spring-mvc-tests">using </a><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/servlet/WebMvcTest.html"><code>@WebMvcTest</code></a> instead.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>With Spring WebFlux endpoints, you can use <a href="https://docs.spring.io/spring-framework/reference/6.2/testing/webtestclient.html"><code>WebTestClient</code></a> as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_8_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_8_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_8_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_java" class="tabpanel" id="_tabs_8_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest;
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.WebTestClient;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>
<span class="hljs-meta">@AutoConfigureWebTestClient</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMockWebTestClientTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">exampleTest</span><span class="hljs-params">(@Autowired WebTestClient webClient)</span> </span>{
		webClient
			.get().uri(<span class="hljs-string">"/"</span>)
			.exchange()
			.expectStatus().isOk()
			.expectBody(String<span class="hljs-class">.<span class="hljs-keyword">class</span>).<span class="hljs-title">isEqualTo</span>("<span class="hljs-title">Hello</span> <span class="hljs-title">World</span>")</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_8_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.WebTestClient
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.expectBody

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>
<span class="hljs-meta">@AutoConfigureWebTestClient</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMockWebTestClientTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">exampleTest</span><span class="hljs-params">(<span class="hljs-meta">@Autowired</span> webClient: <span class="hljs-type">WebTestClient</span>)</span></span> {
		webClient
			.<span class="hljs-keyword">get</span>().uri(<span class="hljs-string">"/"</span>)
			.exchange()
			.expectStatus().isOk
			.expectBody&lt;String&gt;().isEqualTo(<span class="hljs-string">"Hello World"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<div class="paragraph">
<p>Testing within a mocked environment is usually faster than running with a full servlet container.
However, since mocking occurs at the Spring MVC layer, code that relies on lower-level servlet container behavior cannot be directly tested with MockMvc.</p>
</div>
<div class="paragraph">
<p>For example, Spring Boot’s error handling is based on the “error page” support provided by the servlet container.
This means that, whilst you can test your MVC layer throws and handles exceptions as expected, you cannot directly test that a specific <a class="xref page" href="../web/servlet.html#web.servlet.spring-mvc.error-handling.error-pages">custom error page</a> is rendered.
If you need to test these lower-level concerns, you can start a fully running server as described in the next section.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.with-running-server"><a class="anchor" href="#testing.spring-boot-applications.with-running-server"></a>Testing With a Running Server</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you need to start a full running server, we recommend that you use random ports.
If you use <code>@SpringBootTest(webEnvironment=WebEnvironment.RANDOM_PORT)</code>, an available port is picked at random each time your test runs.</p>
</div>
<div class="paragraph">
<p>The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/web/server/LocalServerPort.html"><code>@LocalServerPort</code></a> annotation can be used to <a class="xref page" href="../../how-to/webserver.html#howto.webserver.discover-port">inject the actual port used</a> into your test.
For convenience, tests that need to make REST calls to the started server can additionally autowire a <a href="https://docs.spring.io/spring-framework/reference/6.2/testing/webtestclient.html"><code>WebTestClient</code></a>, which resolves relative links to the running server and comes with a dedicated API for verifying responses, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_9">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_9_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_9_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_9_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_9_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_9_java" class="tabpanel" id="_tabs_9_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.WebTestClient;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>(webEnvironment = WebEnvironment.RANDOM_PORT)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRandomPortWebTestClientTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">exampleTest</span><span class="hljs-params">(@Autowired WebTestClient webClient)</span> </span>{
		webClient
			.get().uri(<span class="hljs-string">"/"</span>)
			.exchange()
			.expectStatus().isOk()
			.expectBody(String<span class="hljs-class">.<span class="hljs-keyword">class</span>).<span class="hljs-title">isEqualTo</span>("<span class="hljs-title">Hello</span> <span class="hljs-title">World</span>")</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_9_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_9_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest.WebEnvironment
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.WebTestClient
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.expectBody

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRandomPortWebTestClientTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">exampleTest</span><span class="hljs-params">(<span class="hljs-meta">@Autowired</span> webClient: <span class="hljs-type">WebTestClient</span>)</span></span> {
		webClient
			.<span class="hljs-keyword">get</span>().uri(<span class="hljs-string">"/"</span>)
			.exchange()
			.expectStatus().isOk
			.expectBody&lt;String&gt;().isEqualTo(<span class="hljs-string">"Hello World"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/reactive/server/WebTestClient.html"><code>WebTestClient</code></a> can also used with a <a href="#testing.spring-boot-applications.with-mock-environment">mock environment</a>, removing the need for a running server, by annotating your test class with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/AutoConfigureWebTestClient.html"><code>@AutoConfigureWebTestClient</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>This setup requires <code>spring-webflux</code> on the classpath.
If you can not or will not add webflux, Spring Boot also provides a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/web/client/TestRestTemplate.html"><code>TestRestTemplate</code></a> facility:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_10">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_10_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_10_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_10_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_10_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_10_java" class="tabpanel" id="_tabs_10_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
<span class="hljs-keyword">import</span> org.springframework.boot.test.web.client.TestRestTemplate;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>(webEnvironment = WebEnvironment.RANDOM_PORT)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRandomPortTestRestTemplateTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">exampleTest</span><span class="hljs-params">(@Autowired TestRestTemplate restTemplate)</span> </span>{
		String body = restTemplate.getForObject(<span class="hljs-string">"/"</span>, String<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;
		assertThat(body).isEqualTo(<span class="hljs-string">"Hello World"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_10_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_10_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.assertj.core.api.Assertions.assertThat
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest.WebEnvironment
<span class="hljs-keyword">import</span> org.springframework.boot.test.web.client.TestRestTemplate

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRandomPortTestRestTemplateTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">exampleTest</span><span class="hljs-params">(<span class="hljs-meta">@Autowired</span> restTemplate: <span class="hljs-type">TestRestTemplate</span>)</span></span> {
		<span class="hljs-keyword">val</span> body = restTemplate.getForObject(<span class="hljs-string">"/"</span>, String::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>)</span>
		assertThat(body).isEqualTo(<span class="hljs-string">"Hello World"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.customizing-web-test-client"><a class="anchor" href="#testing.spring-boot-applications.customizing-web-test-client"></a>Customizing WebTestClient</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To customize the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/reactive/server/WebTestClient.html"><code>WebTestClient</code></a> bean, configure a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/web/reactive/server/WebTestClientBuilderCustomizer.html"><code>WebTestClientBuilderCustomizer</code></a> bean.
Any such beans are called with the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/reactive/server/WebTestClient.Builder.html"><code>WebTestClient.Builder</code></a> that is used to create the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/reactive/server/WebTestClient.html"><code>WebTestClient</code></a>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.jmx"><a class="anchor" href="#testing.spring-boot-applications.jmx"></a>Using JMX</h2>
<div class="sectionbody">
<div class="paragraph">
<p>As the test context framework caches context, JMX is disabled by default to prevent identical components to register on the same domain.
If such test needs access to an <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.management/javax/management/MBeanServer.html" target="_blank"><code>MBeanServer</code></a>, consider marking it dirty as well:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_11">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_11_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_11_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_11_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_11_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_11_java" class="tabpanel" id="_tabs_11_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> javax.management.MBeanServer;

<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest;
<span class="hljs-keyword">import</span> org.springframework.test.annotation.DirtiesContext;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>(properties = <span class="hljs-string">"spring.jmx.enabled=true"</span>)
<span class="hljs-meta">@DirtiesContext</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyJmxTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> MBeanServer mBeanServer;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">exampleTest</span><span class="hljs-params">()</span> </span>{
		assertThat(<span class="hljs-keyword">this</span>.mBeanServer.getDomains()).contains(<span class="hljs-string">"java.lang"</span>);
		<span class="hljs-comment">// ...</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_11_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_11_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> javax.management.MBeanServer

<span class="hljs-keyword">import</span> org.assertj.core.api.Assertions.assertThat
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest
<span class="hljs-keyword">import</span> org.springframework.test.<span class="hljs-keyword">annotation</span>.DirtiesContext

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest(properties = [<span class="hljs-meta-string">"spring.jmx.enabled=true"</span>])</span>
<span class="hljs-meta">@DirtiesContext</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyJmxTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> mBeanServer: MBeanServer) {

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">exampleTest</span><span class="hljs-params">()</span></span> {
		assertThat(mBeanServer.domains).contains(<span class="hljs-string">"java.lang"</span>)
		<span class="hljs-comment">// ...</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.observations"><a class="anchor" href="#testing.spring-boot-applications.observations"></a>Using Observations</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you annotate <a href="#testing.spring-boot-applications.autoconfigured-tests">a sliced test</a> with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/actuate/observability/AutoConfigureObservability.html"><code>@AutoConfigureObservability</code></a>, it auto-configures an <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-observation/1.14.7/io/micrometer/observation/ObservationRegistry.html" target="_blank"><code>ObservationRegistry</code></a>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.metrics"><a class="anchor" href="#testing.spring-boot-applications.metrics"></a>Using Metrics</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Regardless of your classpath, meter registries, except the in-memory backed, are not auto-configured when using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a>.</p>
</div>
<div class="paragraph">
<p>If you need to export metrics to a different backend as part of an integration test, annotate it with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/actuate/observability/AutoConfigureObservability.html"><code>@AutoConfigureObservability</code></a>.</p>
</div>
<div class="paragraph">
<p>If you annotate <a href="#testing.spring-boot-applications.autoconfigured-tests">a sliced test</a> with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/actuate/observability/AutoConfigureObservability.html"><code>@AutoConfigureObservability</code></a>, it auto-configures an in-memory <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/MeterRegistry.html" target="_blank"><code>MeterRegistry</code></a>.
Data exporting in sliced tests is not supported with the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/actuate/observability/AutoConfigureObservability.html"><code>@AutoConfigureObservability</code></a> annotation.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.tracing"><a class="anchor" href="#testing.spring-boot-applications.tracing"></a>Using Tracing</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Regardless of your classpath, tracing components which are reporting data are not auto-configured when using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a>.</p>
</div>
<div class="paragraph">
<p>If you need those components as part of an integration test, annotate the test with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/actuate/observability/AutoConfigureObservability.html"><code>@AutoConfigureObservability</code></a>.</p>
</div>
<div class="paragraph">
<p>If you have created your own reporting components (e.g. a custom <a class="apiref external" href="https://javadoc.io/doc/io.opentelemetry/opentelemetry-sdk-trace/1.43.0/io/opentelemetry/sdk/trace/export/SpanExporter.html" target="_blank"><code>SpanExporter</code></a> or <code>brave.handler.SpanHandler</code>) and you don’t want them to be active in tests, you can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/autoconfigure/tracing/ConditionalOnEnabledTracing.html"><code>@ConditionalOnEnabledTracing</code></a> annotation to disable them.</p>
</div>
<div class="paragraph">
<p>If you annotate <a href="#testing.spring-boot-applications.autoconfigured-tests">a sliced test</a> with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/actuate/observability/AutoConfigureObservability.html"><code>@AutoConfigureObservability</code></a>, it auto-configures a no-op <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-tracing/1.4.6/io/micrometer/tracing/Tracer.html" target="_blank"><code>Tracer</code></a>.
Data exporting in sliced tests is not supported with the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/actuate/observability/AutoConfigureObservability.html"><code>@AutoConfigureObservability</code></a> annotation.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.mocking-beans"><a class="anchor" href="#testing.spring-boot-applications.mocking-beans"></a>Mocking and Spying Beans</h2>
<div class="sectionbody">
<div class="paragraph">
<p>When running tests, it is sometimes necessary to mock certain components within your application context.
For example, you may have a facade over some remote service that is unavailable during development.
Mocking can also be useful when you want to simulate failures that might be hard to trigger in a real environment.</p>
</div>
<div class="paragraph">
<p>Spring Framework includes a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/context/bean/override/mockito/MockitoBean.html"><code>@MockitoBean</code></a> annotation that can be used to define a Mockito mock for a bean inside your <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a>.
Additionally, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/context/bean/override/mockito/MockitoSpyBean.html"><code>@MockitoSpyBean</code></a> can be used to define a Mockito spy.
Learn more about these features in the <a href="https://docs.spring.io/spring-framework/reference/6.2/testing/annotations/integration-spring/annotation-mockitobean.html">Spring Framework documentation</a>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-tests"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-tests"></a>Auto-configured Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot’s auto-configuration system works well for applications but can sometimes be a little too much for tests.
It often helps to load only the parts of the configuration that are required to test a “slice” of your application.
For example, you might want to test that Spring MVC controllers are mapping URLs correctly, and you do not want to involve database calls in those tests, or you might want to test JPA entities, and you are not interested in the web layer when those tests run.</p>
</div>
<div class="paragraph">
<p>The <code>spring-boot-test-autoconfigure</code> module includes a number of annotations that can be used to automatically configure such “slices”.
Each of them works in a similar way, providing a <code>@…​Test</code> annotation that loads the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a> and one or more <code>@AutoConfigure…​</code> annotations that can be used to customize auto-configuration settings.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Each slice restricts component scan to appropriate components and loads a very restricted set of auto-configuration classes.
If you need to exclude one of them, most <code>@…​Test</code> annotations provide an <code>excludeAutoConfiguration</code> attribute.
Alternatively, you can use <code>@ImportAutoConfiguration#exclude</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Including multiple “slices” by using several <code>@…​Test</code> annotations in one test is not supported.
If you need multiple “slices”, pick one of the <code>@…​Test</code> annotations and include the <code>@AutoConfigure…​</code> annotations of the other “slices” by hand.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
It is also possible to use the <code>@AutoConfigure…​</code> annotations with the standard <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> annotation.
You can use this combination if you are not interested in “slicing” your application but you want some of the auto-configured test beans.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.json-tests"><a class="anchor" href="#testing.spring-boot-applications.json-tests"></a>Auto-configured JSON Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To test that object JSON serialization and deserialization is working as expected, you can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/json/JsonTest.html"><code>@JsonTest</code></a> annotation.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/json/JsonTest.html"><code>@JsonTest</code></a> auto-configures the available supported JSON mapper, which can be one of the following libraries:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Jackson <a class="apiref external" href="https://javadoc.io/doc/com.fasterxml.jackson.core/jackson-databind/2.18.4/com/fasterxml/jackson/databind/ObjectMapper.html" target="_blank"><code>ObjectMapper</code></a>, any <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jackson/JsonComponent.html"><code>@JsonComponent</code></a> beans and any Jackson <a class="apiref external" href="https://javadoc.io/doc/com.fasterxml.jackson.core/jackson-databind/2.18.4/com/fasterxml/jackson/databind/Module.html" target="_blank"><code>Module</code></a></p>
</li>
<li>
<p><code>Gson</code></p>
</li>
<li>
<p><code>Jsonb</code></p>
</li>
</ul>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configurations that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/json/JsonTest.html"><code>@JsonTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If you need to configure elements of the auto-configuration, you can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/json/AutoConfigureJsonTesters.html"><code>@AutoConfigureJsonTesters</code></a> annotation.</p>
</div>
<div class="paragraph">
<p>Spring Boot includes AssertJ-based helpers that work with the JSONAssert and JsonPath libraries to check that JSON appears as expected.
The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/json/JacksonTester.html"><code>JacksonTester</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/json/GsonTester.html"><code>GsonTester</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/json/JsonbTester.html"><code>JsonbTester</code></a>, and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/json/BasicJsonTester.html"><code>BasicJsonTester</code></a> classes can be used for Jackson, Gson, Jsonb, and Strings respectively.
Any helper fields on the test class can be <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Autowired.html"><code>@Autowired</code></a> when using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/json/JsonTest.html"><code>@JsonTest</code></a>.
The following example shows a test class for Jackson:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_12">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_12_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_12_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_12_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_12_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_12_java" class="tabpanel" id="_tabs_12_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.json.JsonTest;
<span class="hljs-keyword">import</span> org.springframework.boot.test.json.JacksonTester;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;

</span><span class="fold-block"><span class="hljs-meta">@JsonTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyJsonTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> JacksonTester&lt;VehicleDetails&gt; json;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">serialize</span><span class="hljs-params">()</span> <span class="hljs-keyword">throws</span> Exception </span>{
		VehicleDetails details = <span class="hljs-keyword">new</span> VehicleDetails(<span class="hljs-string">"Honda"</span>, <span class="hljs-string">"Civic"</span>);
		<span class="hljs-comment">// Assert against a `.json` file in the same package as the test</span>
		assertThat(<span class="hljs-keyword">this</span>.json.write(details)).isEqualToJson(<span class="hljs-string">"expected.json"</span>);
		<span class="hljs-comment">// Or use JSON path based assertions</span>
		assertThat(<span class="hljs-keyword">this</span>.json.write(details)).hasJsonPathStringValue(<span class="hljs-string">"@.make"</span>);
		assertThat(<span class="hljs-keyword">this</span>.json.write(details)).extractingJsonPathStringValue(<span class="hljs-string">"@.make"</span>).isEqualTo(<span class="hljs-string">"Honda"</span>);
	}

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">deserialize</span><span class="hljs-params">()</span> <span class="hljs-keyword">throws</span> Exception </span>{
		String content = <span class="hljs-string">"{\"make\":\"Ford\",\"model\":\"Focus\"}"</span>;
		assertThat(<span class="hljs-keyword">this</span>.json.parse(content)).isEqualTo(<span class="hljs-keyword">new</span> VehicleDetails(<span class="hljs-string">"Ford"</span>, <span class="hljs-string">"Focus"</span>));
		assertThat(<span class="hljs-keyword">this</span>.json.parseObject(content).getMake()).isEqualTo(<span class="hljs-string">"Ford"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_12_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_12_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.assertj.core.api.Assertions.assertThat
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.json.JsonTest
<span class="hljs-keyword">import</span> org.springframework.boot.test.json.JacksonTester

</span><span class="fold-block"><span class="hljs-meta">@JsonTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyJsonTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> json: JacksonTester&lt;VehicleDetails&gt;) {

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">serialize</span><span class="hljs-params">()</span></span> {
		<span class="hljs-keyword">val</span> details = VehicleDetails(<span class="hljs-string">"Honda"</span>, <span class="hljs-string">"Civic"</span>)
		<span class="hljs-comment">// Assert against a `.json` file in the same package as the test</span>
		assertThat(json.write(details)).isEqualToJson(<span class="hljs-string">"expected.json"</span>)
		<span class="hljs-comment">// Or use JSON path based assertions</span>
		assertThat(json.write(details)).hasJsonPathStringValue(<span class="hljs-string">"@.make"</span>)
		assertThat(json.write(details)).extractingJsonPathStringValue(<span class="hljs-string">"@.make"</span>).isEqualTo(<span class="hljs-string">"Honda"</span>)
	}

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">deserialize</span><span class="hljs-params">()</span></span> {
		<span class="hljs-keyword">val</span> content = <span class="hljs-string">"{\"make\":\"Ford\",\"model\":\"Focus\"}"</span>
		assertThat(json.parse(content)).isEqualTo(VehicleDetails(<span class="hljs-string">"Ford"</span>, <span class="hljs-string">"Focus"</span>))
		assertThat(json.parseObject(content).make).isEqualTo(<span class="hljs-string">"Ford"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
JSON helper classes can also be used directly in standard unit tests.
To do so, call the <code>initFields</code> method of the helper in your <a class="apiref external" href="https://junit.org/junit5/docs/5.11.4/api/org.junit.jupiter.api/org/junit/jupiter/api/BeforeEach.html" target="_blank"><code>@BeforeEach</code></a> method if you do not use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/json/JsonTest.html"><code>@JsonTest</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If you use Spring Boot’s AssertJ-based helpers to assert on a number value at a given JSON path, you might not be able to use <code>isEqualTo</code> depending on the type.
Instead, you can use AssertJ’s <code>satisfies</code> to assert that the value matches the given condition.
For instance, the following example asserts that the actual number is a float value close to <code>0.15</code> within an offset of <code>0.01</code>.</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_13">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_13_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_13_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_13_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_13_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_13_java" class="tabpanel" id="_tabs_13_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">someTest</span><span class="hljs-params">()</span> <span class="hljs-keyword">throws</span> Exception </span>{
		SomeObject value = <span class="hljs-keyword">new</span> SomeObject(<span class="hljs-number">0.152f</span>);
		assertThat(<span class="hljs-keyword">this</span>.json.write(value)).extractingJsonPathNumberValue(<span class="hljs-string">"@.test.numberValue"</span>)
			.satisfies((number) -&gt; assertThat(number.floatValue()).isCloseTo(<span class="hljs-number">0.15f</span>, within(<span class="hljs-number">0.01f</span>)));
	}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_13_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_13_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin">	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someTest</span><span class="hljs-params">()</span></span> {
		<span class="hljs-keyword">val</span> value = SomeObject(<span class="hljs-number">0.152f</span>)
		assertThat(json.write(value)).extractingJsonPathNumberValue(<span class="hljs-string">"@.test.numberValue"</span>)
			.satisfies(ThrowingConsumer { number -&gt;
				assertThat(number.toFloat()).isCloseTo(<span class="hljs-number">0.15f</span>, within(<span class="hljs-number">0.01f</span>))
			})
	}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.spring-mvc-tests"><a class="anchor" href="#testing.spring-boot-applications.spring-mvc-tests"></a>Auto-configured Spring MVC Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To test whether Spring MVC controllers are working as expected, use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/servlet/WebMvcTest.html"><code>@WebMvcTest</code></a> annotation.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/servlet/WebMvcTest.html"><code>@WebMvcTest</code></a> auto-configures the Spring MVC infrastructure and limits scanned beans to <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Controller.html"><code>@Controller</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/annotation/ControllerAdvice.html"><code>@ControllerAdvice</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jackson/JsonComponent.html"><code>@JsonComponent</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/converter/Converter.html"><code>Converter</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/converter/GenericConverter.html"><code>GenericConverter</code></a>, <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/HandlerInterceptor.html"><code>HandlerInterceptor</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/WebMvcConfigurer.html"><code>WebMvcConfigurer</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/servlet/WebMvcRegistrations.html"><code>WebMvcRegistrations</code></a>, and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/method/support/HandlerMethodArgumentResolver.html"><code>HandlerMethodArgumentResolver</code></a>.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/servlet/WebMvcTest.html"><code>@WebMvcTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configuration settings that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/servlet/WebMvcTest.html"><code>@WebMvcTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you need to register extra components, such as the Jackson <a class="apiref external" href="https://javadoc.io/doc/com.fasterxml.jackson.core/jackson-databind/2.18.4/com/fasterxml/jackson/databind/Module.html" target="_blank"><code>Module</code></a>, you can import additional configuration classes by using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Import.html"><code>@Import</code></a> on your test.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Often, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/servlet/WebMvcTest.html"><code>@WebMvcTest</code></a> is limited to a single controller and is used in combination with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/context/bean/override/mockito/MockitoBean.html"><code>@MockitoBean</code></a> to provide mock implementations for required collaborators.</p>
</div>
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/servlet/WebMvcTest.html"><code>@WebMvcTest</code></a> also auto-configures <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/servlet/MockMvc.html"><code>MockMvc</code></a>.
Mock MVC offers a powerful way to quickly test MVC controllers without needing to start a full HTTP server.
If AssertJ is available, the AssertJ support provided by <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/servlet/assertj/MockMvcTester.html"><code>MockMvcTester</code></a> is auto-configured as well.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can also auto-configure <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/servlet/MockMvc.html"><code>MockMvc</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/servlet/assertj/MockMvcTester.html"><code>MockMvcTester</code></a> in a non-<code>@WebMvcTest</code> (such as <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a>) by annotating it with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/servlet/AutoConfigureMockMvc.html"><code>@AutoConfigureMockMvc</code></a>.
The following example uses <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/servlet/assertj/MockMvcTester.html"><code>MockMvcTester</code></a>:
</td>
</tr>
</tbody></table>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_14">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_14_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_14_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_14_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_14_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_14_java" class="tabpanel" id="_tabs_14_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
<span class="hljs-keyword">import</span> org.springframework.http.MediaType;
<span class="hljs-keyword">import</span> org.springframework.test.context.bean.override.mockito.MockitoBean;
<span class="hljs-keyword">import</span> org.springframework.test.web.servlet.assertj.MockMvcTester;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.mockito.BDDMockito.given;

</span><span class="fold-block"><span class="hljs-meta">@WebMvcTest</span>(UserVehicleController<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">class</span> <span class="hljs-title">MyControllerTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> MockMvcTester mvc;

	<span class="hljs-meta">@MockitoBean</span>
	<span class="hljs-keyword">private</span> UserVehicleService userVehicleService;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">testExample</span><span class="hljs-params">()</span> </span>{
		given(<span class="hljs-keyword">this</span>.userVehicleService.getVehicleDetails(<span class="hljs-string">"sboot"</span>))
			.willReturn(<span class="hljs-keyword">new</span> VehicleDetails(<span class="hljs-string">"Honda"</span>, <span class="hljs-string">"Civic"</span>));
		assertThat(<span class="hljs-keyword">this</span>.mvc.get().uri(<span class="hljs-string">"/sboot/vehicle"</span>).accept(MediaType.TEXT_PLAIN))
			.hasStatusOk()
			.hasBodyTextEqualTo(<span class="hljs-string">"Honda Civic"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_14_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_14_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.assertj.core.api.Assertions.assertThat
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.mockito.BDDMockito.given
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
<span class="hljs-keyword">import</span> org.springframework.http.MediaType
<span class="hljs-keyword">import</span> org.springframework.test.context.bean.<span class="hljs-keyword">override</span>.mockito.MockitoBean
<span class="hljs-keyword">import</span> org.springframework.test.web.servlet.assertj.MockMvcTester

</span><span class="fold-block"><span class="hljs-meta">@WebMvcTest(UserVehicleController::class)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyControllerTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> mvc: MockMvcTester) {

	<span class="hljs-meta">@MockitoBean</span>
	<span class="hljs-keyword">lateinit</span> <span class="hljs-keyword">var</span> userVehicleService: UserVehicleService

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">testExample</span><span class="hljs-params">()</span></span> {
		given(userVehicleService.getVehicleDetails(<span class="hljs-string">"sboot"</span>))
				.willReturn(VehicleDetails(<span class="hljs-string">"Honda"</span>, <span class="hljs-string">"Civic"</span>))
		assertThat(mvc.<span class="hljs-keyword">get</span>().uri(<span class="hljs-string">"/sboot/vehicle"</span>).accept(MediaType.TEXT_PLAIN))
				.hasStatusOk().hasBodyTextEqualTo(<span class="hljs-string">"Honda Civic"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you need to configure elements of the auto-configuration (for example, when servlet filters should be applied) you can use attributes in the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/servlet/AutoConfigureMockMvc.html"><code>@AutoConfigureMockMvc</code></a> annotation.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If you use HtmlUnit and Selenium, auto-configuration also provides an HtmlUnit <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a> bean and/or a Selenium <a class="apiref external" href="https://www.selenium.dev/selenium/docs/api/java/org/openqa/selenium/WebDriver.html" target="_blank"><code>WebDriver</code></a> bean.
The following example uses HtmlUnit:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_15">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_15_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_15_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_15_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_15_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_15_java" class="tabpanel" id="_tabs_15_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.htmlunit.WebClient;
<span class="hljs-keyword">import</span> org.htmlunit.html.HtmlPage;
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
<span class="hljs-keyword">import</span> org.springframework.test.context.bean.override.mockito.MockitoBean;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.mockito.BDDMockito.given;

</span><span class="fold-block"><span class="hljs-meta">@WebMvcTest</span>(UserVehicleController<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">class</span> <span class="hljs-title">MyHtmlUnitTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> WebClient webClient;

	<span class="hljs-meta">@MockitoBean</span>
	<span class="hljs-keyword">private</span> UserVehicleService userVehicleService;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">testExample</span><span class="hljs-params">()</span> <span class="hljs-keyword">throws</span> Exception </span>{
		given(<span class="hljs-keyword">this</span>.userVehicleService.getVehicleDetails(<span class="hljs-string">"sboot"</span>)).willReturn(<span class="hljs-keyword">new</span> VehicleDetails(<span class="hljs-string">"Honda"</span>, <span class="hljs-string">"Civic"</span>));
		HtmlPage page = <span class="hljs-keyword">this</span>.webClient.getPage(<span class="hljs-string">"/sboot/vehicle.html"</span>);
		assertThat(page.getBody().getTextContent()).isEqualTo(<span class="hljs-string">"Honda Civic"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_15_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_15_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.assertj.core.api.Assertions.assertThat
<span class="hljs-keyword">import</span> org.htmlunit.WebClient
<span class="hljs-keyword">import</span> org.htmlunit.html.HtmlPage
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.mockito.BDDMockito.given
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
<span class="hljs-keyword">import</span> org.springframework.test.context.bean.<span class="hljs-keyword">override</span>.mockito.MockitoBean

</span><span class="fold-block"><span class="hljs-meta">@WebMvcTest(UserVehicleController::class)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyHtmlUnitTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> webClient: WebClient) {

	<span class="hljs-meta">@MockitoBean</span>
	<span class="hljs-keyword">lateinit</span> <span class="hljs-keyword">var</span> userVehicleService: UserVehicleService

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">testExample</span><span class="hljs-params">()</span></span> {
		given(userVehicleService.getVehicleDetails(<span class="hljs-string">"sboot"</span>)).willReturn(VehicleDetails(<span class="hljs-string">"Honda"</span>, <span class="hljs-string">"Civic"</span>))
		<span class="hljs-keyword">val</span> page = webClient.getPage&lt;HtmlPage&gt;(<span class="hljs-string">"/sboot/vehicle.html"</span>)
		assertThat(page.body.textContent).isEqualTo(<span class="hljs-string">"Honda Civic"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
By default, Spring Boot puts <a class="apiref external" href="https://www.selenium.dev/selenium/docs/api/java/org/openqa/selenium/WebDriver.html" target="_blank"><code>WebDriver</code></a> beans in a special “scope” to ensure that the driver exits after each test and that a new instance is injected.
If you do not want this behavior, you can add <code>@Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)</code> to your <a class="apiref external" href="https://www.selenium.dev/selenium/docs/api/java/org/openqa/selenium/WebDriver.html" target="_blank"><code>WebDriver</code></a> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> definition.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock warning">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-warning" title="Warning"></i>
</td>
<td class="content">
The <code>webDriver</code> scope created by Spring Boot will replace any user defined scope of the same name.
If you define your own <code>webDriver</code> scope you may find it stops working when you use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/servlet/WebMvcTest.html"><code>@WebMvcTest</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If you have Spring Security on the classpath, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/servlet/WebMvcTest.html"><code>@WebMvcTest</code></a> will also scan <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/config/annotation/web/WebSecurityConfigurer.html"><code>WebSecurityConfigurer</code></a> beans.
Instead of disabling security completely for such tests, you can use Spring Security’s test support.
More details on how to use Spring Security’s <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/servlet/MockMvc.html"><code>MockMvc</code></a> support can be found in this <a class="xref page" href="../../how-to/testing.html#howto.testing.with-spring-security">Testing With Spring Security</a> “How-to Guides” section.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Sometimes writing Spring MVC tests is not enough; Spring Boot can help you run <a href="#testing.spring-boot-applications.with-running-server">full end-to-end tests with an actual server</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.spring-webflux-tests"><a class="anchor" href="#testing.spring-boot-applications.spring-webflux-tests"></a>Auto-configured Spring WebFlux Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To test that <a href="https://docs.spring.io/spring-framework/reference/6.2/web-reactive.html">Spring WebFlux</a> controllers are working as expected, you can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/WebFluxTest.html"><code>@WebFluxTest</code></a> annotation.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/WebFluxTest.html"><code>@WebFluxTest</code></a> auto-configures the Spring WebFlux infrastructure and limits scanned beans to <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Controller.html"><code>@Controller</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/annotation/ControllerAdvice.html"><code>@ControllerAdvice</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jackson/JsonComponent.html"><code>@JsonComponent</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/converter/Converter.html"><code>Converter</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/converter/GenericConverter.html"><code>GenericConverter</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/config/WebFluxConfigurer.html"><code>WebFluxConfigurer</code></a>.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/WebFluxTest.html"><code>@WebFluxTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configurations that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/WebFluxTest.html"><code>@WebFluxTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you need to register extra components, such as Jackson <a class="apiref external" href="https://javadoc.io/doc/com.fasterxml.jackson.core/jackson-databind/2.18.4/com/fasterxml/jackson/databind/Module.html" target="_blank"><code>Module</code></a>, you can import additional configuration classes using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Import.html"><code>@Import</code></a> on your test.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Often, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/WebFluxTest.html"><code>@WebFluxTest</code></a> is limited to a single controller and used in combination with the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/context/bean/override/mockito/MockitoBean.html"><code>@MockitoBean</code></a> annotation to provide mock implementations for required collaborators.</p>
</div>
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/WebFluxTest.html"><code>@WebFluxTest</code></a> also auto-configures <a href="https://docs.spring.io/spring-framework/reference/6.2/testing/webtestclient.html"><code>WebTestClient</code></a>, which offers a powerful way to quickly test WebFlux controllers without needing to start a full HTTP server.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can also auto-configure <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/reactive/server/WebTestClient.html"><code>WebTestClient</code></a> in a non-<code>@WebFluxTest</code> (such as <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a>) by annotating it with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/AutoConfigureWebTestClient.html"><code>@AutoConfigureWebTestClient</code></a>.
The following example shows a class that uses both <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/WebFluxTest.html"><code>@WebFluxTest</code></a> and a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/reactive/server/WebTestClient.html"><code>WebTestClient</code></a>:
</td>
</tr>
</tbody></table>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_16">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_16_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_16_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_16_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_16_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_16_java" class="tabpanel" id="_tabs_16_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
<span class="hljs-keyword">import</span> org.springframework.http.MediaType;
<span class="hljs-keyword">import</span> org.springframework.test.context.bean.override.mockito.MockitoBean;
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.WebTestClient;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.mockito.BDDMockito.given;

</span><span class="fold-block"><span class="hljs-meta">@WebFluxTest</span>(UserVehicleController<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">class</span> <span class="hljs-title">MyControllerTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> WebTestClient webClient;

	<span class="hljs-meta">@MockitoBean</span>
	<span class="hljs-keyword">private</span> UserVehicleService userVehicleService;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">testExample</span><span class="hljs-params">()</span> </span>{
		given(<span class="hljs-keyword">this</span>.userVehicleService.getVehicleDetails(<span class="hljs-string">"sboot"</span>))
			.willReturn(<span class="hljs-keyword">new</span> VehicleDetails(<span class="hljs-string">"Honda"</span>, <span class="hljs-string">"Civic"</span>));
		<span class="hljs-keyword">this</span>.webClient.get().uri(<span class="hljs-string">"/sboot/vehicle"</span>).accept(MediaType.TEXT_PLAIN).exchange()
			.expectStatus().isOk()
			.expectBody(String<span class="hljs-class">.<span class="hljs-keyword">class</span>).<span class="hljs-title">isEqualTo</span>("<span class="hljs-title">Honda</span> <span class="hljs-title">Civic</span>")</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_16_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_16_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.mockito.BDDMockito.given
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest
<span class="hljs-keyword">import</span> org.springframework.http.MediaType
<span class="hljs-keyword">import</span> org.springframework.test.context.bean.<span class="hljs-keyword">override</span>.mockito.MockitoBean
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.WebTestClient
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.expectBody

</span><span class="fold-block"><span class="hljs-meta">@WebFluxTest(UserVehicleController::class)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyControllerTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> webClient: WebTestClient) {

	<span class="hljs-meta">@MockitoBean</span>
	<span class="hljs-keyword">lateinit</span> <span class="hljs-keyword">var</span> userVehicleService: UserVehicleService

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">testExample</span><span class="hljs-params">()</span></span> {
		given(userVehicleService.getVehicleDetails(<span class="hljs-string">"sboot"</span>))
			.willReturn(VehicleDetails(<span class="hljs-string">"Honda"</span>, <span class="hljs-string">"Civic"</span>))
		webClient.<span class="hljs-keyword">get</span>().uri(<span class="hljs-string">"/sboot/vehicle"</span>).accept(MediaType.TEXT_PLAIN).exchange()
			.expectStatus().isOk
			.expectBody&lt;String&gt;().isEqualTo(<span class="hljs-string">"Honda Civic"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
This setup is only supported by WebFlux applications as using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/reactive/server/WebTestClient.html"><code>WebTestClient</code></a> in a mocked web application only works with WebFlux at the moment.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/WebFluxTest.html"><code>@WebFluxTest</code></a> cannot detect routes registered through the functional web framework.
For testing <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/server/RouterFunction.html"><code>RouterFunction</code></a> beans in the context, consider importing your <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/server/RouterFunction.html"><code>RouterFunction</code></a> yourself by using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Import.html"><code>@Import</code></a> or by using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/WebFluxTest.html"><code>@WebFluxTest</code></a> cannot detect custom security configuration registered as a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> of type <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/server/SecurityWebFilterChain.html"><code>SecurityWebFilterChain</code></a>.
To include that in your test, you will need to import the configuration that registers the bean by using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Import.html"><code>@Import</code></a> or by using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Sometimes writing Spring WebFlux tests is not enough; Spring Boot can help you run <a href="#testing.spring-boot-applications.with-running-server">full end-to-end tests with an actual server</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.spring-graphql-tests"><a class="anchor" href="#testing.spring-boot-applications.spring-graphql-tests"></a>Auto-configured Spring GraphQL Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring GraphQL offers a dedicated testing support module; you’ll need to add it to your project:</p>
</div>
<div class="listingblock">
<div class="title">Maven</div>
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">dependencies</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.graphql<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-graphql-test<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">scope</span>&gt;</span>test<span class="hljs-tag">&lt;/<span class="hljs-name">scope</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span>
	<span class="hljs-comment">&lt;!-- Unless already present in the compile scope --&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-starter-webflux<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">scope</span>&gt;</span>test<span class="hljs-tag">&lt;/<span class="hljs-name">scope</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">dependencies</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Gradle</div>
<div class="content">
<pre class="highlightjs highlight"><code class="language-gradle hljs" data-lang="gradle">dependencies {
	testImplementation("org.springframework.graphql:spring-graphql-test")
	// Unless already present in the implementation configuration
	testImplementation("org.springframework.boot:spring-boot-starter-webflux")
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This testing module ships the <a href="https://docs.spring.io/spring-graphql/reference/1.3/testing.html#testing.graphqltester">GraphQlTester</a>.
The tester is heavily used in test, so be sure to become familiar with using it.
There are <a class="apiref" href="https://docs.spring.io/spring-graphql/docs/1.3.x/api/org/springframework/graphql/test/tester/GraphQlTester.html"><code>GraphQlTester</code></a> variants and Spring Boot will auto-configure them depending on the type of tests:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>the <a class="apiref" href="https://docs.spring.io/spring-graphql/docs/1.3.x/api/org/springframework/graphql/test/tester/ExecutionGraphQlServiceTester.html"><code>ExecutionGraphQlServiceTester</code></a> performs tests on the server side, without a client nor a transport</p>
</li>
<li>
<p>the <a class="apiref" href="https://docs.spring.io/spring-graphql/docs/1.3.x/api/org/springframework/graphql/test/tester/HttpGraphQlTester.html"><code>HttpGraphQlTester</code></a> performs tests with a client that connects to a server, with or without a live server</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Spring Boot helps you to test your <a href="https://docs.spring.io/spring-graphql/reference/1.3/controllers.html">Spring GraphQL Controllers</a> with the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/graphql/GraphQlTest.html"><code>@GraphQlTest</code></a> annotation.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/graphql/GraphQlTest.html"><code>@GraphQlTest</code></a> auto-configures the Spring GraphQL infrastructure, without any transport nor server being involved.
This limits scanned beans to <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Controller.html"><code>@Controller</code></a>, <a class="apiref" href="https://docs.spring.io/spring-graphql/docs/1.3.x/api/org/springframework/graphql/execution/RuntimeWiringConfigurer.html"><code>RuntimeWiringConfigurer</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jackson/JsonComponent.html"><code>JsonComponent</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/converter/Converter.html"><code>Converter</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/converter/GenericConverter.html"><code>GenericConverter</code></a>, <a class="apiref" href="https://docs.spring.io/spring-graphql/docs/1.3.x/api/org/springframework/graphql/execution/DataFetcherExceptionResolver.html"><code>DataFetcherExceptionResolver</code></a>, <a class="apiref external" href="https://javadoc.io/doc/com.graphql-java/graphql-java/22.3/graphql/execution/instrumentation/Instrumentation.html" target="_blank"><code>Instrumentation</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/graphql/GraphQlSourceBuilderCustomizer.html"><code>GraphQlSourceBuilderCustomizer</code></a>.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/graphql/GraphQlTest.html"><code>@GraphQlTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configurations that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/graphql/GraphQlTest.html"><code>@GraphQlTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Often, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/graphql/GraphQlTest.html"><code>@GraphQlTest</code></a> is limited to a set of controllers and used in combination with the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/context/bean/override/mockito/MockitoBean.html"><code>@MockitoBean</code></a> annotation to provide mock implementations for required collaborators.</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_17">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_17_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_17_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_17_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_17_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_17_java" class="tabpanel" id="_tabs_17_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.docs.web.graphql.runtimewiring.GreetingController;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.graphql.GraphQlTest;
<span class="hljs-keyword">import</span> org.springframework.graphql.test.tester.GraphQlTester;

</span><span class="fold-block"><span class="hljs-meta">@GraphQlTest</span>(GreetingController<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">class</span> <span class="hljs-title">GreetingControllerTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> GraphQlTester graphQlTester;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">shouldGreetWithSpecificName</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">this</span>.graphQlTester.document(<span class="hljs-string">"{ greeting(name: \"Alice\") } "</span>)
			.execute()
			.path(<span class="hljs-string">"greeting"</span>)
			.entity(String<span class="hljs-class">.<span class="hljs-keyword">class</span>)
			.<span class="hljs-title">isEqualTo</span>("<span class="hljs-title">Hello</span>, <span class="hljs-title">Alice</span>!")</span>;
	}

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">shouldGreetWithDefaultName</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">this</span>.graphQlTester.document(<span class="hljs-string">"{ greeting } "</span>)
			.execute()
			.path(<span class="hljs-string">"greeting"</span>)
			.entity(String<span class="hljs-class">.<span class="hljs-keyword">class</span>)
			.<span class="hljs-title">isEqualTo</span>("<span class="hljs-title">Hello</span>, <span class="hljs-title">Spring</span>!")</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_17_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_17_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.docs.web.graphql.runtimewiring.GreetingController
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.graphql.GraphQlTest
<span class="hljs-keyword">import</span> org.springframework.graphql.test.tester.GraphQlTester

</span><span class="fold-block"><span class="hljs-meta">@GraphQlTest(GreetingController::class)</span>
<span class="hljs-keyword">internal</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">GreetingControllerTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">lateinit</span> <span class="hljs-keyword">var</span> graphQlTester: GraphQlTester

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">shouldGreetWithSpecificName</span><span class="hljs-params">()</span></span> {
		graphQlTester.document(<span class="hljs-string">"{ greeting(name: \"Alice\") } "</span>).execute().path(<span class="hljs-string">"greeting"</span>).entity(String::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>)</span>
				.isEqualTo(<span class="hljs-string">"Hello, Alice!"</span>)
	}

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">shouldGreetWithDefaultName</span><span class="hljs-params">()</span></span> {
		graphQlTester.document(<span class="hljs-string">"{ greeting } "</span>).execute().path(<span class="hljs-string">"greeting"</span>).entity(String::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>)</span>
				.isEqualTo(<span class="hljs-string">"Hello, Spring!"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> tests are full integration tests and involve the entire application.
When using a random or defined port, a live server is configured and an <a class="apiref" href="https://docs.spring.io/spring-graphql/docs/1.3.x/api/org/springframework/graphql/test/tester/HttpGraphQlTester.html"><code>HttpGraphQlTester</code></a> bean is contributed automatically so you can use it to test your server.
When a MOCK environment is configured, you can also request an <a class="apiref" href="https://docs.spring.io/spring-graphql/docs/1.3.x/api/org/springframework/graphql/test/tester/HttpGraphQlTester.html"><code>HttpGraphQlTester</code></a> bean by annotating your test class with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/graphql/tester/AutoConfigureHttpGraphQlTester.html"><code>@AutoConfigureHttpGraphQlTester</code></a>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_18">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_18_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_18_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_18_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_18_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_18_java" class="tabpanel" id="_tabs_18_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.graphql.tester.AutoConfigureHttpGraphQlTester;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest;
<span class="hljs-keyword">import</span> org.springframework.graphql.test.tester.HttpGraphQlTester;

</span><span class="fold-block"><span class="hljs-meta">@AutoConfigureHttpGraphQlTester</span>
<span class="hljs-meta">@SpringBootTest</span>(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">GraphQlIntegrationTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">shouldGreetWithSpecificName</span><span class="hljs-params">(@Autowired HttpGraphQlTester graphQlTester)</span> </span>{
		HttpGraphQlTester authenticatedTester = graphQlTester.mutate()
			.webTestClient((client) -&gt; client.defaultHeaders((headers) -&gt; headers.setBasicAuth(<span class="hljs-string">"admin"</span>, <span class="hljs-string">"ilovespring"</span>)))
			.build();
		authenticatedTester.document(<span class="hljs-string">"{ greeting(name: \"Alice\") } "</span>)
			.execute()
			.path(<span class="hljs-string">"greeting"</span>)
			.entity(String<span class="hljs-class">.<span class="hljs-keyword">class</span>)
			.<span class="hljs-title">isEqualTo</span>("<span class="hljs-title">Hello</span>, <span class="hljs-title">Alice</span>!")</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_18_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_18_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.graphql.tester.AutoConfigureHttpGraphQlTester
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest
<span class="hljs-keyword">import</span> org.springframework.graphql.test.tester.HttpGraphQlTester
<span class="hljs-keyword">import</span> org.springframework.http.HttpHeaders
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.WebTestClient

</span><span class="fold-block"><span class="hljs-meta">@AutoConfigureHttpGraphQlTester</span>
<span class="hljs-meta">@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">GraphQlIntegrationTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">shouldGreetWithSpecificName</span><span class="hljs-params">(<span class="hljs-meta">@Autowired</span> graphQlTester: <span class="hljs-type">HttpGraphQlTester</span>)</span></span> {
		<span class="hljs-keyword">val</span> authenticatedTester = graphQlTester.mutate()
			.webTestClient { client: WebTestClient.Builder -&gt;
				client.defaultHeaders { headers: HttpHeaders -&gt;
					headers.setBasicAuth(<span class="hljs-string">"admin"</span>, <span class="hljs-string">"ilovespring"</span>)
				}
			}.build()
		authenticatedTester.document(<span class="hljs-string">"{ greeting(name: \"Alice\") } "</span>).execute()
			.path(<span class="hljs-string">"greeting"</span>).entity(String::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>).<span class="hljs-title">isEqualTo</span></span>(<span class="hljs-string">"Hello, Alice!"</span>)
	}
}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-spring-data-cassandra"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-data-cassandra"></a>Auto-configured Data Cassandra Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/cassandra/DataCassandraTest.html"><code>@DataCassandraTest</code></a> to test Cassandra applications.
By default, it configures a <a class="apiref" href="https://docs.spring.io/spring-data/cassandra/docs/4.4.x/api/org/springframework/data/cassandra/core/CassandraTemplate.html"><code>CassandraTemplate</code></a>, scans for <a class="apiref" href="https://docs.spring.io/spring-data/cassandra/docs/4.4.x/api/org/springframework/data/cassandra/core/mapping/Table.html"><code>@Table</code></a> classes, and configures Spring Data Cassandra repositories.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/cassandra/DataCassandraTest.html"><code>@DataCassandraTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.
(For more about using Cassandra with Spring Boot, see <a class="xref page" href="../data/nosql.html#data.nosql.cassandra">Cassandra</a>.)</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configuration settings that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/cassandra/DataCassandraTest.html"><code>@DataCassandraTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following example shows a typical setup for using Cassandra tests in Spring Boot:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_19">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_19_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_19_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_19_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_19_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_19_java" class="tabpanel" id="_tabs_19_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.data.cassandra.DataCassandraTest;

</span><span class="fold-block"><span class="hljs-meta">@DataCassandraTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataCassandraTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> SomeRepository repository;

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_19_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_19_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.<span class="hljs-keyword">data</span>.cassandra.DataCassandraTest

</span><span class="fold-block"><span class="hljs-meta">@DataCassandraTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataCassandraTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> repository: SomeRepository)</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-spring-data-couchbase"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-data-couchbase"></a>Auto-configured Data Couchbase Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/couchbase/DataCouchbaseTest.html"><code>@DataCouchbaseTest</code></a> to test Couchbase applications.
By default, it configures a <a class="apiref" href="https://docs.spring.io/spring-data/couchbase/docs/5.4.x/api/org/springframework/data/couchbase/core/CouchbaseTemplate.html"><code>CouchbaseTemplate</code></a> or <a class="apiref" href="https://docs.spring.io/spring-data/couchbase/docs/5.4.x/api/org/springframework/data/couchbase/core/ReactiveCouchbaseTemplate.html"><code>ReactiveCouchbaseTemplate</code></a>, scans for <a class="apiref" href="https://docs.spring.io/spring-data/couchbase/docs/5.4.x/api/org/springframework/data/couchbase/core/mapping/Document.html"><code>@Document</code></a> classes, and configures Spring Data Couchbase repositories.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/couchbase/DataCouchbaseTest.html"><code>@DataCouchbaseTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.
(For more about using Couchbase with Spring Boot, see <a class="xref page" href="../data/nosql.html#data.nosql.couchbase">Couchbase</a>, earlier in this chapter.)</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configuration settings that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/couchbase/DataCouchbaseTest.html"><code>@DataCouchbaseTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following example shows a typical setup for using Couchbase tests in Spring Boot:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_20">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_20_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_20_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_20_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_20_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_20_java" class="tabpanel" id="_tabs_20_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.data.couchbase.DataCouchbaseTest;

</span><span class="fold-block"><span class="hljs-meta">@DataCouchbaseTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataCouchbaseTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> SomeRepository repository;

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_20_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_20_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.<span class="hljs-keyword">data</span>.couchbase.DataCouchbaseTest

</span><span class="fold-block"><span class="hljs-meta">@DataCouchbaseTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataCouchbaseTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> repository: SomeRepository) {

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-spring-data-elasticsearch"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-data-elasticsearch"></a>Auto-configured Data Elasticsearch Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/elasticsearch/DataElasticsearchTest.html"><code>@DataElasticsearchTest</code></a> to test Elasticsearch applications.
By default, it configures an <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/client/elc/ElasticsearchTemplate.html"><code>ElasticsearchTemplate</code></a>, scans for <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/annotations/Document.html"><code>@Document</code></a> classes, and configures Spring Data Elasticsearch repositories.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/elasticsearch/DataElasticsearchTest.html"><code>@DataElasticsearchTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.
(For more about using Elasticsearch with Spring Boot, see <a class="xref page" href="../data/nosql.html#data.nosql.elasticsearch">Elasticsearch</a>, earlier in this chapter.)</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configuration settings that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/elasticsearch/DataElasticsearchTest.html"><code>@DataElasticsearchTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following example shows a typical setup for using Elasticsearch tests in Spring Boot:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_21">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_21_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_21_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_21_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_21_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_21_java" class="tabpanel" id="_tabs_21_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.data.elasticsearch.DataElasticsearchTest;

</span><span class="fold-block"><span class="hljs-meta">@DataElasticsearchTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataElasticsearchTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> SomeRepository repository;

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_21_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_21_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.<span class="hljs-keyword">data</span>.elasticsearch.DataElasticsearchTest

</span><span class="fold-block"><span class="hljs-meta">@DataElasticsearchTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataElasticsearchTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> repository: SomeRepository) {

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-spring-data-jpa"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-data-jpa"></a>Auto-configured Data JPA Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/orm/jpa/DataJpaTest.html"><code>@DataJpaTest</code></a> annotation to test JPA applications.
By default, it scans for <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/Entity.html" target="_blank"><code>@Entity</code></a> classes and configures Spring Data JPA repositories.
If an embedded database is available on the classpath, it configures one as well.
SQL queries are logged by default by setting the <code>spring.jpa.show-sql</code> property to <code>true</code>.
This can be disabled using the <code>showSql</code> attribute of the annotation.</p>
</div>
<div class="paragraph">
<p>Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/orm/jpa/DataJpaTest.html"><code>@DataJpaTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configuration settings that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/orm/jpa/DataJpaTest.html"><code>@DataJpaTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>By default, data JPA tests are transactional and roll back at the end of each test.
See the <a href="https://docs.spring.io/spring-framework/reference/6.2/testing/testcontext-framework/tx.html#testcontext-tx-enabling-transactions">relevant section</a> in the Spring Framework Reference Documentation for more details.
If that is not what you want, you can disable transaction management for a test or for the whole class as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_22">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_22_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_22_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_22_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_22_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_22_java" class="tabpanel" id="_tabs_22_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
<span class="hljs-keyword">import</span> org.springframework.transaction.annotation.Propagation;
<span class="hljs-keyword">import</span> org.springframework.transaction.annotation.Transactional;

</span><span class="fold-block"><span class="hljs-meta">@DataJpaTest</span>
<span class="hljs-meta">@Transactional</span>(propagation = Propagation.NOT_SUPPORTED)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyNonTransactionalTests</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_22_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_22_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
<span class="hljs-keyword">import</span> org.springframework.transaction.<span class="hljs-keyword">annotation</span>.Propagation
<span class="hljs-keyword">import</span> org.springframework.transaction.<span class="hljs-keyword">annotation</span>.Transactional

</span><span class="fold-block"><span class="hljs-meta">@DataJpaTest</span>
<span class="hljs-meta">@Transactional(propagation = Propagation.NOT_SUPPORTED)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyNonTransactionalTests</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Data JPA tests may also inject a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/orm/jpa/TestEntityManager.html"><code>TestEntityManager</code></a> bean, which provides an alternative to the standard JPA <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManager.html" target="_blank"><code>EntityManager</code></a> that is specifically designed for tests.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/orm/jpa/TestEntityManager.html"><code>TestEntityManager</code></a> can also be auto-configured to any of your Spring-based test class by adding <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/orm/jpa/AutoConfigureTestEntityManager.html"><code>@AutoConfigureTestEntityManager</code></a>.
When doing so, make sure that your test is running in a transaction, for instance by adding  <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/transaction/annotation/Transactional.html"><code>@Transactional</code></a> on your test class or method.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>A <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/JdbcTemplate.html"><code>JdbcTemplate</code></a> is also available if you need that.
The following example shows the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/orm/jpa/DataJpaTest.html"><code>@DataJpaTest</code></a> annotation in use:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_23">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_23_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_23_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_23_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_23_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_23_java" class="tabpanel" id="_tabs_23_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;

</span><span class="fold-block"><span class="hljs-meta">@DataJpaTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRepositoryTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> TestEntityManager entityManager;

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> UserRepository repository;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">testExample</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">this</span>.entityManager.persist(<span class="hljs-keyword">new</span> User(<span class="hljs-string">"sboot"</span>, <span class="hljs-string">"1234"</span>));
		User user = <span class="hljs-keyword">this</span>.repository.findByUsername(<span class="hljs-string">"sboot"</span>);
		assertThat(user.getUsername()).isEqualTo(<span class="hljs-string">"sboot"</span>);
		assertThat(user.getEmployeeNumber()).isEqualTo(<span class="hljs-string">"1234"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_23_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_23_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.assertj.core.api.Assertions.assertThat
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager

</span><span class="fold-block"><span class="hljs-meta">@DataJpaTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRepositoryTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> entityManager: TestEntityManager, <span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> repository: UserRepository) {

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">testExample</span><span class="hljs-params">()</span></span> {
		entityManager.persist(User(<span class="hljs-string">"sboot"</span>, <span class="hljs-string">"1234"</span>))
		<span class="hljs-keyword">val</span> user = repository.findByUsername(<span class="hljs-string">"sboot"</span>)
		assertThat(user?.username).isEqualTo(<span class="hljs-string">"sboot"</span>)
		assertThat(user?.employeeNumber).isEqualTo(<span class="hljs-string">"1234"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>In-memory embedded databases generally work well for tests, since they are fast and do not require any installation.
If, however, you prefer to run tests against a real database you can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jdbc/AutoConfigureTestDatabase.html"><code>@AutoConfigureTestDatabase</code></a> annotation, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_24">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_24_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_24_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_24_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_24_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_24_java" class="tabpanel" id="_tabs_24_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

</span><span class="fold-block"><span class="hljs-meta">@DataJpaTest</span>
<span class="hljs-meta">@AutoConfigureTestDatabase</span>(replace = Replace.NONE)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRepositoryTests</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_24_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_24_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest

</span><span class="fold-block"><span class="hljs-meta">@DataJpaTest</span>
<span class="hljs-meta">@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRepositoryTests</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-jdbc"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-jdbc"></a>Auto-configured JDBC Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jdbc/JdbcTest.html"><code>@JdbcTest</code></a> is similar to <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/orm/jpa/DataJpaTest.html"><code>@DataJpaTest</code></a> but is for tests that only require a <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> and do not use Spring Data JDBC.
By default, it configures an in-memory embedded database and a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/JdbcTemplate.html"><code>JdbcTemplate</code></a>.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jdbc/JdbcTest.html"><code>@JdbcTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configurations that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jdbc/JdbcTest.html"><code>@JdbcTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>By default, JDBC tests are transactional and roll back at the end of each test.
See the <a href="https://docs.spring.io/spring-framework/reference/6.2/testing/testcontext-framework/tx.html#testcontext-tx-enabling-transactions">relevant section</a> in the Spring Framework Reference Documentation for more details.
If that is not what you want, you can disable transaction management for a test or for the whole class, as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_25">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_25_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_25_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_25_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_25_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_25_java" class="tabpanel" id="_tabs_25_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.jdbc.JdbcTest;
<span class="hljs-keyword">import</span> org.springframework.transaction.annotation.Propagation;
<span class="hljs-keyword">import</span> org.springframework.transaction.annotation.Transactional;

</span><span class="fold-block"><span class="hljs-meta">@JdbcTest</span>
<span class="hljs-meta">@Transactional</span>(propagation = Propagation.NOT_SUPPORTED)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyTransactionalTests</span> </span>{

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_25_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_25_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.jdbc.JdbcTest
<span class="hljs-keyword">import</span> org.springframework.transaction.<span class="hljs-keyword">annotation</span>.Propagation
<span class="hljs-keyword">import</span> org.springframework.transaction.<span class="hljs-keyword">annotation</span>.Transactional

</span><span class="fold-block"><span class="hljs-meta">@JdbcTest</span>
<span class="hljs-meta">@Transactional(propagation = Propagation.NOT_SUPPORTED)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyTransactionalTests</span></span></span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you prefer your test to run against a real database, you can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jdbc/AutoConfigureTestDatabase.html"><code>@AutoConfigureTestDatabase</code></a> annotation in the same way as for <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/orm/jpa/DataJpaTest.html"><code>@DataJpaTest</code></a>.
(See <a href="#testing.spring-boot-applications.autoconfigured-spring-data-jpa">Auto-configured Data JPA Tests</a>.)</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-spring-data-jdbc"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-data-jdbc"></a>Auto-configured Data JDBC Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/jdbc/DataJdbcTest.html"><code>@DataJdbcTest</code></a> is similar to <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jdbc/JdbcTest.html"><code>@JdbcTest</code></a> but is for tests that use Spring Data JDBC repositories.
By default, it configures an in-memory embedded database, a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/JdbcTemplate.html"><code>JdbcTemplate</code></a>, and Spring Data JDBC repositories.
Only <a class="apiref" href="https://docs.spring.io/spring-data/jdbc/docs/3.4.x/api/org/springframework/data/jdbc/repository/config/AbstractJdbcConfiguration.html"><code>AbstractJdbcConfiguration</code></a> subclasses are scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/jdbc/DataJdbcTest.html"><code>@DataJdbcTest</code></a> annotation is used, regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configurations that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/jdbc/DataJdbcTest.html"><code>@DataJdbcTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>By default, Data JDBC tests are transactional and roll back at the end of each test.
See the <a href="https://docs.spring.io/spring-framework/reference/6.2/testing/testcontext-framework/tx.html#testcontext-tx-enabling-transactions">relevant section</a> in the Spring Framework Reference Documentation for more details.
If that is not what you want, you can disable transaction management for a test or for the whole test class as <a href="#testing.spring-boot-applications.autoconfigured-jdbc">shown in the JDBC example</a>.</p>
</div>
<div class="paragraph">
<p>If you prefer your test to run against a real database, you can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jdbc/AutoConfigureTestDatabase.html"><code>@AutoConfigureTestDatabase</code></a> annotation in the same way as for <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/orm/jpa/DataJpaTest.html"><code>@DataJpaTest</code></a>.
(See <a href="#testing.spring-boot-applications.autoconfigured-spring-data-jpa">Auto-configured Data JPA Tests</a>.)</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-spring-data-r2dbc"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-data-r2dbc"></a>Auto-configured Data R2DBC Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/r2dbc/DataR2dbcTest.html"><code>@DataR2dbcTest</code></a> is similar to <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/jdbc/DataJdbcTest.html"><code>@DataJdbcTest</code></a> but is for tests that use Spring Data R2DBC repositories.
By default, it configures an in-memory embedded database, an <a class="apiref" href="https://docs.spring.io/spring-data/r2dbc/docs/3.4.x/api/org/springframework/data/r2dbc/core/R2dbcEntityTemplate.html"><code>R2dbcEntityTemplate</code></a>, and Spring Data R2DBC repositories.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/r2dbc/DataR2dbcTest.html"><code>@DataR2dbcTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configurations that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/r2dbc/DataR2dbcTest.html"><code>@DataR2dbcTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>By default, Data R2DBC tests are not transactional.</p>
</div>
<div class="paragraph">
<p>If you prefer your test to run against a real database, you can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jdbc/AutoConfigureTestDatabase.html"><code>@AutoConfigureTestDatabase</code></a> annotation in the same way as for <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/orm/jpa/DataJpaTest.html"><code>@DataJpaTest</code></a>.
(See <a href="#testing.spring-boot-applications.autoconfigured-spring-data-jpa">Auto-configured Data JPA Tests</a>.)</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-jooq"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-jooq"></a>Auto-configured jOOQ Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jooq/JooqTest.html"><code>@JooqTest</code></a> in a similar fashion as <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jdbc/JdbcTest.html"><code>@JdbcTest</code></a> but for jOOQ-related tests.
As jOOQ relies heavily on a Java-based schema that corresponds with the database schema, the existing <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> is used.
If you want to replace it with an in-memory database, you can use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jdbc/AutoConfigureTestDatabase.html"><code>@AutoConfigureTestDatabase</code></a> to override those settings.
(For more about using jOOQ with Spring Boot, see <a class="xref page" href="../data/sql.html#data.sql.jooq">Using jOOQ</a>.)
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jooq/JooqTest.html"><code>@JooqTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configurations that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jooq/JooqTest.html"><code>@JooqTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jooq/JooqTest.html"><code>@JooqTest</code></a> configures a <a class="apiref external" href="https://www.jooq.org/javadoc/3.19.23/org/jooq/DSLContext.html" target="_blank"><code>DSLContext</code></a>.
The following example shows the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jooq/JooqTest.html"><code>@JooqTest</code></a> annotation in use:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_26">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_26_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_26_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_26_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_26_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_26_java" class="tabpanel" id="_tabs_26_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.jooq.DSLContext;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.jooq.JooqTest;

</span><span class="fold-block"><span class="hljs-meta">@JooqTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyJooqTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> DSLContext dslContext;

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_26_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_26_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.jooq.DSLContext
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.jooq.JooqTest

</span><span class="fold-block"><span class="hljs-meta">@JooqTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyJooqTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> dslContext: DSLContext) {

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>JOOQ tests are transactional and roll back at the end of each test by default.
If that is not what you want, you can disable transaction management for a test or for the whole test class as <a href="#testing.spring-boot-applications.autoconfigured-jdbc">shown in the JDBC example</a>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-spring-data-mongodb"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-data-mongodb"></a>Auto-configured Data MongoDB Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/mongo/DataMongoTest.html"><code>@DataMongoTest</code></a> to test MongoDB applications.
By default, it configures a <a class="apiref" href="https://docs.spring.io/spring-data/mongodb/docs/4.4.x/api/org/springframework/data/mongodb/core/MongoTemplate.html"><code>MongoTemplate</code></a>, scans for <a class="apiref" href="https://docs.spring.io/spring-data/mongodb/docs/4.4.x/api/org/springframework/data/mongodb/core/mapping/Document.html"><code>@Document</code></a> classes, and configures Spring Data MongoDB repositories.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/mongo/DataMongoTest.html"><code>@DataMongoTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.
(For more about using MongoDB with Spring Boot, see <a class="xref page" href="../data/nosql.html#data.nosql.mongodb">MongoDB</a>.)</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configuration settings that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/mongo/DataMongoTest.html"><code>@DataMongoTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following class shows the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/mongo/DataMongoTest.html"><code>@DataMongoTest</code></a> annotation in use:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_27">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_27_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_27_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_27_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_27_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_27_java" class="tabpanel" id="_tabs_27_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.data.mongo.DataMongoTest;
<span class="hljs-keyword">import</span> org.springframework.data.mongodb.core.MongoTemplate;

</span><span class="fold-block"><span class="hljs-meta">@DataMongoTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataMongoDbTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> MongoTemplate mongoTemplate;

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_27_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_27_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.<span class="hljs-keyword">data</span>.mongo.DataMongoTest
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.mongodb.core.MongoTemplate

</span><span class="fold-block"><span class="hljs-meta">@DataMongoTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataMongoDbTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> mongoTemplate: MongoTemplate) {

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-spring-data-neo4j"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-data-neo4j"></a>Auto-configured Data Neo4j Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/neo4j/DataNeo4jTest.html"><code>@DataNeo4jTest</code></a> to test Neo4j applications.
By default, it scans for <a class="apiref" href="https://docs.spring.io/spring-data/neo4j/docs/7.4.x/api/org/springframework/data/neo4j/core/schema/Node.html"><code>@Node</code></a> classes, and configures Spring Data Neo4j repositories.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/neo4j/DataNeo4jTest.html"><code>@DataNeo4jTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.
(For more about using Neo4J with Spring Boot, see <a class="xref page" href="../data/nosql.html#data.nosql.neo4j">Neo4j</a>.)</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configuration settings that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/neo4j/DataNeo4jTest.html"><code>@DataNeo4jTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following example shows a typical setup for using Neo4J tests in Spring Boot:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_28">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_28_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_28_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_28_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_28_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_28_java" class="tabpanel" id="_tabs_28_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.data.neo4j.DataNeo4jTest;

</span><span class="fold-block"><span class="hljs-meta">@DataNeo</span>4jTest
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataNeo4jTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> SomeRepository repository;

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_28_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_28_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.<span class="hljs-keyword">data</span>.neo4j.DataNeo4jTest

</span><span class="fold-block"><span class="hljs-meta">@DataNeo4jTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataNeo4jTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> repository: SomeRepository) {

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>By default, Data Neo4j tests are transactional and roll back at the end of each test.
See the <a href="https://docs.spring.io/spring-framework/reference/6.2/testing/testcontext-framework/tx.html#testcontext-tx-enabling-transactions">relevant section</a> in the Spring Framework Reference Documentation for more details.
If that is not what you want, you can disable transaction management for a test or for the whole class, as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_29">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_29_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_29_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_29_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_29_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_29_java" class="tabpanel" id="_tabs_29_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.data.neo4j.DataNeo4jTest;
<span class="hljs-keyword">import</span> org.springframework.transaction.annotation.Propagation;
<span class="hljs-keyword">import</span> org.springframework.transaction.annotation.Transactional;

</span><span class="fold-block"><span class="hljs-meta">@DataNeo</span>4jTest
<span class="hljs-meta">@Transactional</span>(propagation = Propagation.NOT_SUPPORTED)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataNeo4jTests</span> </span>{

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_29_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_29_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.<span class="hljs-keyword">data</span>.neo4j.DataNeo4jTest
<span class="hljs-keyword">import</span> org.springframework.transaction.<span class="hljs-keyword">annotation</span>.Propagation
<span class="hljs-keyword">import</span> org.springframework.transaction.<span class="hljs-keyword">annotation</span>.Transactional

</span><span class="fold-block"><span class="hljs-meta">@DataNeo4jTest</span>
<span class="hljs-meta">@Transactional(propagation = Propagation.NOT_SUPPORTED)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataNeo4jTests</span></span></span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Transactional tests are not supported with reactive access.
If you are using this style, you must configure <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/neo4j/DataNeo4jTest.html"><code>@DataNeo4jTest</code></a> tests as described above.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-spring-data-redis"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-data-redis"></a>Auto-configured Data Redis Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/redis/DataRedisTest.html"><code>@DataRedisTest</code></a> to test Redis applications.
By default, it scans for <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/core/RedisHash.html"><code>@RedisHash</code></a> classes and configures Spring Data Redis repositories.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/redis/DataRedisTest.html"><code>@DataRedisTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.
(For more about using Redis with Spring Boot, see <a class="xref page" href="../data/nosql.html#data.nosql.redis">Redis</a>.)</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configuration settings that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/redis/DataRedisTest.html"><code>@DataRedisTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following example shows the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/redis/DataRedisTest.html"><code>@DataRedisTest</code></a> annotation in use:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_30">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_30_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_30_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_30_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_30_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_30_java" class="tabpanel" id="_tabs_30_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.data.redis.DataRedisTest;

</span><span class="fold-block"><span class="hljs-meta">@DataRedisTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataRedisTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> SomeRepository repository;

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_30_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_30_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.<span class="hljs-keyword">data</span>.redis.DataRedisTest

</span><span class="fold-block"><span class="hljs-meta">@DataRedisTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataRedisTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> repository: SomeRepository) {

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-spring-data-ldap"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-data-ldap"></a>Auto-configured Data LDAP Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/ldap/DataLdapTest.html"><code>@DataLdapTest</code></a> to test LDAP applications.
By default, it configures an in-memory embedded LDAP (if available), configures an <a class="apiref" href="https://docs.spring.io/spring-ldap/docs/3.2.x/api/org/springframework/ldap/core/LdapTemplate.html"><code>LdapTemplate</code></a>, scans for <a class="apiref" href="https://docs.spring.io/spring-ldap/docs/3.2.x/api/org/springframework/ldap/odm/annotations/Entry.html"><code>@Entry</code></a> classes, and configures Spring Data LDAP repositories.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/ldap/DataLdapTest.html"><code>@DataLdapTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.
(For more about using LDAP with Spring Boot, see <a class="xref page" href="../data/nosql.html#data.nosql.ldap">LDAP</a>.)</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configuration settings that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/ldap/DataLdapTest.html"><code>@DataLdapTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following example shows the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/data/ldap/DataLdapTest.html"><code>@DataLdapTest</code></a> annotation in use:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_31">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_31_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_31_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_31_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_31_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_31_java" class="tabpanel" id="_tabs_31_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.data.ldap.DataLdapTest;
<span class="hljs-keyword">import</span> org.springframework.ldap.core.LdapTemplate;

</span><span class="fold-block"><span class="hljs-meta">@DataLdapTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataLdapTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> LdapTemplate ldapTemplate;

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_31_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_31_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.<span class="hljs-keyword">data</span>.ldap.DataLdapTest
<span class="hljs-keyword">import</span> org.springframework.ldap.core.LdapTemplate

</span><span class="fold-block"><span class="hljs-meta">@DataLdapTest</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataLdapTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> ldapTemplate: LdapTemplate) {

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>In-memory embedded LDAP generally works well for tests, since it is fast and does not require any developer installation.
If, however, you prefer to run tests against a real LDAP server, you should exclude the embedded LDAP auto-configuration, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_32">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_32_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_32_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_32_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_32_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_32_java" class="tabpanel" id="_tabs_32_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.ldap.embedded.EmbeddedLdapAutoConfiguration;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.data.ldap.DataLdapTest;

</span><span class="fold-block"><span class="hljs-meta">@DataLdapTest</span>(excludeAutoConfiguration = EmbeddedLdapAutoConfiguration<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">class</span> <span class="hljs-title">MyDataLdapTests</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_32_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_32_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.ldap.embedded.EmbeddedLdapAutoConfiguration
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.<span class="hljs-keyword">data</span>.ldap.DataLdapTest

</span><span class="fold-block"><span class="hljs-meta">@DataLdapTest(excludeAutoConfiguration = [EmbeddedLdapAutoConfiguration::class])</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataLdapTests</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-rest-client"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-rest-client"></a>Auto-configured REST Clients</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/client/RestClientTest.html"><code>@RestClientTest</code></a> annotation to test REST clients.
By default, it auto-configures Jackson, GSON, and Jsonb support, configures a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> and a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.Builder.html"><code>RestClient.Builder</code></a>, and adds support for <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/client/MockRestServiceServer.html"><code>MockRestServiceServer</code></a>.
Regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans are not scanned when the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/client/RestClientTest.html"><code>@RestClientTest</code></a> annotation is used.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> can be used to include <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configuration settings that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/client/RestClientTest.html"><code>@RestClientTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The specific beans that you want to test should be specified by using the <code>value</code> or <code>components</code> attribute of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/client/RestClientTest.html"><code>@RestClientTest</code></a>.</p>
</div>
<div class="paragraph">
<p>When using a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> in the beans under test and <code>RestTemplateBuilder.rootUri(String rootUri)</code> has been called when building the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a>, then the root URI should be omitted from the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/client/MockRestServiceServer.html"><code>MockRestServiceServer</code></a> expectations as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_33">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_33_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_33_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_33_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_33_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_33_java" class="tabpanel" id="_tabs_33_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.client.RestClientTest;
<span class="hljs-keyword">import</span> org.springframework.http.MediaType;
<span class="hljs-keyword">import</span> org.springframework.test.web.client.MockRestServiceServer;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

</span><span class="fold-block"><span class="hljs-meta">@RestClientTest</span>(org.springframework.boot.docs.testing.springbootapplications.autoconfiguredrestclient.RemoteVehicleDetailsService<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">class</span> <span class="hljs-title">MyRestTemplateServiceTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> RemoteVehicleDetailsService service;

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> MockRestServiceServer server;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">getVehicleDetailsWhenResultIsSuccessShouldReturnDetails</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">this</span>.server.expect(requestTo(<span class="hljs-string">"/greet/details"</span>)).andRespond(withSuccess(<span class="hljs-string">"hello"</span>, MediaType.TEXT_PLAIN));
		String greeting = <span class="hljs-keyword">this</span>.service.callRestService();
		assertThat(greeting).isEqualTo(<span class="hljs-string">"hello"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_33_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_33_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.assertj.core.api.Assertions.assertThat
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.client.RestClientTest
<span class="hljs-keyword">import</span> org.springframework.http.MediaType
<span class="hljs-keyword">import</span> org.springframework.test.web.client.MockRestServiceServer
<span class="hljs-keyword">import</span> org.springframework.test.web.client.match.MockRestRequestMatchers
<span class="hljs-keyword">import</span> org.springframework.test.web.client.response.MockRestResponseCreators

</span><span class="fold-block"><span class="hljs-meta">@RestClientTest(RemoteVehicleDetailsService::class)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestTemplateServiceTests</span></span>(
	<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> service: RemoteVehicleDetailsService,
	<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> server: MockRestServiceServer) {

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getVehicleDetailsWhenResultIsSuccessShouldReturnDetails</span><span class="hljs-params">()</span></span> {
		server.<span class="hljs-keyword">expect</span>(MockRestRequestMatchers.requestTo(<span class="hljs-string">"/greet/details"</span>))
			.andRespond(MockRestResponseCreators.withSuccess(<span class="hljs-string">"hello"</span>, MediaType.TEXT_PLAIN))
		<span class="hljs-keyword">val</span> greeting = service.callRestService()
		assertThat(greeting).isEqualTo(<span class="hljs-string">"hello"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>When using a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.Builder.html"><code>RestClient.Builder</code></a> in the beans under test, or when using a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> without calling <code>rootUri(String rootURI)</code>, the full URI must be used in the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/client/MockRestServiceServer.html"><code>MockRestServiceServer</code></a> expectations as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_34">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_34_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_34_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_34_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_34_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_34_java" class="tabpanel" id="_tabs_34_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.client.RestClientTest;
<span class="hljs-keyword">import</span> org.springframework.http.MediaType;
<span class="hljs-keyword">import</span> org.springframework.test.web.client.MockRestServiceServer;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

</span><span class="fold-block"><span class="hljs-meta">@RestClientTest</span>(RemoteVehicleDetailsService<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">class</span> <span class="hljs-title">MyRestClientServiceTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> RemoteVehicleDetailsService service;

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> MockRestServiceServer server;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">getVehicleDetailsWhenResultIsSuccessShouldReturnDetails</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">this</span>.server.expect(requestTo(<span class="hljs-string">"https://example.com/greet/details"</span>))
			.andRespond(withSuccess(<span class="hljs-string">"hello"</span>, MediaType.TEXT_PLAIN));
		String greeting = <span class="hljs-keyword">this</span>.service.callRestService();
		assertThat(greeting).isEqualTo(<span class="hljs-string">"hello"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_34_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_34_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.assertj.core.api.Assertions.assertThat
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.client.RestClientTest
<span class="hljs-keyword">import</span> org.springframework.http.MediaType
<span class="hljs-keyword">import</span> org.springframework.test.web.client.MockRestServiceServer
<span class="hljs-keyword">import</span> org.springframework.test.web.client.match.MockRestRequestMatchers
<span class="hljs-keyword">import</span> org.springframework.test.web.client.response.MockRestResponseCreators

</span><span class="fold-block"><span class="hljs-meta">@RestClientTest(RemoteVehicleDetailsService::class)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestClientServiceTests</span></span>(
	<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> service: RemoteVehicleDetailsService,
	<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> server: MockRestServiceServer) {

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getVehicleDetailsWhenResultIsSuccessShouldReturnDetails</span><span class="hljs-params">()</span></span> {
		server.<span class="hljs-keyword">expect</span>(MockRestRequestMatchers.requestTo(<span class="hljs-string">"https://example.com/greet/details"</span>))
			.andRespond(MockRestResponseCreators.withSuccess(<span class="hljs-string">"hello"</span>, MediaType.TEXT_PLAIN))
		<span class="hljs-keyword">val</span> greeting = service.callRestService()
		assertThat(greeting).isEqualTo(<span class="hljs-string">"hello"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-spring-restdocs"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-restdocs"></a>Auto-configured Spring REST Docs Tests</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/restdocs/AutoConfigureRestDocs.html"><code>@AutoConfigureRestDocs</code></a> annotation to use <a class="external" href="https://spring.io/projects/spring-restdocs" target="_blank">Spring REST Docs</a> in your tests with Mock MVC, REST Assured, or WebTestClient.
It removes the need for the JUnit extension in Spring REST Docs.</p>
</div>
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/restdocs/AutoConfigureRestDocs.html"><code>@AutoConfigureRestDocs</code></a> can be used to override the default output directory (<code>target/generated-snippets</code> if you are using Maven or <code>build/generated-snippets</code> if you are using Gradle).
It can also be used to configure the host, scheme, and port that appears in any documented URIs.</p>
</div>
<div class="sect2">
<h3 id="testing.spring-boot-applications.autoconfigured-spring-restdocs.with-mock-mvc"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-restdocs.with-mock-mvc"></a>Auto-configured Spring REST Docs Tests With Mock MVC</h3>
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/restdocs/AutoConfigureRestDocs.html"><code>@AutoConfigureRestDocs</code></a> customizes the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/servlet/MockMvc.html"><code>MockMvc</code></a> bean to use Spring REST Docs when testing servlet-based web applications.
You can inject it by using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Autowired.html"><code>@Autowired</code></a> and use it in your tests as you normally would when using Mock MVC and Spring REST Docs, as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.restdocs.AutoConfigureRestDocs;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
<span class="hljs-keyword">import</span> org.springframework.http.MediaType;
<span class="hljs-keyword">import</span> org.springframework.test.web.servlet.assertj.MockMvcTester;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.document;

</span><span class="fold-block"><span class="hljs-meta">@WebMvcTest</span>(UserController<span class="hljs-class">.<span class="hljs-keyword">class</span>)
@<span class="hljs-title">AutoConfigureRestDocs</span>
<span class="hljs-title">class</span> <span class="hljs-title">MyUserDocumentationTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> MockMvcTester mvc;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">listUsers</span><span class="hljs-params">()</span> </span>{
		assertThat(<span class="hljs-keyword">this</span>.mvc.get().uri(<span class="hljs-string">"/users"</span>).accept(MediaType.TEXT_PLAIN)).hasStatusOk()
			.apply(document(<span class="hljs-string">"list-users"</span>));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>If you prefer to use the AssertJ integration, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/servlet/assertj/MockMvcTester.html"><code>MockMvcTester</code></a> is available as well, as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.restdocs.AutoConfigureRestDocs;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
<span class="hljs-keyword">import</span> org.springframework.http.MediaType;
<span class="hljs-keyword">import</span> org.springframework.test.web.servlet.assertj.MockMvcTester;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.document;

</span><span class="fold-block"><span class="hljs-meta">@WebMvcTest</span>(UserController<span class="hljs-class">.<span class="hljs-keyword">class</span>)
@<span class="hljs-title">AutoConfigureRestDocs</span>
<span class="hljs-title">class</span> <span class="hljs-title">MyUserDocumentationTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> MockMvcTester mvc;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">listUsers</span><span class="hljs-params">()</span> </span>{
		assertThat(<span class="hljs-keyword">this</span>.mvc.get().uri(<span class="hljs-string">"/users"</span>).accept(MediaType.TEXT_PLAIN)).hasStatusOk()
			.apply(document(<span class="hljs-string">"list-users"</span>));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Both reuses the same <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/servlet/MockMvc.html"><code>MockMvc</code></a> instance behind the scenes so any configuration to it applies to both.</p>
</div>
<div class="paragraph">
<p>If you require more control over Spring REST Docs configuration than offered by the attributes of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/restdocs/AutoConfigureRestDocs.html"><code>@AutoConfigureRestDocs</code></a>, you can use a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/restdocs/RestDocsMockMvcConfigurationCustomizer.html"><code>RestDocsMockMvcConfigurationCustomizer</code></a> bean, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_35">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_35_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_35_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_35_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_35_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_35_java" class="tabpanel" id="_tabs_35_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.restdocs.RestDocsMockMvcConfigurationCustomizer;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.TestConfiguration;
<span class="hljs-keyword">import</span> org.springframework.restdocs.mockmvc.MockMvcRestDocumentationConfigurer;
<span class="hljs-keyword">import</span> org.springframework.restdocs.templates.TemplateFormats;

</span><span class="fold-block"><span class="hljs-meta">@TestConfiguration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestDocsConfiguration</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">RestDocsMockMvcConfigurationCustomizer</span> </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">customize</span><span class="hljs-params">(MockMvcRestDocumentationConfigurer configurer)</span> </span>{
		configurer.snippets().withTemplateFormat(TemplateFormats.markdown());
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_35_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_35_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.restdocs.RestDocsMockMvcConfigurationCustomizer
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.TestConfiguration
<span class="hljs-keyword">import</span> org.springframework.restdocs.mockmvc.MockMvcRestDocumentationConfigurer
<span class="hljs-keyword">import</span> org.springframework.restdocs.templates.TemplateFormats

</span><span class="fold-block"><span class="hljs-meta">@TestConfiguration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestDocsConfiguration</span> : <span class="hljs-type">RestDocsMockMvcConfigurationCustomizer {</span></span>

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">customize</span><span class="hljs-params">(configurer: <span class="hljs-type">MockMvcRestDocumentationConfigurer</span>)</span></span> {
		configurer.snippets().withTemplateFormat(TemplateFormats.markdown())
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you want to make use of Spring REST Docs support for a parameterized output directory, you can create a <a class="apiref" href="https://docs.spring.io/spring-restdocs/docs/3.0.x/api/org/springframework/restdocs/mockmvc/RestDocumentationResultHandler.html"><code>RestDocumentationResultHandler</code></a> bean.
The auto-configuration calls <code>alwaysDo</code> with this result handler, thereby causing each <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/servlet/MockMvc.html"><code>MockMvc</code></a> call to automatically generate the default snippets.
The following example shows a <a class="apiref" href="https://docs.spring.io/spring-restdocs/docs/3.0.x/api/org/springframework/restdocs/mockmvc/RestDocumentationResultHandler.html"><code>RestDocumentationResultHandler</code></a> being defined:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_36">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_36_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_36_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_36_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_36_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_36_java" class="tabpanel" id="_tabs_36_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.context.TestConfiguration;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.restdocs.mockmvc.MockMvcRestDocumentation;
<span class="hljs-keyword">import</span> org.springframework.restdocs.mockmvc.RestDocumentationResultHandler;

</span><span class="fold-block"><span class="hljs-meta">@TestConfiguration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyResultHandlerConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> RestDocumentationResultHandler <span class="hljs-title">restDocumentation</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> MockMvcRestDocumentation.document(<span class="hljs-string">"{method-name}"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_36_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_36_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.context.TestConfiguration
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.restdocs.mockmvc.MockMvcRestDocumentation
<span class="hljs-keyword">import</span> org.springframework.restdocs.mockmvc.RestDocumentationResultHandler

</span><span class="fold-block"><span class="hljs-meta">@TestConfiguration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyResultHandlerConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">restDocumentation</span><span class="hljs-params">()</span></span>: RestDocumentationResultHandler {
		<span class="hljs-keyword">return</span> MockMvcRestDocumentation.document(<span class="hljs-string">"{method-name}"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="testing.spring-boot-applications.autoconfigured-spring-restdocs.with-web-test-client"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-restdocs.with-web-test-client"></a>Auto-configured Spring REST Docs Tests With WebTestClient</h3>
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/restdocs/AutoConfigureRestDocs.html"><code>@AutoConfigureRestDocs</code></a> can also be used with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/web/reactive/server/WebTestClient.html"><code>WebTestClient</code></a> when testing reactive web applications.
You can inject it by using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Autowired.html"><code>@Autowired</code></a> and use it in your tests as you normally would when using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/reactive/WebFluxTest.html"><code>@WebFluxTest</code></a> and Spring REST Docs, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_37">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_37_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_37_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_37_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_37_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_37_java" class="tabpanel" id="_tabs_37_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.restdocs.AutoConfigureRestDocs;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.WebTestClient;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.restdocs.webtestclient.WebTestClientRestDocumentation.document;

</span><span class="fold-block"><span class="hljs-meta">@WebFluxTest</span>
<span class="hljs-meta">@AutoConfigureRestDocs</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyUsersDocumentationTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> WebTestClient webTestClient;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">listUsers</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">this</span>.webTestClient
			.get().uri(<span class="hljs-string">"/"</span>)
		.exchange()
		.expectStatus()
			.isOk()
		.expectBody()
			.consumeWith(document(<span class="hljs-string">"list-users"</span>));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_37_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_37_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.restdocs.AutoConfigureRestDocs
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest
<span class="hljs-keyword">import</span> org.springframework.restdocs.webtestclient.WebTestClientRestDocumentation
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.WebTestClient

</span><span class="fold-block"><span class="hljs-meta">@WebFluxTest</span>
<span class="hljs-meta">@AutoConfigureRestDocs</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyUsersDocumentationTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> webTestClient: WebTestClient) {

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">listUsers</span><span class="hljs-params">()</span></span> {
		webTestClient
			.<span class="hljs-keyword">get</span>().uri(<span class="hljs-string">"/"</span>)
			.exchange()
			.expectStatus()
			.isOk
			.expectBody()
			.consumeWith(WebTestClientRestDocumentation.document(<span class="hljs-string">"list-users"</span>))
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you require more control over Spring REST Docs configuration than offered by the attributes of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/restdocs/AutoConfigureRestDocs.html"><code>@AutoConfigureRestDocs</code></a>, you can use a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/restdocs/RestDocsWebTestClientConfigurationCustomizer.html"><code>RestDocsWebTestClientConfigurationCustomizer</code></a> bean, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_38">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_38_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_38_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_38_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_38_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_38_java" class="tabpanel" id="_tabs_38_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.restdocs.RestDocsWebTestClientConfigurationCustomizer;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.TestConfiguration;
<span class="hljs-keyword">import</span> org.springframework.restdocs.webtestclient.WebTestClientRestDocumentationConfigurer;

</span><span class="fold-block"><span class="hljs-meta">@TestConfiguration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestDocsConfiguration</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">RestDocsWebTestClientConfigurationCustomizer</span> </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">customize</span><span class="hljs-params">(WebTestClientRestDocumentationConfigurer configurer)</span> </span>{
		configurer.snippets().withEncoding(<span class="hljs-string">"UTF-8"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_38_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_38_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.restdocs.RestDocsWebTestClientConfigurationCustomizer
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.TestConfiguration
<span class="hljs-keyword">import</span> org.springframework.restdocs.webtestclient.WebTestClientRestDocumentationConfigurer

</span><span class="fold-block"><span class="hljs-meta">@TestConfiguration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestDocsConfiguration</span> : <span class="hljs-type">RestDocsWebTestClientConfigurationCustomizer {</span></span>

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">customize</span><span class="hljs-params">(configurer: <span class="hljs-type">WebTestClientRestDocumentationConfigurer</span>)</span></span> {
		configurer.snippets().withEncoding(<span class="hljs-string">"UTF-8"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you want to make use of Spring REST Docs support for a parameterized output directory, you can use a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/web/reactive/server/WebTestClientBuilderCustomizer.html"><code>WebTestClientBuilderCustomizer</code></a> to configure a consumer for every entity exchange result.
The following example shows such a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/web/reactive/server/WebTestClientBuilderCustomizer.html"><code>WebTestClientBuilderCustomizer</code></a> being defined:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_39">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_39_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_39_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_39_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_39_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_39_java" class="tabpanel" id="_tabs_39_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.context.TestConfiguration;
<span class="hljs-keyword">import</span> org.springframework.boot.test.web.reactive.server.WebTestClientBuilderCustomizer;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.restdocs.webtestclient.WebTestClientRestDocumentation.document;

</span><span class="fold-block"><span class="hljs-meta">@TestConfiguration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebTestClientBuilderCustomizerConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> WebTestClientBuilderCustomizer <span class="hljs-title">restDocumentation</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> (builder) -&gt; builder.entityExchangeResultConsumer(document(<span class="hljs-string">"{method-name}"</span>));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_39_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_39_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.context.TestConfiguration
<span class="hljs-keyword">import</span> org.springframework.boot.test.web.reactive.server.WebTestClientBuilderCustomizer
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.restdocs.webtestclient.WebTestClientRestDocumentation
<span class="hljs-keyword">import</span> org.springframework.test.web.reactive.server.WebTestClient

</span><span class="fold-block"><span class="hljs-meta">@TestConfiguration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebTestClientBuilderCustomizerConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">restDocumentation</span><span class="hljs-params">()</span></span>: WebTestClientBuilderCustomizer {
		<span class="hljs-keyword">return</span> WebTestClientBuilderCustomizer { builder: WebTestClient.Builder -&gt;
			builder.entityExchangeResultConsumer(
				WebTestClientRestDocumentation.document(<span class="hljs-string">"{method-name}"</span>)
			)
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="testing.spring-boot-applications.autoconfigured-spring-restdocs.with-rest-assured"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-spring-restdocs.with-rest-assured"></a>Auto-configured Spring REST Docs Tests With REST Assured</h3>
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/restdocs/AutoConfigureRestDocs.html"><code>@AutoConfigureRestDocs</code></a> makes a <a class="apiref external" href="https://javadoc.io/doc/io.rest-assured/rest-assured/5.5.2/io/restassured/specification/RequestSpecification.html" target="_blank"><code>RequestSpecification</code></a> bean, preconfigured to use Spring REST Docs, available to your tests.
You can inject it by using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Autowired.html"><code>@Autowired</code></a> and use it in your tests as you normally would when using REST Assured and Spring REST Docs, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_40">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_40_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_40_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_40_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_40_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_40_java" class="tabpanel" id="_tabs_40_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.restassured.specification.RequestSpecification;
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.restdocs.AutoConfigureRestDocs;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
<span class="hljs-keyword">import</span> org.springframework.boot.test.web.server.LocalServerPort;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> io.restassured.RestAssured.given;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.hamcrest.Matchers.is;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.restdocs.restassured.RestAssuredRestDocumentation.document;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>(webEnvironment = WebEnvironment.RANDOM_PORT)
<span class="hljs-meta">@AutoConfigureRestDocs</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyUserDocumentationTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">listUsers</span><span class="hljs-params">(@Autowired RequestSpecification documentationSpec, @LocalServerPort <span class="hljs-keyword">int</span> port)</span> </span>{
		given(documentationSpec)
			.filter(document(<span class="hljs-string">"list-users"</span>))
		.when()
			.port(port)
			.get(<span class="hljs-string">"/"</span>)
		.then().assertThat()
			.statusCode(is(<span class="hljs-number">200</span>));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_40_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_40_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.restassured.RestAssured
<span class="hljs-keyword">import</span> io.restassured.specification.RequestSpecification
<span class="hljs-keyword">import</span> org.hamcrest.Matchers
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.restdocs.AutoConfigureRestDocs
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest.WebEnvironment
<span class="hljs-keyword">import</span> org.springframework.boot.test.web.server.LocalServerPort
<span class="hljs-keyword">import</span> org.springframework.restdocs.restassured.RestAssuredRestDocumentation

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)</span>
<span class="hljs-meta">@AutoConfigureRestDocs</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyUserDocumentationTests</span> </span>{

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">listUsers</span><span class="hljs-params">(<span class="hljs-meta">@Autowired</span> documentationSpec: <span class="hljs-type">RequestSpecification</span>?, <span class="hljs-meta">@LocalServerPort</span> port: <span class="hljs-type">Int</span>)</span></span> {
		RestAssured.given(documentationSpec)
			.filter(RestAssuredRestDocumentation.document(<span class="hljs-string">"list-users"</span>))
			.`<span class="hljs-keyword">when</span>`()
			.port(port)[<span class="hljs-string">"/"</span>]
			.then().assertThat()
			.statusCode(Matchers.`<span class="hljs-keyword">is</span>`(<span class="hljs-number">200</span>))
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you require more control over Spring REST Docs configuration than offered by the attributes of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/restdocs/AutoConfigureRestDocs.html"><code>@AutoConfigureRestDocs</code></a>, a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/restdocs/RestDocsRestAssuredConfigurationCustomizer.html"><code>RestDocsRestAssuredConfigurationCustomizer</code></a> bean can be used, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_41">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_41_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_41_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_41_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_41_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_41_java" class="tabpanel" id="_tabs_41_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.restdocs.RestDocsRestAssuredConfigurationCustomizer;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.TestConfiguration;
<span class="hljs-keyword">import</span> org.springframework.restdocs.restassured.RestAssuredRestDocumentationConfigurer;
<span class="hljs-keyword">import</span> org.springframework.restdocs.templates.TemplateFormats;

</span><span class="fold-block"><span class="hljs-meta">@TestConfiguration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestDocsConfiguration</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">RestDocsRestAssuredConfigurationCustomizer</span> </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">customize</span><span class="hljs-params">(RestAssuredRestDocumentationConfigurer configurer)</span> </span>{
		configurer.snippets().withTemplateFormat(TemplateFormats.markdown());
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_41_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_41_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.restdocs.RestDocsRestAssuredConfigurationCustomizer
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.TestConfiguration
<span class="hljs-keyword">import</span> org.springframework.restdocs.restassured.RestAssuredRestDocumentationConfigurer
<span class="hljs-keyword">import</span> org.springframework.restdocs.templates.TemplateFormats

</span><span class="fold-block"><span class="hljs-meta">@TestConfiguration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestDocsConfiguration</span> : <span class="hljs-type">RestDocsRestAssuredConfigurationCustomizer {</span></span>

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">customize</span><span class="hljs-params">(configurer: <span class="hljs-type">RestAssuredRestDocumentationConfigurer</span>)</span></span> {
		configurer.snippets().withTemplateFormat(TemplateFormats.markdown())
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.autoconfigured-webservices"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-webservices"></a>Auto-configured Spring Web Services Tests</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="testing.spring-boot-applications.autoconfigured-webservices.client"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-webservices.client"></a>Auto-configured Spring Web Services Client Tests</h3>
<div class="paragraph">
<p>You can use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/webservices/client/WebServiceClientTest.html"><code>@WebServiceClientTest</code></a> to test applications that call web services using the Spring Web Services project.
By default, it configures a <a class="apiref" href="https://docs.spring.io/spring-ws/docs/4.0.x/api/org/springframework/ws/test/client/MockWebServiceServer.html"><code>MockWebServiceServer</code></a> bean and automatically customizes your <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/webservices/client/WebServiceTemplateBuilder.html"><code>WebServiceTemplateBuilder</code></a>.
(For more about using Web Services with Spring Boot, see <a class="xref page" href="../io/webservices.html">Web Services</a>.)</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configuration settings that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/webservices/client/WebServiceClientTest.html"><code>@WebServiceClientTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following example shows the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/webservices/client/WebServiceClientTest.html"><code>@WebServiceClientTest</code></a> annotation in use:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_42">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_42_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_42_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_42_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_42_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_42_java" class="tabpanel" id="_tabs_42_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.webservices.client.WebServiceClientTest;
<span class="hljs-keyword">import</span> org.springframework.ws.test.client.MockWebServiceServer;
<span class="hljs-keyword">import</span> org.springframework.xml.transform.StringSource;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.assertj.core.api.Assertions.assertThat;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.ws.test.client.RequestMatchers.payload;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.ws.test.client.ResponseCreators.withPayload;

</span><span class="fold-block"><span class="hljs-meta">@WebServiceClientTest</span>(SomeWebService<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">class</span> <span class="hljs-title">MyWebServiceClientTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> MockWebServiceServer server;

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> SomeWebService someWebService;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">mockServerCall</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">this</span>.server
			.expect(payload(<span class="hljs-keyword">new</span> StringSource(<span class="hljs-string">"&lt;request/&gt;"</span>)))
			.andRespond(withPayload(<span class="hljs-keyword">new</span> StringSource(<span class="hljs-string">"&lt;response&gt;&lt;status&gt;200&lt;/status&gt;&lt;/response&gt;"</span>)));
		assertThat(<span class="hljs-keyword">this</span>.someWebService.test())
			.extracting(Response::getStatus)
			.isEqualTo(<span class="hljs-number">200</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_42_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_42_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.assertj.core.api.Assertions.assertThat
<span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.webservices.client.WebServiceClientTest
<span class="hljs-keyword">import</span> org.springframework.ws.test.client.MockWebServiceServer
<span class="hljs-keyword">import</span> org.springframework.ws.test.client.RequestMatchers
<span class="hljs-keyword">import</span> org.springframework.ws.test.client.ResponseCreators
<span class="hljs-keyword">import</span> org.springframework.xml.transform.StringSource

</span><span class="fold-block"><span class="hljs-meta">@WebServiceClientTest(SomeWebService::class)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebServiceClientTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> server: MockWebServiceServer, <span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> someWebService: SomeWebService) {

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">mockServerCall</span><span class="hljs-params">()</span></span> {
		server
			.<span class="hljs-keyword">expect</span>(RequestMatchers.payload(StringSource(<span class="hljs-string">"&lt;request/&gt;"</span>)))
			.andRespond(ResponseCreators.withPayload(StringSource(<span class="hljs-string">"&lt;response&gt;&lt;status&gt;200&lt;/status&gt;&lt;/response&gt;"</span>)))
		assertThat(<span class="hljs-keyword">this</span>.someWebService.test()).extracting(Response::status).isEqualTo(<span class="hljs-number">200</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="testing.spring-boot-applications.autoconfigured-webservices.server"><a class="anchor" href="#testing.spring-boot-applications.autoconfigured-webservices.server"></a>Auto-configured Spring Web Services Server Tests</h3>
<div class="paragraph">
<p>You can use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/webservices/server/WebServiceServerTest.html"><code>@WebServiceServerTest</code></a> to test applications that implement web services using the Spring Web Services project.
By default, it configures a <a class="apiref" href="https://docs.spring.io/spring-ws/docs/4.0.x/api/org/springframework/ws/test/server/MockWebServiceClient.html"><code>MockWebServiceClient</code></a> bean that can be used to call your web service endpoints.
(For more about using Web Services with Spring Boot, see <a class="xref page" href="../io/webservices.html">Web Services</a>.)</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A list of the auto-configuration settings that are enabled by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/webservices/server/WebServiceServerTest.html"><code>@WebServiceServerTest</code></a> can be <a class="xref page" href="../../appendix/test-auto-configuration/index.html">found in the appendix</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following example shows the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/webservices/server/WebServiceServerTest.html"><code>@WebServiceServerTest</code></a> annotation in use:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_43">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_43_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_43_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_43_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_43_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_43_java" class="tabpanel" id="_tabs_43_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.webservices.server.WebServiceServerTest;
<span class="hljs-keyword">import</span> org.springframework.ws.test.server.MockWebServiceClient;
<span class="hljs-keyword">import</span> org.springframework.ws.test.server.RequestCreators;
<span class="hljs-keyword">import</span> org.springframework.ws.test.server.ResponseMatchers;
<span class="hljs-keyword">import</span> org.springframework.xml.transform.StringSource;

</span><span class="fold-block"><span class="hljs-meta">@WebServiceServerTest</span>(ExampleEndpoint<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">class</span> <span class="hljs-title">MyWebServiceServerTests</span> </span>{

	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-keyword">private</span> MockWebServiceClient client;

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">mockServerCall</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">this</span>.client
			.sendRequest(RequestCreators.withPayload(<span class="hljs-keyword">new</span> StringSource(<span class="hljs-string">"&lt;ExampleRequest/&gt;"</span>)))
			.andExpect(ResponseMatchers.payload(<span class="hljs-keyword">new</span> StringSource(<span class="hljs-string">"&lt;ExampleResponse&gt;42&lt;/ExampleResponse&gt;"</span>)));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_43_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_43_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.junit.jupiter.api.Test
<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Autowired
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.webservices.server.WebServiceServerTest
<span class="hljs-keyword">import</span> org.springframework.ws.test.server.MockWebServiceClient
<span class="hljs-keyword">import</span> org.springframework.ws.test.server.RequestCreators
<span class="hljs-keyword">import</span> org.springframework.ws.test.server.ResponseMatchers
<span class="hljs-keyword">import</span> org.springframework.xml.transform.StringSource

</span><span class="fold-block"><span class="hljs-meta">@WebServiceServerTest(ExampleEndpoint::class)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebServiceServerTests</span></span>(<span class="hljs-meta">@Autowired</span> <span class="hljs-keyword">val</span> client: MockWebServiceClient) {

	<span class="hljs-meta">@Test</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">mockServerCall</span><span class="hljs-params">()</span></span> {
		client
			.sendRequest(RequestCreators.withPayload(StringSource(<span class="hljs-string">"&lt;ExampleRequest/&gt;"</span>)))
			.andExpect(ResponseMatchers.payload(StringSource(<span class="hljs-string">"&lt;ExampleResponse&gt;42&lt;/ExampleResponse&gt;"</span>)))
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.additional-autoconfiguration-and-slicing"><a class="anchor" href="#testing.spring-boot-applications.additional-autoconfiguration-and-slicing"></a>Additional Auto-configuration and Slicing</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Each slice provides one or more <code>@AutoConfigure…​</code> annotations that namely defines the auto-configurations that should be included as part of a slice.
Additional auto-configurations can be added on a test-by-test basis by creating a custom <code>@AutoConfigure…​</code> annotation or by adding <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/ImportAutoConfiguration.html"><code>@ImportAutoConfiguration</code></a> to the test as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_44">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_44_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_44_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_44_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_44_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_44_java" class="tabpanel" id="_tabs_44_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.ImportAutoConfiguration;
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.integration.IntegrationAutoConfiguration;
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.jdbc.JdbcTest;

</span><span class="fold-block"><span class="hljs-meta">@JdbcTest</span>
<span class="hljs-meta">@ImportAutoConfiguration</span>(IntegrationAutoConfiguration<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">class</span> <span class="hljs-title">MyJdbcTests</span> </span>{

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_44_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_44_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.ImportAutoConfiguration
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.integration.IntegrationAutoConfiguration
<span class="hljs-keyword">import</span> org.springframework.boot.test.autoconfigure.jdbc.JdbcTest

</span><span class="fold-block"><span class="hljs-meta">@JdbcTest</span>
<span class="hljs-meta">@ImportAutoConfiguration(IntegrationAutoConfiguration::class)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyJdbcTests</span></span></span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Make sure to not use the regular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Import.html"><code>@Import</code></a> annotation to import auto-configurations as they are handled in a specific way by Spring Boot.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Alternatively, additional auto-configurations can be added for any use of a slice annotation by registering them in a file stored in <code>META-INF/spring</code> as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="title">META-INF/spring/org.springframework.boot.test.autoconfigure.jdbc.JdbcTest.imports</div>
<div class="content">
<pre class="highlightjs highlight"><code class="language-none hljs">com.example.IntegrationAutoConfiguration</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>In this example, the <code>com.example.IntegrationAutoConfiguration</code> is enabled on every test annotated with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/jdbc/JdbcTest.html"><code>@JdbcTest</code></a>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can use comments with <code>#</code> in this file.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A slice or <code>@AutoConfigure…​</code> annotation can be customized this way as long as it is meta-annotated with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/ImportAutoConfiguration.html"><code>@ImportAutoConfiguration</code></a>.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.user-configuration-and-slicing"><a class="anchor" href="#testing.spring-boot-applications.user-configuration-and-slicing"></a>User Configuration and Slicing</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you <a class="xref page" href="../using/structuring-your-code.html">structure your code</a> in a sensible way, your <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html"><code>@SpringBootApplication</code></a> class is <a href="#testing.spring-boot-applications.detecting-configuration">used by default</a> as the configuration of your tests.</p>
</div>
<div class="paragraph">
<p>It then becomes important not to litter the application’s main class with configuration settings that are specific to a particular area of its functionality.</p>
</div>
<div class="paragraph">
<p>Assume that you are using Spring Data MongoDB, you rely on the auto-configuration for it, and you have enabled auditing.
You could define your <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html"><code>@SpringBootApplication</code></a> as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_45">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_45_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_45_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_45_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_45_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_45_java" class="tabpanel" id="_tabs_45_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.SpringBootApplication;
<span class="hljs-keyword">import</span> org.springframework.data.mongodb.config.EnableMongoAuditing;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootApplication</span>
<span class="hljs-meta">@EnableMongoAuditing</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplication</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_45_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_45_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.SpringBootApplication
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.mongodb.config.EnableMongoAuditing

</span><span class="fold-block"><span class="hljs-meta">@SpringBootApplication</span>
<span class="hljs-meta">@EnableMongoAuditing</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplication</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Because this class is the source configuration for the test, any slice test actually tries to enable Mongo auditing, which is definitely not what you want to do.
A recommended approach is to move that area-specific configuration to a separate <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> class at the same level as your application, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_46">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_46_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_46_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_46_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_46_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_46_java" class="tabpanel" id="_tabs_46_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.data.mongodb.config.EnableMongoAuditing;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-meta">@EnableMongoAuditing</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMongoConfiguration</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_46_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_46_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.mongodb.config.EnableMongoAuditing

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-meta">@EnableMongoAuditing</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMongoConfiguration</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Depending on the complexity of your application, you may either have a single <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> class for your customizations or one class per domain area.
The latter approach lets you enable it in one of your tests, if necessary, with the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Import.html"><code>@Import</code></a> annotation.
See <a class="xref page" href="../../how-to/testing.html#howto.testing.slice-tests">this how-to section</a> for more details on when you might want to enable specific <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> classes for slice tests.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Test slices exclude <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> classes from scanning.
For example, for a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/web/servlet/WebMvcTest.html"><code>@WebMvcTest</code></a>, the following configuration will not include the given <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/WebMvcConfigurer.html"><code>WebMvcConfigurer</code></a> bean in the application context loaded by the test slice:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_47">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_47_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_47_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_47_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_47_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_47_java" class="tabpanel" id="_tabs_47_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> WebMvcConfigurer <span class="hljs-title">testConfigurer</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> WebMvcConfigurer() {
			<span class="hljs-comment">// ...</span>
		};
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_47_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_47_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.web.servlet.config.<span class="hljs-keyword">annotation</span>.WebMvcConfigurer

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">testConfigurer</span><span class="hljs-params">()</span></span>: WebMvcConfigurer {
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">object</span> : WebMvcConfigurer {
			<span class="hljs-comment">// ...</span>
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The configuration below will, however, cause the custom <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/WebMvcConfigurer.html"><code>WebMvcConfigurer</code></a> to be loaded by the test slice.</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_48">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_48_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_48_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_48_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_48_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_48_java" class="tabpanel" id="_tabs_48_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.stereotype.Component;
<span class="hljs-keyword">import</span> org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebMvcConfigurer</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">WebMvcConfigurer</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_48_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_48_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.stereotype.Component
<span class="hljs-keyword">import</span> org.springframework.web.servlet.config.<span class="hljs-keyword">annotation</span>.WebMvcConfigurer

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebMvcConfigurer</span> : <span class="hljs-type">WebMvcConfigurer {</span></span>

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Another source of confusion is classpath scanning.
Assume that, while you structured your code in a sensible way, you need to scan an additional package.
Your application may resemble the following code:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_49">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_49_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_49_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_49_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_49_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_49_java" class="tabpanel" id="_tabs_49_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.SpringBootApplication;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.ComponentScan;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootApplication</span>
<span class="hljs-meta">@ComponentScan</span>({ <span class="hljs-string">"com.example.app"</span>, <span class="hljs-string">"com.example.another"</span> })
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplication</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_49_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_49_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.SpringBootApplication
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.ComponentScan

</span><span class="fold-block"><span class="hljs-meta">@SpringBootApplication</span>
<span class="hljs-meta">@ComponentScan(<span class="hljs-meta-string">"com.example.app"</span>, <span class="hljs-meta-string">"com.example.another"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplication</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Doing so effectively overrides the default component scan directive with the side effect of scanning those two packages regardless of the slice that you chose.
For instance, a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/autoconfigure/orm/jpa/DataJpaTest.html"><code>@DataJpaTest</code></a> seems to suddenly scan components and user configurations of your application.
Again, moving the custom directive to a separate class is a good way to fix this issue.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If this is not an option for you, you can create a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringBootConfiguration.html"><code>@SpringBootConfiguration</code></a> somewhere in the hierarchy of your test so that it is used instead.
Alternatively, you can specify a source for your test, which disables the behavior of finding a default one.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing.spring-boot-applications.spock"><a class="anchor" href="#testing.spring-boot-applications.spock"></a>Using Spock to Test Spring Boot Applications</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spock 2.2 or later can be used to test a Spring Boot application.
To do so, add a dependency on a <code>-groovy-4.0</code> version of Spock’s <code>spock-spring</code> module to your application’s build.
<code>spock-spring</code> integrates Spring’s test framework into Spock.
See <a class="external" href="https://spockframework.org/spock/docs/2.2-M1/modules.html#_spring_module" target="_blank">the documentation for Spock’s Spring module</a> for further details.</p>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="spring-applications.html">Testing Spring Applications</a></span>
<span class="next"><a href="testcontainers.html">Testcontainers</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../reference/testing/spring-boot-applications.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="spring-boot-applications.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../3.3/reference/testing/spring-boot-applications.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../4.0-SNAPSHOT/reference/testing/spring-boot-applications.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.5-SNAPSHOT/reference/testing/spring-boot-applications.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.4-SNAPSHOT/reference/testing/spring-boot-applications.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.3-SNAPSHOT/reference/testing/spring-boot-applications.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>