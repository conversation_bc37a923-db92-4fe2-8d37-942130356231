#!/usr/bin/env python3
"""
Test script to verify HTML cleaning functionality
"""

import sys
from pathlib import Path
from spring_docs_scraper import SpringDocs<PERSON>craper

def test_html_cleaning():
    """Test the HTML cleaning function"""
    
    # Create a scraper instance
    scraper = SpringDocsScraper("https://example.com")
    
    # Test HTML content with scripts and multiple head elements
    test_html = """<!DOCTYPE html>
<html>
<head>
    <title>Original Title</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width">
    <link rel="stylesheet" href="style.css">
    <link rel="canonical" href="https://example.com/page">
    <script src="script1.js"></script>
    <script>
        console.log("inline script");
    </script>
</head>
<body>
    <h1>Test Content</h1>
    <p>This is test content.</p>
    <script>
        alert("body script");
    </script>
    <div>More content</div>
</body>
</html>"""
    
    # Test the cleaning function
    test_title = "Test Page Title"
    test_url = "https://docs.spring.io/spring-boot/3.4/test.html"
    
    cleaned_html = scraper.clean_html_content(test_html, test_title, test_url)
    
    print("=== CLEANED HTML ===")
    print(cleaned_html)
    print("\n=== ANALYSIS ===")
    
    # Check if it has only 3 head elements
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(cleaned_html, 'html.parser')
    
    head_elements = soup.head.find_all()
    print(f"Number of head elements: {len(head_elements)}")
    
    for i, element in enumerate(head_elements, 1):
        print(f"{i}. {element.name}: {element}")
    
    # Check for script tags
    scripts = soup.find_all('script')
    print(f"\nNumber of script tags: {len(scripts)}")
    if scripts:
        print("WARNING: Script tags found!")
        for script in scripts:
            print(f"  - {script}")
    else:
        print("✅ No script tags found")

if __name__ == "__main__":
    test_html_cleaning()
