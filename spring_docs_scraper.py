#!/usr/bin/env python3
"""
Spring Documentation Scraper using <PERSON><PERSON>
Scrapes Spring Boot documentation from docs.spring.io with full-page screenshots
and organized markdown files.
"""

import asyncio
import json
import re
import os
from datetime import datetime
from pathlib import Path
from urllib.parse import urljoin, urlparse
from typing import Set, List, Dict, Optional
import html2text
from playwright.async_api import async_playwright, Page, Browser


class SpringDocsScraper:
    def __init__(self, start_url: str, output_dir: str = "output"):
        self.start_url = start_url
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # URL management
        self.url_queue: List[str] = [start_url]
        self.processed_urls: Set[str] = set()
        self.failed_urls: Set[str] = set()
        
        # Cache file for persistence
        self.cache_file = self.output_dir / "scraper_cache.json"
        
        # HTML to Markdown converter
        self.html_converter = html2text.HTML2Text()
        self.html_converter.ignore_links = False
        self.html_converter.ignore_images = False
        self.html_converter.body_width = 0  # No line wrapping
        
        # Load existing cache if available
        self.load_cache()
    
    def load_cache(self):
        """Load previously processed URLs from cache"""
        if self.cache_file.exists():
            try:
                with open(self.cache_file, 'r') as f:
                    cache_data = json.load(f)
                    self.processed_urls = set(cache_data.get('processed_urls', []))
                    self.failed_urls = set(cache_data.get('failed_urls', []))
                    # Add unprocessed URLs back to queue with validation
                    cached_queue = cache_data.get('url_queue', [])
                    skipped_processed = 0
                    added_to_queue = 0

                    for url in cached_queue:
                        # Validate: Check if URL is already processed
                        if url in self.processed_urls:
                            skipped_processed += 1
                            continue

                        # Validate: Check if URL is already in current queue
                        if url in self.url_queue:
                            continue

                        # URL passed validation, add to queue
                        self.url_queue.append(url)
                        added_to_queue += 1

                    print(f"Loaded cache: {len(self.processed_urls)} processed, "
                          f"{len(self.url_queue)} in queue "
                          f"({added_to_queue} added, {skipped_processed} already processed)")
            except Exception as e:
                print(f"Error loading cache: {e}")
    
    def save_cache(self):
        """Save current state to cache"""
        cache_data = {
            'processed_urls': list(self.processed_urls),
            'failed_urls': list(self.failed_urls),
            'url_queue': self.url_queue,
            'last_updated': datetime.now().isoformat()
        }
        with open(self.cache_file, 'w') as f:
            json.dump(cache_data, f, indent=2)
    
    def is_valid_url(self, url: str) -> bool:
        """Check if URL belongs to allowed domains and paths, excluding asset files"""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            path = parsed.path.lower()

            # Exclude URLs ending with common asset file extensions
            excluded_extensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot']
            if any(path.endswith(ext) for ext in excluded_extensions):
                return False

            # Only accept docs.spring.io or spring.io domains
            # if domain not in ['docs.spring.io', 'spring.io']:
            if domain not in ['docs.spring.io']:
                return False

            # For docs.spring.io, only accept URLs that start with /spring-boot/
            # Allow both versioned paths (/spring-boot/3.4/) and search page (/spring-boot/search.html)
            if domain == 'docs.spring.io' and not (path.startswith('/spring-boot/3.4/') or path == '/spring-boot/search.html'):
                return False

            # Skip URLs containing 'spring.io/authors/' path (specific author pages)
            if 'spring.io/authors/' in url.lower():
                return False

            # Skip API documentation URLs (using the improved pattern matching)
            api_patterns = [
                r'docs\.spring\.io/spring-boot/(?:\d+\.\d+(?:\.\d+)?(?:-[a-z0-9]+)?/)?api/java',
                r'docs\.spring\.io/spring-boot/(?:\d+\.\d+(?:\.\d+)?(?:-[a-z0-9]+)?/)?api/kotlin',
                r'docs\.spring\.io/spring-boot/(?:\d+\.\d+(?:\.\d+)?(?:-[a-z0-9]+)?/)?maven-plugin/api/java',
                r'docs\.spring\.io/spring-boot/(?:\d+\.\d+(?:\.\d+)?(?:-[a-z0-9]+)?/)?gradle-plugin/api/java'
            ]

            for pattern in api_patterns:
                if re.search(pattern, url.lower()):
                    return False

            return True
        except:
            return False
    
    def normalize_url(self, url: str) -> str:
        """Normalize URL by removing fragments and query params"""
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
    
    def extract_urls_from_page(self, page_content: str, base_url: str) -> List[str]:
        """Extract all URLs from page content"""
        urls = []
        
        # Find all href attributes
        href_pattern = r'href=["\']([^"\']+)["\']'
        matches = re.findall(href_pattern, page_content, re.IGNORECASE)
        
        for match in matches:
            try:
                # Convert relative URLs to absolute
                absolute_url = urljoin(base_url, match)
                normalized_url = self.normalize_url(absolute_url)
                
                if self.is_valid_url(normalized_url):
                    print(f"Extracted URL: {normalized_url}")
                    urls.append(normalized_url)
            except:
                continue
        
        return list(set(urls))  # Remove duplicates
    
    def create_file_paths(self, url: str, title: str) -> tuple[Path, Path, Path]:
        """Create file paths for markdown, HTML, and screenshot based on URL structure"""
        parsed = urlparse(url)

        # Create directory structure based on URL path
        path_parts = [part for part in parsed.path.split('/') if part]

        # Replace dots with underscores in domain for folder name
        domain_folder = parsed.netloc.replace('.', '_')

        # Build directory path for each output type
        if path_parts:
            relative_path = domain_folder + '/' + '/'.join(path_parts[:-1])
        else:
            relative_path = domain_folder

        # Create paths for each output folder
        markdown_dir = self.output_dir / "markdown" / relative_path
        html_dir = self.output_dir / "html" / relative_path
        screenshot_dir = self.output_dir / "screenshot" / relative_path

        # Create directories
        markdown_dir.mkdir(parents=True, exist_ok=True)
        html_dir.mkdir(parents=True, exist_ok=True)
        screenshot_dir.mkdir(parents=True, exist_ok=True)

        # Create safe filename from title
        safe_title = re.sub(r'[^\w\s-]', '', title).strip()
        safe_title = re.sub(r'[-\s]+', '_', safe_title)[:100]  # Limit length

        if not safe_title:
            safe_title = path_parts[-1] if path_parts else 'index'

        # Create file paths
        md_path = markdown_dir / f"{safe_title}.md"
        html_path = html_dir / f"{safe_title}.html"
        screenshot_path = screenshot_dir / f"{safe_title}.png"

        return md_path, html_path, screenshot_path

    def clean_html_content(self, html_content: str, title: str, url: str) -> str:
        """Clean HTML content and keep only 3 head elements: title, link with current URL, and crawled_at meta"""
        from bs4 import BeautifulSoup
        from datetime import datetime

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Create new HTML structure
            new_html = BeautifulSoup('<!DOCTYPE html><html><head></head><body></body></html>', 'html.parser')

            # 1. Add title tag
            title_tag = new_html.new_tag('title')
            title_tag.string = title
            new_html.head.append(title_tag)

            # 2. Add link tag with current URL
            link_tag = new_html.new_tag('link', href=url)
            new_html.head.append(link_tag)

            # 3. Add crawled_at meta tag
            meta_tag = new_html.new_tag('meta', attrs={'name': 'crawled_at', 'content': datetime.now().isoformat()})
            new_html.head.append(meta_tag)

            # Copy body content but remove all script tags
            if soup.body:
                # Remove all script tags from the body
                for script in soup.body.find_all('script'):
                    script.decompose()
                new_html.body.replace_with(soup.body)
            else:
                # If no body tag, copy all content to body but exclude script tags
                for element in soup.find_all():
                    if element.name not in ['html', 'head', 'title', 'meta', 'link', 'script']:
                        new_html.body.append(element)

            return str(new_html)

        except Exception as e:
            print(f"Warning: HTML cleaning failed: {e}")
            # Fallback: return original content with basic structure (no scripts)
            current_time = datetime.now().isoformat()
            # Remove script tags from fallback content
            try:
                fallback_soup = BeautifulSoup(html_content, 'html.parser')
                for script in fallback_soup.find_all('script'):
                    script.decompose()
                clean_content = str(fallback_soup)
            except:
                clean_content = html_content

            return f"""<!DOCTYPE html>
<html>
<head>
    <title>{title}</title>
    <link href="{url}"/>
    <meta name="crawled_at" content="{current_time}"/>
</head>
<body>
{clean_content}
</body>
</html>"""

    async def scrape_page(self, page: Page, url: str, max_retries: int = 3) -> Optional[List[str]]:
        """Scrape a single page and return discovered URLs with retry logic"""

        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    print(f"Scraping: {url} (attempt {attempt + 1}/{max_retries})")
                else:
                    print(f"Scraping: {url}")

                # Navigate to page with increased timeout and different wait strategies
                try:
                    # Try with networkidle first (30s timeout)
                    await page.goto(url, wait_until='networkidle', timeout=30000)
                except Exception as e:
                    if "timeout" in str(e).lower() and attempt < max_retries - 1:
                        print(f"  Networkidle timeout, trying with domcontentloaded...")
                        # Fallback to domcontentloaded (faster, 60s timeout)
                        await page.goto(url, wait_until='domcontentloaded', timeout=60000)
                    else:
                        raise e

                # Get page title
                title = await page.title()

                # Create file paths for all three output types
                md_path, html_path, screenshot_path = self.create_file_paths(url, title)

                # Take full-page screenshot
                await page.screenshot(path=str(screenshot_path), full_page=True)

                # Get full page HTML content
                full_html_content = await page.content()

                # Extract main content for markdown conversion
                main_content = ""
                try:
                    main_element = await page.query_selector('main, article, .content, #content')
                    if main_element:
                        main_content = await main_element.inner_html()
                    else:
                        main_content = await page.inner_html('body')
                except:
                    main_content = full_html_content

                # Convert HTML to Markdown
                markdown_content = self.html_converter.handle(main_content)

                # Create markdown file content
                current_time = datetime.now().isoformat()
                md_content = f"""Title: {title}
Source: {url}
HTML: {html_path.relative_to(self.output_dir)}
Screenshot: {screenshot_path.relative_to(self.output_dir)}
crawled_at: {current_time}
---
{markdown_content.strip()}
---
"""

                # Save markdown file
                with open(md_path, 'w', encoding='utf-8') as f:
                    f.write(md_content)

                # Clean and save HTML file
                cleaned_html = self.clean_html_content(full_html_content, title, url)
                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(cleaned_html)

                print(f"Saved markdown: {md_path}")
                print(f"Saved HTML: {html_path}")
                print(f"Saved screenshot: {screenshot_path}")

                # Extract URLs from page body content
                page_html = await page.inner_html('body')
                discovered_urls = self.extract_urls_from_page(page_html, url)

                # Add new URLs to queue with validation
                new_urls = []
                skipped_processed = 0
                skipped_in_queue = 0
                skipped_failed = 0

                for discovered_url in discovered_urls:
                    # Validate: Check if URL is already processed
                    if discovered_url in self.processed_urls:
                        skipped_processed += 1
                        continue

                    # Validate: Check if URL is already in queue
                    if discovered_url in self.url_queue:
                        skipped_in_queue += 1
                        continue

                    # Validate: Check if URL previously failed
                    if discovered_url in self.failed_urls:
                        skipped_failed += 1
                        continue

                    # URL passed all validations, add to queue
                    self.url_queue.append(discovered_url)
                    new_urls.append(discovered_url)

                # Report discovery results
                total_discovered = len(discovered_urls)
                if total_discovered > 0:
                    print(f"Discovered {total_discovered} URLs: {len(new_urls)} new, "
                          f"{skipped_processed} already processed, "
                          f"{skipped_in_queue} already in queue, "
                          f"{skipped_failed} previously failed")

                # If we reach here, the scraping was successful
                return new_urls

            except Exception as e:
                error_msg = str(e)
                print(f"  Attempt {attempt + 1} failed: {error_msg}")

                # If this is the last attempt, add to failed URLs
                if attempt == max_retries - 1:
                    print(f"Error scraping {url} after {max_retries} attempts: {error_msg}")
                    self.failed_urls.add(url)
                    return None
                else:
                    # Wait before retrying (exponential backoff)
                    wait_time = 2 ** attempt  # 1s, 2s, 4s...
                    print(f"  Waiting {wait_time}s before retry...")
                    await asyncio.sleep(wait_time)

        # This should never be reached, but just in case
        return None
    
    async def run(self):
        """Main scraping loop"""
        async with async_playwright() as p:
            # Launch browser with better settings for slow pages
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ]
            )
            page = await browser.new_page()

            # Set a reasonable viewport
            await page.set_viewport_size({"width": 1920, "height": 1080})

            # Set longer default timeouts
            page.set_default_timeout(90000)  # 90 seconds default timeout
            page.set_default_navigation_timeout(90000)  # 90 seconds navigation timeout
            
            try:
                while self.url_queue:
                    current_url = self.url_queue.pop(0)

                    # Check if URL is already processed before scraping
                    if current_url in self.processed_urls:
                        print(f"Skipping already processed URL: {current_url}")
                        continue

                    # Check if URL is in failed URLs
                    if current_url in self.failed_urls:
                        print(f"Skipping previously failed URL: {current_url}")
                        continue

                    # Scrape the page
                    result = await self.scrape_page(page, current_url)

                    # Only mark as processed if scraping was successful
                    if result is not None:
                        self.processed_urls.add(current_url)
                        print(f"✅ Successfully processed: {current_url}")
                    else:
                        print(f"❌ Failed to process: {current_url} (added to failed_urls)")
                    
                    # Save cache periodically
                    if len(self.processed_urls) % 10 == 0:
                        self.save_cache()
                        print(f"Progress: {len(self.processed_urls)} processed, {len(self.url_queue)} remaining")
                    
                    # Small delay to be respectful
                    await asyncio.sleep(1)
                
            finally:
                await browser.close()
                self.save_cache()
                
                print(f"\nScraping completed!")
                print(f"Total pages processed: {len(self.processed_urls)}")
                print(f"Failed URLs: {len(self.failed_urls)}")
                print(f"Output directory: {self.output_dir}")


async def main():
    """Main entry point"""
    start_url = "https://docs.spring.io/spring-boot/3.4/index.html"

    scraper = SpringDocsScraper(start_url)
    await scraper.run()


if __name__ == "__main__":
    asyncio.run(main())
