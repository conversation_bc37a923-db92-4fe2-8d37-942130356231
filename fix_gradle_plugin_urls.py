#!/usr/bin/env python3
"""
Script to extract missing URLs from gradle-plugin/index.html and add them to the scraper queue
"""

import json
import re
from pathlib import Path
from urllib.parse import urljoin, urlparse
from datetime import datetime

def normalize_url(url: str) -> str:
    """Normalize URL by removing fragments and query params"""
    parsed = urlparse(url)
    return f"{parsed.scheme}://{parsed.netloc}{parsed.path}"

def is_valid_url(url: str) -> bool:
    """Check if URL belongs to allowed domains and paths, excluding asset files"""
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        path = parsed.path.lower()

        # Exclude URLs ending with common asset file extensions
        excluded_extensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot']
        if any(path.endswith(ext) for ext in excluded_extensions):
            return False

        # Only accept docs.spring.io or spring.io domains
        if domain not in ['docs.spring.io', 'spring.io']:
            return False

        # For docs.spring.io, only accept URLs that start with /spring-boot/
        if domain == 'docs.spring.io' and not path.startswith('/spring-boot/3.4/'):
            return False

        # Skip URLs containing 'spring.io/authors/' path (specific author pages)
        if 'spring.io/authors/' in url.lower():
            return False

        # Skip API documentation URLs (using the improved pattern matching)
        # BUT allow gradle-plugin content pages (not API docs)
        api_patterns = [
            r'docs\.spring\.io/spring-boot/(?:\d+\.\d+(?:\.\d+)?(?:-[a-z0-9]+)?/)?api/java',
            r'docs\.spring\.io/spring-boot/(?:\d+\.\d+(?:\.\d+)?(?:-[a-z0-9]+)?/)?api/kotlin',
            r'docs\.spring\.io/spring-boot/(?:\d+\.\d+(?:\.\d+)?(?:-[a-z0-9]+)?/)?maven-plugin',
            # Note: Removed gradle-plugin pattern to allow gradle-plugin content pages
        ]

        for pattern in api_patterns:
            if re.search(pattern, url.lower()):
                return False

        # Skip gradle-plugin API docs specifically, but allow content pages
        if '/gradle-plugin/api/' in url.lower():
            return False

        return True
    except:
        return False

def extract_urls_from_html_file(html_file_path: str, base_url: str) -> list:
    """Extract URLs from HTML file content"""
    urls = []
    
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            page_content = f.read()
        
        # Find all href attributes
        href_pattern = r'href=["\']([^"\']+)["\']'
        matches = re.findall(href_pattern, page_content, re.IGNORECASE)
        
        for match in matches:
            try:
                # Convert relative URLs to absolute
                absolute_url = urljoin(base_url, match)
                normalized_url = normalize_url(absolute_url)
                
                if is_valid_url(normalized_url):
                    urls.append(normalized_url)
            except:
                continue
        
        return list(set(urls))  # Remove duplicates
    except Exception as e:
        print(f"Error reading HTML file: {e}")
        return []

def main():
    """Main function to fix gradle plugin URLs"""
    
    # Paths
    cache_file = Path("output/scraper_cache.json")
    html_file = Path("output/html/docs_spring_io/spring-boot/3.4/gradle-plugin/Gradle_Plugin_Spring_Boot.html")
    
    # Base URL for the gradle plugin index page
    base_url = "https://docs.spring.io/spring-boot/3.4/gradle-plugin/index.html"
    
    print("=== FIXING GRADLE PLUGIN URLs ===")
    print(f"HTML file: {html_file}")
    print(f"Base URL: {base_url}")
    print()
    
    # Check if files exist
    if not cache_file.exists():
        print(f"❌ Cache file not found: {cache_file}")
        return
    
    if not html_file.exists():
        print(f"❌ HTML file not found: {html_file}")
        return
    
    # Load current cache
    try:
        with open(cache_file, 'r') as f:
            cache_data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading cache: {e}")
        return
    
    processed_urls = set(cache_data.get('processed_urls', []))
    failed_urls = set(cache_data.get('failed_urls', []))
    url_queue = cache_data.get('url_queue', [])
    
    print(f"Current state:")
    print(f"  - Processed URLs: {len(processed_urls)}")
    print(f"  - Failed URLs: {len(failed_urls)}")
    print(f"  - Queue URLs: {len(url_queue)}")
    print()
    
    # Extract URLs from the HTML file
    print("Extracting URLs from HTML file...")
    discovered_urls = extract_urls_from_html_file(str(html_file), base_url)

    print(f"Found {len(discovered_urls)} valid URLs in HTML file")

    # Debug: Show some sample URLs
    print("Sample discovered URLs:")
    for url in sorted(discovered_urls)[:10]:
        print(f"  - {url}")
    if len(discovered_urls) > 10:
        print(f"  ... and {len(discovered_urls) - 10} more")
    print()

    # Filter URLs that are specifically gradle-plugin related
    gradle_plugin_urls = [url for url in discovered_urls if '/gradle-plugin/' in url]

    print(f"Found {len(gradle_plugin_urls)} gradle-plugin URLs:")
    for url in sorted(gradle_plugin_urls):
        print(f"  - {url}")
    print()
    
    # Add new URLs to queue with validation
    new_urls = []
    skipped_processed = 0
    skipped_in_queue = 0
    skipped_failed = 0
    
    for url in gradle_plugin_urls:
        # Skip if already processed
        if url in processed_urls:
            skipped_processed += 1
            continue
        
        # Skip if already in queue
        if url in url_queue:
            skipped_in_queue += 1
            continue
        
        # Skip if previously failed
        if url in failed_urls:
            skipped_failed += 1
            continue
        
        # Add to queue
        url_queue.append(url)
        new_urls.append(url)
    
    print(f"=== RESULTS ===")
    print(f"URLs added to queue: {len(new_urls)}")
    print(f"Already processed: {skipped_processed}")
    print(f"Already in queue: {skipped_in_queue}")
    print(f"Previously failed: {skipped_failed}")
    print()
    
    if new_urls:
        print("New URLs added to queue:")
        for url in new_urls:
            print(f"  ✅ {url}")
        print()
        
        # Update cache
        cache_data['url_queue'] = url_queue
        cache_data['last_updated'] = datetime.now().isoformat()
        
        # Save updated cache
        try:
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
            print(f"✅ Cache updated successfully!")
            print(f"New queue size: {len(url_queue)}")
        except Exception as e:
            print(f"❌ Error saving cache: {e}")
    else:
        print("No new URLs to add.")

if __name__ == "__main__":
    main()
