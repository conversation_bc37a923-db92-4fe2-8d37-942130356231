#!/usr/bin/env python3
"""
Test script to check why search.html URL is not being extracted
"""

import re
from urllib.parse import urljoin, urlparse

def normalize_url(url: str) -> str:
    """Normalize URL by removing fragments and query params"""
    parsed = urlparse(url)
    return f"{parsed.scheme}://{parsed.netloc}{parsed.path}"

def is_valid_url(url: str) -> bool:
    """Check if URL belongs to allowed domains and paths, excluding asset files"""
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        path = parsed.path.lower()

        print(f"  Checking URL: {url}")
        print(f"    Domain: {domain}")
        print(f"    Path: {path}")

        # Exclude URLs ending with common asset file extensions
        excluded_extensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot']
        if any(path.endswith(ext) for ext in excluded_extensions):
            print(f"    ❌ REJECTED: Asset file extension")
            return False

        # Only accept docs.spring.io or spring.io domains
        if domain not in ['docs.spring.io', 'spring.io']:
            print(f"    ❌ REJECTED: Invalid domain")
            return False

        # For docs.spring.io, only accept URLs that start with /spring-boot/
        # Allow both versioned paths (/spring-boot/3.4/) and search page (/spring-boot/search.html)
        if domain == 'docs.spring.io' and not (path.startswith('/spring-boot/3.4/') or path == '/spring-boot/search.html'):
            print(f"    ❌ REJECTED: Path doesn't start with /spring-boot/3.4/ and is not search.html")
            return False

        # Skip URLs containing 'spring.io/authors/' path (specific author pages)
        if 'spring.io/authors/' in url.lower():
            print(f"    ❌ REJECTED: Author page")
            return False

        # Skip API documentation URLs (using the improved pattern matching)
        api_patterns = [
            r'docs\.spring\.io/spring-boot/(?:\d+\.\d+(?:\.\d+)?(?:-[a-z0-9]+)?/)?api/java',
            r'docs\.spring\.io/spring-boot/(?:\d+\.\d+(?:\.\d+)?(?:-[a-z0-9]+)?/)?api/kotlin',
            r'docs\.spring\.io/spring-boot/(?:\d+\.\d+(?:\.\d+)?(?:-[a-z0-9]+)?/)?maven-plugin',
        ]

        for pattern in api_patterns:
            if re.search(pattern, url.lower()):
                print(f"    ❌ REJECTED: API documentation pattern")
                return False

        # Skip gradle-plugin API docs specifically, but allow content pages
        if '/gradle-plugin/api/' in url.lower():
            print(f"    ❌ REJECTED: Gradle plugin API docs")
            return False

        print(f"    ✅ ACCEPTED")
        return True
    except Exception as e:
        print(f"    ❌ REJECTED: Exception {e}")
        return False

def extract_urls_from_page(page_content: str, base_url: str) -> list:
    """Extract all URLs from page content"""
    urls = []
    
    # Find all href attributes
    href_pattern = r'href=["\']([^"\']+)["\']'
    matches = re.findall(href_pattern, page_content, re.IGNORECASE)
    
    print(f"Found {len(matches)} href matches")
    
    for i, match in enumerate(matches):
        print(f"\nMatch {i+1}: {match}")
        try:
            # Convert relative URLs to absolute
            absolute_url = urljoin(base_url, match)
            print(f"  Absolute URL: {absolute_url}")
            
            normalized_url = normalize_url(absolute_url)
            print(f"  Normalized URL: {normalized_url}")
            
            if is_valid_url(normalized_url):
                urls.append(normalized_url)
        except Exception as e:
            print(f"  ❌ Error processing: {e}")
            continue
    
    return list(set(urls))  # Remove duplicates

def test_search_url():
    """Test the search.html URL extraction"""
    
    # Test HTML content with the search link
    test_html = '<a class="search-link" href="../search.html">Search in all Spring Docs</a>'
    
    # Base URL from Community page
    base_url = "https://docs.spring.io/spring-boot/3.4/community.html"
    
    print("=== SEARCH URL EXTRACTION TEST ===")
    print(f"Test HTML: {test_html}")
    print(f"Base URL: {base_url}")
    print()
    
    # Extract URLs
    extracted_urls = extract_urls_from_page(test_html, base_url)
    
    print(f"\n=== FINAL RESULTS ===")
    print(f"Number of URLs extracted: {len(extracted_urls)}")
    
    for i, url in enumerate(extracted_urls, 1):
        print(f"{i}. {url}")
    
    # Check if the expected URL is present
    expected_url = "https://docs.spring.io/spring-boot/3.4/search.html"
    
    print(f"\n=== VERIFICATION ===")
    print(f"Expected URL: {expected_url}")
    
    if expected_url in extracted_urls:
        print("✅ SUCCESS: Expected URL found!")
    else:
        print("❌ FAILURE: Expected URL not found!")

if __name__ == "__main__":
    test_search_url()
