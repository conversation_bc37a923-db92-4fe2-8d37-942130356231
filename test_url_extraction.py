#!/usr/bin/env python3
"""
Test script to verify URL extraction functionality
"""

import re
from urllib.parse import urljoin, urlparse

def normalize_url(url: str) -> str:
    """Normalize URL by removing fragments and query params"""
    parsed = urlparse(url)
    return f"{parsed.scheme}://{parsed.netloc}{parsed.path}"

def is_valid_url(url: str) -> bool:
    """Simplified validation for testing"""
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        path = parsed.path.lower()

        # Exclude URLs ending with common asset file extensions
        excluded_extensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot']
        if any(path.endswith(ext) for ext in excluded_extensions):
            return False

        # Only accept docs.spring.io or spring.io domains
        if domain not in ['docs.spring.io', 'spring.io']:
            return False

        # For docs.spring.io, only accept URLs that start with /spring-boot/
        if domain == 'docs.spring.io' and not path.startswith('/spring-boot/3.4/'):
            return False

        return True
    except:
        return False

def extract_urls_from_page(page_content: str, base_url: str) -> list:
    """Extract all URLs from page content"""
    urls = []
    
    # Find all href attributes
    href_pattern = r'href=["\']([^"\']+)["\']'
    matches = re.findall(href_pattern, page_content, re.IGNORECASE)
    
    for match in matches:
        try:
            # Convert relative URLs to absolute
            absolute_url = urljoin(base_url, match)
            normalized_url = normalize_url(absolute_url)
            
            if is_valid_url(normalized_url):
                urls.append(normalized_url)
        except:
            continue
    
    return list(set(urls))  # Remove duplicates

def test_url_extraction():
    """Test the URL extraction function with your specific example"""
    
    # Test HTML content
    test_html = '<a class="nav-link" href="../gradle-plugin/reacting.html">Reacting to Other Plugins</a>'
    
    # Base URL (simulating being on a Spring Boot 3.4 page)
    base_url = "https://docs.spring.io/spring-boot/3.4/build-tool-plugin/index.html"
    
    print("=== URL EXTRACTION TEST ===")
    print(f"Test HTML: {test_html}")
    print(f"Base URL: {base_url}")
    print()
    
    # Extract URLs
    extracted_urls = extract_urls_from_page(test_html, base_url)
    
    print("=== RESULTS ===")
    print(f"Number of URLs extracted: {len(extracted_urls)}")
    
    for i, url in enumerate(extracted_urls, 1):
        print(f"{i}. {url}")
    
    # Check if the expected URL is present
    expected_url = "https://docs.spring.io/spring-boot/3.4/gradle-plugin/reacting.html"
    
    print(f"\n=== VERIFICATION ===")
    print(f"Expected URL: {expected_url}")
    
    if expected_url in extracted_urls:
        print("✅ SUCCESS: Expected URL found!")
    else:
        print("❌ FAILURE: Expected URL not found!")
        
        # Debug: Show step-by-step process
        print("\n=== DEBUG INFO ===")
        href_pattern = r'href=["\']([^"\']+)["\']'
        matches = re.findall(href_pattern, test_html, re.IGNORECASE)
        print(f"Regex matches: {matches}")
        
        if matches:
            relative_url = matches[0]
            print(f"Relative URL found: {relative_url}")
            
            absolute_url = urljoin(base_url, relative_url)
            print(f"Absolute URL: {absolute_url}")
            
            normalized_url = normalize_url(absolute_url)
            print(f"Normalized URL: {normalized_url}")
            
            is_valid = is_valid_url(normalized_url)
            print(f"Is valid: {is_valid}")

if __name__ == "__main__":
    test_url_extraction()
