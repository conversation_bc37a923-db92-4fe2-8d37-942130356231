#!/usr/bin/env python3
"""
Script to download images referenced in existing markdown files and update their paths
"""

import os
import re
import requests
from pathlib import Path
from urllib.parse import urljoin, urlparse

class MarkdownImageFixer:
    def __init__(self, output_dir: str = "output"):
        self.output_dir = Path(output_dir)
        self.images_dir = self.output_dir / "images"
        self.images_dir.mkdir(exist_ok=True)
        self.downloaded_images = set()

    def download_image(self, image_url: str, base_url: str) -> str:
        """Download an image and return the local path"""
        try:
            # Convert relative URLs to absolute
            absolute_image_url = urljoin(base_url, image_url)
            
            # Skip if already downloaded
            if absolute_image_url in self.downloaded_images:
                return self.get_local_image_path(absolute_image_url)
            
            # Create local file path
            parsed = urlparse(absolute_image_url)
            # Create a safe filename from the URL path
            path_parts = [part for part in parsed.path.split('/') if part]
            if path_parts:
                filename = path_parts[-1]
                # Create subdirectory structure to avoid conflicts
                subdir = '_'.join(path_parts[:-1]) if len(path_parts) > 1 else 'root'
                local_dir = self.images_dir / subdir
                local_dir.mkdir(parents=True, exist_ok=True)
                local_path = local_dir / filename
            else:
                local_path = self.images_dir / 'unknown_image'
            
            # Download the image
            print(f"Downloading: {absolute_image_url}")
            response = requests.get(absolute_image_url, stream=True)
            
            if response.status_code == 200:
                with open(local_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                self.downloaded_images.add(absolute_image_url)
                print(f"✅ Downloaded: {local_path}")
                return str(local_path)
            else:
                print(f"❌ Failed to download {absolute_image_url}: HTTP {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Error downloading {image_url}: {e}")
            return None

    def get_local_image_path(self, absolute_image_url: str) -> str:
        """Get the local path for an already downloaded image"""
        parsed = urlparse(absolute_image_url)
        path_parts = [part for part in parsed.path.split('/') if part]
        if path_parts:
            filename = path_parts[-1]
            subdir = '_'.join(path_parts[:-1]) if len(path_parts) > 1 else 'root'
            local_path = self.images_dir / subdir / filename
            return str(local_path)
        return str(self.images_dir / 'unknown_image')

    def get_relative_image_path(self, local_image_path: str, md_file_path: Path) -> str:
        """Get relative path from markdown file to image file"""
        try:
            image_path = Path(local_image_path)
            relative_path = os.path.relpath(image_path, md_file_path.parent)
            return relative_path.replace('\\', '/')  # Use forward slashes for web
        except:
            return local_image_path

    def extract_source_url_from_markdown(self, md_content: str) -> str:
        """Extract source URL from markdown metadata"""
        # Look for "Source: URL" in the metadata section
        source_pattern = r'Source:\s*([^\n]+)'
        match = re.search(source_pattern, md_content)
        if match:
            return match.group(1).strip()
        return None

    def process_markdown_file(self, md_file_path: Path):
        """Process a single markdown file to download images and update paths"""
        try:
            # Read the markdown file
            with open(md_file_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            # Extract source URL from metadata
            source_url = self.extract_source_url_from_markdown(md_content)
            
            if not source_url:
                print(f"⚠️  No source URL found in {md_file_path}")
                return
            
            print(f"\nProcessing: {md_file_path}")
            print(f"Source URL: {source_url}")
            
            # Find all markdown image references: ![alt](image_path)
            image_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
            matches = re.findall(image_pattern, md_content)
            
            if not matches:
                print(f"ℹ️  No images found in {md_file_path}")
                return
            
            print(f"Found {len(matches)} images:")
            
            updated_content = md_content
            images_processed = 0
            
            for alt_text, image_path in matches:
                print(f"  Found image: ![{alt_text}]({image_path})")
                
                # Skip if it's already an absolute URL (http/https)
                if image_path.startswith(('http://', 'https://')):
                    print(f"    Skipping absolute URL")
                    continue
                
                # Skip if it's already pointing to local images folder (starts with ../images/ or similar)
                if image_path.startswith(('../images/', './images/', 'images/')):
                    print(f"    Already pointing to local images")
                    continue
                
                # Download the image
                local_path = self.download_image(image_path, source_url)
                if local_path:
                    # Update the image path to point to local file
                    relative_path = self.get_relative_image_path(local_path, md_file_path)
                    # Replace the old image reference with the new one
                    old_ref = f'![{alt_text}]({image_path})'
                    new_ref = f'![{alt_text}]({relative_path})'
                    updated_content = updated_content.replace(old_ref, new_ref)
                    print(f"    Updated to: {relative_path}")
                    images_processed += 1
            
            if images_processed > 0:
                # Save the updated markdown
                with open(md_file_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                print(f"✅ Updated {images_processed} images in {md_file_path}")
            else:
                print(f"ℹ️  No images to update in {md_file_path}")
                
        except Exception as e:
            print(f"❌ Error processing {md_file_path}: {e}")

    def process_all_markdown_files(self):
        """Process all markdown files in the output directory"""
        markdown_dir = self.output_dir / "markdown"
        
        if not markdown_dir.exists():
            print(f"❌ Markdown directory not found: {markdown_dir}")
            return
        
        # Find all markdown files
        md_files = list(markdown_dir.rglob("*.md"))
        
        if not md_files:
            print(f"❌ No markdown files found in {markdown_dir}")
            return
        
        print(f"Found {len(md_files)} markdown files to process")
        
        for md_file in md_files:
            self.process_markdown_file(md_file)
        
        print(f"\n🎉 Processing complete!")
        print(f"Downloaded {len(self.downloaded_images)} unique images")
        print(f"Images saved to: {self.images_dir}")

def main():
    """Main entry point"""
    fixer = MarkdownImageFixer()

    # Process all markdown files
    print("Processing all markdown files to download images...")
    fixer.process_all_markdown_files()

if __name__ == "__main__":
    main()
