{"processed_urls": ["https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/chroma.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/elasticsearch.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/azure.html", "https://docs.spring.io/spring-ai/reference/api/image/stabilityai-image.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/bedrock.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/perplexity-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/audio/speech.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/ollama-embeddings.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/ollama-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/mcp/mcp-overview.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/cloud-bindings.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/weaviate.html", "https://docs.spring.io/spring-ai/reference/api/chat/moonshot-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/ollama-embeddings.html", "https://docs.spring.io/spring-ai/reference/getting-started.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/prompt-engineering-patterns.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/zhipuai-embeddings.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/retrieval-augmented-generation.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/mongodb.html", "https://docs.spring.io/spring-ai/reference/api/tools.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/azure-openai-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/multimodality.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/weaviate.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/oci-genai-embeddings.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/google-vertexai.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/index.html", "https://docs.spring.io/spring-ai/reference/api/imageclient.html", "https://docs.spring.io/spring-ai/reference/api/chat/deepseek-chat.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/postgresml-embeddings.html", "https://docs.spring.io/spring-ai/reference/search.html", "https://docs.spring.io/spring-ai/reference/api/chat/comparison.html", "https://docs.spring.io/spring-ai/reference/api/audio/transcriptions/openai-transcriptions.html", "https://docs.spring.io/spring-ai/reference/api/chat/mistralai-chat.html", "https://docs.spring.io/spring-ai/reference/api/docker-compose.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/pgvector.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/gemfire.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/oracle.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/apache-cassandra.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/neo4j.html", "https://docs.spring.io/spring-ai/reference/api/chat/groq-chat.html", "https://docs.spring.io/spring-ai/reference/api/bedrock.html", "https://docs.spring.io/spring-ai/reference/api/chat/zhipuai-chat.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/mistralai-embeddings.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/onnx.html", "https://docs.spring.io/spring-ai/reference/api/chat/huggingface.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/qdrant.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/azure-cosmos-db.html", "https://docs.spring.io/spring-ai/reference/api/chat/bedrock-converse.html", "https://docs.spring.io/spring-ai/reference/api/mcp/mcp-overview.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/azure-openai-embeddings.html", "https://docs.spring.io/spring-ai/reference/api/etl-pipeline.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/redis.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/chroma.html", "https://docs.spring.io/spring-ai/reference/concepts.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/bedrock-cohere-embedding.html", "https://docs.spring.io/spring-ai/reference/api/chat-memory.html", "https://docs.spring.io/spring-ai/reference/api/audio/transcriptions.html", "https://docs.spring.io/spring-ai/reference/api/chat/oci-genai/cohere-chat.html", "https://docs.spring.io/spring-ai/reference/api/moderation/mistral-ai-moderation.html", "https://docs.spring.io/spring-ai/reference/api/retrieval-augmented-generation.html", "https://docs.spring.io/spring-ai/reference/index.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/oci-genai-embeddings.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/vertexai-embeddings-multimodal.html", "https://docs.spring.io/spring-ai/reference/api/chat/openai-chat.html", "https://docs.spring.io/spring-ai/reference/api/chat/azure-openai-chat.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/minimax-embeddings.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/opensearch.html", "https://docs.spring.io/spring-ai/reference/api/testcontainers.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/pinecone.html", "https://docs.spring.io/spring-ai/reference/api/image/openai-image.html", "https://docs.spring.io/spring-ai/reference/api/chat/anthropic-chat.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/azure.html", "https://docs.spring.io/spring-ai/reference/api/chat/nvidia-chat.html", "https://docs.spring.io/spring-ai/reference/api/chat/google-vertexai.html", "https://docs.spring.io/spring-ai/reference/api/cloud-bindings.html", "https://docs.spring.io/spring-ai/reference/api/chat/perplexity-chat.html", "https://docs.spring.io/spring-ai/reference/api/chat/prompt-engineering-patterns.html", "https://docs.spring.io/spring-ai/reference/api/chatclient.html", "https://docs.spring.io/spring-ai/reference/upgrade-notes.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/qianfan-embeddings.html", "https://docs.spring.io/spring-ai/reference/", "https://docs.spring.io/spring-ai/reference/api/chatmodel.html", "https://docs.spring.io/spring-ai/reference/api/image/zhipuai-image.html", "https://docs.spring.io/spring-ai/reference/api/chat/dmr-chat.html", "https://docs.spring.io/spring-ai/reference/api/prompt.html", "https://docs.spring.io/spring-ai/reference/api/mcp/mcp-server-boot-starter-docs.html", "https://docs.spring.io/spring-ai/reference/api/image/azure-openai-image.html", "https://docs.spring.io/spring-ai/reference/api/audio/speech.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/typesense.html", "https://docs.spring.io/spring-ai/reference/spring-projects.html", "https://docs.spring.io/spring-ai/reference/api/audio/transcriptions/azure-openai-transcriptions.html", "https://docs.spring.io/spring-ai/reference/api/effective-agents.html", "https://docs.spring.io/spring-ai/reference/api/image/qianfan-image.html", "https://docs.spring.io/spring-ai/reference/api/embeddings.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/couchbase.html", "https://docs.spring.io/spring-ai/reference/api/audio/speech/openai-speech.html", "https://docs.spring.io/spring-ai/reference/api/moderation/openai-moderation.html", "https://docs.spring.io/spring-ai/reference/api/mcp/mcp-helpers.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/hana.html", "https://docs.spring.io/spring-ai/reference/api/index.html", "https://docs.spring.io/spring-ai/reference/api/chat/ollama-chat.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/openai-embeddings.html", "https://docs.spring.io/spring-ai/reference/api/tools-migration.html", "https://docs.spring.io/spring-ai/reference/api/chat/vertexai-gemini-chat.html", "https://docs.spring.io/spring-ai/reference/api/structured-output-converter.html", "https://docs.spring.io/spring-ai/reference/api/advisors.html", "https://docs.spring.io/spring-ai/reference/api/mcp/mcp-client-boot-starter-docs.html", "https://docs.spring.io/spring-ai/reference/api/multimodality.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/bedrock-titan-embedding.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/milvus.html", "https://docs.spring.io/spring-ai/reference/api/testing.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/mariadb.html", "https://docs.spring.io/spring-ai/reference/api/chat/qianfan-chat.html", "https://docs.spring.io/spring-ai/reference/api/chat/minimax-chat.html", "https://docs.spring.io/spring-ai/reference/api/embeddings/vertexai-embeddings-text.html", "https://docs.spring.io/spring-ai/reference/observability/index.html"], "failed_urls": ["https://docs.spring.io/spring-ai/reference/api/chat/functions/openai-chat-functions.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/couchbase.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/tools.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat-memory.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/elasticsearch.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/huggingface.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/vertexai-gemini-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/effective-agents.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/advisors.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/bedrock-titan-embedding.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/hanadb-provision-a-trial-account.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/bedrock-cohere-embedding.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/docker-compose.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/image/qianfan-image.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/etl-pipeline.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/minimax-embeddings.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/audio/transcriptions/openai-transcriptions.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chatmodel.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/opensearch.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/gemfire.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/mistralai-embeddings.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/mistralai-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/mcp/mcp-helpers.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/mcp/mcp-client-boot-starter-docs.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/onnx.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/oci-genai/cohere-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/moderation/mistral-ai-moderation.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/mariadb.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/testcontainers.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/moonshot-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/audio/transcriptions/azure-openai-transcriptions.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/azure-openai-embeddings.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/pinecone.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/zhipuai-embeddings.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/observability/index.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/pgvector.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/qianfan-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/neo4j.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/minimax-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/hana.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/image/azure-openai-image.html", "https://docs.spring.io/spring-ai/reference/api/bedrock-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/audio/speech/openai-speech.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/nvidia-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/groq-chat.html", "https://docs.spring.io/spring-ai/reference/api/vectordbs/understand-vectordbs.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/redis.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/tools-migration.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/testing.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/openai-embeddings.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/upgrade-notes.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/qianfan-embeddings.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/dmr-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/image/openai-image.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/structured-output-converter.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/deepseek-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/image/zhipuai-image.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/getting-started.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/vertexai-embeddings-multimodal.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/qdrant.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/imageclient.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/azure-cosmos-db.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/audio/transcriptions.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/index.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/typesense.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/image/stabilityai-image.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/concepts.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/comparison.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/oracle.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/prompt.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chatclient.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/openai-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/bedrock-converse.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/milvus.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/anthropic-chat.html", "https://docs.spring.io/spring-ai/reference/api/usage-handling.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/vertexai-embeddings-text.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/mongodb.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/moderation/openai-moderation.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/vectordbs/apache-cassandra.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/zhipuai-chat.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/postgresml-embeddings.html", "https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/mcp/mcp-server-boot-starter-docs.html"], "url_queue": [], "last_updated": "2025-06-04T18:28:00.434861"}