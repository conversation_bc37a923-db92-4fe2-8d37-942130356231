{"processed_urls": ["https://docs.spring.io/spring-boot/3.4/api/rest/actuator/sessions.html", "https://docs.spring.io/spring-boot/3.4/reference/web/spring-hateoas.html", "https://docs.spring.io/spring-boot/3.4/specification/configuration-metadata/index.html", "https://docs.spring.io/spring-boot/3.4/reference/features/developing-auto-configuration.html", "https://docs.spring.io/spring-boot/3.4/reference/io/quartz.html", "https://docs.spring.io/spring-boot/3.4/how-to/nosql.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/health.html", "https://docs.spring.io/spring-boot/3.4/how-to/native-image/index.html", "https://docs.spring.io/spring-boot/3.4/specification/configuration-metadata/format.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/endpoints.html", "https://docs.spring.io/spring-boot/3.4/reference/web/reactive.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/jmx.html", "https://docs.spring.io/spring-boot/3.4/specification/executable-jar/jarfile-class.html", "https://docs.spring.io/spring-boot/3.4/how-to/application.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/enabling.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/flyway.html", "https://docs.spring.io/spring-boot/3.4/reference/using/using-the-springbootapplication-annotation.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/http-exchanges.html", "https://docs.spring.io/spring-boot/3.4/appendix/auto-configuration-classes/index.html", "https://docs.spring.io/spring-boot/3.4/cli/using-the-cli.html", "https://docs.spring.io/spring-boot/3.4/reference/features/ssl.html", "https://docs.spring.io/spring-boot/3.4/reference/messaging/pulsar.html", "https://docs.spring.io/spring-boot/3.4/reference/web/spring-security.html", "https://docs.spring.io/spring-boot/3.4/reference/features/kotlin.html", "https://docs.spring.io/spring-boot/3.4/reference/packaging/aot.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/configprops.html", "https://docs.spring.io/spring-boot/3.4/reference/packaging/class-data-sharing.html", "https://docs.spring.io/spring-boot/3.4/reference/io/jta.html", "https://docs.spring.io/spring-boot/3.4/index.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/mappings.html", "https://docs.spring.io/spring-boot/3.4/how-to/data-access.html", "https://docs.spring.io/spring-boot/3.4/reference/data/index.html", "https://docs.spring.io/spring-boot/3.4/cli/installation.html", "https://docs.spring.io/spring-boot/3.4/reference/features/dev-services.html", "https://docs.spring.io/spring-boot/3.4/specification/executable-jar/index.html", "https://docs.spring.io/spring-boot/3.4/reference/packaging/native-image/introducing-graalvm-native-images.html", "https://docs.spring.io/spring-boot/3.4/reference/features/external-config.html", "https://docs.spring.io/spring-boot/3.4/maven-plugin/goals.html", "https://docs.spring.io/spring-boot/3.4/appendix/auto-configuration-classes/core.html", "https://docs.spring.io/spring-boot/3.4/reference/testing/test-utilities.html", "https://docs.spring.io/spring-boot/3.4/reference/messaging/kafka.html", "https://docs.spring.io/spring-boot/3.4/reference/using/configuration-classes.html", "https://docs.spring.io/spring-boot/3.4/appendix/test-auto-configuration/slices.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/logfile.html", "https://docs.spring.io/spring-boot/3.4/reference/testing/spring-boot-applications.html", "https://docs.spring.io/spring-boot/3.4/build-tool-plugin/antlib.html", "https://docs.spring.io/spring-boot/3.4/maven-plugin/index.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/quartz.html", "https://docs.spring.io/spring-boot/3.4/reference/messaging/jms.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/threaddump.html", "https://docs.spring.io/spring-boot/3.4/maven-plugin/packaging.html", "https://docs.spring.io/spring-boot/3.4/reference/data/nosql.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/env.html", "https://docs.spring.io/spring-boot/3.4/appendix/test-auto-configuration/index.html", "https://docs.spring.io/spring-boot/3.4/gradle-plugin/index.html", "https://docs.spring.io/spring-boot/3.4/reference/messaging/spring-integration.html", "https://docs.spring.io/spring-boot/3.4/maven-plugin/aot.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/shutdown.html", "https://docs.spring.io/spring-boot/3.4/specification/configuration-metadata/annotation-processor.html", "https://docs.spring.io/spring-boot/3.4/reference/using/packaging-for-production.html", "https://docs.spring.io/spring-boot/3.4/how-to/http-clients.html", "https://docs.spring.io/spring-boot/3.4/how-to/native-image/testing-native-applications.html", "https://docs.spring.io/spring-boot/3.4/tutorial/first-application/index.html", "https://docs.spring.io/spring-boot/3.4/reference/packaging/container-images/cloud-native-buildpacks.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/loggers.html", "https://docs.spring.io/spring-boot/3.4/reference/packaging/native-image/index.html", "https://docs.spring.io/spring-boot/3.4/how-to/docker-compose.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/auditevents.html", "https://docs.spring.io/spring-boot/3.4/reference/packaging/container-images/efficient-images.html", "https://docs.spring.io/spring-boot/3.4/reference/messaging/rsocket.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/heapdump.html", "https://docs.spring.io/spring-boot/3.4/reference/using/running-your-application.html", "https://docs.spring.io/spring-boot/3.4/reference/features/spring-application.html", "https://docs.spring.io/spring-boot/3.4/specification/executable-jar/alternatives.html", "https://docs.spring.io/spring-boot/3.4/maven-plugin/help.html", "https://docs.spring.io/spring-boot/3.4/reference/packaging/native-image/advanced-topics.html", "https://docs.spring.io/spring-boot/3.4/reference/web/servlet.html", "https://docs.spring.io/spring-boot/3.4/gradle-plugin/running.html", "https://docs.spring.io/spring-boot/3.4/gradle-plugin/getting-started.html", "https://docs.spring.io/spring-boot/3.4/maven-plugin/integration-tests.html", "https://docs.spring.io/spring-boot/3.4/how-to/batch.html", "https://docs.spring.io/spring-boot/3.4/reference/io/caching.html", "https://docs.spring.io/spring-boot/3.4/reference/features/internationalization.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/scheduledtasks.html", "https://docs.spring.io/spring-boot/3.4/reference/index.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/prometheus.html", "https://docs.spring.io/spring-boot/3.4/specification/configuration-metadata/manual-hints.html", "https://docs.spring.io/spring-boot/3.4/how-to/webserver.html", "https://docs.spring.io/spring-boot/3.4/reference/web/spring-session.html", "https://docs.spring.io/spring-boot/3.4/reference/web/spring-graphql.html", "https://docs.spring.io/spring-boot/3.4/how-to/native-image/developing-your-first-application.html", "https://docs.spring.io/spring-boot/3.4/how-to/deployment/installing.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/process-monitoring.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/conditions.html", "https://docs.spring.io/spring-boot/3.4/reference/packaging/container-images/index.html", "https://docs.spring.io/spring-boot/3.4/how-to/logging.html", "https://docs.spring.io/spring-boot/3.4/reference/messaging/amqp.html", "https://docs.spring.io/spring-boot/3.4/how-to/aot.html", "https://docs.spring.io/spring-boot/3.4/reference/messaging/index.html", "https://docs.spring.io/spring-boot/3.4/how-to/spring-mvc.html", "https://docs.spring.io/spring-boot/3.4/reference/testing/spring-applications.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/liquibase.html", "https://docs.spring.io/spring-boot/3.4/reference/packaging/efficient.html", "https://docs.spring.io/spring-boot/3.4/gradle-plugin/packaging-oci-image.html", "https://docs.spring.io/spring-boot/3.4/reference/features/task-execution-and-scheduling.html", "https://docs.spring.io/spring-boot/3.4/specification/executable-jar/launching.html", "https://docs.spring.io/spring-boot/3.4/reference/features/json.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/caches.html", "https://docs.spring.io/spring-boot/3.4/how-to/build.html", "https://docs.spring.io/spring-boot/3.4/reference/packaging/checkpoint-restore.html", "https://docs.spring.io/spring-boot/3.4/build-tool-plugin/index.html", "https://docs.spring.io/spring-boot/3.4/how-to/deployment/traditional-deployment.html", "https://docs.spring.io/spring-boot/3.4/gradle-plugin/packaging.html", "https://docs.spring.io/spring-boot/3.4/reference/using/auto-configuration.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/tracing.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/httpexchanges.html", "https://docs.spring.io/spring-boot/3.4/reference/packaging/index.html", "https://docs.spring.io/spring-boot/3.4/gradle-plugin/publishing.html", "https://docs.spring.io/spring-boot/3.4/reference/messaging/websockets.html", "https://docs.spring.io/spring-boot/3.4/appendix/dependency-versions/properties.html", "https://docs.spring.io/spring-boot/3.4/how-to/deployment/cloud.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/observability.html", "https://docs.spring.io/spring-boot/3.4/how-to/security.html", "https://docs.spring.io/spring-boot/3.4/how-to/hotswapping.html", "https://docs.spring.io/spring-boot/3.4/reference/using/build-systems.html", "https://docs.spring.io/spring-boot/3.4/upgrading.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/startup.html", "https://docs.spring.io/spring-boot/3.4/how-to/actuator.html", "https://docs.spring.io/spring-boot/3.4/maven-plugin/getting-started.html", "https://docs.spring.io/spring-boot/3.4/appendix/dependency-versions/index.html", "https://docs.spring.io/spring-boot/3.4/appendix/dependency-versions/coordinates.html", "https://docs.spring.io/spring-boot/3.4/how-to/jersey.html", "https://docs.spring.io/spring-boot/3.4/reference/io/email.html", "https://docs.spring.io/spring-boot/3.4/how-to/data-initialization.html", "https://docs.spring.io/spring-boot/3.4/gradle-plugin/reacting.html", "https://docs.spring.io/spring-boot/3.4/how-to/testing.html", "https://docs.spring.io/spring-boot/3.4/reference/io/hazelcast.html", "https://docs.spring.io/spring-boot/3.4/how-to/properties-and-configuration.html", "https://docs.spring.io/spring-boot/3.4/reference/features/logging.html", "https://docs.spring.io/spring-boot/3.4/reference/io/index.html", "https://docs.spring.io/spring-boot/3.4/reference/io/rest-client.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/metrics.html", "https://docs.spring.io/spring-boot/3.4/reference/using/devtools.html", "https://docs.spring.io/spring-boot/3.4/maven-plugin/using.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/sbom.html", "https://docs.spring.io/spring-boot/3.4/reference/io/validation.html", "https://docs.spring.io/spring-boot/3.4/specification/executable-jar/nested-jars.html", "https://docs.spring.io/spring-boot/3.4/appendix/auto-configuration-classes/actuator.html", "https://docs.spring.io/spring-boot/3.4/reference/using/index.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/cloud-foundry.html", "https://docs.spring.io/spring-boot/3.4/reference/web/index.html", "https://docs.spring.io/spring-boot/3.4/reference/testing/index.html", "https://docs.spring.io/spring-boot/3.4/how-to/class-data-sharing.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/integrationgraph.html", "https://docs.spring.io/spring-boot/3.4/how-to/index.html", "https://docs.spring.io/spring-boot/3.4/maven-plugin/run.html", "https://docs.spring.io/spring-boot/3.4/tutorial/index.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/metrics.html", "https://docs.spring.io/spring-boot/3.4/reference/using/structuring-your-code.html", "https://docs.spring.io/spring-boot/3.4/gradle-plugin/managing-dependencies.html", "https://docs.spring.io/spring-boot/3.4/cli/index.html", "https://docs.spring.io/spring-boot/3.4/reference/features/index.html", "https://docs.spring.io/spring-boot/3.4/system-requirements.html", "https://docs.spring.io/spring-boot/3.4/reference/testing/testcontainers.html", "https://docs.spring.io/spring-boot/3.4/reference/data/sql.html", "https://docs.spring.io/spring-boot/3.4/reference/using/spring-beans-and-dependency-injection.html", "https://docs.spring.io/spring-boot/3.4/appendix/application-properties/index.html", "https://docs.spring.io/spring-boot/3.4/specification/executable-jar/restrictions.html", "https://docs.spring.io/spring-boot/3.4/how-to/messaging.html", "https://docs.spring.io/spring-boot/3.4/maven-plugin/build-image.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/index.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/loggers.html", "https://docs.spring.io/spring-boot/3.4/community.html", "https://docs.spring.io/spring-boot/3.4/gradle-plugin/integrating-with-actuator.html", "https://docs.spring.io/spring-boot/3.4/specification/executable-jar/property-launcher.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/info.html", "https://docs.spring.io/spring-boot/3.4/documentation.html", "https://docs.spring.io/spring-boot/3.4/reference/testing/test-scope-dependencies.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/auditing.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/index.html", "https://docs.spring.io/spring-boot/3.4/reference/actuator/monitoring.html", "https://docs.spring.io/spring-boot/3.4/build-tool-plugin/other-build-systems.html", "https://docs.spring.io/spring-boot/3.4/reference/web/graceful-shutdown.html", "https://docs.spring.io/spring-boot/3.4/reference/features/aop.html", "https://docs.spring.io/spring-boot/3.4/how-to/deployment/index.html", "https://docs.spring.io/spring-boot/3.4/installing.html", "https://docs.spring.io/spring-boot/3.4/reference/features/profiles.html", "https://docs.spring.io/spring-boot/3.4/api/rest/actuator/beans.html", "https://docs.spring.io/spring-boot/3.4/reference/packaging/container-images/dockerfiles.html", "https://docs.spring.io/spring-boot/3.4/reference/io/webservices.html", "https://docs.spring.io/spring-boot/3.4/gradle-plugin/aot.html", "https://docs.spring.io/spring-boot/3.4/maven-plugin/build-info.html"], "failed_urls": [], "url_queue": [], "last_updated": "2025-06-04T16:56:18.530119"}