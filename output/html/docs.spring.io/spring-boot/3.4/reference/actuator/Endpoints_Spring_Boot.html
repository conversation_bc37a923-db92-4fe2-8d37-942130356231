<!DOCTYPE html>
<html><head><title>Endpoints :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/reference/actuator/endpoints.html"/><meta content="2025-06-04T16:00:43.870374" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Endpoints">
<div class="toc-menu"><h3>Endpoints</h3><ul><li data-level="1"><a href="#actuator.endpoints.controlling-access">Controlling Access to Endpoints</a></li><li data-level="2"><a href="#actuator.endpoints.controlling-access.limiting">Limiting Access</a></li><li data-level="1"><a href="#actuator.endpoints.exposing">Exposing Endpoints</a></li><li data-level="1"><a href="#actuator.endpoints.security">Security</a></li><li data-level="2"><a href="#actuator.endpoints.security.csrf">Cross Site Request Forgery Protection</a></li><li data-level="1"><a href="#actuator.endpoints.caching">Configuring Endpoints</a></li><li data-level="1"><a href="#actuator.endpoints.sanitization">Sanitize Sensitive Values</a></li><li data-level="1"><a href="#actuator.endpoints.hypermedia">Hypermedia for Actuator Web Endpoints</a></li><li data-level="1"><a href="#actuator.endpoints.cors">CORS Support</a></li><li data-level="1"><a href="#actuator.endpoints.implementing-custom">Implementing Custom Endpoints</a></li><li data-level="2"><a href="#actuator.endpoints.implementing-custom.input">Receiving Input</a></li><li data-level="2"><a href="#actuator.endpoints.implementing-custom.web">Custom Web Endpoints</a></li><li data-level="1"><a href="#actuator.endpoints.health">Health Information</a></li><li data-level="2"><a href="#actuator.endpoints.health.auto-configured-health-indicators">Auto-configured HealthIndicators</a></li><li data-level="2"><a href="#actuator.endpoints.health.writing-custom-health-indicators">Writing Custom HealthIndicators</a></li><li data-level="2"><a href="#actuator.endpoints.health.reactive-health-indicators">Reactive Health Indicators</a></li><li data-level="2"><a href="#actuator.endpoints.health.auto-configured-reactive-health-indicators">Auto-configured ReactiveHealthIndicators</a></li><li data-level="2"><a href="#actuator.endpoints.health.groups">Health Groups</a></li><li data-level="2"><a href="#actuator.endpoints.health.datasource">DataSource Health</a></li><li data-level="1"><a href="#actuator.endpoints.kubernetes-probes">Kubernetes Probes</a></li><li data-level="2"><a href="#actuator.endpoints.kubernetes-probes.external-state">Checking External State With Kubernetes Probes</a></li><li data-level="2"><a href="#actuator.endpoints.kubernetes-probes.lifecycle">Application Lifecycle and Probe States</a></li><li data-level="1"><a href="#actuator.endpoints.info">Application Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.auto-configured-info-contributors">Auto-configured InfoContributors</a></li><li data-level="2"><a href="#actuator.endpoints.info.custom-application-information">Custom Application Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.git-commit-information">Git Commit Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.build-information">Build Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.java-information">Java Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.os-information">OS Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.process-information">Process Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.ssl-information">SSL Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.writing-custom-info-contributors">Writing Custom InfoContributors</a></li><li data-level="1"><a href="#actuator.endpoints.sbom">Software Bill of Materials (SBOM)</a></li><li data-level="2"><a href="#actuator.endpoints.sbom.other-formats">Other SBOM formats</a></li><li data-level="2"><a href="#actuator.endpoints.sbom.additional">Additional SBOMs</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/actuator/endpoints.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../index.html">Spring Boot</a></li>
<li><a href="../index.html">Reference</a></li>
<li><a href="index.html">Production-ready Features</a></li>
<li><a href="endpoints.html">Endpoints</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../reference/actuator/endpoints.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Endpoints</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Endpoints</h3><ul><li data-level="1"><a href="#actuator.endpoints.controlling-access">Controlling Access to Endpoints</a></li><li data-level="2"><a href="#actuator.endpoints.controlling-access.limiting">Limiting Access</a></li><li data-level="1"><a href="#actuator.endpoints.exposing">Exposing Endpoints</a></li><li data-level="1"><a href="#actuator.endpoints.security">Security</a></li><li data-level="2"><a href="#actuator.endpoints.security.csrf">Cross Site Request Forgery Protection</a></li><li data-level="1"><a href="#actuator.endpoints.caching">Configuring Endpoints</a></li><li data-level="1"><a href="#actuator.endpoints.sanitization">Sanitize Sensitive Values</a></li><li data-level="1"><a href="#actuator.endpoints.hypermedia">Hypermedia for Actuator Web Endpoints</a></li><li data-level="1"><a href="#actuator.endpoints.cors">CORS Support</a></li><li data-level="1"><a href="#actuator.endpoints.implementing-custom">Implementing Custom Endpoints</a></li><li data-level="2"><a href="#actuator.endpoints.implementing-custom.input">Receiving Input</a></li><li data-level="2"><a href="#actuator.endpoints.implementing-custom.web">Custom Web Endpoints</a></li><li data-level="1"><a href="#actuator.endpoints.health">Health Information</a></li><li data-level="2"><a href="#actuator.endpoints.health.auto-configured-health-indicators">Auto-configured HealthIndicators</a></li><li data-level="2"><a href="#actuator.endpoints.health.writing-custom-health-indicators">Writing Custom HealthIndicators</a></li><li data-level="2"><a href="#actuator.endpoints.health.reactive-health-indicators">Reactive Health Indicators</a></li><li data-level="2"><a href="#actuator.endpoints.health.auto-configured-reactive-health-indicators">Auto-configured ReactiveHealthIndicators</a></li><li data-level="2"><a href="#actuator.endpoints.health.groups">Health Groups</a></li><li data-level="2"><a href="#actuator.endpoints.health.datasource">DataSource Health</a></li><li data-level="1"><a href="#actuator.endpoints.kubernetes-probes">Kubernetes Probes</a></li><li data-level="2"><a href="#actuator.endpoints.kubernetes-probes.external-state">Checking External State With Kubernetes Probes</a></li><li data-level="2"><a href="#actuator.endpoints.kubernetes-probes.lifecycle">Application Lifecycle and Probe States</a></li><li data-level="1"><a href="#actuator.endpoints.info">Application Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.auto-configured-info-contributors">Auto-configured InfoContributors</a></li><li data-level="2"><a href="#actuator.endpoints.info.custom-application-information">Custom Application Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.git-commit-information">Git Commit Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.build-information">Build Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.java-information">Java Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.os-information">OS Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.process-information">Process Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.ssl-information">SSL Information</a></li><li data-level="2"><a href="#actuator.endpoints.info.writing-custom-info-contributors">Writing Custom InfoContributors</a></li><li data-level="1"><a href="#actuator.endpoints.sbom">Software Bill of Materials (SBOM)</a></li><li data-level="2"><a href="#actuator.endpoints.sbom.other-formats">Other SBOM formats</a></li><li data-level="2"><a href="#actuator.endpoints.sbom.additional">Additional SBOMs</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>Actuator endpoints let you monitor and interact with your application.
Spring Boot includes a number of built-in endpoints and lets you add your own.
For example, the <code>health</code> endpoint provides basic application health information.</p>
</div>
<div class="paragraph">
<p>You can <a href="#actuator.endpoints.controlling-access">control access</a> to each individual endpoint and <a href="#actuator.endpoints.exposing">expose them (make them remotely accessible) over HTTP or JMX</a>.
An endpoint is considered to be available when access to it is permitted and it is exposed.
The built-in endpoints are auto-configured only when they are available.
Most applications choose exposure over HTTP, where the ID of the endpoint and a prefix of <code>/actuator</code> is mapped to a URL.
For example, by default, the <code>health</code> endpoint is mapped to <code>/actuator/health</code>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
To learn more about the Actuator’s endpoints and their request and response formats, see the <a class="xref page" href="../../api/rest/actuator/index.html">API documentation</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following technology-agnostic endpoints are available:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 28.5714%;"/>
<col style="width: 71.4286%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">ID</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>auditevents</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes audit events information for the current application.
  Requires an <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/audit/AuditEventRepository.html"><code>AuditEventRepository</code></a> bean.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>beans</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Displays a complete list of all the Spring beans in your application.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>caches</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes available caches.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>conditions</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Shows the conditions that were evaluated on configuration and auto-configuration classes and the reasons why they did or did not match.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>configprops</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Displays a collated list of all <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a>.
Subject to <a href="#actuator.endpoints.sanitization">sanitization</a>.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>env</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes properties from Spring’s <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/ConfigurableEnvironment.html"><code>ConfigurableEnvironment</code></a>.
Subject to <a href="#actuator.endpoints.sanitization">sanitization</a>.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>flyway</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Shows any Flyway database migrations that have been applied.
  Requires one or more <a class="apiref external" href="https://javadoc.io/doc/org.flywaydb/flyway-core/10.20.1/org/flywaydb/core/Flyway.html" target="_blank"><code>Flyway</code></a> beans.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>health</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Shows application health information.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>httpexchanges</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Displays HTTP exchange information (by default, the last 100 HTTP request-response exchanges).
  Requires an <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/web/exchanges/HttpExchangeRepository.html"><code>HttpExchangeRepository</code></a> bean.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>info</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Displays arbitrary application info.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>integrationgraph</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Shows the Spring Integration graph.
  Requires a dependency on <code>spring-integration-core</code>.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>loggers</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Shows and modifies the configuration of loggers in the application.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>liquibase</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Shows any Liquibase database migrations that have been applied.
  Requires one or more <a class="apiref external" href="https://javadoc.io/doc/org.liquibase/liquibase-core/4.29.2/liquibase/Liquibase.html" target="_blank"><code>Liquibase</code></a> beans.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>metrics</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Shows “metrics” information for the current application to diagnose the metrics the application has recorded.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>mappings</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Displays a collated list of all <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/annotation/RequestMapping.html"><code>@RequestMapping</code></a> paths.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>quartz</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Shows information about Quartz Scheduler jobs.
Subject to <a href="#actuator.endpoints.sanitization">sanitization</a>.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>scheduledtasks</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Displays the scheduled tasks in your application.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>sessions</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Allows retrieval and deletion of user sessions from a Spring Session-backed session store.
  Requires a servlet-based web application that uses Spring Session.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>shutdown</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Lets the application be gracefully shutdown.
  Only works when using jar packaging.
  Disabled by default.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>startup</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Shows the <a class="xref page" href="../features/spring-application.html#features.spring-application.startup-tracking">startup steps data</a> collected by the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/metrics/ApplicationStartup.html"><code>ApplicationStartup</code></a>.
  Requires the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringApplication.html"><code>SpringApplication</code></a> to be configured with a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/metrics/buffering/BufferingApplicationStartup.html"><code>BufferingApplicationStartup</code></a>.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>threaddump</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Performs a thread dump.</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>If your application is a web application (Spring MVC, Spring WebFlux, or Jersey), you can use the following additional endpoints:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 28.5714%;"/>
<col style="width: 71.4286%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">ID</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>heapdump</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Returns a heap dump file.
  On a HotSpot JVM, an <code>HPROF</code>-format file is returned.
  On an OpenJ9 JVM, a <code>PHD</code>-format file is returned.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>logfile</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Returns the contents of the logfile (if the <code>logging.file.name</code> or the <code>logging.file.path</code> property has been set).
  Supports the use of the HTTP <code>Range</code> header to retrieve part of the log file’s content.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>prometheus</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes metrics in a format that can be scraped by a Prometheus server.
  Requires a dependency on <code>micrometer-registry-prometheus</code>.</p></td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="sect1">
<h2 id="actuator.endpoints.controlling-access"><a class="anchor" href="#actuator.endpoints.controlling-access"></a>Controlling Access to Endpoints</h2>
<div class="sectionbody">
<div class="paragraph">
<p>By default, access to all endpoints except for <code>shutdown</code> is unrestricted.
To configure the permitted access to an endpoint, use its <code>management.endpoint.&lt;id&gt;.access</code> property.
The following example allows unrestricted access to the <code>shutdown</code> endpoint:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_1_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_1_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_1_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_properties" class="tabpanel" id="_tabs_1_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.shutdown.access</span>=<span class="hljs-string">unrestricted</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_1_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoint:</span>
    <span class="hljs-attr">shutdown:</span>
      <span class="hljs-attr">access:</span> <span class="hljs-string">unrestricted</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you prefer access to be opt-in rather than opt-out, set the <code>management.endpoints.access.default</code> property to <code>none</code> and use individual endpoint <code>access</code> properties to opt back in.
The following example allows read-only access to the <code>loggers</code> endpoint and denies access to all other endpoints:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_2_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_2_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_2_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_properties" class="tabpanel" id="_tabs_2_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoints.access.default</span>=<span class="hljs-string">none</span>
<span class="hljs-meta">management.endpoint.loggers.access</span>=<span class="hljs-string">read-only</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_2_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoints:</span>
    <span class="hljs-attr">access:</span>
      <span class="hljs-attr">default:</span> <span class="hljs-string">none</span>
  <span class="hljs-attr">endpoint:</span>
    <span class="hljs-attr">loggers:</span>
      <span class="hljs-attr">access:</span> <span class="hljs-string">read-only</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Inaccessible endpoints are removed entirely from the application context.
If you want to change only the technologies over which an endpoint is exposed, use the <a href="#actuator.endpoints.exposing"><code>include</code> and <code>exclude</code> properties</a> instead.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.controlling-access.limiting"><a class="anchor" href="#actuator.endpoints.controlling-access.limiting"></a>Limiting Access</h3>
<div class="paragraph">
<p>Application-wide endpoint access can be limited using the <code>management.endpoints.access.max-permitted</code> property.
This property takes precedence over the default access or an individual endpoint’s access level.
Set it to <code>none</code> to make all endpoints inaccessible.
Set it to <code>read-only</code> to only allow read access to endpoints.</p>
</div>
<div class="paragraph">
<p>For <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/Endpoint.html"><code>@Endpoint</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/jmx/annotation/JmxEndpoint.html"><code>@JmxEndpoint</code></a>, and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/web/annotation/WebEndpoint.html"><code>@WebEndpoint</code></a>, read access equates to the endpoint methods annotated with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/ReadOperation.html"><code>@ReadOperation</code></a>.
For <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/web/annotation/ControllerEndpoint.html"><code>@ControllerEndpoint</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/web/annotation/RestControllerEndpoint.html"><code>@RestControllerEndpoint</code></a>, read access equates to request mappings that can handle <code>GET</code> and <code>HEAD</code> requests.
For <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/web/annotation/ServletEndpoint.html"><code>@ServletEndpoint</code></a>, read access equates to <code>GET</code> and <code>HEAD</code> requests.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.endpoints.exposing"><a class="anchor" href="#actuator.endpoints.exposing"></a>Exposing Endpoints</h2>
<div class="sectionbody">
<div class="paragraph">
<p>By default, only the health endpoint is exposed over HTTP and JMX.
Since Endpoints may contain sensitive information, you should carefully consider when to expose them.</p>
</div>
<div class="paragraph">
<p>To change which endpoints are exposed, use the following technology-specific <code>include</code> and <code>exclude</code> properties:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 75%;"/>
<col style="width: 25%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Property</th>
<th class="tableblock halign-left valign-top">Default</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>management.endpoints.jmx.exposure.exclude</code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>management.endpoints.jmx.exposure.include</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>health</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>management.endpoints.web.exposure.exclude</code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>management.endpoints.web.exposure.include</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>health</code></p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>The <code>include</code> property lists the IDs of the endpoints that are exposed.
The <code>exclude</code> property lists the IDs of the endpoints that should not be exposed.
The <code>exclude</code> property takes precedence over the <code>include</code> property.
You can configure both the <code>include</code> and the <code>exclude</code> properties with a list of endpoint IDs.</p>
</div>
<div class="paragraph">
<p>For example, to only expose the <code>health</code> and <code>info</code> endpoints over JMX, use the following property:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_3_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_3_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_3_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_properties" class="tabpanel" id="_tabs_3_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoints.jmx.exposure.include</span>=<span class="hljs-string">health,info</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_3_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoints:</span>
    <span class="hljs-attr">jmx:</span>
      <span class="hljs-attr">exposure:</span>
        <span class="hljs-attr">include:</span> <span class="hljs-string">"health,info"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p><code>*</code> can be used to select all endpoints.
For example, to expose everything over HTTP except the <code>env</code> and <code>beans</code> endpoints, use the following properties:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_4_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_4_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_4_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_properties" class="tabpanel" id="_tabs_4_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoints.web.exposure.include</span>=<span class="hljs-string">*</span>
<span class="hljs-meta">management.endpoints.web.exposure.exclude</span>=<span class="hljs-string">env,beans</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_4_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoints:</span>
    <span class="hljs-attr">web:</span>
      <span class="hljs-attr">exposure:</span>
        <span class="hljs-attr">include:</span> <span class="hljs-string">"*"</span>
        <span class="hljs-attr">exclude:</span> <span class="hljs-string">"env,beans"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<code>*</code> has a special meaning in YAML, so be sure to add quotation marks if you want to include (or exclude) all endpoints.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If your application is exposed publicly, we strongly recommend that you also <a href="#actuator.endpoints.security">secure your endpoints</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you want to implement your own strategy for when endpoints are exposed, you can register an <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/EndpointFilter.html"><code>EndpointFilter</code></a> bean.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.endpoints.security"><a class="anchor" href="#actuator.endpoints.security"></a>Security</h2>
<div class="sectionbody">
<div class="paragraph">
<p>For security purposes, only the <code>/health</code> endpoint is exposed over HTTP by default.
You can use the <code>management.endpoints.web.exposure.include</code> property to configure the endpoints that are exposed.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Before setting the <code>management.endpoints.web.exposure.include</code>, ensure that the exposed actuators do not contain sensitive information, are secured by placing them behind a firewall, or are secured by something like Spring Security.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If Spring Security is on the classpath and no other <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/SecurityFilterChain.html"><code>SecurityFilterChain</code></a> bean is present, all actuators other than <code>/health</code> are secured by Spring Boot auto-configuration.
If you define a custom <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/SecurityFilterChain.html"><code>SecurityFilterChain</code></a> bean, Spring Boot auto-configuration backs off and lets you fully control the actuator access rules.</p>
</div>
<div class="paragraph">
<p>If you wish to configure custom security for HTTP endpoints (for example, to allow only users with a certain role to access them), Spring Boot provides some convenient <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/util/matcher/RequestMatcher.html"><code>RequestMatcher</code></a> objects that you can use in combination with Spring Security.</p>
</div>
<div class="paragraph">
<p>A typical Spring Security configuration might look something like the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_5_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_5_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_5_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_java" class="tabpanel" id="_tabs_5_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.security.config.annotation.web.builders.HttpSecurity;
<span class="hljs-keyword">import</span> org.springframework.security.web.SecurityFilterChain;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.security.config.Customizer.withDefaults;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MySecurityConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> SecurityFilterChain <span class="hljs-title">securityFilterChain</span><span class="hljs-params">(HttpSecurity http)</span> <span class="hljs-keyword">throws</span> Exception </span>{
		http.securityMatcher(EndpointRequest.toAnyEndpoint());
		http.authorizeHttpRequests((requests) -&gt; requests.anyRequest().hasRole(<span class="hljs-string">"ENDPOINT_ADMIN"</span>));
		http.httpBasic(withDefaults());
		<span class="hljs-keyword">return</span> http.build();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_5_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.security.config.Customizer.withDefaults
<span class="hljs-keyword">import</span> org.springframework.security.config.<span class="hljs-keyword">annotation</span>.web.builders.HttpSecurity
<span class="hljs-keyword">import</span> org.springframework.security.web.SecurityFilterChain

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MySecurityConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">securityFilterChain</span><span class="hljs-params">(http: <span class="hljs-type">HttpSecurity</span>)</span></span>: SecurityFilterChain {
		http.securityMatcher(EndpointRequest.toAnyEndpoint()).authorizeHttpRequests { requests -&gt;
			requests.anyRequest().hasRole(<span class="hljs-string">"ENDPOINT_ADMIN"</span>)
		}
		http.httpBasic(withDefaults())
		<span class="hljs-keyword">return</span> http.build()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The preceding example uses <code>EndpointRequest.toAnyEndpoint()</code> to match a request to any endpoint and then ensures that all have the <code>ENDPOINT_ADMIN</code> role.
Several other matcher methods are also available on <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/autoconfigure/security/servlet/EndpointRequest.html"><code>EndpointRequest</code></a>.
See the <a class="xref page" href="../../api/rest/actuator/index.html">API documentation</a> for details.</p>
</div>
<div class="paragraph">
<p>If you deploy applications behind a firewall, you may prefer that all your actuator endpoints can be accessed without requiring authentication.
You can do so by changing the <code>management.endpoints.web.exposure.include</code> property, as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_6_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_6_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_6_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_properties" class="tabpanel" id="_tabs_6_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoints.web.exposure.include</span>=<span class="hljs-string">*</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_6_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoints:</span>
    <span class="hljs-attr">web:</span>
      <span class="hljs-attr">exposure:</span>
        <span class="hljs-attr">include:</span> <span class="hljs-string">"*"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Additionally, if Spring Security is present, you would need to add custom security configuration that allows unauthenticated access to the endpoints, as the following example shows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_7_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_7_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_7_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_java" class="tabpanel" id="_tabs_7_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.security.config.annotation.web.builders.HttpSecurity;
<span class="hljs-keyword">import</span> org.springframework.security.web.SecurityFilterChain;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MySecurityConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> SecurityFilterChain <span class="hljs-title">securityFilterChain</span><span class="hljs-params">(HttpSecurity http)</span> <span class="hljs-keyword">throws</span> Exception </span>{
		http.securityMatcher(EndpointRequest.toAnyEndpoint());
		http.authorizeHttpRequests((requests) -&gt; requests.anyRequest().permitAll());
		<span class="hljs-keyword">return</span> http.build();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_7_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.security.config.<span class="hljs-keyword">annotation</span>.web.builders.HttpSecurity
<span class="hljs-keyword">import</span> org.springframework.security.web.SecurityFilterChain

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MySecurityConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">securityFilterChain</span><span class="hljs-params">(http: <span class="hljs-type">HttpSecurity</span>)</span></span>: SecurityFilterChain {
		http.securityMatcher(EndpointRequest.toAnyEndpoint()).authorizeHttpRequests { requests -&gt;
			requests.anyRequest().permitAll()
		}
		<span class="hljs-keyword">return</span> http.build()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
In both of the preceding examples, the configuration applies only to the actuator endpoints.
Since Spring Boot’s security configuration backs off completely in the presence of any <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/SecurityFilterChain.html"><code>SecurityFilterChain</code></a> bean, you need to configure an additional <a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/SecurityFilterChain.html"><code>SecurityFilterChain</code></a> bean with rules that apply to the rest of the application.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.security.csrf"><a class="anchor" href="#actuator.endpoints.security.csrf"></a>Cross Site Request Forgery Protection</h3>
<div class="paragraph">
<p>Since Spring Boot relies on Spring Security’s defaults, CSRF protection is turned on by default.
This means that the actuator endpoints that require a <code>POST</code> (shutdown and loggers endpoints), a <code>PUT</code>, or a <code>DELETE</code> get a 403 (forbidden) error when the default security configuration is in use.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
We recommend disabling CSRF protection completely only if you are creating a service that is used by non-browser clients.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>You can find additional information about CSRF protection in the <a href="https://docs.spring.io/spring-security/reference/6.4/features/exploits/csrf.html">Spring Security Reference Guide</a>.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.endpoints.caching"><a class="anchor" href="#actuator.endpoints.caching"></a>Configuring Endpoints</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Endpoints automatically cache responses to read operations that do not take any parameters.
To configure the amount of time for which an endpoint caches a response, use its <code>cache.time-to-live</code> property.
The following example sets the time-to-live of the <code>beans</code> endpoint’s cache to 10 seconds:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_8_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_8_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_8_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_properties" class="tabpanel" id="_tabs_8_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.beans.cache.time-to-live</span>=<span class="hljs-string">10s</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_8_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoint:</span>
    <span class="hljs-attr">beans:</span>
      <span class="hljs-attr">cache:</span>
        <span class="hljs-attr">time-to-live:</span> <span class="hljs-string">"10s"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <code>management.endpoint.&lt;name&gt;</code> prefix uniquely identifies the endpoint that is being configured.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.endpoints.sanitization"><a class="anchor" href="#actuator.endpoints.sanitization"></a>Sanitize Sensitive Values</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Information returned by the <code>/env</code>, <code>/configprops</code> and <code>/quartz</code> endpoints can be sensitive, so by default values are always fully sanitized (replaced by <code>******</code>).</p>
</div>
<div class="paragraph">
<p>Values can only be viewed in an unsanitized form when:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>The <code>show-values</code> property has been set to something other than <code>never</code></p>
</li>
<li>
<p>No custom <a class="xref page" href="../../how-to/actuator.html#howto.actuator.customizing-sanitization"><code>SanitizingFunction</code></a> beans apply</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The <code>show-values</code> property can be configured for sanitizable endpoints to one of the following values:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>never</code>  - values are always fully sanitized (replaced by <code>******</code>)</p>
</li>
<li>
<p><code>always</code> - values are shown to all users (as long as no <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/SanitizingFunction.html"><code>SanitizingFunction</code></a> bean applies)</p>
</li>
<li>
<p><code>when-authorized</code> - values are shown only to authorized users (as long as no <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/SanitizingFunction.html"><code>SanitizingFunction</code></a> bean applies)</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>For HTTP endpoints, a user is considered to be authorized if they have authenticated and have the roles configured by the endpoint’s roles property.
By default, any authenticated user is authorized.</p>
</div>
<div class="paragraph">
<p>For JMX endpoints, all users are always authorized.</p>
</div>
<div class="paragraph">
<p>The following example allows all users with the <code>admin</code> role to view values from the <code>/env</code> endpoint in their original form.
Unauthorized users, or users without the <code>admin</code> role, will see only sanitized values.</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_9">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_9_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_9_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_9_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_9_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_9_properties" class="tabpanel" id="_tabs_9_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.env.show-values</span>=<span class="hljs-string">when-authorized</span>
<span class="hljs-meta">management.endpoint.env.roles</span>=<span class="hljs-string">admin</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_9_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_9_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoint:</span>
    <span class="hljs-attr">env:</span>
      <span class="hljs-attr">show-values:</span> <span class="hljs-string">when-authorized</span>
      <span class="hljs-attr">roles:</span> <span class="hljs-string">"admin"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
This example assumes that no <a class="xref page" href="../../how-to/actuator.html#howto.actuator.customizing-sanitization"><code>SanitizingFunction</code></a> beans have been defined.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.endpoints.hypermedia"><a class="anchor" href="#actuator.endpoints.hypermedia"></a>Hypermedia for Actuator Web Endpoints</h2>
<div class="sectionbody">
<div class="paragraph">
<p>A “discovery page” is added with links to all the endpoints.
The “discovery page” is available on <code>/actuator</code> by default.</p>
</div>
<div class="paragraph">
<p>To disable the “discovery page”, add the following property to your application properties:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_10">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_10_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_10_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_10_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_10_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_10_properties" class="tabpanel" id="_tabs_10_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoints.web.discovery.enabled</span>=<span class="hljs-string">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_10_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_10_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoints:</span>
    <span class="hljs-attr">web:</span>
      <span class="hljs-attr">discovery:</span>
        <span class="hljs-attr">enabled:</span> <span class="hljs-literal">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>When a custom management context path is configured, the “discovery page” automatically moves from <code>/actuator</code> to the root of the management context.
For example, if the management context path is <code>/management</code>, the discovery page is available from <code>/management</code>.
When the management context path is set to <code>/</code>, the discovery page is disabled to prevent the possibility of a clash with other mappings.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.endpoints.cors"><a class="anchor" href="#actuator.endpoints.cors"></a>CORS Support</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="external" href="https://en.wikipedia.org/wiki/Cross-origin_resource_sharing" target="_blank">Cross-origin resource sharing</a> (CORS) is a <a class="external" href="https://www.w3.org/TR/cors/" target="_blank">W3C specification</a> that lets you specify in a flexible way what kind of cross-domain requests are authorized.
If you use Spring MVC or Spring WebFlux, you can configure Actuator’s web endpoints to support such scenarios.</p>
</div>
<div class="paragraph">
<p>CORS support is disabled by default and is only enabled once you have set the <code>management.endpoints.web.cors.allowed-origins</code> property.
The following configuration permits <code>GET</code> and <code>POST</code> calls from the <code>example.com</code> domain:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_11">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_11_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_11_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_11_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_11_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_11_properties" class="tabpanel" id="_tabs_11_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoints.web.cors.allowed-origins</span>=<span class="hljs-string">https://example.com</span>
<span class="hljs-meta">management.endpoints.web.cors.allowed-methods</span>=<span class="hljs-string">GET,POST</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_11_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_11_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoints:</span>
    <span class="hljs-attr">web:</span>
      <span class="hljs-attr">cors:</span>
        <span class="hljs-attr">allowed-origins:</span> <span class="hljs-string">"https://example.com"</span>
        <span class="hljs-attr">allowed-methods:</span> <span class="hljs-string">"GET,POST"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
See <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/autoconfigure/endpoint/web/CorsEndpointProperties.html"><code>CorsEndpointProperties</code></a> for a complete list of options.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.endpoints.implementing-custom"><a class="anchor" href="#actuator.endpoints.implementing-custom"></a>Implementing Custom Endpoints</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you add a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> annotated with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/Endpoint.html"><code>@Endpoint</code></a>, any methods annotated with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/ReadOperation.html"><code>@ReadOperation</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/WriteOperation.html"><code>@WriteOperation</code></a>, or <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/DeleteOperation.html"><code>@DeleteOperation</code></a> are automatically exposed over JMX and, in a web application, over HTTP as well.
Endpoints can be exposed over HTTP by using Jersey, Spring MVC, or Spring WebFlux.
If both Jersey and Spring MVC are available, Spring MVC is used.</p>
</div>
<div class="paragraph">
<p>The following example exposes a read operation that returns a custom object:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_12">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_12_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_12_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_12_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_12_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_12_java" class="tabpanel" id="_tabs_12_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">	<span class="hljs-meta">@ReadOperation</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> CustomData <span class="hljs-title">getData</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> CustomData(<span class="hljs-string">"test"</span>, <span class="hljs-number">5</span>);
	}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_12_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_12_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin">	<span class="hljs-meta">@ReadOperation</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getData</span><span class="hljs-params">()</span></span>: CustomData {
		<span class="hljs-keyword">return</span> CustomData(<span class="hljs-string">"test"</span>, <span class="hljs-number">5</span>)
	}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also write technology-specific endpoints by using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/jmx/annotation/JmxEndpoint.html"><code>@JmxEndpoint</code></a> or <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/web/annotation/WebEndpoint.html"><code>@WebEndpoint</code></a>.
These endpoints are restricted to their respective technologies.
For example, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/web/annotation/WebEndpoint.html"><code>@WebEndpoint</code></a> is exposed only over HTTP and not over JMX.</p>
</div>
<div class="paragraph">
<p>You can write technology-specific extensions by using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/web/annotation/EndpointWebExtension.html"><code>@EndpointWebExtension</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/jmx/annotation/EndpointJmxExtension.html"><code>@EndpointJmxExtension</code></a>.
These annotations let you provide technology-specific operations to augment an existing endpoint.</p>
</div>
<div class="paragraph">
<p>Finally, if you need access to web-framework-specific functionality, you can implement servlet or Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Controller.html"><code>@Controller</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/annotation/RestController.html"><code>@RestController</code></a> endpoints at the cost of them not being available over JMX or when using a different web framework.</p>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.implementing-custom.input"><a class="anchor" href="#actuator.endpoints.implementing-custom.input"></a>Receiving Input</h3>
<div class="paragraph">
<p>Operations on an endpoint receive input through their parameters.
When exposed over the web, the values for these parameters are taken from the URL’s query parameters and from the JSON request body.
When exposed over JMX, the parameters are mapped to the parameters of the MBean’s operations.
Parameters are required by default.
They can be made optional by annotating them with either <code>@javax.annotation.Nullable</code> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/lang/Nullable.html"><code>@Nullable</code></a>.</p>
</div>
<div class="paragraph">
<p>You can map each root property in the JSON request body to a parameter of the endpoint.
Consider the following JSON request body:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-json hljs" data-lang="json">{
	<span class="hljs-attr">"name"</span>: <span class="hljs-string">"test"</span>,
	<span class="hljs-attr">"counter"</span>: <span class="hljs-number">42</span>
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>You can use this to invoke a write operation that takes <code>String name</code> and <code>int counter</code> parameters, as the following example shows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_13">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_13_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_13_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_13_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_13_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_13_java" class="tabpanel" id="_tabs_13_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">	<span class="hljs-meta">@WriteOperation</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">updateData</span><span class="hljs-params">(String name, <span class="hljs-keyword">int</span> counter)</span> </span>{
		<span class="hljs-comment">// injects "test" and 42</span>
	}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_13_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_13_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin">	<span class="hljs-meta">@WriteOperation</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">updateData</span><span class="hljs-params">(name: <span class="hljs-type">String</span>?, counter: <span class="hljs-type">Int</span>)</span></span> {
		<span class="hljs-comment">// injects "test" and 42</span>
	}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Because endpoints are technology agnostic, only simple types can be specified in the method signature.
In particular, declaring a single parameter with a <a class="apiref external" href="https://javadoc.io/doc/org.liquibase/liquibase-core/4.29.2/liquibase/report/CustomData.html" target="_blank"><code>CustomData</code></a> type that defines a <code>name</code> and <code>counter</code> properties is not supported.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
To let the input be mapped to the operation method’s parameters, Java code that implements an endpoint should be compiled with <code>-parameters</code>.
For Kotlin code, please review <a href="https://docs.spring.io/spring-framework/reference/6.2/languages/kotlin/classes-interfaces.html">the recommendation</a> of the Spring Framework reference.
This will happen automatically if you use Spring Boot’s Gradle plugin or if you use Maven and <code>spring-boot-starter-parent</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="sect3">
<h4 id="actuator.endpoints.implementing-custom.input.conversion"><a class="anchor" href="#actuator.endpoints.implementing-custom.input.conversion"></a>Input Type Conversion</h4>
<div class="paragraph">
<p>The parameters passed to endpoint operation methods are, if necessary, automatically converted to the required type.
Before calling an operation method, the input received over JMX or HTTP is converted to the required types by using an instance of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/convert/ApplicationConversionService.html"><code>ApplicationConversionService</code></a> as well as any <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/converter/Converter.html"><code>Converter</code></a> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/converter/GenericConverter.html"><code>GenericConverter</code></a> beans qualified with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/EndpointConverter.html"><code>@EndpointConverter</code></a>.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.implementing-custom.web"><a class="anchor" href="#actuator.endpoints.implementing-custom.web"></a>Custom Web Endpoints</h3>
<div class="paragraph">
<p>Operations on an <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/Endpoint.html"><code>@Endpoint</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/web/annotation/WebEndpoint.html"><code>@WebEndpoint</code></a>, or <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/web/annotation/EndpointWebExtension.html"><code>@EndpointWebExtension</code></a> are automatically exposed over HTTP using Jersey, Spring MVC, or Spring WebFlux.
If both Jersey and Spring MVC are available, Spring MVC is used.</p>
</div>
<div class="sect3">
<h4 id="actuator.endpoints.implementing-custom.web.request-predicates"><a class="anchor" href="#actuator.endpoints.implementing-custom.web.request-predicates"></a>Web Endpoint Request Predicates</h4>
<div class="paragraph">
<p>A request predicate is automatically generated for each operation on a web-exposed endpoint.</p>
</div>
</div>
<div class="sect3">
<h4 id="actuator.endpoints.implementing-custom.web.path-predicates"><a class="anchor" href="#actuator.endpoints.implementing-custom.web.path-predicates"></a>Path</h4>
<div class="paragraph">
<p>The path of the predicate is determined by the ID of the endpoint and the base path of the web-exposed endpoints.
The default base path is <code>/actuator</code>.
For example, an endpoint with an ID of <code>sessions</code> uses <code>/actuator/sessions</code> as its path in the predicate.</p>
</div>
<div class="paragraph">
<p>You can further customize the path by annotating one or more parameters of the operation method with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/Selector.html"><code>@Selector</code></a>.
Such a parameter is added to the path predicate as a path variable.
The variable’s value is passed into the operation method when the endpoint operation is invoked.
If you want to capture all remaining path elements, you can add <code>@Selector(Match=ALL_REMAINING)</code> to the last parameter and make it a type that is conversion-compatible with a <code>String[]</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="actuator.endpoints.implementing-custom.web.method-predicates"><a class="anchor" href="#actuator.endpoints.implementing-custom.web.method-predicates"></a>HTTP method</h4>
<div class="paragraph">
<p>The HTTP method of the predicate is determined by the operation type, as shown in the following table:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 75%;"/>
<col style="width: 25%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Operation</th>
<th class="tableblock halign-left valign-top">HTTP method</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/ReadOperation.html"><code>@ReadOperation</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>GET</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/WriteOperation.html"><code>@WriteOperation</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>POST</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/DeleteOperation.html"><code>@DeleteOperation</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>DELETE</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="actuator.endpoints.implementing-custom.web.consumes-predicates"><a class="anchor" href="#actuator.endpoints.implementing-custom.web.consumes-predicates"></a>Consumes</h4>
<div class="paragraph">
<p>For a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/WriteOperation.html"><code>@WriteOperation</code></a> (HTTP <code>POST</code>) that uses the request body, the <code>consumes</code> clause of the predicate is <code>application/vnd.spring-boot.actuator.v2+json, application/json</code>.
For all other operations, the <code>consumes</code> clause is empty.</p>
</div>
</div>
<div class="sect3">
<h4 id="actuator.endpoints.implementing-custom.web.produces-predicates"><a class="anchor" href="#actuator.endpoints.implementing-custom.web.produces-predicates"></a>Produces</h4>
<div class="paragraph">
<p>The <code>produces</code> clause of the predicate can be determined by the <code>produces</code> attribute of the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/DeleteOperation.html"><code>@DeleteOperation</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/ReadOperation.html"><code>@ReadOperation</code></a>, and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/WriteOperation.html"><code>@WriteOperation</code></a> annotations.
The attribute is optional.
If it is not used, the <code>produces</code> clause is determined automatically.</p>
</div>
<div class="paragraph">
<p>If the operation method returns <code>void</code> or <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" target="_blank"><code>Void</code></a>, the <code>produces</code> clause is empty.
If the operation method returns a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/io/Resource.html"><code>Resource</code></a>, the <code>produces</code> clause is <code>application/octet-stream</code>.
For all other operations, the <code>produces</code> clause is <code>application/vnd.spring-boot.actuator.v2+json, application/json</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="actuator.endpoints.implementing-custom.web.response-status"><a class="anchor" href="#actuator.endpoints.implementing-custom.web.response-status"></a>Web Endpoint Response Status</h4>
<div class="paragraph">
<p>The default response status for an endpoint operation depends on the operation type (read, write, or delete) and what, if anything, the operation returns.</p>
</div>
<div class="paragraph">
<p>If a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/ReadOperation.html"><code>@ReadOperation</code></a> returns a value, the response status will be 200 (OK).
If it does not return a value, the response status will be 404 (Not Found).</p>
</div>
<div class="paragraph">
<p>If a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/WriteOperation.html"><code>@WriteOperation</code></a> or <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/DeleteOperation.html"><code>@DeleteOperation</code></a> returns a value, the response status will be 200 (OK).
If it does not return a value, the response status will be 204 (No Content).</p>
</div>
<div class="paragraph">
<p>If an operation is invoked without a required parameter or with a parameter that cannot be converted to the required type, the operation method is not called, and the response status will be 400 (Bad Request).</p>
</div>
</div>
<div class="sect3">
<h4 id="actuator.endpoints.implementing-custom.web.range-requests"><a class="anchor" href="#actuator.endpoints.implementing-custom.web.range-requests"></a>Web Endpoint Range Requests</h4>
<div class="paragraph">
<p>You can use an HTTP range request to request part of an HTTP resource.
When using Spring MVC or Spring Web Flux, operations that return a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/io/Resource.html"><code>Resource</code></a> automatically support range requests.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Range requests are not supported when using Jersey.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect3">
<h4 id="actuator.endpoints.implementing-custom.web.security"><a class="anchor" href="#actuator.endpoints.implementing-custom.web.security"></a>Web Endpoint Security</h4>
<div class="paragraph">
<p>An operation on a web endpoint or a web-specific endpoint extension can receive the current <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/security/Principal.html" target="_blank"><code>Principal</code></a> or <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/SecurityContext.html"><code>SecurityContext</code></a> as a method parameter.
The former is typically used in conjunction with either <code>@javax.annotation.Nullable</code> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/lang/Nullable.html"><code>@Nullable</code></a> to provide different behavior for authenticated and unauthenticated users.
The latter is typically used to perform authorization checks by using its <code>isUserInRole(String)</code> method.</p>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.endpoints.health"><a class="anchor" href="#actuator.endpoints.health"></a>Health Information</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can use health information to check the status of your running application.
It is often used by monitoring software to alert someone when a production system goes down.
The information exposed by the <code>health</code> endpoint depends on the <code>management.endpoint.health.show-details</code> and <code>management.endpoint.health.show-components</code> properties, which can be configured with one of the following values:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 25%;"/>
<col style="width: 75%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Name</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>never</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Details are never shown.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>when-authorized</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Details are shown only to authorized users.
  Authorized roles can be configured by using <code>management.endpoint.health.roles</code>.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>always</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Details are shown to all users.</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>The default value is <code>never</code>.
A user is considered to be authorized when they are in one or more of the endpoint’s roles.
If the endpoint has no configured roles (the default), all authenticated users are considered to be authorized.
You can configure the roles by using the <code>management.endpoint.health.roles</code> property.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If you have secured your application and wish to use <code>always</code>, your security configuration must permit access to the health endpoint for both authenticated and unauthenticated users.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Health information is collected from the content of a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthContributorRegistry.html"><code>HealthContributorRegistry</code></a> (by default, all <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthContributor.html"><code>HealthContributor</code></a> instances defined in your <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a>).
Spring Boot includes a number of auto-configured <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthContributor.html"><code>HealthContributor</code></a> beans, and you can also write your own.</p>
</div>
<div class="paragraph">
<p>A <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthContributor.html"><code>HealthContributor</code></a> can be either a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> or a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/CompositeHealthContributor.html"><code>CompositeHealthContributor</code></a>.
A <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> provides actual health information, including a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/Status.html"><code>Status</code></a>.
A <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/CompositeHealthContributor.html"><code>CompositeHealthContributor</code></a> provides a composite of other <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthContributor.html"><code>HealthContributor</code></a> instances.
Taken together, contributors form a tree structure to represent the overall system health.</p>
</div>
<div class="paragraph">
<p>By default, the final system health is derived by a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/StatusAggregator.html"><code>StatusAggregator</code></a>, which sorts the statuses from each <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> based on an ordered list of statuses.
The first status in the sorted list is used as the overall health status.
If no <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> returns a status that is known to the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/StatusAggregator.html"><code>StatusAggregator</code></a>, an <code>UNKNOWN</code> status is used.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthContributorRegistry.html"><code>HealthContributorRegistry</code></a> to register and unregister health indicators at runtime.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.health.auto-configured-health-indicators"><a class="anchor" href="#actuator.endpoints.health.auto-configured-health-indicators"></a>Auto-configured HealthIndicators</h3>
<div class="paragraph">
<p>When appropriate, Spring Boot auto-configures the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> beans listed in the following table.
You can also enable or disable selected indicators by configuring <code>management.health.key.enabled</code>,
with the <code>key</code> listed in the following table:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 16.6666%;"/>
<col style="width: 33.3333%;"/>
<col style="width: 50.0001%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Key</th>
<th class="tableblock halign-left valign-top">Name</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>cassandra</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/cassandra/CassandraDriverHealthIndicator.html"><code>CassandraDriverHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a Cassandra database is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>couchbase</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/couchbase/CouchbaseHealthIndicator.html"><code>CouchbaseHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a Couchbase cluster is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>db</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/jdbc/DataSourceHealthIndicator.html"><code>DataSourceHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a connection to <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> can be obtained.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>diskspace</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/system/DiskSpaceHealthIndicator.html"><code>DiskSpaceHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks for low disk space.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>elasticsearch</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/elasticsearch/ElasticsearchRestClientHealthIndicator.html"><code>ElasticsearchRestClientHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that an Elasticsearch cluster is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>hazelcast</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/hazelcast/HazelcastHealthIndicator.html"><code>HazelcastHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a Hazelcast server is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>jms</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/jms/JmsHealthIndicator.html"><code>JmsHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a JMS broker is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>ldap</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/ldap/LdapHealthIndicator.html"><code>LdapHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that an LDAP server is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>mail</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/mail/MailHealthIndicator.html"><code>MailHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a mail server is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>mongo</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/data/mongo/MongoHealthIndicator.html"><code>MongoHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a Mongo database is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>neo4j</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/neo4j/Neo4jHealthIndicator.html"><code>Neo4jHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a Neo4j database is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>ping</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/PingHealthIndicator.html"><code>PingHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Always responds with <code>UP</code>.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>rabbit</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/amqp/RabbitHealthIndicator.html"><code>RabbitHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a Rabbit server is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>redis</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/data/redis/RedisHealthIndicator.html"><code>RedisHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a Redis server is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>ssl</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/ssl/SslHealthIndicator.html"><code>SslHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that SSL certificates are ok.</p></td>
</tr>
</tbody>
</table>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can disable them all by setting the <code>management.health.defaults.enabled</code> property.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The <code>ssl</code> <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> has a "warning threshold" property named <code>management.health.ssl.certificate-validity-warning-threshold</code>.
If an SSL certificate will be invalid within the time span defined by this threshold, the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> will warn you but it will still return HTTP 200 to not disrupt the application.
You can use this threshold to give yourself enough lead time to rotate the soon to be expired certificate.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Additional <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> beans are available but are not enabled by default:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 23.0769%;"/>
<col style="width: 30.7692%;"/>
<col style="width: 46.1539%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Key</th>
<th class="tableblock halign-left valign-top">Name</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>livenessstate</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/availability/LivenessStateHealthIndicator.html"><code>LivenessStateHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes the “Liveness” application availability state.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>readinessstate</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/availability/ReadinessStateHealthIndicator.html"><code>ReadinessStateHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes the “Readiness” application availability state.</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.health.writing-custom-health-indicators"><a class="anchor" href="#actuator.endpoints.health.writing-custom-health-indicators"></a>Writing Custom HealthIndicators</h3>
<div class="paragraph">
<p>To provide custom health information, you can register Spring beans that implement the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> interface.
You need to provide an implementation of the <code>health()</code> method and return a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/Health.html"><code>Health</code></a> response.
The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/Health.html"><code>Health</code></a> response should include a status and can optionally include additional details to be displayed.
The following code shows a sample <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> implementation:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_14">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_14_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_14_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_14_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_14_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_14_java" class="tabpanel" id="_tabs_14_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.actuate.health.Health;
<span class="hljs-keyword">import</span> org.springframework.boot.actuate.health.HealthIndicator;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyHealthIndicator</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">HealthIndicator</span> </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> Health <span class="hljs-title">health</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">int</span> errorCode = check();
		<span class="hljs-keyword">if</span> (errorCode != <span class="hljs-number">0</span>) {
			<span class="hljs-keyword">return</span> Health.down().withDetail(<span class="hljs-string">"Error Code"</span>, errorCode).build();
		}
		<span class="hljs-keyword">return</span> Health.up().build();
	}

	<span class="hljs-function"><span class="hljs-keyword">private</span> <span class="hljs-keyword">int</span> <span class="hljs-title">check</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-comment">// perform some specific health check</span>
		<span class="hljs-keyword">return</span> ...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_14_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_14_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.actuate.health.Health
<span class="hljs-keyword">import</span> org.springframework.boot.actuate.health.HealthIndicator
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyHealthIndicator</span> : <span class="hljs-type">HealthIndicator {</span></span>

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">health</span><span class="hljs-params">()</span></span>: Health {
		<span class="hljs-keyword">val</span> errorCode = check()
		<span class="hljs-keyword">if</span> (errorCode != <span class="hljs-number">0</span>) {
			<span class="hljs-keyword">return</span> Health.down().withDetail(<span class="hljs-string">"Error Code"</span>, errorCode).build()
		}
		<span class="hljs-keyword">return</span> Health.up().build()
	}

	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">check</span><span class="hljs-params">()</span></span>: <span class="hljs-built_in">Int</span> {
		<span class="hljs-comment">// perform some specific health check</span>
		<span class="hljs-keyword">return</span>  ...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The identifier for a given <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> is the name of the bean without the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> suffix, if it exists.
In the preceding example, the health information is available in an entry named <code>my</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Health indicators are usually called over HTTP and need to respond before any connection timeouts.
Spring Boot will log a warning message for any health indicator that takes longer than 10 seconds to respond.
If you want to configure this threshold, you can use the <code>management.endpoint.health.logging.slow-indicator-threshold</code> property.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>In addition to Spring Boot’s predefined <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/Status.html"><code>Status</code></a> types, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/Health.html"><code>Health</code></a> can return a custom <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/Status.html"><code>Status</code></a> that represents a new system state.
In such cases, you also need to provide a custom implementation of the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/StatusAggregator.html"><code>StatusAggregator</code></a> interface, or you must configure the default implementation by using the <code>management.endpoint.health.status.order</code> configuration property.</p>
</div>
<div class="paragraph">
<p>For example, assume a new <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/Status.html"><code>Status</code></a> with a code of <code>FATAL</code> is being used in one of your <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> implementations.
To configure the severity order, add the following property to your application properties:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_15">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_15_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_15_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_15_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_15_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_15_properties" class="tabpanel" id="_tabs_15_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.health.status.order</span>=<span class="hljs-string">fatal,down,out-of-service,unknown,up</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_15_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_15_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoint:</span>
    <span class="hljs-attr">health:</span>
      <span class="hljs-attr">status:</span>
        <span class="hljs-attr">order:</span> <span class="hljs-string">"fatal,down,out-of-service,unknown,up"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The HTTP status code in the response reflects the overall health status.
By default, <code>OUT_OF_SERVICE</code> and <code>DOWN</code> map to 503.
Any unmapped health statuses, including <code>UP</code>, map to 200.
You might also want to register custom status mappings if you access the health endpoint over HTTP.
Configuring a custom mapping disables the defaults mappings for <code>DOWN</code> and <code>OUT_OF_SERVICE</code>.
If you want to retain the default mappings, you must explicitly configure them, alongside any custom mappings.
For example, the following property maps <code>FATAL</code> to 503 (service unavailable) and retains the default mappings for <code>DOWN</code> and <code>OUT_OF_SERVICE</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_16">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_16_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_16_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_16_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_16_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_16_properties" class="tabpanel" id="_tabs_16_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.health.status.http-mapping.down</span>=<span class="hljs-string">503</span>
<span class="hljs-meta">management.endpoint.health.status.http-mapping.fatal</span>=<span class="hljs-string">503</span>
<span class="hljs-meta">management.endpoint.health.status.http-mapping.out-of-service</span>=<span class="hljs-string">503</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_16_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_16_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoint:</span>
    <span class="hljs-attr">health:</span>
      <span class="hljs-attr">status:</span>
        <span class="hljs-attr">http-mapping:</span>
          <span class="hljs-attr">down:</span> <span class="hljs-number">503</span>
          <span class="hljs-attr">fatal:</span> <span class="hljs-number">503</span>
          <span class="hljs-attr">out-of-service:</span> <span class="hljs-number">503</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you need more control, you can define your own <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HttpCodeStatusMapper.html"><code>HttpCodeStatusMapper</code></a> bean.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following table shows the default status mappings for the built-in statuses:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 25%;"/>
<col style="width: 75%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Status</th>
<th class="tableblock halign-left valign-top">Mapping</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>DOWN</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>SERVICE_UNAVAILABLE</code> (<code>503</code>)</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OUT_OF_SERVICE</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>SERVICE_UNAVAILABLE</code> (<code>503</code>)</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>UP</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">No mapping by default, so HTTP status is <code>200</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>UNKNOWN</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">No mapping by default, so HTTP status is <code>200</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.health.reactive-health-indicators"><a class="anchor" href="#actuator.endpoints.health.reactive-health-indicators"></a>Reactive Health Indicators</h3>
<div class="paragraph">
<p>For reactive applications, such as those that use Spring WebFlux, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/ReactiveHealthContributor.html"><code>ReactiveHealthContributor</code></a> provides a non-blocking contract for getting application health.
Similar to a traditional <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthContributor.html"><code>HealthContributor</code></a>, health information is collected from the content of a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/ReactiveHealthContributorRegistry.html"><code>ReactiveHealthContributorRegistry</code></a> (by default, all <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthContributor.html"><code>HealthContributor</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/ReactiveHealthContributor.html"><code>ReactiveHealthContributor</code></a> instances defined in your <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a>).
Regular <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthContributor.html"><code>HealthContributor</code></a> instances that do not check against a reactive API are executed on the elastic scheduler.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
In a reactive application, you should use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/ReactiveHealthContributorRegistry.html"><code>ReactiveHealthContributorRegistry</code></a> to register and unregister health indicators at runtime.
If you need to register a regular <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthContributor.html"><code>HealthContributor</code></a>, you should wrap it with <code>ReactiveHealthContributor#adapt</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>To provide custom health information from a reactive API, you can register Spring beans that implement the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/ReactiveHealthIndicator.html"><code>ReactiveHealthIndicator</code></a> interface.
The following code shows a sample <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/ReactiveHealthIndicator.html"><code>ReactiveHealthIndicator</code></a> implementation:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_17">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_17_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_17_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_17_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_17_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_17_java" class="tabpanel" id="_tabs_17_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> reactor.core.publisher.Mono;

<span class="hljs-keyword">import</span> org.springframework.boot.actuate.health.Health;
<span class="hljs-keyword">import</span> org.springframework.boot.actuate.health.ReactiveHealthIndicator;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyReactiveHealthIndicator</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">ReactiveHealthIndicator</span> </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> Mono&lt;Health&gt; <span class="hljs-title">health</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> doHealthCheck().onErrorResume((exception) -&gt;
			Mono.just(<span class="hljs-keyword">new</span> Health.Builder().down(exception).build()));
	}

	<span class="hljs-function"><span class="hljs-keyword">private</span> Mono&lt;Health&gt; <span class="hljs-title">doHealthCheck</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-comment">// perform some specific health check</span>
		<span class="hljs-keyword">return</span> ...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_17_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_17_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.actuate.health.Health
<span class="hljs-keyword">import</span> org.springframework.boot.actuate.health.ReactiveHealthIndicator
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component
<span class="hljs-keyword">import</span> reactor.core.publisher.Mono

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyReactiveHealthIndicator</span> : <span class="hljs-type">ReactiveHealthIndicator {</span></span>

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">health</span><span class="hljs-params">()</span></span>: Mono&lt;Health&gt; {
		<span class="hljs-keyword">return</span> doHealthCheck()!!.onErrorResume { exception: Throwable? -&gt;
			Mono.just(Health.Builder().down(exception).build())
		}
	}

	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">doHealthCheck</span><span class="hljs-params">()</span></span>: Mono&lt;Health&gt;? {
		<span class="hljs-comment">// perform some specific health check</span>
		<span class="hljs-keyword">return</span>  ...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
To handle the error automatically, consider extending from <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/AbstractReactiveHealthIndicator.html"><code>AbstractReactiveHealthIndicator</code></a>.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.health.auto-configured-reactive-health-indicators"><a class="anchor" href="#actuator.endpoints.health.auto-configured-reactive-health-indicators"></a>Auto-configured ReactiveHealthIndicators</h3>
<div class="paragraph">
<p>When appropriate, Spring Boot auto-configures the following <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/ReactiveHealthIndicator.html"><code>ReactiveHealthIndicator</code></a> beans:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 16.6666%;"/>
<col style="width: 33.3333%;"/>
<col style="width: 50.0001%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Key</th>
<th class="tableblock halign-left valign-top">Name</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>cassandra</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/cassandra/CassandraDriverReactiveHealthIndicator.html"><code>CassandraDriverReactiveHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a Cassandra database is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>couchbase</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/couchbase/CouchbaseReactiveHealthIndicator.html"><code>CouchbaseReactiveHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a Couchbase cluster is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>elasticsearch</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/data/elasticsearch/ElasticsearchReactiveHealthIndicator.html"><code>ElasticsearchReactiveHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that an Elasticsearch cluster is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>mongo</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/data/mongo/MongoReactiveHealthIndicator.html"><code>MongoReactiveHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a Mongo database is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>neo4j</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/neo4j/Neo4jReactiveHealthIndicator.html"><code>Neo4jReactiveHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a Neo4j database is up.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>redis</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/data/redis/RedisReactiveHealthIndicator.html"><code>RedisReactiveHealthIndicator</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Checks that a Redis server is up.</p></td>
</tr>
</tbody>
</table>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If necessary, reactive indicators replace the regular ones.
Also, any <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthIndicator.html"><code>HealthIndicator</code></a> that is not handled explicitly is wrapped automatically.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.health.groups"><a class="anchor" href="#actuator.endpoints.health.groups"></a>Health Groups</h3>
<div class="paragraph">
<p>It is sometimes useful to organize health indicators into groups that you can use for different purposes.</p>
</div>
<div class="paragraph">
<p>To create a health indicator group, you can use the <code>management.endpoint.health.group.&lt;name&gt;</code> property and specify a list of health indicator IDs to <code>include</code> or <code>exclude</code>.
For example, to create a group that includes only database indicators you can define the following:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_18">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_18_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_18_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_18_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_18_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_18_properties" class="tabpanel" id="_tabs_18_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.health.group.custom.include</span>=<span class="hljs-string">db</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_18_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_18_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoint:</span>
    <span class="hljs-attr">health:</span>
      <span class="hljs-attr">group:</span>
        <span class="hljs-attr">custom:</span>
          <span class="hljs-attr">include:</span> <span class="hljs-string">"db"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can then check the result by hitting <code><a class="bare external" href="http://localhost:8080/actuator/health/custom" target="_blank">localhost:8080/actuator/health/custom</a></code>.</p>
</div>
<div class="paragraph">
<p>Similarly, to create a group that excludes the database indicators from the group and includes all the other indicators, you can define the following:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_19">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_19_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_19_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_19_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_19_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_19_properties" class="tabpanel" id="_tabs_19_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.health.group.custom.exclude</span>=<span class="hljs-string">db</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_19_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_19_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoint:</span>
    <span class="hljs-attr">health:</span>
      <span class="hljs-attr">group:</span>
        <span class="hljs-attr">custom:</span>
          <span class="hljs-attr">exclude:</span> <span class="hljs-string">"db"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>By default, startup will fail if a health group includes or excludes a health indicator that does not exist.
To disable this behavior set <code>management.endpoint.health.validate-group-membership</code> to <code>false</code>.</p>
</div>
<div class="paragraph">
<p>By default, groups inherit the same <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/StatusAggregator.html"><code>StatusAggregator</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HttpCodeStatusMapper.html"><code>HttpCodeStatusMapper</code></a> settings as the system health.
However, you can also define these on a per-group basis.
You can also override the <code>show-details</code> and <code>roles</code> properties if required:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_20">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_20_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_20_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_20_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_20_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_20_properties" class="tabpanel" id="_tabs_20_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.health.group.custom.show-details</span>=<span class="hljs-string">when-authorized</span>
<span class="hljs-meta">management.endpoint.health.group.custom.roles</span>=<span class="hljs-string">admin</span>
<span class="hljs-meta">management.endpoint.health.group.custom.status.order</span>=<span class="hljs-string">fatal,up</span>
<span class="hljs-meta">management.endpoint.health.group.custom.status.http-mapping.fatal</span>=<span class="hljs-string">500</span>
<span class="hljs-meta">management.endpoint.health.group.custom.status.http-mapping.out-of-service</span>=<span class="hljs-string">500</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_20_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_20_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoint:</span>
    <span class="hljs-attr">health:</span>
      <span class="hljs-attr">group:</span>
        <span class="hljs-attr">custom:</span>
          <span class="hljs-attr">show-details:</span> <span class="hljs-string">"when-authorized"</span>
          <span class="hljs-attr">roles:</span> <span class="hljs-string">"admin"</span>
          <span class="hljs-attr">status:</span>
            <span class="hljs-attr">order:</span> <span class="hljs-string">"fatal,up"</span>
            <span class="hljs-attr">http-mapping:</span>
              <span class="hljs-attr">fatal:</span> <span class="hljs-number">500</span>
              <span class="hljs-attr">out-of-service:</span> <span class="hljs-number">500</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can use <code>@Qualifier("groupname")</code> if you need to register custom <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/StatusAggregator.html"><code>StatusAggregator</code></a> or <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HttpCodeStatusMapper.html"><code>HttpCodeStatusMapper</code></a> beans for use with the group.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>A health group can also include/exclude a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/CompositeHealthContributor.html"><code>CompositeHealthContributor</code></a>.
You can also include/exclude only a certain component of a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/CompositeHealthContributor.html"><code>CompositeHealthContributor</code></a>.
This can be done using the fully qualified name of the component as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.health.group.custom.include</span>=<span class="hljs-string">"test/primary"</span>
<span class="hljs-meta">management.endpoint.health.group.custom.exclude</span>=<span class="hljs-string">"test/primary/b"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>In the example above, the <code>custom</code> group will include the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthContributor.html"><code>HealthContributor</code></a> with the name <code>primary</code> which is a component of the composite <code>test</code>.
Here, <code>primary</code> itself is a composite and the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/health/HealthContributor.html"><code>HealthContributor</code></a> with the name <code>b</code> will be excluded from the <code>custom</code> group.</p>
</div>
<div class="paragraph">
<p>Health groups can be made available at an additional path on either the main or management port.
This is useful in cloud environments such as Kubernetes, where it is quite common to use a separate management port for the actuator endpoints for security purposes.
Having a separate port could lead to unreliable health checks because the main application might not work properly even if the health check is successful.
The health group can be configured with an additional path as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.health.group.live.additional-path</span>=<span class="hljs-string">"server:/healthz"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This would make the <code>live</code> health group available on the main server port at <code>/healthz</code>.
The prefix is mandatory and must be either <code>server:</code> (represents the main server port) or <code>management:</code> (represents the management port, if configured.)
The path must be a single path segment.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.health.datasource"><a class="anchor" href="#actuator.endpoints.health.datasource"></a>DataSource Health</h3>
<div class="paragraph">
<p>The <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> health indicator shows the health of both standard data sources and routing data source beans.
The health of a routing data source includes the health of each of its target data sources.
In the health endpoint’s response, each of a routing data source’s targets is named by using its routing key.
If you prefer not to include routing data sources in the indicator’s output, set <code>management.health.db.ignore-routing-data-sources</code> to <code>true</code>.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.endpoints.kubernetes-probes"><a class="anchor" href="#actuator.endpoints.kubernetes-probes"></a>Kubernetes Probes</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Applications deployed on Kubernetes can provide information about their internal state with <a class="external" href="https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#container-probes" target="_blank">Container Probes</a>.
Depending on <a class="external" href="https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/" target="_blank">your Kubernetes configuration</a>, the kubelet calls those probes and reacts to the result.</p>
</div>
<div class="paragraph">
<p>By default, Spring Boot manages your <a class="xref page" href="../features/spring-application.html#features.spring-application.application-availability">Application Availability</a> state.
If deployed in a Kubernetes environment, actuator gathers the “Liveness” and “Readiness” information from the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/availability/ApplicationAvailability.html"><code>ApplicationAvailability</code></a> interface and uses that information in dedicated <a href="#actuator.endpoints.health.auto-configured-health-indicators">health indicators</a>: <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/availability/LivenessStateHealthIndicator.html"><code>LivenessStateHealthIndicator</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/availability/ReadinessStateHealthIndicator.html"><code>ReadinessStateHealthIndicator</code></a>.
These indicators are shown on the global health endpoint (<code>"/actuator/health"</code>).
They are also exposed as separate HTTP Probes by using <a href="#actuator.endpoints.health.groups">health groups</a>: <code>"/actuator/health/liveness"</code> and <code>"/actuator/health/readiness"</code>.</p>
</div>
<div class="paragraph">
<p>You can then configure your Kubernetes infrastructure with the following endpoint information:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">livenessProbe:</span>
  <span class="hljs-attr">httpGet:</span>
    <span class="hljs-attr">path:</span> <span class="hljs-string">"/actuator/health/liveness"</span>
    <span class="hljs-attr">port:</span> <span class="hljs-string">&lt;actuator-port&gt;</span>
  <span class="hljs-attr">failureThreshold:</span> <span class="hljs-string">...</span>
  <span class="hljs-attr">periodSeconds:</span> <span class="hljs-string">...</span>

<span class="hljs-attr">readinessProbe:</span>
  <span class="hljs-attr">httpGet:</span>
    <span class="hljs-attr">path:</span> <span class="hljs-string">"/actuator/health/readiness"</span>
    <span class="hljs-attr">port:</span> <span class="hljs-string">&lt;actuator-port&gt;</span>
  <span class="hljs-attr">failureThreshold:</span> <span class="hljs-string">...</span>
  <span class="hljs-attr">periodSeconds:</span> <span class="hljs-string">...</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<code>&lt;actuator-port&gt;</code> should be set to the port that the actuator endpoints are available on.
It could be the main web server port or a separate management port if the <code>"management.server.port"</code> property has been set.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>These health groups are automatically enabled only if the application <a class="xref page" href="../../how-to/deployment/cloud.html#howto.deployment.cloud.kubernetes">runs in a Kubernetes environment</a>.
You can enable them in any environment by using the <code>management.endpoint.health.probes.enabled</code> configuration property.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If an application takes longer to start than the configured liveness period, Kubernetes mentions the <code>"startupProbe"</code> as a possible solution.
Generally speaking, the <code>"startupProbe"</code> is not necessarily needed here, as the <code>"readinessProbe"</code> fails until all startup tasks are done.
This means your application will not receive traffic until it is ready.
However, if your application takes a long time to start, consider using a <code>"startupProbe"</code> to make sure that Kubernetes won’t kill your application while it is in the process of starting.
See the section that describes <a href="#actuator.endpoints.kubernetes-probes.lifecycle">how probes behave during the application lifecycle</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If your Actuator endpoints are deployed on a separate management context, the endpoints do not use the same web infrastructure (port, connection pools, framework components) as the main application.
In this case, a probe check could be successful even if the main application does not work properly (for example, it cannot accept new connections).
For this reason, it is a good idea to make the <code>liveness</code> and <code>readiness</code> health groups available on the main server port.
This can be done by setting the following property:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.health.probes.add-additional-paths</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This would make the <code>liveness</code> group available at <code>/livez</code> and the <code>readiness</code> group available at <code>/readyz</code> on the main server port.
Paths can be customized using the <code>additional-path</code> property on each group, see <a href="#actuator.endpoints.health.groups">health groups</a> for details.</p>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.kubernetes-probes.external-state"><a class="anchor" href="#actuator.endpoints.kubernetes-probes.external-state"></a>Checking External State With Kubernetes Probes</h3>
<div class="paragraph">
<p>Actuator configures the “liveness” and “readiness” probes as Health Groups.
This means that all the <a href="#actuator.endpoints.health.groups">health groups features</a> are available for them.
You can, for example, configure additional Health Indicators:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_21">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_21_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_21_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_21_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_21_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_21_properties" class="tabpanel" id="_tabs_21_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.health.group.readiness.include</span>=<span class="hljs-string">readinessState,customCheck</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_21_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_21_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoint:</span>
    <span class="hljs-attr">health:</span>
      <span class="hljs-attr">group:</span>
        <span class="hljs-attr">readiness:</span>
          <span class="hljs-attr">include:</span> <span class="hljs-string">"readinessState,customCheck"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>By default, Spring Boot does not add other health indicators to these groups.</p>
</div>
<div class="paragraph">
<p>The “liveness” probe should not depend on health checks for external systems.
If the <a class="xref page" href="../features/spring-application.html#features.spring-application.application-availability.liveness">liveness state of an application</a> is broken, Kubernetes tries to solve that problem by restarting the application instance.
This means that if an external system (such as a database, a Web API, or an external cache) fails, Kubernetes might restart all application instances and create cascading failures.</p>
</div>
<div class="paragraph">
<p>As for the “readiness” probe, the choice of checking external systems must be made carefully by the application developers.
For this reason, Spring Boot does not include any additional health checks in the readiness probe.
If the <a class="xref page" href="../features/spring-application.html#features.spring-application.application-availability.readiness">readiness state of an application instance</a> is unready, Kubernetes does not route traffic to that instance.
Some external systems might not be shared by application instances, in which case they could be included in a readiness probe.
Other external systems might not be essential to the application (the application could have circuit breakers and fallbacks), in which case they definitely should not be included.
Unfortunately, an external system that is shared by all application instances is common, and you have to make a judgement call: Include it in the readiness probe and expect that the application is taken out of service when the external service is down or leave it out and deal with failures higher up the stack, perhaps by using a circuit breaker in the caller.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If all instances of an application are unready, a Kubernetes Service with <code>type=ClusterIP</code> or <code>NodePort</code> does not accept any incoming connections.
There is no HTTP error response (503 and so on), since there is no connection.
A service with <code>type=LoadBalancer</code> might or might not accept connections, depending on the provider.
A service that has an explicit <a class="external" href="https://kubernetes.io/docs/concepts/services-networking/ingress/" target="_blank">ingress</a> also responds in a way that depends on the implementation — the ingress service itself has to decide how to handle the “connection refused” from downstream.
HTTP 503 is quite likely in the case of both load balancer and ingress.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Also, if an application uses Kubernetes <a class="external" href="https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/" target="_blank">autoscaling</a>, it may react differently to applications being taken out of the load-balancer, depending on its autoscaler configuration.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.kubernetes-probes.lifecycle"><a class="anchor" href="#actuator.endpoints.kubernetes-probes.lifecycle"></a>Application Lifecycle and Probe States</h3>
<div class="paragraph">
<p>An important aspect of the Kubernetes Probes support is its consistency with the application lifecycle.
There is a significant difference between the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/availability/AvailabilityState.html"><code>AvailabilityState</code></a> (which is the in-memory, internal state of the application)
and the actual probe (which exposes that state).
Depending on the phase of application lifecycle, the probe might not be available.</p>
</div>
<div class="paragraph">
<p>Spring Boot publishes <a class="xref page" href="../features/spring-application.html#features.spring-application.application-events-and-listeners">application events during startup and shutdown</a>,
and probes can listen to such events and expose the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/availability/AvailabilityState.html"><code>AvailabilityState</code></a> information.</p>
</div>
<div class="paragraph">
<p>The following tables show the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/availability/AvailabilityState.html"><code>AvailabilityState</code></a> and the state of HTTP connectors at different stages.</p>
</div>
<div class="paragraph">
<p>When a Spring Boot application starts:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 14.2857%;"/>
<col style="width: 14.2857%;"/>
<col style="width: 14.2857%;"/>
<col style="width: 21.4285%;"/>
<col style="width: 35.7144%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Startup phase</th>
<th class="tableblock halign-left valign-top">LivenessState</th>
<th class="tableblock halign-left valign-top">ReadinessState</th>
<th class="tableblock halign-left valign-top">HTTP server</th>
<th class="tableblock halign-left valign-top">Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Starting</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>BROKEN</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>REFUSING_TRAFFIC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Not started</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Kubernetes checks the "liveness" Probe and restarts the application if it takes too long.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Started</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>CORRECT</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>REFUSING_TRAFFIC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Refuses requests</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The application context is refreshed. The application performs startup tasks and does not receive traffic yet.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Ready</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>CORRECT</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>ACCEPTING_TRAFFIC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Accepts requests</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Startup tasks are finished. The application is receiving traffic.</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>When a Spring Boot application shuts down:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 14.2857%;"/>
<col style="width: 14.2857%;"/>
<col style="width: 14.2857%;"/>
<col style="width: 21.4285%;"/>
<col style="width: 35.7144%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Shutdown phase</th>
<th class="tableblock halign-left valign-top">Liveness State</th>
<th class="tableblock halign-left valign-top">Readiness State</th>
<th class="tableblock halign-left valign-top">HTTP server</th>
<th class="tableblock halign-left valign-top">Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Running</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>CORRECT</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>ACCEPTING_TRAFFIC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Accepts requests</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Shutdown has been requested.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Graceful shutdown</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>CORRECT</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>REFUSING_TRAFFIC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">New requests are rejected</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">If enabled, <a class="xref page" href="../web/graceful-shutdown.html">graceful shutdown processes in-flight requests</a>.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Shutdown complete</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">N/A</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">N/A</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Server is shut down</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The application context is closed and the application is shut down.</p></td>
</tr>
</tbody>
</table>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
See <a class="xref page" href="../../how-to/deployment/cloud.html#howto.deployment.cloud.kubernetes.container-lifecycle">Kubernetes Container Lifecycle</a> for more information about Kubernetes deployment.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.endpoints.info"><a class="anchor" href="#actuator.endpoints.info"></a>Application Information</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Application information exposes various information collected from all <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/info/InfoContributor.html"><code>InfoContributor</code></a> beans defined in your <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a>.
Spring Boot includes a number of auto-configured <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/info/InfoContributor.html"><code>InfoContributor</code></a> beans, and you can write your own.</p>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.info.auto-configured-info-contributors"><a class="anchor" href="#actuator.endpoints.info.auto-configured-info-contributors"></a>Auto-configured InfoContributors</h3>
<div class="paragraph">
<p>When appropriate, Spring auto-configures the following <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/info/InfoContributor.html"><code>InfoContributor</code></a> beans:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 5.8823%;"/>
<col style="width: 23.5294%;"/>
<col style="width: 47.0588%;"/>
<col style="width: 23.5295%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">ID</th>
<th class="tableblock halign-left valign-top">Name</th>
<th class="tableblock halign-left valign-top">Description</th>
<th class="tableblock halign-left valign-top">Prerequisites</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>build</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/info/BuildInfoContributor.html"><code>BuildInfoContributor</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes build information.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">A <code>META-INF/build-info.properties</code> resource.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>env</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/info/EnvironmentInfoContributor.html"><code>EnvironmentInfoContributor</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes any property from the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> whose name starts with <code>info.</code>.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">None.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>git</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/info/GitInfoContributor.html"><code>GitInfoContributor</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes git information.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">A <code>git.properties</code> resource.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/info/JavaInfoContributor.html"><code>JavaInfoContributor</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes Java runtime information.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">None.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>os</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/info/OsInfoContributor.html"><code>OsInfoContributor</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes Operating System information.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">None.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>process</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/info/ProcessInfoContributor.html"><code>ProcessInfoContributor</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes process information.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">None.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>ssl</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/info/SslInfoContributor.html"><code>SslInfoContributor</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Exposes SSL certificate information.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">An <a class="xref page" href="../features/ssl.html#features.ssl.bundles">SSL Bundle</a> configured.</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>Whether an individual contributor is enabled is controlled by its <code>management.info.&lt;id&gt;.enabled</code> property.
Different contributors have different defaults for this property, depending on their prerequisites and the nature of the information that they expose.</p>
</div>
<div class="paragraph">
<p>With no prerequisites to indicate that they should be enabled, the <code>env</code>, <code>java</code>, <code>os</code>, and <code>process</code> contributors are disabled by default. The <code>ssl</code> contributor has a prerequisite of having an <a class="xref page" href="../features/ssl.html#features.ssl.bundles">SSL Bundle</a> configured but it is disabled by default.
Each can be enabled by setting its <code>management.info.&lt;id&gt;.enabled</code> property to <code>true</code>.</p>
</div>
<div class="paragraph">
<p>The <code>build</code> and <code>git</code> info contributors are enabled by default.
Each can be disabled by setting its <code>management.info.&lt;id&gt;.enabled</code> property to <code>false</code>.
Alternatively, to disable every contributor that is usually enabled by default, set the <code>management.info.defaults.enabled</code> property to <code>false</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.info.custom-application-information"><a class="anchor" href="#actuator.endpoints.info.custom-application-information"></a>Custom Application Information</h3>
<div class="paragraph">
<p>When the <code>env</code> contributor is enabled, you can customize the data exposed by the <code>info</code> endpoint by setting <code>info.*</code> Spring properties.
All <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> properties under the <code>info</code> key are automatically exposed.
For example, you could add the following settings to your <code>application.properties</code> file:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_22">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_22_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_22_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_22_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_22_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_22_properties" class="tabpanel" id="_tabs_22_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">info.app.encoding</span>=<span class="hljs-string">UTF-8</span>
<span class="hljs-meta">info.app.java.source</span>=<span class="hljs-string">17</span>
<span class="hljs-meta">info.app.java.target</span>=<span class="hljs-string">17</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_22_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_22_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">info:</span>
  <span class="hljs-attr">app:</span>
    <span class="hljs-attr">encoding:</span> <span class="hljs-string">"UTF-8"</span>
    <span class="hljs-attr">java:</span>
      <span class="hljs-attr">source:</span> <span class="hljs-string">"17"</span>
      <span class="hljs-attr">target:</span> <span class="hljs-string">"17"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<div class="paragraph">
<p>Rather than hardcoding those values, you could also <a class="xref page" href="../../how-to/properties-and-configuration.html#howto.properties-and-configuration.expand-properties">expand info properties at build time</a>.</p>
</div>
<div class="paragraph">
<p>Assuming you use Maven, you could rewrite the preceding example as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_23">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_23_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_23_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_23_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_23_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_23_properties" class="tabpanel" id="_tabs_23_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">info.app.encoding</span>=<span class="hljs-string">@project.build.sourceEncoding@</span>
<span class="hljs-meta">info.app.java.source</span>=<span class="hljs-string">@java.version@</span>
<span class="hljs-meta">info.app.java.target</span>=<span class="hljs-string">@java.version@</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_23_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_23_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">info:</span>
  <span class="hljs-attr">app:</span>
    <span class="hljs-attr">encoding:</span> <span class="hljs-string">"@project.build.sourceEncoding@"</span>
    <span class="hljs-attr">java:</span>
      <span class="hljs-attr">source:</span> <span class="hljs-string">"@java.version@"</span>
      <span class="hljs-attr">target:</span> <span class="hljs-string">"@java.version@"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.info.git-commit-information"><a class="anchor" href="#actuator.endpoints.info.git-commit-information"></a>Git Commit Information</h3>
<div class="paragraph">
<p>Another useful feature of the <code>info</code> endpoint is its ability to publish information about the state of your <code>git</code> source code repository when the project was built.
If a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/info/GitProperties.html"><code>GitProperties</code></a> bean is available, you can use the <code>info</code> endpoint to expose these properties.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
A <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/info/GitProperties.html"><code>GitProperties</code></a> bean is auto-configured if a <code>git.properties</code> file is available at the root of the classpath.
See <a class="xref page" href="../../how-to/build.html#howto.build.generate-git-info">Generate Git Information</a> for more detail.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>By default, the endpoint exposes <code>git.branch</code>, <code>git.commit.id</code>, and <code>git.commit.time</code> properties, if present.
If you do not want any of these properties in the endpoint response, they need to be excluded from the <code>git.properties</code> file.
If you want to display the full git information (that is, the full content of <code>git.properties</code>), use the <code>management.info.git.mode</code> property, as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_24">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_24_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_24_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_24_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_24_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_24_properties" class="tabpanel" id="_tabs_24_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.info.git.mode</span>=<span class="hljs-string">full</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_24_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_24_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">info:</span>
    <span class="hljs-attr">git:</span>
      <span class="hljs-attr">mode:</span> <span class="hljs-string">"full"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>To disable the git commit information from the <code>info</code> endpoint completely, set the <code>management.info.git.enabled</code> property to <code>false</code>, as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_25">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_25_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_25_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_25_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_25_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_25_properties" class="tabpanel" id="_tabs_25_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.info.git.enabled</span>=<span class="hljs-string">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_25_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_25_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">info:</span>
    <span class="hljs-attr">git:</span>
      <span class="hljs-attr">enabled:</span> <span class="hljs-literal">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.info.build-information"><a class="anchor" href="#actuator.endpoints.info.build-information"></a>Build Information</h3>
<div class="paragraph">
<p>If a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/info/BuildProperties.html"><code>BuildProperties</code></a> bean is available, the <code>info</code> endpoint can also publish information about your build.
This happens if a <code>META-INF/build-info.properties</code> file is available in the classpath.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The Maven and Gradle plugins can both generate that file.
See <a class="xref page" href="../../how-to/build.html#howto.build.generate-info">Generate Build Information</a> for more details.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.info.java-information"><a class="anchor" href="#actuator.endpoints.info.java-information"></a>Java Information</h3>
<div class="paragraph">
<p>The <code>info</code> endpoint publishes information about your Java runtime environment, see <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/info/JavaInfo.html"><code>JavaInfo</code></a> for more details.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.info.os-information"><a class="anchor" href="#actuator.endpoints.info.os-information"></a>OS Information</h3>
<div class="paragraph">
<p>The <code>info</code> endpoint publishes information about your Operating System, see <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/info/OsInfo.html"><code>OsInfo</code></a> for more details.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.info.process-information"><a class="anchor" href="#actuator.endpoints.info.process-information"></a>Process Information</h3>
<div class="paragraph">
<p>The <code>info</code> endpoint publishes information about your process, see <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/info/ProcessInfo.html"><code>ProcessInfo</code></a> for more details.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.info.ssl-information"><a class="anchor" href="#actuator.endpoints.info.ssl-information"></a>SSL Information</h3>
<div class="paragraph">
<p>The <code>info</code> endpoint publishes information about your SSL certificates (that are configured through <a class="xref page" href="../features/ssl.html#features.ssl.bundles">SSL Bundles</a>), see <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/info/SslInfo.html"><code>SslInfo</code></a> for more details. This endpoint reuses the "warning threshold" property of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/ssl/SslHealthIndicator.html"><code>SslHealthIndicator</code></a>: if an SSL certificate will be invalid within the time span defined by this threshold, it will trigger a warning. See the <code>management.health.ssl.certificate-validity-warning-threshold</code> property.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.info.writing-custom-info-contributors"><a class="anchor" href="#actuator.endpoints.info.writing-custom-info-contributors"></a>Writing Custom InfoContributors</h3>
<div class="paragraph">
<p>To provide custom application information, you can register Spring beans that implement the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/info/InfoContributor.html"><code>InfoContributor</code></a> interface.</p>
</div>
<div class="paragraph">
<p>The following example contributes an <code>example</code> entry with a single value:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_26">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_26_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_26_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_26_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_26_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_26_java" class="tabpanel" id="_tabs_26_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.Collections;

<span class="hljs-keyword">import</span> org.springframework.boot.actuate.info.Info;
<span class="hljs-keyword">import</span> org.springframework.boot.actuate.info.InfoContributor;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyInfoContributor</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">InfoContributor</span> </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">contribute</span><span class="hljs-params">(Info.Builder builder)</span> </span>{
		builder.withDetail(<span class="hljs-string">"example"</span>, Collections.singletonMap(<span class="hljs-string">"key"</span>, <span class="hljs-string">"value"</span>));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_26_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_26_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.actuate.info.Info
<span class="hljs-keyword">import</span> org.springframework.boot.actuate.info.InfoContributor
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component
<span class="hljs-keyword">import</span> java.util.Collections

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyInfoContributor</span> : <span class="hljs-type">InfoContributor {</span></span>

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">contribute</span><span class="hljs-params">(builder: <span class="hljs-type">Info</span>.<span class="hljs-type">Builder</span>)</span></span> {
		builder.withDetail(<span class="hljs-string">"example"</span>, Collections.singletonMap(<span class="hljs-string">"key"</span>, <span class="hljs-string">"value"</span>))
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you reach the <code>info</code> endpoint, you should see a response that contains the following additional entry:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-json hljs" data-lang="json">{
	<span class="hljs-attr">"example"</span>: {
		<span class="hljs-attr">"key"</span> : <span class="hljs-string">"value"</span>
	}
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.endpoints.sbom"><a class="anchor" href="#actuator.endpoints.sbom"></a>Software Bill of Materials (SBOM)</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The <code>sbom</code> endpoint exposes the <a class="external" href="https://en.wikipedia.org/wiki/Software_supply_chain" target="_blank">Software Bill of Materials</a>.
CycloneDX SBOMs can be auto-detected, but other formats can be manually configured, too.</p>
</div>
<div class="paragraph">
<p>The <code>sbom</code> actuator endpoint will then expose an SBOM called "application", which describes the contents of your application.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
To automatically generate a CycloneDX SBOM at project build time, please see the <a class="xref page" href="../../how-to/build.html#howto.build.generate-cyclonedx-sbom">Generate a CycloneDX SBOM</a> section.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.sbom.other-formats"><a class="anchor" href="#actuator.endpoints.sbom.other-formats"></a>Other SBOM formats</h3>
<div class="paragraph">
<p>If you want to publish an SBOM in a different format, there are some configuration properties which you can use.</p>
</div>
<div class="paragraph">
<p>The configuration property <code>management.endpoint.sbom.application.location</code> sets the location for the application SBOM.
For example, setting this to <code>classpath:sbom.json</code> will use the contents of the <code>/sbom.json</code> resource on the classpath.</p>
</div>
<div class="paragraph">
<p>The media type for SBOMs in CycloneDX, SPDX and Syft format is detected automatically.
To override the auto-detected media type, use the configuration property <code>management.endpoint.sbom.application.media-type</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.endpoints.sbom.additional"><a class="anchor" href="#actuator.endpoints.sbom.additional"></a>Additional SBOMs</h3>
<div class="paragraph">
<p>The actuator endpoint can handle multiple SBOMs.
To add SBOMs, use the configuration property <code>management.endpoint.sbom.additional</code>, as shown in this example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_27">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_27_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_27_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_27_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_27_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_27_properties" class="tabpanel" id="_tabs_27_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.endpoint.sbom.additional.system.location</span>=<span class="hljs-string">optional:file:/system.spdx.json</span>
<span class="hljs-meta">management.endpoint.sbom.additional.system.media-type</span>=<span class="hljs-string">application/spdx+json</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_27_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_27_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">endpoint:</span>
    <span class="hljs-attr">sbom:</span>
      <span class="hljs-attr">additional:</span>
        <span class="hljs-attr">system:</span>
          <span class="hljs-attr">location:</span> <span class="hljs-string">"optional:file:/system.spdx.json"</span>
          <span class="hljs-attr">media-type:</span> <span class="hljs-string">"application/spdx+json"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>This will add an SBOM called "system", which is stored in <code>/system.spdx.json</code>.
The <code>optional:</code> prefix can be used to prevent a startup failure if the file doesn’t exist.</p>
</div>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="enabling.html">Enabling Production-ready Features</a></span>
<span class="next"><a href="monitoring.html">Monitoring and Management Over HTTP</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../reference/actuator/endpoints.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="endpoints.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../3.3/reference/actuator/endpoints.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../4.0-SNAPSHOT/reference/actuator/endpoints.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.5-SNAPSHOT/reference/actuator/endpoints.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.4-SNAPSHOT/reference/actuator/endpoints.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.3-SNAPSHOT/reference/actuator/endpoints.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>