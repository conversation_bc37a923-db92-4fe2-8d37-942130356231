<!DOCTYPE html>
<html><head><title>Externalized Configuration :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/reference/features/external-config.html"/><meta content="2025-06-04T16:02:16.043583" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-application.html">SpringApplication</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Externalized Configuration">
<div class="toc-menu"><h3>Externalized Configuration</h3><ul><li data-level="1"><a href="#features.external-config.command-line-args">Accessing Command Line Properties</a></li><li data-level="1"><a href="#features.external-config.application-json">JSON Application Properties</a></li><li data-level="1"><a href="#features.external-config.files">External Application Properties</a></li><li data-level="2"><a href="#features.external-config.files.optional-prefix">Optional Locations</a></li><li data-level="2"><a href="#features.external-config.files.wildcard-locations">Wildcard Locations</a></li><li data-level="2"><a href="#features.external-config.files.profile-specific">Profile Specific Files</a></li><li data-level="2"><a href="#features.external-config.files.importing">Importing Additional Data</a></li><li data-level="2"><a href="#features.external-config.files.importing-extensionless">Importing Extensionless Files</a></li><li data-level="2"><a href="#features.external-config.files.configtree">Using Configuration Trees</a></li><li data-level="2"><a href="#features.external-config.files.property-placeholders">Property Placeholders</a></li><li data-level="2"><a href="#features.external-config.files.multi-document">Working With Multi-Document Files</a></li><li data-level="2"><a href="#features.external-config.files.activation-properties">Activation Properties</a></li><li data-level="1"><a href="#features.external-config.encrypting">Encrypting Properties</a></li><li data-level="1"><a href="#features.external-config.yaml">Working With YAML</a></li><li data-level="2"><a href="#features.external-config.yaml.mapping-to-properties">Mapping YAML to Properties</a></li><li data-level="2"><a href="#features.external-config.yaml.directly-loading">Directly Loading YAML</a></li><li data-level="1"><a href="#features.external-config.random-values">Configuring Random Values</a></li><li data-level="1"><a href="#features.external-config.system-environment">Configuring System Environment Properties</a></li><li data-level="1"><a href="#features.external-config.typesafe-configuration-properties">Type-safe Configuration Properties</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.java-bean-binding">JavaBean Properties Binding</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.constructor-binding">Constructor Binding</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.enabling-annotated-types">Enabling @ConfigurationProperties-annotated Types</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.using-annotated-types">Using @ConfigurationProperties-annotated Types</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.third-party-configuration">Third-party Configuration</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.relaxed-binding">Relaxed Binding</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.merging-complex-types">Merging Complex Types</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.conversion">Properties Conversion</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.validation">@ConfigurationProperties Validation</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.vs-value-annotation">@ConfigurationProperties vs. @Value</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/features/external-config.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../index.html">Spring Boot</a></li>
<li><a href="../index.html">Reference</a></li>
<li><a href="index.html">Core Features</a></li>
<li><a href="external-config.html">Externalized Configuration</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../reference/features/external-config.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Externalized Configuration</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Externalized Configuration</h3><ul><li data-level="1"><a href="#features.external-config.command-line-args">Accessing Command Line Properties</a></li><li data-level="1"><a href="#features.external-config.application-json">JSON Application Properties</a></li><li data-level="1"><a href="#features.external-config.files">External Application Properties</a></li><li data-level="2"><a href="#features.external-config.files.optional-prefix">Optional Locations</a></li><li data-level="2"><a href="#features.external-config.files.wildcard-locations">Wildcard Locations</a></li><li data-level="2"><a href="#features.external-config.files.profile-specific">Profile Specific Files</a></li><li data-level="2"><a href="#features.external-config.files.importing">Importing Additional Data</a></li><li data-level="2"><a href="#features.external-config.files.importing-extensionless">Importing Extensionless Files</a></li><li data-level="2"><a href="#features.external-config.files.configtree">Using Configuration Trees</a></li><li data-level="2"><a href="#features.external-config.files.property-placeholders">Property Placeholders</a></li><li data-level="2"><a href="#features.external-config.files.multi-document">Working With Multi-Document Files</a></li><li data-level="2"><a href="#features.external-config.files.activation-properties">Activation Properties</a></li><li data-level="1"><a href="#features.external-config.encrypting">Encrypting Properties</a></li><li data-level="1"><a href="#features.external-config.yaml">Working With YAML</a></li><li data-level="2"><a href="#features.external-config.yaml.mapping-to-properties">Mapping YAML to Properties</a></li><li data-level="2"><a href="#features.external-config.yaml.directly-loading">Directly Loading YAML</a></li><li data-level="1"><a href="#features.external-config.random-values">Configuring Random Values</a></li><li data-level="1"><a href="#features.external-config.system-environment">Configuring System Environment Properties</a></li><li data-level="1"><a href="#features.external-config.typesafe-configuration-properties">Type-safe Configuration Properties</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.java-bean-binding">JavaBean Properties Binding</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.constructor-binding">Constructor Binding</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.enabling-annotated-types">Enabling @ConfigurationProperties-annotated Types</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.using-annotated-types">Using @ConfigurationProperties-annotated Types</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.third-party-configuration">Third-party Configuration</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.relaxed-binding">Relaxed Binding</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.merging-complex-types">Merging Complex Types</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.conversion">Properties Conversion</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.validation">@ConfigurationProperties Validation</a></li><li data-level="2"><a href="#features.external-config.typesafe-configuration-properties.vs-value-annotation">@ConfigurationProperties vs. @Value</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot lets you externalize your configuration so that you can work with the same application code in different environments.
You can use a variety of external configuration sources including Java properties files, YAML files, environment variables, and command-line arguments.</p>
</div>
<div class="paragraph">
<p>Property values can be injected directly into your beans by using the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Value.html"><code>@Value</code></a> annotation, accessed through Spring’s <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> abstraction, or be <a href="#features.external-config.typesafe-configuration-properties">bound to structured objects</a> through <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a>.</p>
</div>
<div class="paragraph">
<p>Spring Boot uses a very particular <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/PropertySource.html"><code>PropertySource</code></a> order that is designed to allow sensible overriding of values.
Later property sources can override the values defined in earlier ones.</p>
</div>
<div class="paragraph" id="features.external-config.order">
<p>Sources are considered in the following order:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Default properties (specified by setting <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringApplication.html#setDefaultProperties(java.util.Map)"><code>SpringApplication.setDefaultProperties(Map)</code></a>).</p>
</li>
<li>
<p><a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/PropertySource.html"><code>@PropertySource</code></a> annotations on your <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> classes.
Please note that such property sources are not added to the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> until the application context is being refreshed.
This is too late to configure certain properties such as <code>logging.*</code> and <code>spring.main.*</code> which are read before refresh begins.</p>
</li>
<li>
<p>Config data (such as <code>application.properties</code> files).</p>
</li>
<li>
<p>A <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/env/RandomValuePropertySource.html"><code>RandomValuePropertySource</code></a> that has properties only in <code>random.*</code>.</p>
</li>
<li>
<p>OS environment variables.</p>
</li>
<li>
<p>Java System properties (<code>System.getProperties()</code>).</p>
</li>
<li>
<p>JNDI attributes from <code>java:comp/env</code>.</p>
</li>
<li>
<p><a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/ServletContext.html" target="_blank"><code>ServletContext</code></a> init parameters.</p>
</li>
<li>
<p><a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/ServletConfig.html" target="_blank"><code>ServletConfig</code></a> init parameters.</p>
</li>
<li>
<p>Properties from <code>SPRING_APPLICATION_JSON</code> (inline JSON embedded in an environment variable or system property).</p>
</li>
<li>
<p>Command line arguments.</p>
</li>
<li>
<p><code>properties</code> attribute on your tests.
Available on <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/context/SpringBootTest.html"><code>@SpringBootTest</code></a> and the <a class="xref page" href="../testing/spring-boot-applications.html#testing.spring-boot-applications.autoconfigured-tests">test annotations for testing a particular slice of your application</a>.</p>
</li>
<li>
<p><a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/context/DynamicPropertySource.html"><code>@DynamicPropertySource</code></a> annotations in your tests.</p>
</li>
<li>
<p><a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/context/TestPropertySource.html"><code>@TestPropertySource</code></a> annotations on your tests.</p>
</li>
<li>
<p><a class="xref page" href="../using/devtools.html#using.devtools.globalsettings">Devtools global settings properties</a> in the <code>$HOME/.config/spring-boot</code> directory when devtools is active.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>Config data files are considered in the following order:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p><a href="#features.external-config.files">Application properties</a> packaged inside your jar (<code>application.properties</code> and YAML variants).</p>
</li>
<li>
<p><a href="#features.external-config.files.profile-specific">Profile-specific application properties</a> packaged inside your jar (<code>application-{profile}.properties</code> and YAML variants).</p>
</li>
<li>
<p><a href="#features.external-config.files">Application properties</a> outside of your packaged jar (<code>application.properties</code> and YAML variants).</p>
</li>
<li>
<p><a href="#features.external-config.files.profile-specific">Profile-specific application properties</a> outside of your packaged jar (<code>application-{profile}.properties</code> and YAML variants).</p>
</li>
</ol>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
It is recommended to stick with one format for your entire application.
If you have configuration files with both <code>.properties</code> and YAML format in the same location, <code>.properties</code> takes precedence.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If you use environment variables rather than system properties, most operating systems disallow period-separated key names, but you can use underscores instead (for example, <code>SPRING_CONFIG_NAME</code> instead of <code>spring.config.name</code>).
See <a href="#features.external-config.typesafe-configuration-properties.relaxed-binding.environment-variables">Binding From Environment Variables</a> for details.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If your application runs in a servlet container or application server, then JNDI properties (in <code>java:comp/env</code>) or servlet context initialization parameters can be used instead of, or as well as, environment variables or system properties.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>To provide a concrete example, suppose you develop a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> that uses a <code>name</code> property, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_1_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_1_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_1_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_java" class="tabpanel" id="_tabs_1_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Value;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-meta">@Value</span>(<span class="hljs-string">"${name}"</span>)
	<span class="hljs-keyword">private</span> String name;

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_1_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Value
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-meta">@Value(<span class="hljs-meta-string">"\${name}"</span>)</span>
	<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> name: String? = <span class="hljs-literal">null</span>

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>On your application classpath (for example, inside your jar) you can have an <code>application.properties</code> file that provides a sensible default property value for <code>name</code>.
When running in a new environment, an <code>application.properties</code> file can be provided outside of your jar that overrides the <code>name</code>.
For one-off testing, you can launch with a specific command line switch (for example, <code>java -jar app.jar --name="Spring"</code>).</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The <code>env</code> and <code>configprops</code> endpoints can be useful in determining why a property has a particular value.
You can use these two endpoints to diagnose unexpected property values.
See the <a class="xref page" href="../actuator/endpoints.html">Production ready features</a> section for details.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="features.external-config.command-line-args"><a class="anchor" href="#features.external-config.command-line-args"></a>Accessing Command Line Properties</h2>
<div class="sectionbody">
<div class="paragraph">
<p>By default, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringApplication.html"><code>SpringApplication</code></a> converts any command line option arguments (that is, arguments starting with <code>--</code>, such as <code>--server.port=9000</code>) to a <code>property</code> and adds them to the Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a>.
As mentioned previously, command line properties always take precedence over file-based property sources.</p>
</div>
<div class="paragraph">
<p>If you do not want command line properties to be added to the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a>, you can disable them by using <code>SpringApplication.setAddCommandLineProperties(false)</code>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="features.external-config.application-json"><a class="anchor" href="#features.external-config.application-json"></a>JSON Application Properties</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Environment variables and system properties often have restrictions that mean some property names cannot be used.
To help with this, Spring Boot allows you to encode a block of properties into a single JSON structure.</p>
</div>
<div class="paragraph">
<p>When your application starts, any <code>spring.application.json</code> or <code>SPRING_APPLICATION_JSON</code> properties will be parsed and added to the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a>.</p>
</div>
<div class="paragraph">
<p>For example, the <code>SPRING_APPLICATION_JSON</code> property can be supplied on the command line in a UN*X shell as an environment variable:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-shell hljs" data-lang="shell"><span class="hljs-meta">$</span><span class="bash"> SPRING_APPLICATION_JSON=<span class="hljs-string">'{"my":{"name":"test"}}'</span> java -jar myapp.jar</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>In the preceding example, you end up with <code>my.name=test</code> in the Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a>.</p>
</div>
<div class="paragraph">
<p>The same JSON can also be provided as a system property:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-shell hljs" data-lang="shell"><span class="hljs-meta">$</span><span class="bash"> java -Dspring.application.json=<span class="hljs-string">'{"my":{"name":"test"}}'</span> -jar myapp.jar</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Or you could supply the JSON by using a command line argument:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-shell hljs" data-lang="shell"><span class="hljs-meta">$</span><span class="bash"> java -jar myapp.jar --spring.application.json=<span class="hljs-string">'{"my":{"name":"test"}}'</span></span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>If you are deploying to a classic Application Server, you could also use a JNDI variable named <code>java:comp/env/spring.application.json</code>.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Although <code>null</code> values from the JSON will be added to the resulting property source, the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/PropertySourcesPropertyResolver.html"><code>PropertySourcesPropertyResolver</code></a> treats <code>null</code> properties as missing values.
This means that the JSON cannot override properties from lower order property sources with a <code>null</code> value.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="features.external-config.files"><a class="anchor" href="#features.external-config.files"></a>External Application Properties</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot will automatically find and load <code>application.properties</code> and <code>application.yaml</code> files from the following locations when your application starts:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>From the classpath</p>
<div class="olist loweralpha">
<ol class="loweralpha" type="a">
<li>
<p>The classpath root</p>
</li>
<li>
<p>The classpath <code>/config</code> package</p>
</li>
</ol>
</div>
</li>
<li>
<p>From the current directory</p>
<div class="olist loweralpha">
<ol class="loweralpha" type="a">
<li>
<p>The current directory</p>
</li>
<li>
<p>The <code>config/</code> subdirectory in the current directory</p>
</li>
<li>
<p>Immediate child directories of the <code>config/</code> subdirectory</p>
</li>
</ol>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>The list is ordered by precedence (with values from lower items overriding earlier ones).
Documents from the loaded files are added as <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/PropertySource.html"><code>PropertySource</code></a> instances to the Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a>.</p>
</div>
<div class="paragraph">
<p>If you do not like <code>application</code> as the configuration file name, you can switch to another file name by specifying a <code>spring.config.name</code> environment property.
For example, to look for <code>myproject.properties</code> and <code>myproject.yaml</code> files you can run your application as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-shell hljs" data-lang="shell"><span class="hljs-meta">$</span><span class="bash"> java -jar myproject.jar --spring.config.name=myproject</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>You can also refer to an explicit location by using the <code>spring.config.location</code> environment property.
This property accepts a comma-separated list of one or more locations to check.</p>
</div>
<div class="paragraph">
<p>The following example shows how to specify two distinct files:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-shell hljs" data-lang="shell"><span class="hljs-meta">$</span><span class="bash"> java -jar myproject.jar --spring.config.location=\</span>
	optional:classpath:/default.properties,\
	optional:classpath:/override.properties</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Use the prefix <code>optional:</code> if the <a href="#features.external-config.files.optional-prefix">locations are optional</a> and you do not mind if they do not exist.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock warning">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-warning" title="Warning"></i>
</td>
<td class="content">
<code>spring.config.name</code>, <code>spring.config.location</code>, and <code>spring.config.additional-location</code> are used very early to determine which files have to be loaded.
They must be defined as an environment property (typically an OS environment variable, a system property, or a command-line argument).
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If <code>spring.config.location</code> contains directories (as opposed to files), they should end in <code>/</code>.
At runtime they will be appended with the names generated from <code>spring.config.name</code> before being loaded.
Files specified in <code>spring.config.location</code> are imported directly.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Both directory and file location values are also expanded to check for <a href="#features.external-config.files.profile-specific">profile-specific files</a>.
For example, if you have a <code>spring.config.location</code> of <code>classpath:myconfig.properties</code>, you will also find appropriate <code>classpath:myconfig-&lt;profile&gt;.properties</code> files are loaded.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>In most situations, each <code>spring.config.location</code> item you add will reference a single file or directory.
Locations are processed in the order that they are defined and later ones can override the values of earlier ones.</p>
</div>
<div class="paragraph" id="features.external-config.files.location-groups">
<p>If you have a complex location setup, and you use profile-specific configuration files, you may need to provide further hints so that Spring Boot knows how they should be grouped.
A location group is a collection of locations that are all considered at the same level.
For example, you might want to group all classpath locations, then all external locations.
Items within a location group should be separated with <code>;</code>.
See the example in the <a href="#features.external-config.files.profile-specific">Profile Specific Files</a> section for more details.</p>
</div>
<div class="paragraph">
<p>Locations configured by using <code>spring.config.location</code> replace the default locations.
For example, if <code>spring.config.location</code> is configured with the value <code>optional:classpath:/custom-config/,optional:file:./custom-config/</code>, the complete set of locations considered is:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p><code>optional:classpath:custom-config/</code></p>
</li>
<li>
<p><code>optional:file:./custom-config/</code></p>
</li>
</ol>
</div>
<div class="paragraph">
<p>If you prefer to add additional locations, rather than replacing them, you can use <code>spring.config.additional-location</code>.
Properties loaded from additional locations can override those in the default locations.
For example, if <code>spring.config.additional-location</code> is configured with the value <code>optional:classpath:/custom-config/,optional:file:./custom-config/</code>, the complete set of locations considered is:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p><code>optional:classpath:/;optional:classpath:/config/</code></p>
</li>
<li>
<p><code>optional:file:./;optional:file:./config/;optional:file:./config/*/</code></p>
</li>
<li>
<p><code>optional:classpath:custom-config/</code></p>
</li>
<li>
<p><code>optional:file:./custom-config/</code></p>
</li>
</ol>
</div>
<div class="paragraph">
<p>This search ordering lets you specify default values in one configuration file and then selectively override those values in another.
You can provide default values for your application in <code>application.properties</code> (or whatever other basename you choose with <code>spring.config.name</code>) in one of the default locations.
These default values can then be overridden at runtime with a different file located in one of the custom locations.</p>
</div>
<div class="sect2">
<h3 id="features.external-config.files.optional-prefix"><a class="anchor" href="#features.external-config.files.optional-prefix"></a>Optional Locations</h3>
<div class="paragraph">
<p>By default, when a specified config data location does not exist, Spring Boot will throw a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/config/ConfigDataLocationNotFoundException.html"><code>ConfigDataLocationNotFoundException</code></a> and your application will not start.</p>
</div>
<div class="paragraph">
<p>If you want to specify a location, but you do not mind if it does not always exist, you can use the <code>optional:</code> prefix.
You can use this prefix with the <code>spring.config.location</code> and <code>spring.config.additional-location</code> properties, as well as with <a href="#features.external-config.files.importing"><code>spring.config.import</code></a> declarations.</p>
</div>
<div class="paragraph">
<p>For example, a <code>spring.config.import</code> value of <code>optional:file:./myconfig.properties</code> allows your application to start, even if the <code>myconfig.properties</code> file is missing.</p>
</div>
<div class="paragraph">
<p>If you want to ignore all <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/config/ConfigDataLocationNotFoundException.html"><code>ConfigDataLocationNotFoundException</code></a> errors and always continue to start your application, you can use the <code>spring.config.on-not-found</code> property.
Set the value to <code>ignore</code> using <code>SpringApplication.setDefaultProperties(…​)</code> or with a system/environment variable.</p>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.files.wildcard-locations"><a class="anchor" href="#features.external-config.files.wildcard-locations"></a>Wildcard Locations</h3>
<div class="paragraph">
<p>If a config file location includes the <code>*</code> character for the last path segment, it is considered a wildcard location.
Wildcards are expanded when the config is loaded so that immediate subdirectories are also checked.
Wildcard locations are particularly useful in an environment such as Kubernetes when there are multiple sources of config properties.</p>
</div>
<div class="paragraph">
<p>For example, if you have some Redis configuration and some MySQL configuration, you might want to keep those two pieces of configuration separate, while requiring that both those are present in an <code>application.properties</code> file.
This might result in two separate <code>application.properties</code> files mounted at different locations such as <code>/config/redis/application.properties</code> and <code>/config/mysql/application.properties</code>.
In such a case, having a wildcard location of <code>config/*/</code>, will result in both files being processed.</p>
</div>
<div class="paragraph">
<p>By default, Spring Boot includes <code>config/*/</code> in the default search locations.
It means that all subdirectories of the <code>/config</code> directory outside of your jar will be searched.</p>
</div>
<div class="paragraph">
<p>You can use wildcard locations yourself with the <code>spring.config.location</code> and <code>spring.config.additional-location</code> properties.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
A wildcard location must contain only one <code>*</code> and end with <code>*/</code> for search locations that are directories or <code>*/&lt;filename&gt;</code> for search locations that are files.
Locations with wildcards are sorted alphabetically based on the absolute path of the file names.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Wildcard locations only work with external directories.
You cannot use a wildcard in a <code>classpath:</code> location.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.files.profile-specific"><a class="anchor" href="#features.external-config.files.profile-specific"></a>Profile Specific Files</h3>
<div class="paragraph">
<p>As well as <code>application</code> property files, Spring Boot will also attempt to load profile-specific files using the naming convention <code>application-{profile}</code>.
For example, if your application activates a profile named <code>prod</code> and uses YAML files, then both <code>application.yaml</code> and <code>application-prod.yaml</code> will be considered.</p>
</div>
<div class="paragraph">
<p>Profile-specific properties are loaded from the same locations as standard <code>application.properties</code>, with profile-specific files always overriding the non-specific ones.
If several profiles are specified, a last-wins strategy applies.
For example, if profiles <code>prod,live</code> are specified by the <code>spring.profiles.active</code> property, values in <code>application-prod.properties</code> can be overridden by those in <code>application-live.properties</code>.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>The last-wins strategy applies at the <a href="#features.external-config.files.location-groups">location group</a> level.
A <code>spring.config.location</code> of <code>classpath:/cfg/,classpath:/ext/</code> will not have the same override rules as <code>classpath:/cfg/;classpath:/ext/</code>.</p>
</div>
<div class="paragraph">
<p>For example, continuing our <code>prod,live</code> example above, we might have the following files:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>/cfg
  application-live.properties
/ext
  application-live.properties
  application-prod.properties</pre>
</div>
</div>
<div class="paragraph">
<p>When we have a <code>spring.config.location</code> of <code>classpath:/cfg/,classpath:/ext/</code> we process all <code>/cfg</code> files before all <code>/ext</code> files:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p><code>/cfg/application-live.properties</code></p>
</li>
<li>
<p><code>/ext/application-prod.properties</code></p>
</li>
<li>
<p><code>/ext/application-live.properties</code></p>
</li>
</ol>
</div>
<div class="paragraph">
<p>When we have <code>classpath:/cfg/;classpath:/ext/</code> instead (with a <code>;</code> delimiter) we process <code>/cfg</code> and <code>/ext</code> at the same level:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p><code>/ext/application-prod.properties</code></p>
</li>
<li>
<p><code>/cfg/application-live.properties</code></p>
</li>
<li>
<p><code>/ext/application-live.properties</code></p>
</li>
</ol>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> has a set of default profiles (by default, <code>[default]</code>) that are used if no active profiles are set.
In other words, if no profiles are explicitly activated, then properties from <code>application-default</code> are considered.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Properties files are only ever loaded once.
If you have already directly <a href="#features.external-config.files.importing">imported</a> a profile specific property files then it will not be imported a second time.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.files.importing"><a class="anchor" href="#features.external-config.files.importing"></a>Importing Additional Data</h3>
<div class="paragraph">
<p>Application properties may import further config data from other locations using the <code>spring.config.import</code> property.
Imports are processed as they are discovered, and are treated as additional documents inserted immediately below the one that declares the import.</p>
</div>
<div class="paragraph">
<p>For example, you might have the following in your classpath <code>application.properties</code> file:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_2_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_2_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_2_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_properties" class="tabpanel" id="_tabs_2_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.application.name</span>=<span class="hljs-string">myapp</span>
<span class="hljs-meta">spring.config.import</span>=<span class="hljs-string">optional:file:./dev.properties</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_2_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">application:</span>
    <span class="hljs-attr">name:</span> <span class="hljs-string">"myapp"</span>
  <span class="hljs-attr">config:</span>
    <span class="hljs-attr">import:</span> <span class="hljs-string">"optional:file:./dev.properties"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>This will trigger the import of a <code>dev.properties</code> file in current directory (if such a file exists).
Values from the imported <code>dev.properties</code> will take precedence over the file that triggered the import.
In the above example, the <code>dev.properties</code> could redefine <code>spring.application.name</code> to a different value.</p>
</div>
<div class="paragraph">
<p>An import will only be imported once no matter how many times it is declared.</p>
</div>
<div class="sect3">
<h4 id="features.external-config.files.importing.fixed-and-relative-paths"><a class="anchor" href="#features.external-config.files.importing.fixed-and-relative-paths"></a>Using “Fixed” and “Import Relative” Locations</h4>
<div class="paragraph">
<p>Imports may be specified as <em>fixed</em> or <em>import relative</em> locations.
A fixed location always resolves to the same underlying resource, regardless of where the <code>spring.config.import</code> property is declared.
An import relative location resolves relative to the file that declares the <code>spring.config.import</code> property.</p>
</div>
<div class="paragraph">
<p>A location starting with a forward slash (<code>/</code>) or a URL style prefix (<code>file:</code>, <code>classpath:</code>, etc.) is considered fixed.
All other locations are considered import relative.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<code>optional:</code> prefixes are not considered when determining if a location is fixed or import relative.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>As an example, say we have a <code>/demo</code> directory containing our <code>application.jar</code> file.
We might add a <code>/demo/application.properties</code> file with the following content:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.config.import</span>=<span class="hljs-string">optional:core/core.properties</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This is an import relative location and so will attempt to load the file <code>/demo/core/core.properties</code> if it exists.</p>
</div>
<div class="paragraph">
<p>If <code>/demo/core/core.properties</code> has the following content:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.config.import</span>=<span class="hljs-string">optional:extra/extra.properties</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>It will attempt to load <code>/demo/core/extra/extra.properties</code>.
The <code>optional:extra/extra.properties</code> is relative to <code>/demo/core/core.properties</code> so the full directory is <code>/demo/core/</code> + <code>extra/extra.properties</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="features.external-config.files.importing.import-property-order"><a class="anchor" href="#features.external-config.files.importing.import-property-order"></a>Property Ordering</h4>
<div class="paragraph">
<p>The order an import is defined inside a single document within the properties/yaml file does not matter.
For instance, the two examples below produce the same result:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_3_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_3_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_3_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_properties" class="tabpanel" id="_tabs_3_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.config.import</span>=<span class="hljs-string">my.properties</span>
<span class="hljs-meta">my.property</span>=<span class="hljs-string">value</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_3_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">config:</span>
    <span class="hljs-attr">import:</span> <span class="hljs-string">"my.properties"</span>
<span class="hljs-attr">my:</span>
  <span class="hljs-attr">property:</span> <span class="hljs-string">"value"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_4_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_4_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_4_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_properties" class="tabpanel" id="_tabs_4_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">my.property</span>=<span class="hljs-string">value</span>
<span class="hljs-meta">spring.config.import</span>=<span class="hljs-string">my.properties</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_4_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">my:</span>
  <span class="hljs-attr">property:</span> <span class="hljs-string">"value"</span>
<span class="hljs-attr">spring:</span>
  <span class="hljs-attr">config:</span>
    <span class="hljs-attr">import:</span> <span class="hljs-string">"my.properties"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>In both of the above examples, the values from the <code>my.properties</code> file will take precedence over the file that triggered its import.</p>
</div>
<div class="paragraph">
<p>Several locations can be specified under a single <code>spring.config.import</code> key.
Locations will be processed in the order that they are defined, with later imports taking precedence.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
When appropriate, <a href="#features.external-config.files.profile-specific">Profile-specific variants</a> are also considered for import.
The example above would import both <code>my.properties</code> as well as any <code>my-&lt;profile&gt;.properties</code> variants.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<div class="paragraph">
<p>Spring Boot includes pluggable API that allows various different location addresses to be supported.
By default you can import Java Properties, YAML and <a href="#features.external-config.files.configtree">configuration trees</a>.</p>
</div>
<div class="paragraph">
<p>Third-party jars can offer support for additional technologies (there is no requirement for files to be local).
For example, you can imagine config data being from external stores such as Consul, Apache ZooKeeper or Netflix Archaius.</p>
</div>
<div class="paragraph">
<p>If you want to support your own locations, see the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/config/ConfigDataLocationResolver.html"><code>ConfigDataLocationResolver</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/config/ConfigDataLoader.html"><code>ConfigDataLoader</code></a> classes in the <code>org.springframework.boot.context.config</code> package.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.files.importing-extensionless"><a class="anchor" href="#features.external-config.files.importing-extensionless"></a>Importing Extensionless Files</h3>
<div class="paragraph">
<p>Some cloud platforms cannot add a file extension to volume mounted files.
To import these extensionless files, you need to give Spring Boot a hint so that it knows how to load them.
You can do this by putting an extension hint in square brackets.</p>
</div>
<div class="paragraph">
<p>For example, suppose you have a <code>/etc/config/myconfig</code> file that you wish to import as yaml.
You can import it from your <code>application.properties</code> using the following:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_5_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_5_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_5_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_properties" class="tabpanel" id="_tabs_5_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.config.import</span>=<span class="hljs-string">file:/etc/config/myconfig[.yaml]</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_5_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">config:</span>
    <span class="hljs-attr">import:</span> <span class="hljs-string">"file:/etc/config/myconfig[.yaml]"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.files.configtree"><a class="anchor" href="#features.external-config.files.configtree"></a>Using Configuration Trees</h3>
<div class="paragraph">
<p>When running applications on a cloud platform (such as Kubernetes) you often need to read config values that the platform supplies.
It is not uncommon to use environment variables for such purposes, but this can have drawbacks, especially if the value is supposed to be kept secret.</p>
</div>
<div class="paragraph">
<p>As an alternative to environment variables, many cloud platforms now allow you to map configuration into mounted data volumes.
For example, Kubernetes can volume mount both <a class="external" href="https://kubernetes.io/docs/tasks/configure-pod-container/configure-pod-configmap/#populate-a-volume-with-data-stored-in-a-configmap" target="_blank"><code>ConfigMaps</code></a> and <a class="external" href="https://kubernetes.io/docs/concepts/configuration/secret/#using-secrets-as-files-from-a-pod" target="_blank"><code>Secrets</code></a>.</p>
</div>
<div class="paragraph">
<p>There are two common volume mount patterns that can be used:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>A single file contains a complete set of properties (usually written as YAML).</p>
</li>
<li>
<p>Multiple files are written to a directory tree, with the filename becoming the ‘key’ and the contents becoming the ‘value’.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>For the first case, you can import the YAML or Properties file directly using <code>spring.config.import</code> as described <a href="#features.external-config.files.importing">above</a>.
For the second case, you need to use the <code>configtree:</code> prefix so that Spring Boot knows it needs to expose all the files as properties.</p>
</div>
<div class="paragraph">
<p>As an example, let’s imagine that Kubernetes has mounted the following volume:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-none hljs">etc/
  config/
    myapp/
      username
      password</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The contents of the <code>username</code> file would be a config value, and the contents of <code>password</code> would be a secret.</p>
</div>
<div class="paragraph">
<p>To import these properties, you can add the following to your <code>application.properties</code> or <code>application.yaml</code> file:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_6_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_6_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_6_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_properties" class="tabpanel" id="_tabs_6_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.config.import</span>=<span class="hljs-string">optional:configtree:/etc/config/</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_6_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">config:</span>
    <span class="hljs-attr">import:</span> <span class="hljs-string">"optional:configtree:/etc/config/"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can then access or inject <code>myapp.username</code> and <code>myapp.password</code> properties from the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> in the usual way.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The names of the folders and files under the config tree form the property name.
In the above example, to access the properties as <code>username</code> and <code>password</code>, you can set <code>spring.config.import</code> to <code>optional:configtree:/etc/config/myapp</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Filenames with dot notation are also correctly mapped.
For example, in the above example, a file named <code>myapp.username</code> in <code>/etc/config</code> would result in a <code>myapp.username</code> property in the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Configuration tree values can be bound to both string <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" target="_blank"><code>String</code></a> and <code>byte[]</code> types depending on the contents expected.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If you have multiple config trees to import from the same parent folder you can use a wildcard shortcut.
Any <code>configtree:</code> location that ends with <code>/*/</code> will import all immediate children as config trees.
As with a non-wildcard import, the names of the folders and files under each config tree form the property name.</p>
</div>
<div class="paragraph">
<p>For example, given the following volume:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-none hljs">etc/
  config/
    dbconfig/
      db/
        username
        password
    mqconfig/
      mq/
        username
        password</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>You can use <code>configtree:/etc/config/*/</code> as the import location:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_7_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_7_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_7_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_properties" class="tabpanel" id="_tabs_7_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.config.import</span>=<span class="hljs-string">optional:configtree:/etc/config/*/</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_7_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">config:</span>
    <span class="hljs-attr">import:</span> <span class="hljs-string">"optional:configtree:/etc/config/*/"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>This will add <code>db.username</code>, <code>db.password</code>, <code>mq.username</code> and <code>mq.password</code> properties.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Directories loaded using a wildcard are sorted alphabetically.
If you need a different order, then you should list each location as a separate import
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Configuration trees can also be used for Docker secrets.
When a Docker swarm service is granted access to a secret, the secret gets mounted into the container.
For example, if a secret named <code>db.password</code> is mounted at location <code>/run/secrets/</code>, you can make <code>db.password</code> available to the Spring environment using the following:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_8_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_8_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_8_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_properties" class="tabpanel" id="_tabs_8_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.config.import</span>=<span class="hljs-string">optional:configtree:/run/secrets/</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_8_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">config:</span>
    <span class="hljs-attr">import:</span> <span class="hljs-string">"optional:configtree:/run/secrets/"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.files.property-placeholders"><a class="anchor" href="#features.external-config.files.property-placeholders"></a>Property Placeholders</h3>
<div class="paragraph">
<p>The values in <code>application.properties</code> and <code>application.yaml</code> are filtered through the existing <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> when they are used, so you can refer back to previously defined values (for example, from System properties or environment variables).
The standard <code>${name}</code> property-placeholder syntax can be used anywhere within a value.
Property placeholders can also specify a default value using a <code>:</code> to separate the default value from the property name, for example <code>${name:default}</code>.</p>
</div>
<div class="paragraph">
<p>The use of placeholders with and without defaults is shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_9">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_9_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_9_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_9_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_9_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_9_properties" class="tabpanel" id="_tabs_9_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">app.name</span>=<span class="hljs-string">MyApp</span>
<span class="hljs-meta">app.description</span>=<span class="hljs-string">${app.name} is a Spring Boot application written by ${username:Unknown}</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_9_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_9_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">app:</span>
  <span class="hljs-attr">name:</span> <span class="hljs-string">"MyApp"</span>
  <span class="hljs-attr">description:</span> <span class="hljs-string">"${app.name} is a Spring Boot application written by ${username:Unknown}"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Assuming that the <code>username</code> property has not been set elsewhere, <code>app.description</code> will have the value <code>MyApp is a Spring Boot application written by Unknown</code>.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>You should always refer to property names in the placeholder using their canonical form (kebab-case using only lowercase letters).
This will allow Spring Boot to use the same logic as it does when <a href="#features.external-config.typesafe-configuration-properties.relaxed-binding">relaxed binding</a> <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a>.</p>
</div>
<div class="paragraph">
<p>For example, <code>${demo.item-price}</code> will pick up <code>demo.item-price</code> and <code>demo.itemPrice</code> forms from the <code>application.properties</code> file, as well as <code>DEMO_ITEMPRICE</code> from the system environment.
If you used <code>${demo.itemPrice}</code> instead, <code>demo.item-price</code> and <code>DEMO_ITEMPRICE</code> would not be considered.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can also use this technique to create “short” variants of existing Spring Boot properties.
See the <a class="xref page" href="../../how-to/properties-and-configuration.html#howto.properties-and-configuration.short-command-line-arguments">Use ‘Short’ Command Line Arguments</a> section in “How-to Guides” for details.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.files.multi-document"><a class="anchor" href="#features.external-config.files.multi-document"></a>Working With Multi-Document Files</h3>
<div class="paragraph">
<p>Spring Boot allows you to split a single physical file into multiple logical documents which are each added independently.
Documents are processed in order, from top to bottom.
Later documents can override the properties defined in earlier ones.</p>
</div>
<div class="paragraph">
<p>For <code>application.yaml</code> files, the standard YAML multi-document syntax is used.
Three consecutive hyphens represent the end of one document, and the start of the next.</p>
</div>
<div class="paragraph">
<p>For example, the following file has two logical documents:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">application:</span>
    <span class="hljs-attr">name:</span> <span class="hljs-string">"MyApp"</span>
<span class="hljs-meta">---</span>
<span class="hljs-attr">spring:</span>
  <span class="hljs-attr">application:</span>
    <span class="hljs-attr">name:</span> <span class="hljs-string">"MyCloudApp"</span>
  <span class="hljs-attr">config:</span>
    <span class="hljs-attr">activate:</span>
      <span class="hljs-attr">on-cloud-platform:</span> <span class="hljs-string">"kubernetes"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>For <code>application.properties</code> files a special <code>#---</code> or <code>!---</code> comment is used to mark the document splits:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.application.name</span>=<span class="hljs-string">MyApp</span>
<span class="hljs-comment">#---</span>
<span class="hljs-meta">spring.application.name</span>=<span class="hljs-string">MyCloudApp</span>
<span class="hljs-meta">spring.config.activate.on-cloud-platform</span>=<span class="hljs-string">kubernetes</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Property file separators must not have any leading whitespace and must have exactly three hyphen characters.
The lines immediately before and after the separator must not be same comment prefix.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Multi-document property files are often used in conjunction with activation properties such as <code>spring.config.activate.on-profile</code>.
See the <a href="#features.external-config.files.activation-properties">next section</a> for details.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock warning">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-warning" title="Warning"></i>
</td>
<td class="content">
Multi-document property files cannot be loaded by using the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/PropertySource.html"><code>@PropertySource</code></a> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/context/TestPropertySource.html"><code>@TestPropertySource</code></a> annotations.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.files.activation-properties"><a class="anchor" href="#features.external-config.files.activation-properties"></a>Activation Properties</h3>
<div class="paragraph">
<p>It is sometimes useful to only activate a given set of properties when certain conditions are met.
For example, you might have properties that are only relevant when a specific profile is active.</p>
</div>
<div class="paragraph">
<p>You can conditionally activate a properties document using <code>spring.config.activate.*</code>.</p>
</div>
<div class="paragraph">
<p>The following activation properties are available:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<caption class="title">Table 1. activation properties</caption>
<colgroup>
<col style="width: 20%;"/>
<col style="width: 80%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Property</th>
<th class="tableblock halign-left valign-top">Note</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>on-profile</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">A profile expression that must match for the document to be active, or a list of profile expressions of which at least one must match for the document to be active.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>on-cloud-platform</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/cloud/CloudPlatform.html"><code>CloudPlatform</code></a> that must be detected for the document to be active.</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>For example, the following specifies that the second document is only active when running on Kubernetes, and only when either the “prod” or “staging” profiles are active:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_10">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_10_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_10_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_10_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_10_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_10_properties" class="tabpanel" id="_tabs_10_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-attr">myprop</span>=<span class="hljs-string">always-set</span>
<span class="hljs-comment">#---</span>
<span class="hljs-meta">spring.config.activate.on-cloud-platform</span>=<span class="hljs-string">kubernetes</span>
<span class="hljs-meta">spring.config.activate.on-profile</span>=<span class="hljs-string">prod | staging</span>
<span class="hljs-attr">myotherprop</span>=<span class="hljs-string">sometimes-set</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_10_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_10_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">myprop:</span>
  <span class="hljs-string">"always-set"</span>
<span class="hljs-meta">---</span>
<span class="hljs-attr">spring:</span>
  <span class="hljs-attr">config:</span>
    <span class="hljs-attr">activate:</span>
      <span class="hljs-attr">on-cloud-platform:</span> <span class="hljs-string">"kubernetes"</span>
      <span class="hljs-attr">on-profile:</span> <span class="hljs-string">"prod | staging"</span>
<span class="hljs-attr">myotherprop:</span> <span class="hljs-string">"sometimes-set"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="features.external-config.encrypting"><a class="anchor" href="#features.external-config.encrypting"></a>Encrypting Properties</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot does not provide any built-in support for encrypting property values, however, it does provide the hook points necessary to modify values contained in the Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a>.
The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/env/EnvironmentPostProcessor.html"><code>EnvironmentPostProcessor</code></a> interface allows you to manipulate the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> before the application starts.
See <a class="xref page" href="../../how-to/application.html#howto.application.customize-the-environment-or-application-context">Customize the Environment or ApplicationContext Before It Starts</a> for details.</p>
</div>
<div class="paragraph">
<p>If you need a secure way to store credentials and passwords, the <a class="external" href="https://cloud.spring.io/spring-cloud-vault/" target="_blank">Spring Cloud Vault</a> project provides support for storing externalized configuration in <a class="external" href="https://www.vaultproject.io/" target="_blank">HashiCorp Vault</a>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="features.external-config.yaml"><a class="anchor" href="#features.external-config.yaml"></a>Working With YAML</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="external" href="https://yaml.org" target="_blank">YAML</a> is a superset of JSON and, as such, is a convenient format for specifying hierarchical configuration data.
The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringApplication.html"><code>SpringApplication</code></a> class automatically supports YAML as an alternative to properties whenever you have the <a class="external" href="https://github.com/snakeyaml/snakeyaml" target="_blank">SnakeYAML</a> library on your classpath.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If you use starters, SnakeYAML is automatically provided by <code>spring-boot-starter</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="features.external-config.yaml.mapping-to-properties"><a class="anchor" href="#features.external-config.yaml.mapping-to-properties"></a>Mapping YAML to Properties</h3>
<div class="paragraph">
<p>YAML documents need to be converted from their hierarchical format to a flat structure that can be used with the Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a>.
For example, consider the following YAML document:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">environments:</span>
  <span class="hljs-attr">dev:</span>
    <span class="hljs-attr">url:</span> <span class="hljs-string">"https://dev.example.com"</span>
    <span class="hljs-attr">name:</span> <span class="hljs-string">"Developer Setup"</span>
  <span class="hljs-attr">prod:</span>
    <span class="hljs-attr">url:</span> <span class="hljs-string">"https://another.example.com"</span>
    <span class="hljs-attr">name:</span> <span class="hljs-string">"My Cool App"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>In order to access these properties from the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a>, they would be flattened as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">environments.dev.url</span>=<span class="hljs-string">https://dev.example.com</span>
<span class="hljs-meta">environments.dev.name</span>=<span class="hljs-string">Developer Setup</span>
<span class="hljs-meta">environments.prod.url</span>=<span class="hljs-string">https://another.example.com</span>
<span class="hljs-meta">environments.prod.name</span>=<span class="hljs-string">My Cool App</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Likewise, YAML lists also need to be flattened.
They are represented as property keys with <code>[index]</code> dereferencers.
For example, consider the following YAML:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"> <span class="hljs-attr">my:</span>
  <span class="hljs-attr">servers:</span>
  <span class="hljs-bullet">-</span> <span class="hljs-string">"dev.example.com"</span>
  <span class="hljs-bullet">-</span> <span class="hljs-string">"another.example.com"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The preceding example would be transformed into these properties:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">my.servers[0]</span>=<span class="hljs-string">dev.example.com</span>
<span class="hljs-meta">my.servers[1]</span>=<span class="hljs-string">another.example.com</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Properties that use the <code>[index]</code> notation can be bound to Java <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" target="_blank"><code>List</code></a> or <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Set.html" target="_blank"><code>Set</code></a> objects using Spring Boot’s <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/bind/Binder.html"><code>Binder</code></a> class.
For more details see the <a href="#features.external-config.typesafe-configuration-properties">Type-safe Configuration Properties</a> section below.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock warning">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-warning" title="Warning"></i>
</td>
<td class="content">
YAML files cannot be loaded by using the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/PropertySource.html"><code>@PropertySource</code></a> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/test/context/TestPropertySource.html"><code>@TestPropertySource</code></a> annotations.
So, in the case that you need to load values that way, you need to use a properties file.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.yaml.directly-loading"><a class="anchor" href="#features.external-config.yaml.directly-loading"></a>Directly Loading YAML</h3>
<div class="paragraph">
<p>Spring Framework provides two convenient classes that can be used to load YAML documents.
The <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/config/YamlPropertiesFactoryBean.html"><code>YamlPropertiesFactoryBean</code></a> loads YAML as <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Properties.html" target="_blank"><code>Properties</code></a> and the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/config/YamlMapFactoryBean.html"><code>YamlMapFactoryBean</code></a> loads YAML as a <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" target="_blank"><code>Map</code></a>.</p>
</div>
<div class="paragraph">
<p>You can also use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/env/YamlPropertySourceLoader.html"><code>YamlPropertySourceLoader</code></a> class if you want to load YAML as a Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/PropertySource.html"><code>PropertySource</code></a>.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="features.external-config.random-values"><a class="anchor" href="#features.external-config.random-values"></a>Configuring Random Values</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/env/RandomValuePropertySource.html"><code>RandomValuePropertySource</code></a> is useful for injecting random values (for example, into secrets or test cases).
It can produce integers, longs, uuids, or strings, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_11">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_11_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_11_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_11_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_11_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_11_properties" class="tabpanel" id="_tabs_11_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">my.secret</span>=<span class="hljs-string">${random.value}</span>
<span class="hljs-meta">my.number</span>=<span class="hljs-string">${random.int}</span>
<span class="hljs-meta">my.bignumber</span>=<span class="hljs-string">${random.long}</span>
<span class="hljs-meta">my.uuid</span>=<span class="hljs-string">${random.uuid}</span>
<span class="hljs-meta">my.number-less-than-ten</span>=<span class="hljs-string">${random.int(10)}</span>
<span class="hljs-meta">my.number-in-range</span>=<span class="hljs-string">${random.int[1024,65536]}</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_11_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_11_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">my:</span>
  <span class="hljs-attr">secret:</span> <span class="hljs-string">"${random.value}"</span>
  <span class="hljs-attr">number:</span> <span class="hljs-string">"${random.int}"</span>
  <span class="hljs-attr">bignumber:</span> <span class="hljs-string">"${random.long}"</span>
  <span class="hljs-attr">uuid:</span> <span class="hljs-string">"${random.uuid}"</span>
  <span class="hljs-attr">number-less-than-ten:</span> <span class="hljs-string">"${random.int(10)}"</span>
  <span class="hljs-attr">number-in-range:</span> <span class="hljs-string">"${random.int[1024,65536]}"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The <code>random.int*</code> syntax is <code>OPEN value (,max) CLOSE</code> where the <code>OPEN,CLOSE</code> are any character and <code>value,max</code> are integers.
If <code>max</code> is provided, then <code>value</code> is the minimum value and <code>max</code> is the maximum value (exclusive).</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="features.external-config.system-environment"><a class="anchor" href="#features.external-config.system-environment"></a>Configuring System Environment Properties</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot supports setting a prefix for environment properties.
This is useful if the system environment is shared by multiple Spring Boot applications with different configuration requirements.
The prefix for system environment properties can be set directly on <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringApplication.html"><code>SpringApplication</code></a> by calling the <code>setEnvironmentPrefix(…​)</code> method before the application is run.</p>
</div>
<div class="paragraph">
<p>For example, if you set the prefix to <code>input</code>, a property such as <code>remote.timeout</code> will be resolved as <code>INPUT_REMOTE_TIMEOUT</code> in the system environment.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The prefix <em>only</em> applies to system environment properties.
The example above would continue to use <code>remote.timeout</code> when reading properties from other sources.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="features.external-config.typesafe-configuration-properties"><a class="anchor" href="#features.external-config.typesafe-configuration-properties"></a>Type-safe Configuration Properties</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Using the <code>@Value("${property}")</code> annotation to inject configuration properties can sometimes be cumbersome, especially if you are working with multiple properties or your data is hierarchical in nature.
Spring Boot provides an alternative method of working with properties that lets strongly typed beans govern and validate the configuration of your application.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
See also the <a href="#features.external-config.typesafe-configuration-properties.vs-value-annotation">differences between </a><a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Value.html"><code>@Value</code></a> and type-safe configuration properties.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="features.external-config.typesafe-configuration-properties.java-bean-binding"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.java-bean-binding"></a>JavaBean Properties Binding</h3>
<div class="paragraph">
<p>It is possible to bind a bean declaring standard JavaBean properties as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_12">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_12_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_12_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_12_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_12_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_12_java" class="tabpanel" id="_tabs_12_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.net.InetAddress;
<span class="hljs-keyword">import</span> java.util.ArrayList;
<span class="hljs-keyword">import</span> java.util.Collections;
<span class="hljs-keyword">import</span> java.util.List;

<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"my.service"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">boolean</span> enabled;

	<span class="hljs-keyword">private</span> InetAddress remoteAddress;

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> Security security = <span class="hljs-keyword">new</span> Security();

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// getters / setters...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">boolean</span> <span class="hljs-title">isEnabled</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.enabled;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setEnabled</span><span class="hljs-params">(<span class="hljs-keyword">boolean</span> enabled)</span> </span>{
		<span class="hljs-keyword">this</span>.enabled = enabled;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> InetAddress <span class="hljs-title">getRemoteAddress</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.remoteAddress;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setRemoteAddress</span><span class="hljs-params">(InetAddress remoteAddress)</span> </span>{
		<span class="hljs-keyword">this</span>.remoteAddress = remoteAddress;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Security <span class="hljs-title">getSecurity</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.security;
	}

</span><span class="fold-block">	<span class="hljs-keyword">public</span> <span class="hljs-keyword">static</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Security</span> </span>{

		<span class="hljs-keyword">private</span> String username;

		<span class="hljs-keyword">private</span> String password;

		<span class="hljs-keyword">private</span> List&lt;String&gt; roles = <span class="hljs-keyword">new</span> ArrayList&lt;&gt;(Collections.singleton(<span class="hljs-string">"USER"</span>));

</span><span class="fold-block is-hidden-unfolded">		<span class="hljs-comment">// getters / setters...</span>

</span><span class="fold-block is-hidden-folded">		<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">getUsername</span><span class="hljs-params">()</span> </span>{
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.username;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setUsername</span><span class="hljs-params">(String username)</span> </span>{
			<span class="hljs-keyword">this</span>.username = username;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">getPassword</span><span class="hljs-params">()</span> </span>{
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.password;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setPassword</span><span class="hljs-params">(String password)</span> </span>{
			<span class="hljs-keyword">this</span>.password = password;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> List&lt;String&gt; <span class="hljs-title">getRoles</span><span class="hljs-params">()</span> </span>{
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.roles;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setRoles</span><span class="hljs-params">(List&lt;String&gt; roles)</span> </span>{
			<span class="hljs-keyword">this</span>.roles = roles;
		}

</span><span class="fold-block">	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_12_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_12_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> java.net.InetAddress

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"my.service"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-keyword">var</span> isEnabled = <span class="hljs-literal">false</span>

	<span class="hljs-keyword">var</span> remoteAddress: InetAddress? = <span class="hljs-literal">null</span>

	<span class="hljs-keyword">val</span> security = Security()

	<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Security</span> </span>{

		<span class="hljs-keyword">var</span> username: String? = <span class="hljs-literal">null</span>

		<span class="hljs-keyword">var</span> password: String? = <span class="hljs-literal">null</span>

		<span class="hljs-keyword">var</span> roles: List&lt;String&gt; = ArrayList(setOf(<span class="hljs-string">"USER"</span>))

	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The preceding POJO defines the following properties:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>my.service.enabled</code>, with a value of <code>false</code> by default.</p>
</li>
<li>
<p><code>my.service.remote-address</code>, with a type that can be coerced from <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" target="_blank"><code>String</code></a>.</p>
</li>
<li>
<p><code>my.service.security.username</code>, with a nested "security" object whose name is determined by the name of the property.
In particular, the type is not used at all there and could have been <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/security/SecurityProperties.html"><code>SecurityProperties</code></a>.</p>
</li>
<li>
<p><code>my.service.security.password</code>.</p>
</li>
<li>
<p><code>my.service.security.roles</code>, with a collection of <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" target="_blank"><code>String</code></a> that defaults to <code>USER</code>.</p>
</li>
</ul>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
To use a reserved keyword in the name of a property, such as <code>my.service.import</code>, use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/bind/Name.html"><code>@Name</code></a> annotation on the property’s field.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The properties that map to <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> classes available in Spring Boot, which are configured through properties files, YAML files, environment variables, and other mechanisms, are public API but the accessors (getters/setters) of the class itself are not meant to be used directly.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>Such arrangement relies on a default empty constructor and getters and setters are usually mandatory, since binding is through standard Java Beans property descriptors, just like in Spring MVC.
A setter may be omitted in the following cases:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Maps, as long as they are initialized, need a getter but not necessarily a setter, since they can be mutated by the binder.</p>
</li>
<li>
<p>Collections and arrays can be accessed either through an index (typically with YAML) or by using a single comma-separated value (properties).
In the latter case, a setter is mandatory.
We recommend to always add a setter for such types.
If you initialize a collection, make sure it is not immutable (as in the preceding example).</p>
</li>
<li>
<p>If nested POJO properties are initialized (like the <code>Security</code> field in the preceding example), a setter is not required.
If you want the binder to create the instance on the fly by using its default constructor, you need a setter.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Some people use Project Lombok to add getters and setters automatically.
Make sure that Lombok does not generate any particular constructor for such a type, as it is used automatically by the container to instantiate the object.</p>
</div>
<div class="paragraph">
<p>Finally, only standard Java Bean properties are considered and binding on static properties is not supported.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.typesafe-configuration-properties.constructor-binding"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.constructor-binding"></a>Constructor Binding</h3>
<div class="paragraph">
<p>The example in the previous section can be rewritten in an immutable fashion as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_13">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_13_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_13_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_13_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_13_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_13_java" class="tabpanel" id="_tabs_13_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.net.InetAddress;
<span class="hljs-keyword">import</span> java.util.List;

<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.bind.DefaultValue;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"my.service"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// fields...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> <span class="hljs-keyword">boolean</span> enabled;

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> InetAddress remoteAddress;

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> Security security;


</span><span class="fold-block">	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyProperties</span><span class="hljs-params">(<span class="hljs-keyword">boolean</span> enabled, InetAddress remoteAddress, Security security)</span> </span>{
		<span class="hljs-keyword">this</span>.enabled = enabled;
		<span class="hljs-keyword">this</span>.remoteAddress = remoteAddress;
		<span class="hljs-keyword">this</span>.security = security;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// getters...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">boolean</span> <span class="hljs-title">isEnabled</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.enabled;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> InetAddress <span class="hljs-title">getRemoteAddress</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.remoteAddress;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Security <span class="hljs-title">getSecurity</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.security;
	}

</span><span class="fold-block">	<span class="hljs-keyword">public</span> <span class="hljs-keyword">static</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Security</span> </span>{

</span><span class="fold-block is-hidden-unfolded">		<span class="hljs-comment">// fields...</span>

</span><span class="fold-block is-hidden-folded">		<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> String username;

		<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> String password;

		<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> List&lt;String&gt; roles;


</span><span class="fold-block">		<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">Security</span><span class="hljs-params">(String username, String password, @DefaultValue(<span class="hljs-string">"USER"</span>)</span> List&lt;String&gt; roles) </span>{
			<span class="hljs-keyword">this</span>.username = username;
			<span class="hljs-keyword">this</span>.password = password;
			<span class="hljs-keyword">this</span>.roles = roles;
		}

</span><span class="fold-block is-hidden-unfolded">		<span class="hljs-comment">// getters...</span>

</span><span class="fold-block is-hidden-folded">		<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">getUsername</span><span class="hljs-params">()</span> </span>{
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.username;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">getPassword</span><span class="hljs-params">()</span> </span>{
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.password;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> List&lt;String&gt; <span class="hljs-title">getRoles</span><span class="hljs-params">()</span> </span>{
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.roles;
		}

</span><span class="fold-block">	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_13_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_13_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.bind.DefaultValue
<span class="hljs-keyword">import</span> java.net.InetAddress

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"my.service"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span></span>(<span class="hljs-keyword">val</span> enabled: <span class="hljs-built_in">Boolean</span>, <span class="hljs-keyword">val</span> remoteAddress: InetAddress,
		<span class="hljs-keyword">val</span> security: Security) {

	<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Security</span></span>(<span class="hljs-keyword">val</span> username: String, <span class="hljs-keyword">val</span> password: String,
			<span class="hljs-meta">@param:DefaultValue</span>(<span class="hljs-string">"USER"</span>) <span class="hljs-keyword">val</span> roles: List&lt;String&gt;)

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>In this setup, the presence of a single parameterized constructor implies that constructor binding should be used.
This means that the binder will find a constructor with the parameters that you wish to have bound.
If your class has multiple constructors, the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/bind/ConstructorBinding.html"><code>@ConstructorBinding</code></a> annotation can be used to specify which constructor to use for constructor binding.</p>
</div>
<div class="paragraph">
<p>To opt-out of constructor binding for a class, the parameterized constructor must be annotated with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Autowired.html"><code>@Autowired</code></a> or made <code>private</code>.
Kotlin developers can use an empty primary constructor to opt-out of constructor binding.</p>
</div>
<div class="paragraph">
<p>For example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_14">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_14_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_14_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_14_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_14_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_14_java" class="tabpanel" id="_tabs_14_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"my"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// fields...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-keyword">final</span> MyBean myBean;

	<span class="hljs-keyword">private</span> String name;


</span><span class="fold-block">	<span class="hljs-meta">@Autowired</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyProperties</span><span class="hljs-params">(MyBean myBean)</span> </span>{
		<span class="hljs-keyword">this</span>.myBean = myBean;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// getters / setters...</span>

</span><span class="fold-block is-hidden-folded">
	<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">getName</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.name;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setName</span><span class="hljs-params">(String name)</span> </span>{
		<span class="hljs-keyword">this</span>.name = name;
	}


</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_14_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_14_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"my"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span></span>() {

	<span class="hljs-keyword">constructor</span>(name: String) : <span class="hljs-keyword">this</span>() {
		<span class="hljs-keyword">this</span>.name = name
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// vars...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-keyword">var</span> name: String? = <span class="hljs-literal">null</span>

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Constructor binding can be used with records.
Unless your record has multiple constructors, there is no need to use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/bind/ConstructorBinding.html"><code>@ConstructorBinding</code></a>.</p>
</div>
<div class="paragraph">
<p>Nested members of a constructor bound class (such as <code>Security</code> in the example above) will also be bound through their constructor.</p>
</div>
<div class="paragraph">
<p>Default values can be specified using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/bind/DefaultValue.html"><code>@DefaultValue</code></a> on constructor parameters and record components.
The conversion service will be applied to coerce the annotation’s <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" target="_blank"><code>String</code></a> value to the target type of a missing property.</p>
</div>
<div class="paragraph">
<p>Referring to the previous example, if no properties are bound to <code>Security</code>, the <code>MyProperties</code> instance will contain a <code>null</code> value for <code>security</code>.
To make it contain a non-null instance of <code>Security</code> even when no properties are bound to it (when using Kotlin, this will require the <code>username</code> and <code>password</code> parameters of <code>Security</code> to be declared as nullable as they do not have default values), use an empty <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/bind/DefaultValue.html"><code>@DefaultValue</code></a> annotation:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_15">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_15_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_15_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_15_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_15_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_15_java" class="tabpanel" id="_tabs_15_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyProperties</span><span class="hljs-params">(<span class="hljs-keyword">boolean</span> enabled, InetAddress remoteAddress, @DefaultValue Security security)</span> </span>{
		<span class="hljs-keyword">this</span>.enabled = enabled;
		<span class="hljs-keyword">this</span>.remoteAddress = remoteAddress;
		<span class="hljs-keyword">this</span>.security = security;
	}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_15_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_15_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span></span>(<span class="hljs-keyword">val</span> enabled: <span class="hljs-built_in">Boolean</span>, <span class="hljs-keyword">val</span> remoteAddress: InetAddress,
		<span class="hljs-meta">@DefaultValue</span> <span class="hljs-keyword">val</span> security: Security) {

	<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Security</span></span>(<span class="hljs-keyword">val</span> username: String?, <span class="hljs-keyword">val</span> password: String?,
			<span class="hljs-meta">@param:DefaultValue</span>(<span class="hljs-string">"USER"</span>) <span class="hljs-keyword">val</span> roles: List&lt;String&gt;)

}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
To use constructor binding the class must be enabled using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> or configuration property scanning.
You cannot use constructor binding with beans that are created by the regular Spring mechanisms (for example <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> beans, beans created by using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> methods or beans loaded by using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Import.html"><code>@Import</code></a>)
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
To use constructor binding the class must be compiled with <code>-parameters</code>.
This will happen automatically if you use Spring Boot’s Gradle plugin or if you use Maven and <code>spring-boot-starter-parent</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The use of <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" target="_blank"><code>Optional</code></a> with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> is not recommended as it is primarily intended for use as a return type.
As such, it is not well-suited to configuration property injection.
For consistency with properties of other types, if you do declare an <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" target="_blank"><code>Optional</code></a> property and it has no value, <code>null</code> rather than an empty <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" target="_blank"><code>Optional</code></a> will be bound.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
To use a reserved keyword in the name of a property, such as <code>my.service.import</code>, use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/bind/Name.html"><code>@Name</code></a> annotation on the constructor parameter.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.typesafe-configuration-properties.enabling-annotated-types"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.enabling-annotated-types"></a>Enabling @ConfigurationProperties-annotated Types</h3>
<div class="paragraph">
<p>Spring Boot provides infrastructure to bind <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> types and register them as beans.
You can either enable configuration properties on a class-by-class basis or enable configuration property scanning that works in a similar manner to component scanning.</p>
</div>
<div class="paragraph">
<p>Sometimes, classes annotated with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> might not be suitable for scanning, for example, if you’re developing your own auto-configuration or you want to enable them conditionally.
In these cases, specify the list of types to process using the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a> annotation.
This can be done on any <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> class, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_16">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_16_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_16_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_16_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_16_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_16_java" class="tabpanel" id="_tabs_16_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.EnableConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-meta">@EnableConfigurationProperties</span>(SomeProperties<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">public</span> <span class="hljs-title">class</span> <span class="hljs-title">MyConfiguration</span> </span>{

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_16_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_16_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.EnableConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-meta">@EnableConfigurationProperties(SomeProperties::class)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyConfiguration</span></span></span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_17">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_17_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_17_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_17_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_17_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_17_java" class="tabpanel" id="_tabs_17_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"some.properties"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">SomeProperties</span> </span>{

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_17_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_17_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"some.properties"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">SomeProperties</span></span></span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>To use configuration property scanning, add the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationPropertiesScan.html"><code>@ConfigurationPropertiesScan</code></a> annotation to your application.
Typically, it is added to the main application class that is annotated with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html"><code>@SpringBootApplication</code></a> but it can be added to any <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> class.
By default, scanning will occur from the package of the class that declares the annotation.
If you want to define specific packages to scan, you can do so as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_18">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_18_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_18_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_18_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_18_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_18_java" class="tabpanel" id="_tabs_18_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.SpringBootApplication;
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationPropertiesScan;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootApplication</span>
<span class="hljs-meta">@ConfigurationPropertiesScan</span>({ <span class="hljs-string">"com.example.app"</span>, <span class="hljs-string">"com.example.another"</span> })
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplication</span> </span>{

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_18_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_18_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.SpringBootApplication
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationPropertiesScan

</span><span class="fold-block"><span class="hljs-meta">@SpringBootApplication</span>
<span class="hljs-meta">@ConfigurationPropertiesScan(<span class="hljs-meta-string">"com.example.app"</span>, <span class="hljs-meta-string">"com.example.another"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplication</span></span></span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>When the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> bean is registered using configuration property scanning or through <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/EnableConfigurationProperties.html"><code>@EnableConfigurationProperties</code></a>, the bean has a conventional name: <code>&lt;prefix&gt;-&lt;fqn&gt;</code>, where <code>&lt;prefix&gt;</code> is the environment key prefix specified in the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> annotation and <code>&lt;fqn&gt;</code> is the fully qualified name of the bean.
If the annotation does not provide any prefix, only the fully qualified name of the bean is used.</p>
</div>
<div class="paragraph">
<p>Assuming that it is in the <code>com.example.app</code> package, the bean name of the <code>SomeProperties</code> example above is <code>some.properties-com.example.app.SomeProperties</code>.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>We recommend that <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> only deal with the environment and, in particular, does not inject other beans from the context.
For corner cases, setter injection can be used or any of the <code>*Aware</code> interfaces provided by the framework (such as <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/EnvironmentAware.html"><code>EnvironmentAware</code></a> if you need access to the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a>).
If you still want to inject other beans using the constructor, the configuration properties bean must be annotated with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> and use JavaBean-based property binding.</p>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.typesafe-configuration-properties.using-annotated-types"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.using-annotated-types"></a>Using @ConfigurationProperties-annotated Types</h3>
<div class="paragraph">
<p>This style of configuration works particularly well with the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/SpringApplication.html"><code>SpringApplication</code></a> external YAML configuration, as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">my:</span>
  <span class="hljs-attr">service:</span>
    <span class="hljs-attr">remote-address:</span> <span class="hljs-number">192.168</span><span class="hljs-number">.1</span><span class="hljs-number">.1</span>
    <span class="hljs-attr">security:</span>
      <span class="hljs-attr">username:</span> <span class="hljs-string">"admin"</span>
      <span class="hljs-attr">roles:</span>
      <span class="hljs-bullet">-</span> <span class="hljs-string">"USER"</span>
      <span class="hljs-bullet">-</span> <span class="hljs-string">"ADMIN"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>To work with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans, you can inject them in the same way as any other bean, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_19">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_19_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_19_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_19_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_19_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_19_java" class="tabpanel" id="_tabs_19_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.stereotype.Service;

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> MyProperties properties;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyService</span><span class="hljs-params">(MyProperties properties)</span> </span>{
		<span class="hljs-keyword">this</span>.properties = properties;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">openConnection</span><span class="hljs-params">()</span> </span>{
		Server server = <span class="hljs-keyword">new</span> Server(<span class="hljs-keyword">this</span>.properties.getRemoteAddress());
		server.start();
		<span class="hljs-comment">// ...</span>
	}

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_19_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_19_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.stereotype.Service

</span><span class="fold-block"><span class="hljs-meta">@Service</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyService</span></span>(<span class="hljs-keyword">val</span> properties: MyProperties) {

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">openConnection</span><span class="hljs-params">()</span></span> {
		<span class="hljs-keyword">val</span> server = Server(properties.remoteAddress)
		server.start()
		<span class="hljs-comment">// ...</span>
	}

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> also lets you generate metadata files that can be used by IDEs to offer auto-completion for your own keys.
See the <a class="xref page" href="../../specification/configuration-metadata/index.html">appendix</a> for details.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.typesafe-configuration-properties.third-party-configuration"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.third-party-configuration"></a>Third-party Configuration</h3>
<div class="paragraph">
<p>As well as using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> to annotate a class, you can also use it on public <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> methods.
Doing so can be particularly useful when you want to bind properties to third-party components that are outside of your control.</p>
</div>
<div class="paragraph">
<p>To configure a bean from the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> properties, add <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> to its bean registration, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_20">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_20_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_20_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_20_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_20_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_20_java" class="tabpanel" id="_tabs_20_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">ThirdPartyConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@ConfigurationProperties</span>(prefix = <span class="hljs-string">"another"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> AnotherComponent <span class="hljs-title">anotherComponent</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> AnotherComponent();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_20_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_20_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">ThirdPartyConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@ConfigurationProperties(prefix = <span class="hljs-meta-string">"another"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">anotherComponent</span><span class="hljs-params">()</span></span>: AnotherComponent = AnotherComponent()

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Any JavaBean property defined with the <code>another</code> prefix is mapped onto that <code>AnotherComponent</code> bean in manner similar to the preceding <code>SomeProperties</code> example.</p>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.typesafe-configuration-properties.relaxed-binding"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.relaxed-binding"></a>Relaxed Binding</h3>
<div class="paragraph">
<p>Spring Boot uses some relaxed rules for binding <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> properties to <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans, so there does not need to be an exact match between the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> property name and the bean property name.
Common examples where this is useful include dash-separated environment properties (for example, <code>context-path</code> binds to <code>contextPath</code>), and capitalized environment properties (for example, <code>PORT</code> binds to <code>port</code>).</p>
</div>
<div class="paragraph">
<p>As an example, consider the following <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> class:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_21">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_21_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_21_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_21_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_21_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_21_java" class="tabpanel" id="_tabs_21_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(prefix = <span class="hljs-string">"my.main-project.person"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyPersonProperties</span> </span>{

	<span class="hljs-keyword">private</span> String firstName;

	<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">getFirstName</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.firstName;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setFirstName</span><span class="hljs-params">(String firstName)</span> </span>{
		<span class="hljs-keyword">this</span>.firstName = firstName;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_21_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_21_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(prefix = <span class="hljs-meta-string">"my.main-project.person"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyPersonProperties</span> </span>{

	<span class="hljs-keyword">var</span> firstName: String? = <span class="hljs-literal">null</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>With the preceding code, the following properties names can all be used:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<caption class="title">Table 2. relaxed binding</caption>
<colgroup>
<col style="width: 20%;"/>
<col style="width: 80%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Property</th>
<th class="tableblock halign-left valign-top">Note</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>my.main-project.person.first-name</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Kebab case, which is recommended for use in <code>.properties</code> and YAML files.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>my.main-project.person.firstName</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Standard camel case syntax.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>my.main-project.person.first_name</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Underscore notation, which is an alternative format for use in <code>.properties</code> and YAML files.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>MY_MAINPROJECT_PERSON_FIRSTNAME</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Upper case format, which is recommended when using system environment variables.</p></td>
</tr>
</tbody>
</table>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <code>prefix</code> value for the annotation <em>must</em> be in kebab case (lowercase and separated by <code>-</code>, such as <code>my.main-project.person</code>).
</td>
</tr>
</tbody></table>
</div>
<table class="tableblock frame-all grid-all stretch">
<caption class="title">Table 3. relaxed binding rules per property source</caption>
<colgroup>
<col style="width: 20%;"/>
<col style="width: 40%;"/>
<col style="width: 40%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Property Source</th>
<th class="tableblock halign-left valign-top">Simple</th>
<th class="tableblock halign-left valign-top">List</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Properties Files</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Camel case, kebab case, or underscore notation</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Standard list syntax using <code>[ ]</code> or comma-separated values</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">YAML Files</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Camel case, kebab case, or underscore notation</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Standard YAML list syntax or comma-separated values</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Environment Variables</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Upper case format with underscore as the delimiter (see <a href="#features.external-config.typesafe-configuration-properties.relaxed-binding.environment-variables">Binding From Environment Variables</a>).</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Numeric values surrounded by underscores (see <a href="#features.external-config.typesafe-configuration-properties.relaxed-binding.environment-variables">Binding From Environment Variables</a>)</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">System properties</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Camel case, kebab case, or underscore notation</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Standard list syntax using <code>[ ]</code> or comma-separated values</p></td>
</tr>
</tbody>
</table>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
We recommend that, when possible, properties are stored in lower-case kebab format, such as <code>my.person.first-name=Rod</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="sect3">
<h4 id="features.external-config.typesafe-configuration-properties.relaxed-binding.maps"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.relaxed-binding.maps"></a>Binding Maps</h4>
<div class="paragraph">
<p>When binding to <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" target="_blank"><code>Map</code></a> properties you may need to use a special bracket notation so that the original <code>key</code> value is preserved.
If the key is not surrounded by <code>[]</code>, any characters that are not alpha-numeric, <code>-</code> or <code>.</code> are removed.</p>
</div>
<div class="paragraph">
<p>For example, consider binding the following properties to a <code>Map&lt;String,String&gt;</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_22">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_22_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_22_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_22_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_22_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_22_properties" class="tabpanel" id="_tabs_22_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">my.map[/key1]</span>=<span class="hljs-string">value1</span>
<span class="hljs-meta">my.map[/key2]</span>=<span class="hljs-string">value2</span>
<span class="hljs-meta">my.map./key3</span>=<span class="hljs-string">value3</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_22_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_22_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">my:</span>
  <span class="hljs-attr">map:</span>
    <span class="hljs-string">"[/key1]"</span><span class="hljs-string">:</span> <span class="hljs-string">"value1"</span>
    <span class="hljs-string">"[/key2]"</span><span class="hljs-string">:</span> <span class="hljs-string">"value2"</span>
    <span class="hljs-string">"/key3"</span><span class="hljs-string">:</span> <span class="hljs-string">"value3"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
For YAML files, the brackets need to be surrounded by quotes for the keys to be parsed properly.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The properties above will bind to a <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" target="_blank"><code>Map</code></a> with <code>/key1</code>, <code>/key2</code> and <code>key3</code> as the keys in the map.
The slash has been removed from <code>key3</code> because it was not surrounded by square brackets.</p>
</div>
<div class="paragraph">
<p>When binding to scalar values, keys with <code>.</code> in them do not need to be surrounded by <code>[]</code>.
Scalar values include enums and all types in the <code>java.lang</code> package except for <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" target="_blank"><code>Object</code></a>.
Binding <code>a.b=c</code> to <code>Map&lt;String, String&gt;</code> will preserve the <code>.</code> in the key and return a Map with the entry <code>{"a.b"="c"}</code>.
For any other types you need to use the bracket notation if your <code>key</code> contains a <code>.</code>.
For example, binding <code>a.b=c</code> to <code>Map&lt;String, Object&gt;</code> will return a Map with the entry <code>{"a"={"b"="c"}}</code> whereas <code>[a.b]=c</code> will return a Map with the entry <code>{"a.b"="c"}</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="features.external-config.typesafe-configuration-properties.relaxed-binding.environment-variables"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.relaxed-binding.environment-variables"></a>Binding From Environment Variables</h4>
<div class="paragraph">
<p>Most operating systems impose strict rules around the names that can be used for environment variables.
For example, Linux shell variables can contain only letters (<code>a</code> to <code>z</code> or <code>A</code> to <code>Z</code>), numbers (<code>0</code> to <code>9</code>) or the underscore character (<code>_</code>).
By convention, Unix shell variables will also have their names in UPPERCASE.</p>
</div>
<div class="paragraph">
<p>Spring Boot’s relaxed binding rules are, as much as possible, designed to be compatible with these naming restrictions.</p>
</div>
<div class="paragraph">
<p>To convert a property name in the canonical-form to an environment variable name you can follow these rules:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Replace dots (<code>.</code>) with underscores (<code>_</code>).</p>
</li>
<li>
<p>Remove any dashes (<code>-</code>).</p>
</li>
<li>
<p>Convert to uppercase.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>For example, the configuration property <code>spring.main.log-startup-info</code> would be an environment variable named <code>SPRING_MAIN_LOGSTARTUPINFO</code>.</p>
</div>
<div class="paragraph">
<p>Environment variables can also be used when binding to object lists.
To bind to a <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" target="_blank"><code>List</code></a>, the element number should be surrounded with underscores in the variable name.</p>
</div>
<div class="paragraph">
<p>For example, the configuration property <code>my.service[0].other</code> would use an environment variable named <code>MY_SERVICE_0_OTHER</code>.</p>
</div>
<div class="paragraph">
<p>Support for binding from environment variables is applied to the <code>systemEnvironment</code> property source and to any additional property source whose name ends with <code>-systemEnvironment</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="features.external-config.typesafe-configuration-properties.relaxed-binding.maps-from-environment-variables"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.relaxed-binding.maps-from-environment-variables"></a>Binding Maps From Environment Variables</h4>
<div class="paragraph">
<p>When Spring Boot binds an environment variable to a property class, it lowercases the environment variable name before binding.
Most of the time this detail isn’t important, except when binding to <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" target="_blank"><code>Map</code></a> properties.</p>
</div>
<div class="paragraph">
<p>The keys in the <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" target="_blank"><code>Map</code></a> are always in lowercase, as seen in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_23">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_23_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_23_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_23_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_23_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_23_java" class="tabpanel" id="_tabs_23_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.HashMap;
<span class="hljs-keyword">import</span> java.util.Map;

<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(prefix = <span class="hljs-string">"my.props"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMapsProperties</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> Map&lt;String, String&gt; values = <span class="hljs-keyword">new</span> HashMap&lt;&gt;();

	<span class="hljs-function"><span class="hljs-keyword">public</span> Map&lt;String, String&gt; <span class="hljs-title">getValues</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.values;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_23_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_23_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(prefix = <span class="hljs-meta-string">"my.props"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMapsProperties</span> </span>{

	<span class="hljs-keyword">val</span> values: Map&lt;String, String&gt; = HashMap()

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>When setting <code>MY_PROPS_VALUES_KEY=value</code>, the <code>values</code> <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" target="_blank"><code>Map</code></a> contains a <code>{"key"="value"}</code> entry.</p>
</div>
<div class="paragraph">
<p>Only the environment variable <strong>name</strong> is lower-cased, not the value.
When setting <code>MY_PROPS_VALUES_KEY=VALUE</code>, the <code>values</code> <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" target="_blank"><code>Map</code></a> contains a <code>{"key"="VALUE"}</code> entry.</p>
</div>
</div>
<div class="sect3">
<h4 id="features.external-config.typesafe-configuration-properties.relaxed-binding.caching"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.relaxed-binding.caching"></a>Caching</h4>
<div class="paragraph">
<p>Relaxed binding uses a cache to improve performance. By default, this caching is only applied to immutable property sources.
To customize this behavior, for example to enable caching for mutable property sources, use <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/source/ConfigurationPropertyCaching.html"><code>ConfigurationPropertyCaching</code></a>.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.typesafe-configuration-properties.merging-complex-types"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.merging-complex-types"></a>Merging Complex Types</h3>
<div class="paragraph">
<p>When lists are configured in more than one place, overriding works by replacing the entire list.</p>
</div>
<div class="paragraph">
<p>For example, assume a <code>MyPojo</code> object with <code>name</code> and <code>description</code> attributes that are <code>null</code> by default.
The following example exposes a list of <code>MyPojo</code> objects from <code>MyProperties</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_24">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_24_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_24_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_24_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_24_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_24_java" class="tabpanel" id="_tabs_24_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.ArrayList;
<span class="hljs-keyword">import</span> java.util.List;

<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"my"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> List&lt;MyPojo&gt; list = <span class="hljs-keyword">new</span> ArrayList&lt;&gt;();

	<span class="hljs-function"><span class="hljs-keyword">public</span> List&lt;MyPojo&gt; <span class="hljs-title">getList</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.list;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_24_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_24_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"my"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-keyword">val</span> list: List&lt;MyPojo&gt; = ArrayList()

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Consider the following configuration:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_25">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_25_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_25_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_25_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_25_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_25_properties" class="tabpanel" id="_tabs_25_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">my.list[0].name</span>=<span class="hljs-string">my name</span>
<span class="hljs-meta">my.list[0].description</span>=<span class="hljs-string">my description</span>
<span class="hljs-comment">#---</span>
<span class="hljs-meta">spring.config.activate.on-profile</span>=<span class="hljs-string">dev</span>
<span class="hljs-meta">my.list[0].name</span>=<span class="hljs-string">my another name</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_25_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_25_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">my:</span>
  <span class="hljs-attr">list:</span>
  <span class="hljs-bullet">-</span> <span class="hljs-attr">name:</span> <span class="hljs-string">"my name"</span>
    <span class="hljs-attr">description:</span> <span class="hljs-string">"my description"</span>
<span class="hljs-meta">---</span>
<span class="hljs-attr">spring:</span>
  <span class="hljs-attr">config:</span>
    <span class="hljs-attr">activate:</span>
      <span class="hljs-attr">on-profile:</span> <span class="hljs-string">"dev"</span>
<span class="hljs-attr">my:</span>
  <span class="hljs-attr">list:</span>
  <span class="hljs-bullet">-</span> <span class="hljs-attr">name:</span> <span class="hljs-string">"my another name"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If the <code>dev</code> profile is not active, <code>MyProperties.list</code> contains one <code>MyPojo</code> entry, as previously defined.
If the <code>dev</code> profile is enabled, however, the <code>list</code> <em>still</em> contains only one entry (with a name of <code>my another name</code> and a description of <code>null</code>).
This configuration <em>does not</em> add a second <code>MyPojo</code> instance to the list, and it does not merge the items.</p>
</div>
<div class="paragraph">
<p>When a <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" target="_blank"><code>List</code></a> is specified in multiple profiles, the one with the highest priority (and only that one) is used.
Consider the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_26">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_26_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_26_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_26_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_26_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_26_properties" class="tabpanel" id="_tabs_26_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">my.list[0].name</span>=<span class="hljs-string">my name</span>
<span class="hljs-meta">my.list[0].description</span>=<span class="hljs-string">my description</span>
<span class="hljs-meta">my.list[1].name</span>=<span class="hljs-string">another name</span>
<span class="hljs-meta">my.list[1].description</span>=<span class="hljs-string">another description</span>
<span class="hljs-comment">#---</span>
<span class="hljs-meta">spring.config.activate.on-profile</span>=<span class="hljs-string">dev</span>
<span class="hljs-meta">my.list[0].name</span>=<span class="hljs-string">my another name</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_26_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_26_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">my:</span>
  <span class="hljs-attr">list:</span>
  <span class="hljs-bullet">-</span> <span class="hljs-attr">name:</span> <span class="hljs-string">"my name"</span>
    <span class="hljs-attr">description:</span> <span class="hljs-string">"my description"</span>
  <span class="hljs-bullet">-</span> <span class="hljs-attr">name:</span> <span class="hljs-string">"another name"</span>
    <span class="hljs-attr">description:</span> <span class="hljs-string">"another description"</span>
<span class="hljs-meta">---</span>
<span class="hljs-attr">spring:</span>
  <span class="hljs-attr">config:</span>
    <span class="hljs-attr">activate:</span>
      <span class="hljs-attr">on-profile:</span> <span class="hljs-string">"dev"</span>
<span class="hljs-attr">my:</span>
  <span class="hljs-attr">list:</span>
  <span class="hljs-bullet">-</span> <span class="hljs-attr">name:</span> <span class="hljs-string">"my another name"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>In the preceding example, if the <code>dev</code> profile is active, <code>MyProperties.list</code> contains <em>one</em> <code>MyPojo</code> entry (with a name of <code>my another name</code> and a description of <code>null</code>).
For YAML, both comma-separated lists and YAML lists can be used for completely overriding the contents of the list.</p>
</div>
<div class="paragraph">
<p>For <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" target="_blank"><code>Map</code></a> properties, you can bind with property values drawn from multiple sources.
However, for the same property in multiple sources, the one with the highest priority is used.
The following example exposes a <code>Map&lt;String, MyPojo&gt;</code> from <code>MyProperties</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_27">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_27_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_27_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_27_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_27_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_27_java" class="tabpanel" id="_tabs_27_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.LinkedHashMap;
<span class="hljs-keyword">import</span> java.util.Map;

<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"my"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> Map&lt;String, MyPojo&gt; map = <span class="hljs-keyword">new</span> LinkedHashMap&lt;&gt;();

	<span class="hljs-function"><span class="hljs-keyword">public</span> Map&lt;String, MyPojo&gt; <span class="hljs-title">getMap</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.map;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_27_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_27_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"my"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-keyword">val</span> map: Map&lt;String, MyPojo&gt; = LinkedHashMap()

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Consider the following configuration:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_28">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_28_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_28_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_28_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_28_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_28_properties" class="tabpanel" id="_tabs_28_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">my.map.key1.name</span>=<span class="hljs-string">my name 1</span>
<span class="hljs-meta">my.map.key1.description</span>=<span class="hljs-string">my description 1</span>
<span class="hljs-comment">#---</span>
<span class="hljs-meta">spring.config.activate.on-profile</span>=<span class="hljs-string">dev</span>
<span class="hljs-meta">my.map.key1.name</span>=<span class="hljs-string">dev name 1</span>
<span class="hljs-meta">my.map.key2.name</span>=<span class="hljs-string">dev name 2</span>
<span class="hljs-meta">my.map.key2.description</span>=<span class="hljs-string">dev description 2</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_28_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_28_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">my:</span>
  <span class="hljs-attr">map:</span>
    <span class="hljs-attr">key1:</span>
      <span class="hljs-attr">name:</span> <span class="hljs-string">"my name 1"</span>
      <span class="hljs-attr">description:</span> <span class="hljs-string">"my description 1"</span>
<span class="hljs-meta">---</span>
<span class="hljs-attr">spring:</span>
  <span class="hljs-attr">config:</span>
    <span class="hljs-attr">activate:</span>
      <span class="hljs-attr">on-profile:</span> <span class="hljs-string">"dev"</span>
<span class="hljs-attr">my:</span>
  <span class="hljs-attr">map:</span>
    <span class="hljs-attr">key1:</span>
      <span class="hljs-attr">name:</span> <span class="hljs-string">"dev name 1"</span>
    <span class="hljs-attr">key2:</span>
      <span class="hljs-attr">name:</span> <span class="hljs-string">"dev name 2"</span>
      <span class="hljs-attr">description:</span> <span class="hljs-string">"dev description 2"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If the <code>dev</code> profile is not active, <code>MyProperties.map</code> contains one entry with key <code>key1</code> (with a name of <code>my name 1</code> and a description of <code>my description 1</code>).
If the <code>dev</code> profile is enabled, however, <code>map</code> contains two entries with keys <code>key1</code> (with a name of <code>dev name 1</code> and a description of <code>my description 1</code>) and <code>key2</code> (with a name of <code>dev name 2</code> and a description of <code>dev description 2</code>).</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The preceding merging rules apply to properties from all property sources, and not just files.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.typesafe-configuration-properties.conversion"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.conversion"></a>Properties Conversion</h3>
<div class="paragraph">
<p>Spring Boot attempts to coerce the external application properties to the right type when it binds to the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.
If you need custom type conversion, you can provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/ConversionService.html"><code>ConversionService</code></a> bean (with a bean named <code>conversionService</code>) or custom property editors (through a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/config/CustomEditorConfigurer.html"><code>CustomEditorConfigurer</code></a> bean) or custom converters (with bean definitions annotated as <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationPropertiesBinding.html"><code>@ConfigurationPropertiesBinding</code></a>).</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>Beans used for property conversion are requested very early during the application lifecycle so make sure to limit the dependencies that your <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/ConversionService.html"><code>ConversionService</code></a> is using.
Typically, any dependency that you require may not be fully initialized at creation time.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You may want to rename your custom <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/ConversionService.html"><code>ConversionService</code></a> if it is not required for configuration keys coercion and only rely on custom converters qualified with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationPropertiesBinding.html"><code>@ConfigurationPropertiesBinding</code></a>.
When qualifying a <code>@Bean</code> method with <code>@ConfigurationPropertiesBinding</code>, the method should be <code>static</code> to avoid “bean is not eligible for getting processed by all BeanPostProcessors” warnings.
</td>
</tr>
</tbody></table>
</div>
<div class="sect3">
<h4 id="features.external-config.typesafe-configuration-properties.conversion.durations"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.conversion.durations"></a>Converting Durations</h4>
<div class="paragraph">
<p>Spring Boot has dedicated support for expressing durations.
If you expose a <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/Duration.html" target="_blank"><code>Duration</code></a> property, the following formats in application properties are available:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>A regular <code>long</code> representation (using milliseconds as the default unit unless a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/convert/DurationUnit.html"><code>@DurationUnit</code></a> has been specified)</p>
</li>
<li>
<p>The standard ISO-8601 format <a class="external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/Duration.html#parse(java.lang.CharSequence)" target="_blank">used by </a><a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/Duration.html" target="_blank"><code>Duration</code></a></p>
</li>
<li>
<p>A more readable format where the value and the unit are coupled (<code>10s</code> means 10 seconds)</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Consider the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_29">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_29_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_29_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_29_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_29_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_29_java" class="tabpanel" id="_tabs_29_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.time.Duration;
<span class="hljs-keyword">import</span> java.time.temporal.ChronoUnit;

<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.convert.DurationUnit;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"my"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-meta">@DurationUnit</span>(ChronoUnit.SECONDS)
	<span class="hljs-keyword">private</span> Duration sessionTimeout = Duration.ofSeconds(<span class="hljs-number">30</span>);

	<span class="hljs-keyword">private</span> Duration readTimeout = Duration.ofMillis(<span class="hljs-number">1000</span>);

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// getters / setters...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> Duration <span class="hljs-title">getSessionTimeout</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.sessionTimeout;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setSessionTimeout</span><span class="hljs-params">(Duration sessionTimeout)</span> </span>{
		<span class="hljs-keyword">this</span>.sessionTimeout = sessionTimeout;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Duration <span class="hljs-title">getReadTimeout</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.readTimeout;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setReadTimeout</span><span class="hljs-params">(Duration readTimeout)</span> </span>{
		<span class="hljs-keyword">this</span>.readTimeout = readTimeout;
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_29_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_29_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.boot.convert.DurationUnit
<span class="hljs-keyword">import</span> java.time.Duration
<span class="hljs-keyword">import</span> java.time.temporal.ChronoUnit

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"my"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-meta">@DurationUnit(ChronoUnit.SECONDS)</span>
	<span class="hljs-keyword">var</span> sessionTimeout = Duration.ofSeconds(<span class="hljs-number">30</span>)

	<span class="hljs-keyword">var</span> readTimeout = Duration.ofMillis(<span class="hljs-number">1000</span>)

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>To specify a session timeout of 30 seconds, <code>30</code>, <code>PT30S</code> and <code>30s</code> are all equivalent.
A read timeout of 500ms can be specified in any of the following form: <code>500</code>, <code>PT0.5S</code> and <code>500ms</code>.</p>
</div>
<div class="paragraph">
<p>You can also use any of the supported units.
These are:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>ns</code> for nanoseconds</p>
</li>
<li>
<p><code>us</code> for microseconds</p>
</li>
<li>
<p><code>ms</code> for milliseconds</p>
</li>
<li>
<p><code>s</code> for seconds</p>
</li>
<li>
<p><code>m</code> for minutes</p>
</li>
<li>
<p><code>h</code> for hours</p>
</li>
<li>
<p><code>d</code> for days</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The default unit is milliseconds and can be overridden using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/convert/DurationUnit.html"><code>@DurationUnit</code></a> as illustrated in the sample above.</p>
</div>
<div class="paragraph">
<p>If you prefer to use constructor binding, the same properties can be exposed, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_30">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_30_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_30_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_30_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_30_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_30_java" class="tabpanel" id="_tabs_30_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.time.Duration;
<span class="hljs-keyword">import</span> java.time.temporal.ChronoUnit;

<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.bind.DefaultValue;
<span class="hljs-keyword">import</span> org.springframework.boot.convert.DurationUnit;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"my"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// fields...</span>
</span><span class="fold-block is-hidden-folded">	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> Duration sessionTimeout;

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> Duration readTimeout;

</span><span class="fold-block">	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyProperties</span><span class="hljs-params">(@DurationUnit(ChronoUnit.SECONDS)</span> @<span class="hljs-title">DefaultValue</span><span class="hljs-params">(<span class="hljs-string">"30s"</span>)</span> Duration sessionTimeout,
			@<span class="hljs-title">DefaultValue</span><span class="hljs-params">(<span class="hljs-string">"1000ms"</span>)</span> Duration readTimeout) </span>{
		<span class="hljs-keyword">this</span>.sessionTimeout = sessionTimeout;
		<span class="hljs-keyword">this</span>.readTimeout = readTimeout;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// getters...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> Duration <span class="hljs-title">getSessionTimeout</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.sessionTimeout;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Duration <span class="hljs-title">getReadTimeout</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.readTimeout;
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_30_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_30_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.bind.DefaultValue
<span class="hljs-keyword">import</span> org.springframework.boot.convert.DurationUnit
<span class="hljs-keyword">import</span> java.time.Duration
<span class="hljs-keyword">import</span> java.time.temporal.ChronoUnit

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"my"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span></span>(<span class="hljs-meta">@param:DurationUnit</span>(ChronoUnit.SECONDS) <span class="hljs-meta">@param:DefaultValue</span>(<span class="hljs-string">"30s"</span>) <span class="hljs-keyword">val</span> sessionTimeout: Duration,
		<span class="hljs-meta">@param:DefaultValue</span>(<span class="hljs-string">"1000ms"</span>) <span class="hljs-keyword">val</span> readTimeout: Duration)</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you are upgrading a <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" target="_blank"><code>Long</code></a> property, make sure to define the unit (using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/convert/DurationUnit.html"><code>@DurationUnit</code></a>) if it is not milliseconds.
Doing so gives a transparent upgrade path while supporting a much richer format.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect3">
<h4 id="features.external-config.typesafe-configuration-properties.conversion.periods"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.conversion.periods"></a>Converting Periods</h4>
<div class="paragraph">
<p>In addition to durations, Spring Boot can also work with <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/Period.html" target="_blank"><code>Period</code></a> type.
The following formats can be used in application properties:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>An regular <code>int</code> representation (using days as the default unit unless a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/convert/PeriodUnit.html"><code>@PeriodUnit</code></a> has been specified)</p>
</li>
<li>
<p>The standard ISO-8601 format <a class="external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/Period.html#parse(java.lang.CharSequence)" target="_blank">used by </a><a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/Period.html" target="_blank"><code>Period</code></a></p>
</li>
<li>
<p>A simpler format where the value and the unit pairs are coupled (<code>1y3d</code> means 1 year and 3 days)</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The following units are supported with the simple format:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>y</code> for years</p>
</li>
<li>
<p><code>m</code> for months</p>
</li>
<li>
<p><code>w</code> for weeks</p>
</li>
<li>
<p><code>d</code> for days</p>
</li>
</ul>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/Period.html" target="_blank"><code>Period</code></a> type never actually stores the number of weeks, it is a shortcut that means “7 days”.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect3">
<h4 id="features.external-config.typesafe-configuration-properties.conversion.data-sizes"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.conversion.data-sizes"></a>Converting Data Sizes</h4>
<div class="paragraph">
<p>Spring Framework has a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/util/unit/DataSize.html"><code>DataSize</code></a> value type that expresses a size in bytes.
If you expose a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/util/unit/DataSize.html"><code>DataSize</code></a> property, the following formats in application properties are available:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>A regular <code>long</code> representation (using bytes as the default unit unless a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/convert/DataSizeUnit.html"><code>@DataSizeUnit</code></a> has been specified)</p>
</li>
<li>
<p>A more readable format where the value and the unit are coupled (<code>10MB</code> means 10 megabytes)</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Consider the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_31">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_31_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_31_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_31_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_31_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_31_java" class="tabpanel" id="_tabs_31_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.convert.DataSizeUnit;
<span class="hljs-keyword">import</span> org.springframework.util.unit.DataSize;
<span class="hljs-keyword">import</span> org.springframework.util.unit.DataUnit;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"my"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-meta">@DataSizeUnit</span>(DataUnit.MEGABYTES)
	<span class="hljs-keyword">private</span> DataSize bufferSize = DataSize.ofMegabytes(<span class="hljs-number">2</span>);

	<span class="hljs-keyword">private</span> DataSize sizeThreshold = DataSize.ofBytes(<span class="hljs-number">512</span>);

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// getters/setters...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> DataSize <span class="hljs-title">getBufferSize</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.bufferSize;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setBufferSize</span><span class="hljs-params">(DataSize bufferSize)</span> </span>{
		<span class="hljs-keyword">this</span>.bufferSize = bufferSize;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> DataSize <span class="hljs-title">getSizeThreshold</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.sizeThreshold;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setSizeThreshold</span><span class="hljs-params">(DataSize sizeThreshold)</span> </span>{
		<span class="hljs-keyword">this</span>.sizeThreshold = sizeThreshold;
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_31_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_31_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.boot.convert.DataSizeUnit
<span class="hljs-keyword">import</span> org.springframework.util.unit.DataSize
<span class="hljs-keyword">import</span> org.springframework.util.unit.DataUnit

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"my"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-meta">@DataSizeUnit(DataUnit.MEGABYTES)</span>
	<span class="hljs-keyword">var</span> bufferSize = DataSize.ofMegabytes(<span class="hljs-number">2</span>)

	<span class="hljs-keyword">var</span> sizeThreshold = DataSize.ofBytes(<span class="hljs-number">512</span>)

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>To specify a buffer size of 10 megabytes, <code>10</code> and <code>10MB</code> are equivalent.
A size threshold of 256 bytes can be specified as <code>256</code> or <code>256B</code>.</p>
</div>
<div class="paragraph">
<p>You can also use any of the supported units.
These are:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>B</code> for bytes</p>
</li>
<li>
<p><code>KB</code> for kilobytes</p>
</li>
<li>
<p><code>MB</code> for megabytes</p>
</li>
<li>
<p><code>GB</code> for gigabytes</p>
</li>
<li>
<p><code>TB</code> for terabytes</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The default unit is bytes and can be overridden using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/convert/DataSizeUnit.html"><code>@DataSizeUnit</code></a> as illustrated in the sample above.</p>
</div>
<div class="paragraph">
<p>If you prefer to use constructor binding, the same properties can be exposed, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_32">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_32_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_32_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_32_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_32_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_32_java" class="tabpanel" id="_tabs_32_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.bind.DefaultValue;
<span class="hljs-keyword">import</span> org.springframework.boot.convert.DataSizeUnit;
<span class="hljs-keyword">import</span> org.springframework.util.unit.DataSize;
<span class="hljs-keyword">import</span> org.springframework.util.unit.DataUnit;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"my"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// fields...</span>
</span><span class="fold-block is-hidden-folded">	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> DataSize bufferSize;

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> DataSize sizeThreshold;

</span><span class="fold-block">	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyProperties</span><span class="hljs-params">(@DataSizeUnit(DataUnit.MEGABYTES)</span> @<span class="hljs-title">DefaultValue</span><span class="hljs-params">(<span class="hljs-string">"2MB"</span>)</span> DataSize bufferSize,
			@<span class="hljs-title">DefaultValue</span><span class="hljs-params">(<span class="hljs-string">"512B"</span>)</span> DataSize sizeThreshold) </span>{
		<span class="hljs-keyword">this</span>.bufferSize = bufferSize;
		<span class="hljs-keyword">this</span>.sizeThreshold = sizeThreshold;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// getters...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> DataSize <span class="hljs-title">getBufferSize</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.bufferSize;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> DataSize <span class="hljs-title">getSizeThreshold</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.sizeThreshold;
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_32_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_32_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.bind.DefaultValue
<span class="hljs-keyword">import</span> org.springframework.boot.convert.DataSizeUnit
<span class="hljs-keyword">import</span> org.springframework.util.unit.DataSize
<span class="hljs-keyword">import</span> org.springframework.util.unit.DataUnit

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"my"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span></span>(<span class="hljs-meta">@param:DataSizeUnit</span>(DataUnit.MEGABYTES) <span class="hljs-meta">@param:DefaultValue</span>(<span class="hljs-string">"2MB"</span>) <span class="hljs-keyword">val</span> bufferSize: DataSize,
		<span class="hljs-meta">@param:DefaultValue</span>(<span class="hljs-string">"512B"</span>) <span class="hljs-keyword">val</span> sizeThreshold: DataSize)</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you are upgrading a <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" target="_blank"><code>Long</code></a> property, make sure to define the unit (using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/convert/DataSizeUnit.html"><code>@DataSizeUnit</code></a>) if it is not bytes.
Doing so gives a transparent upgrade path while supporting a much richer format.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect3">
<h4 id="features.external-config.typesafe-configuration-properties.conversion.base64"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.conversion.base64"></a>Converting Base64 Data</h4>
<div class="paragraph">
<p>Spring Boot supports resolving binary data that have been Base64 encoded.
If you expose a <code>Resource</code> property, the base64 encoded text can be provided as the value with a <code>base64:</code> prefix, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_33">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_33_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_33_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_33_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_33_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_33_properties" class="tabpanel" id="_tabs_33_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">my.property</span>=<span class="hljs-string">base64:SGVsbG8gV29ybGQ=</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_33_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_33_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">my:</span>
  <span class="hljs-attr">property:</span> <span class="hljs-string">base64:SGVsbG8gV29ybGQ=</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <code>Resource</code> property can also be used to provide the path to the resource, making it more versatile.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.typesafe-configuration-properties.validation"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.validation"></a>@ConfigurationProperties Validation</h3>
<div class="paragraph">
<p>Spring Boot attempts to validate <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> classes whenever they are annotated with Spring’s <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/validation/annotation/Validated.html"><code>@Validated</code></a> annotation.
You can use JSR-303 <code>jakarta.validation</code> constraint annotations directly on your configuration class.
To do so, ensure that a compliant JSR-303 implementation is on your classpath and then add constraint annotations to your fields, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_34">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_34_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_34_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_34_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_34_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_34_java" class="tabpanel" id="_tabs_34_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.net.InetAddress;

<span class="hljs-keyword">import</span> jakarta.validation.constraints.NotNull;

<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.validation.annotation.Validated;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"my.service"</span>)
<span class="hljs-meta">@Validated</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-meta">@NotNull</span>
	<span class="hljs-keyword">private</span> InetAddress remoteAddress;

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// getters/setters...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> InetAddress <span class="hljs-title">getRemoteAddress</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.remoteAddress;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setRemoteAddress</span><span class="hljs-params">(InetAddress remoteAddress)</span> </span>{
		<span class="hljs-keyword">this</span>.remoteAddress = remoteAddress;
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_34_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_34_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> jakarta.validation.constraints.NotNull
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.validation.<span class="hljs-keyword">annotation</span>.Validated
<span class="hljs-keyword">import</span> java.net.InetAddress

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"my.service"</span>)</span>
<span class="hljs-meta">@Validated</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-keyword">var</span> remoteAddress: <span class="hljs-meta">@NotNull</span> InetAddress? = <span class="hljs-literal">null</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can also trigger validation by annotating the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> method that creates the configuration properties with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/validation/annotation/Validated.html"><code>@Validated</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>To cascade validation to nested properties the associated field must be annotated with <a class="apiref external" href="https://jakarta.ee/specifications/bean-validation/3.0/apidocs/jakarta/validation/Valid.html" target="_blank"><code>@Valid</code></a>.
The following example builds on the preceding <code>MyProperties</code> example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_35">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_35_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_35_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_35_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_35_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_35_java" class="tabpanel" id="_tabs_35_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.net.InetAddress;

<span class="hljs-keyword">import</span> jakarta.validation.Valid;
<span class="hljs-keyword">import</span> jakarta.validation.constraints.NotEmpty;
<span class="hljs-keyword">import</span> jakarta.validation.constraints.NotNull;

<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.validation.annotation.Validated;

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"my.service"</span>)
<span class="hljs-meta">@Validated</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-meta">@NotNull</span>
	<span class="hljs-keyword">private</span> InetAddress remoteAddress;

	<span class="hljs-meta">@Valid</span>
	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> Security security = <span class="hljs-keyword">new</span> Security();

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// getters/setters...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> InetAddress <span class="hljs-title">getRemoteAddress</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.remoteAddress;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setRemoteAddress</span><span class="hljs-params">(InetAddress remoteAddress)</span> </span>{
		<span class="hljs-keyword">this</span>.remoteAddress = remoteAddress;
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Security <span class="hljs-title">getSecurity</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.security;
	}

</span><span class="fold-block">	<span class="hljs-keyword">public</span> <span class="hljs-keyword">static</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Security</span> </span>{

		<span class="hljs-meta">@NotEmpty</span>
		<span class="hljs-keyword">private</span> String username;

</span><span class="fold-block is-hidden-unfolded">		<span class="hljs-comment">// getters/setters...</span>

</span><span class="fold-block is-hidden-folded">		<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">getUsername</span><span class="hljs-params">()</span> </span>{
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.username;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">setUsername</span><span class="hljs-params">(String username)</span> </span>{
			<span class="hljs-keyword">this</span>.username = username;
		}

</span><span class="fold-block">	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_35_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_35_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> jakarta.validation.Valid
<span class="hljs-keyword">import</span> jakarta.validation.constraints.NotEmpty
<span class="hljs-keyword">import</span> jakarta.validation.constraints.NotNull
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.validation.<span class="hljs-keyword">annotation</span>.Validated
<span class="hljs-keyword">import</span> java.net.InetAddress

</span><span class="fold-block"><span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"my.service"</span>)</span>
<span class="hljs-meta">@Validated</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyProperties</span> </span>{

	<span class="hljs-keyword">var</span> remoteAddress: <span class="hljs-meta">@NotNull</span> InetAddress? = <span class="hljs-literal">null</span>

	<span class="hljs-meta">@Valid</span>
	<span class="hljs-keyword">val</span> security = Security()

	<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Security</span> </span>{

		<span class="hljs-meta">@NotEmpty</span>
		<span class="hljs-keyword">var</span> username: String? = <span class="hljs-literal">null</span>

	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also add a custom Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/validation/Validator.html"><code>Validator</code></a> by creating a bean definition called <code>configurationPropertiesValidator</code>.
The <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> method should be declared <code>static</code>.
The configuration properties validator is created very early in the application’s lifecycle, and declaring the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> method as static lets the bean be created without having to instantiate the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> class.
Doing so avoids any problems that may be caused by early instantiation.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The <code>spring-boot-actuator</code> module includes an endpoint that exposes all <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> beans.
Point your web browser to <code>/actuator/configprops</code> or use the equivalent JMX endpoint.
See the <a class="xref page" href="../actuator/endpoints.html">Production ready features</a> section for details.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="features.external-config.typesafe-configuration-properties.vs-value-annotation"><a class="anchor" href="#features.external-config.typesafe-configuration-properties.vs-value-annotation"></a>@ConfigurationProperties vs. @Value</h3>
<div class="paragraph">
<p>The <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Value.html"><code>@Value</code></a> annotation is a core container feature, and it does not provide the same features as type-safe configuration properties.
The following table summarizes the features that are supported by <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Value.html"><code>@Value</code></a>:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;"/>
<col style="width: 25%;"/>
<col style="width: 25%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Feature</th>
<th class="tableblock halign-left valign-top"><code>@ConfigurationProperties</code></th>
<th class="tableblock halign-left valign-top"><code>@Value</code></th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#features.external-config.typesafe-configuration-properties.relaxed-binding">Relaxed binding</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Yes</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Limited (see <a href="#features.external-config.typesafe-configuration-properties.vs-value-annotation.note">note below</a>)</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="xref page" href="../../specification/configuration-metadata/index.html">Meta-data support</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Yes</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">No</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>SpEL</code> evaluation</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">No</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Yes</p></td>
</tr>
</tbody>
</table>
<div class="admonitionblock note" id="features.external-config.typesafe-configuration-properties.vs-value-annotation.note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>If you do want to use <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Value.html"><code>@Value</code></a>, we recommend that you refer to property names using their canonical form (kebab-case using only lowercase letters).
This will allow Spring Boot to use the same logic as it does when <a href="#features.external-config.typesafe-configuration-properties.relaxed-binding">relaxed binding</a> <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a>.</p>
</div>
<div class="paragraph">
<p>For example, <code>@Value("${demo.item-price}")</code> will pick up <code>demo.item-price</code> and <code>demo.itemPrice</code> forms from the <code>application.properties</code> file, as well as <code>DEMO_ITEMPRICE</code> from the system environment.
If you used <code>@Value("${demo.itemPrice}")</code> instead, <code>demo.item-price</code> and <code>DEMO_ITEMPRICE</code> would not be considered.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If you define a set of configuration keys for your own components, we recommend you group them in a POJO annotated with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationProperties.html"><code>@ConfigurationProperties</code></a>.
Doing so will provide you with structured, type-safe object that you can inject into your own beans.</p>
</div>
<div class="paragraph">
<p><code>SpEL</code> expressions from  <a href="#features.external-config.files">application property files</a> are not processed at time of parsing these files and populating the environment.
However, it is possible to write a <code>SpEL</code> expression in <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Value.html"><code>@Value</code></a>.
If the value of a property from an application property file is a <code>SpEL</code> expression, it will be evaluated when consumed through <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Value.html"><code>@Value</code></a>.</p>
</div>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="spring-application.html">SpringApplication</a></span>
<span class="next"><a href="profiles.html">Profiles</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../reference/features/external-config.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="external-config.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../3.3/reference/features/external-config.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../4.0-SNAPSHOT/reference/features/external-config.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.5-SNAPSHOT/reference/features/external-config.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.4-SNAPSHOT/reference/features/external-config.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.3-SNAPSHOT/reference/features/external-config.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>