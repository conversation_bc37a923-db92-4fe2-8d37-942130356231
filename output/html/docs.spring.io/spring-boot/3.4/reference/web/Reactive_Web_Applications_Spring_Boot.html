<!DOCTYPE html>
<html><head><title>Reactive Web Applications :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/reference/web/reactive.html"/><meta content="2025-06-04T16:00:19.912042" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Reactive Web Applications">
<div class="toc-menu"><h3>Reactive Web Applications</h3><ul><li data-level="1"><a href="#web.reactive.webflux">The “Spring WebFlux Framework”</a></li><li data-level="2"><a href="#web.reactive.webflux.auto-configuration">Spring WebFlux Auto-configuration</a></li><li data-level="2"><a href="#web.reactive.webflux.conversion-service">Spring WebFlux Conversion Service</a></li><li data-level="2"><a href="#web.reactive.webflux.httpcodecs">HTTP Codecs with HttpMessageReaders and HttpMessageWriters</a></li><li data-level="2"><a href="#web.reactive.webflux.static-content">Static Content</a></li><li data-level="2"><a href="#web.reactive.webflux.welcome-page">Welcome Page</a></li><li data-level="2"><a href="#web.reactive.webflux.template-engines">Template Engines</a></li><li data-level="2"><a href="#web.reactive.webflux.error-handling">Error Handling</a></li><li data-level="2"><a href="#web.reactive.webflux.web-filters">Web Filters</a></li><li data-level="1"><a href="#web.reactive.reactive-server">Embedded Reactive Server Support</a></li><li data-level="2"><a href="#web.reactive.reactive-server.customizing">Customizing Reactive Servers</a></li><li data-level="1"><a href="#web.reactive.reactive-server-resources-configuration">Reactive Server Resources Configuration</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/web/reactive.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../index.html">Spring Boot</a></li>
<li><a href="../index.html">Reference</a></li>
<li><a href="index.html">Web</a></li>
<li><a href="reactive.html">Reactive Web Applications</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../reference/web/reactive.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Reactive Web Applications</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Reactive Web Applications</h3><ul><li data-level="1"><a href="#web.reactive.webflux">The “Spring WebFlux Framework”</a></li><li data-level="2"><a href="#web.reactive.webflux.auto-configuration">Spring WebFlux Auto-configuration</a></li><li data-level="2"><a href="#web.reactive.webflux.conversion-service">Spring WebFlux Conversion Service</a></li><li data-level="2"><a href="#web.reactive.webflux.httpcodecs">HTTP Codecs with HttpMessageReaders and HttpMessageWriters</a></li><li data-level="2"><a href="#web.reactive.webflux.static-content">Static Content</a></li><li data-level="2"><a href="#web.reactive.webflux.welcome-page">Welcome Page</a></li><li data-level="2"><a href="#web.reactive.webflux.template-engines">Template Engines</a></li><li data-level="2"><a href="#web.reactive.webflux.error-handling">Error Handling</a></li><li data-level="2"><a href="#web.reactive.webflux.web-filters">Web Filters</a></li><li data-level="1"><a href="#web.reactive.reactive-server">Embedded Reactive Server Support</a></li><li data-level="2"><a href="#web.reactive.reactive-server.customizing">Customizing Reactive Servers</a></li><li data-level="1"><a href="#web.reactive.reactive-server-resources-configuration">Reactive Server Resources Configuration</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot simplifies development of reactive web applications by providing auto-configuration for Spring Webflux.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="web.reactive.webflux"><a class="anchor" href="#web.reactive.webflux"></a>The “Spring WebFlux Framework”</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring WebFlux is the new reactive web framework introduced in Spring Framework 5.0.
Unlike Spring MVC, it does not require the servlet API, is fully asynchronous and non-blocking, and implements the <a class="external" href="https://www.reactive-streams.org/" target="_blank">Reactive Streams</a> specification through <a class="external" href="https://projectreactor.io/" target="_blank">the Reactor project</a>.</p>
</div>
<div class="paragraph">
<p>Spring WebFlux comes in two flavors: functional and annotation-based.
The annotation-based one is quite close to the Spring MVC model, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_1_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_1_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_1_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_java" class="tabpanel" id="_tabs_1_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> reactor.core.publisher.Flux;
<span class="hljs-keyword">import</span> reactor.core.publisher.Mono;

<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.DeleteMapping;
<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.GetMapping;
<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.PathVariable;
<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.RequestMapping;
<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.RestController;

</span><span class="fold-block"><span class="hljs-meta">@RestController</span>
<span class="hljs-meta">@RequestMapping</span>(<span class="hljs-string">"/users"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestController</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> UserRepository userRepository;

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> CustomerRepository customerRepository;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyRestController</span><span class="hljs-params">(UserRepository userRepository, CustomerRepository customerRepository)</span> </span>{
		<span class="hljs-keyword">this</span>.userRepository = userRepository;
		<span class="hljs-keyword">this</span>.customerRepository = customerRepository;
	}

	<span class="hljs-meta">@GetMapping</span>(<span class="hljs-string">"/{userId}"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> Mono&lt;User&gt; <span class="hljs-title">getUser</span><span class="hljs-params">(@PathVariable Long userId)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.userRepository.findById(userId);
	}

	<span class="hljs-meta">@GetMapping</span>(<span class="hljs-string">"/{userId}/customers"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> Flux&lt;Customer&gt; <span class="hljs-title">getUserCustomers</span><span class="hljs-params">(@PathVariable Long userId)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.userRepository.findById(userId).flatMapMany(<span class="hljs-keyword">this</span>.customerRepository::findByUser);
	}

	<span class="hljs-meta">@DeleteMapping</span>(<span class="hljs-string">"/{userId}"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> Mono&lt;Void&gt; <span class="hljs-title">deleteUser</span><span class="hljs-params">(@PathVariable Long userId)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.userRepository.deleteById(userId);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_1_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.DeleteMapping
<span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.GetMapping
<span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.PathVariable
<span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.RequestMapping
<span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.RestController
<span class="hljs-keyword">import</span> reactor.core.publisher.Flux
<span class="hljs-keyword">import</span> reactor.core.publisher.Mono

</span><span class="fold-block"><span class="hljs-meta">@RestController</span>
<span class="hljs-meta">@RequestMapping(<span class="hljs-meta-string">"/users"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestController</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> userRepository: UserRepository, <span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> customerRepository: CustomerRepository) {

	<span class="hljs-meta">@GetMapping(<span class="hljs-meta-string">"/{userId}"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getUser</span><span class="hljs-params">(<span class="hljs-meta">@PathVariable</span> userId: <span class="hljs-type">Long</span>)</span></span>: Mono&lt;User?&gt; {
		<span class="hljs-keyword">return</span> userRepository.findById(userId)
	}

	<span class="hljs-meta">@GetMapping(<span class="hljs-meta-string">"/{userId}/customers"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getUserCustomers</span><span class="hljs-params">(<span class="hljs-meta">@PathVariable</span> userId: <span class="hljs-type">Long</span>)</span></span>: Flux&lt;Customer&gt; {
		<span class="hljs-keyword">return</span> userRepository.findById(userId).flatMapMany { user: User? -&gt;
			customerRepository.findByUser(user)
		}
	}

	<span class="hljs-meta">@DeleteMapping(<span class="hljs-meta-string">"/{userId}"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">deleteUser</span><span class="hljs-params">(<span class="hljs-meta">@PathVariable</span> userId: <span class="hljs-type">Long</span>)</span></span>: Mono&lt;<span class="hljs-built_in">Void</span>&gt; {
		<span class="hljs-keyword">return</span> userRepository.deleteById(userId)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>WebFlux is part of the Spring Framework and detailed information is available in its <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webflux.html">reference documentation</a>.</p>
</div>
<div class="paragraph">
<p>“WebFlux.fn”, the functional variant, separates the routing configuration from the actual handling of the requests, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_2_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_2_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_2_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_java" class="tabpanel" id="_tabs_2_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.http.MediaType;
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.RequestPredicate;
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.RouterFunction;
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.ServerResponse;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.web.reactive.function.server.RequestPredicates.accept;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.web.reactive.function.server.RouterFunctions.route;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRoutingConfiguration</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">static</span> <span class="hljs-keyword">final</span> RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> RouterFunction&lt;ServerResponse&gt; <span class="hljs-title">monoRouterFunction</span><span class="hljs-params">(MyUserHandler userHandler)</span> </span>{
		<span class="hljs-keyword">return</span> route()
				.GET(<span class="hljs-string">"/{user}"</span>, ACCEPT_JSON, userHandler::getUser)
				.GET(<span class="hljs-string">"/{user}/customers"</span>, ACCEPT_JSON, userHandler::getUserCustomers)
				.DELETE(<span class="hljs-string">"/{user}"</span>, ACCEPT_JSON, userHandler::deleteUser)
				.build();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_2_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.http.MediaType
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.RequestPredicates.DELETE
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.RequestPredicates.GET
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.RequestPredicates.accept
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.RouterFunction
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.RouterFunctions
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.ServerResponse

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRoutingConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">monoRouterFunction</span><span class="hljs-params">(userHandler: <span class="hljs-type">MyUserHandler</span>)</span></span>: RouterFunction&lt;ServerResponse&gt; {
		<span class="hljs-keyword">return</span> RouterFunctions.route(
			GET(<span class="hljs-string">"/{user}"</span>).and(ACCEPT_JSON), userHandler::getUser).andRoute(
			GET(<span class="hljs-string">"/{user}/customers"</span>).and(ACCEPT_JSON), userHandler::getUserCustomers).andRoute(
			DELETE(<span class="hljs-string">"/{user}"</span>).and(ACCEPT_JSON), userHandler::deleteUser)
	}

	<span class="hljs-keyword">companion</span> <span class="hljs-keyword">object</span> {
		<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> ACCEPT_JSON = accept(MediaType.APPLICATION_JSON)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_3_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_3_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_3_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_java" class="tabpanel" id="_tabs_3_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> reactor.core.publisher.Mono;

<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.ServerRequest;
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.ServerResponse;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyUserHandler</span> </span>{

	<span class="hljs-function"><span class="hljs-keyword">public</span> Mono&lt;ServerResponse&gt; <span class="hljs-title">getUser</span><span class="hljs-params">(ServerRequest request)</span> </span>{
		...
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Mono&lt;ServerResponse&gt; <span class="hljs-title">getUserCustomers</span><span class="hljs-params">(ServerRequest request)</span> </span>{
		...
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Mono&lt;ServerResponse&gt; <span class="hljs-title">deleteUser</span><span class="hljs-params">(ServerRequest request)</span> </span>{
		...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_3_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.stereotype.Component
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.ServerRequest
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.ServerResponse
<span class="hljs-keyword">import</span> reactor.core.publisher.Mono

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyUserHandler</span> </span>{

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getUser</span><span class="hljs-params">(request: <span class="hljs-type">ServerRequest</span>?)</span></span>: Mono&lt;ServerResponse&gt; {
		...
	}

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getUserCustomers</span><span class="hljs-params">(request: <span class="hljs-type">ServerRequest</span>?)</span></span>: Mono&lt;ServerResponse&gt; {
		...
	}

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">deleteUser</span><span class="hljs-params">(request: <span class="hljs-type">ServerRequest</span>?)</span></span>: Mono&lt;ServerResponse&gt; {
		...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>“WebFlux.fn” is part of the Spring Framework and detailed information is available in its <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webflux-functional.html">reference documentation</a>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can define as many <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/server/RouterFunction.html"><code>RouterFunction</code></a> beans as you like to modularize the definition of the router.
Beans can be ordered if you need to apply a precedence.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>To get started, add the <code>spring-boot-starter-webflux</code> module to your application.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Adding both <code>spring-boot-starter-web</code> and <code>spring-boot-starter-webflux</code> modules in your application results in Spring Boot auto-configuring Spring MVC, not WebFlux.
This behavior has been chosen because many Spring developers add <code>spring-boot-starter-webflux</code> to their Spring MVC application to use the reactive <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a>.
You can still enforce your choice by setting the chosen application type to <code>SpringApplication.setWebApplicationType(WebApplicationType.REACTIVE)</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="web.reactive.webflux.auto-configuration"><a class="anchor" href="#web.reactive.webflux.auto-configuration"></a>Spring WebFlux Auto-configuration</h3>
<div class="paragraph">
<p>Spring Boot provides auto-configuration for Spring WebFlux that works well with most applications.</p>
</div>
<div class="paragraph">
<p>The auto-configuration adds the following features on top of Spring’s defaults:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Configuring codecs for <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/codec/HttpMessageReader.html"><code>HttpMessageReader</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/codec/HttpMessageWriter.html"><code>HttpMessageWriter</code></a> instances (described <a href="#web.reactive.webflux.httpcodecs">later in this document</a>).</p>
</li>
<li>
<p>Support for serving static resources, including support for WebJars (described <a class="xref page" href="servlet.html#web.servlet.spring-mvc.static-content">later in this document</a>).</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>If you want to keep Spring Boot WebFlux features and you want to add additional <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webflux/config.html">WebFlux configuration</a>, you can add your own <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> class of type <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/config/WebFluxConfigurer.html"><code>WebFluxConfigurer</code></a> but <strong>without</strong> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/config/EnableWebFlux.html"><code>@EnableWebFlux</code></a>.</p>
</div>
<div class="paragraph">
<p>If you want to add additional customization to the auto-configured <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/server/reactive/HttpHandler.html"><code>HttpHandler</code></a>, you can define beans of type <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/reactive/WebHttpHandlerBuilderCustomizer.html"><code>WebHttpHandlerBuilderCustomizer</code></a> and use them to modify the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/server/adapter/WebHttpHandlerBuilder.html"><code>WebHttpHandlerBuilder</code></a>.</p>
</div>
<div class="paragraph">
<p>If you want to take complete control of Spring WebFlux, you can add your own <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> annotated with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/config/EnableWebFlux.html"><code>@EnableWebFlux</code></a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="web.reactive.webflux.conversion-service"><a class="anchor" href="#web.reactive.webflux.conversion-service"></a>Spring WebFlux Conversion Service</h3>
<div class="paragraph">
<p>If you want to customize the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/ConversionService.html"><code>ConversionService</code></a> used by Spring WebFlux, you can provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/config/WebFluxConfigurer.html"><code>WebFluxConfigurer</code></a> bean with an <code>addFormatters</code> method.</p>
</div>
<div class="paragraph">
<p>Conversion can also be customized using the <code>spring.webflux.format.*</code> configuration properties.
When not configured, the following defaults are used:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 33.3333%;"/>
<col style="width: 33.3333%;"/>
<col style="width: 33.3334%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Property</th>
<th class="tableblock halign-left valign-top"><code>DateTimeFormatter</code></th>
<th class="tableblock halign-left valign-top">Formats</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring.webflux.format.date</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>ofLocalizedDate(FormatStyle.SHORT)</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java.util.Date</code> and <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDate.html" target="_blank"><code>LocalDate</code></a></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring.webflux.format.time</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>ofLocalizedTime(FormatStyle.SHORT)</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">java.time’s <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalTime.html" target="_blank"><code>LocalTime</code></a> and <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/OffsetTime.html" target="_blank"><code>OffsetTime</code></a></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring.webflux.format.date-time</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>ofLocalizedDateTime(FormatStyle.SHORT)</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">java.time’s <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" target="_blank"><code>LocalDateTime</code></a>, <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/OffsetDateTime.html" target="_blank"><code>OffsetDateTime</code></a>, and <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/ZonedDateTime.html" target="_blank"><code>ZonedDateTime</code></a></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="web.reactive.webflux.httpcodecs"><a class="anchor" href="#web.reactive.webflux.httpcodecs"></a>HTTP Codecs with HttpMessageReaders and HttpMessageWriters</h3>
<div class="paragraph">
<p>Spring WebFlux uses the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/codec/HttpMessageReader.html"><code>HttpMessageReader</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/codec/HttpMessageWriter.html"><code>HttpMessageWriter</code></a> interfaces to convert HTTP requests and responses.
They are configured with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/codec/CodecConfigurer.html"><code>CodecConfigurer</code></a> to have sensible defaults by looking at the libraries available in your classpath.</p>
</div>
<div class="paragraph">
<p>Spring Boot provides dedicated configuration properties for codecs, <code>spring.codec.*</code>.
It also applies further customization by using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/codec/CodecCustomizer.html"><code>CodecCustomizer</code></a> instances.
For example, <code>spring.jackson.*</code> configuration keys are applied to the Jackson codec.</p>
</div>
<div class="paragraph">
<p>If you need to add or customize codecs, you can create a custom <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/codec/CodecCustomizer.html"><code>CodecCustomizer</code></a> component, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_4_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_4_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_4_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_java" class="tabpanel" id="_tabs_4_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.codec.CodecCustomizer;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.http.codec.ServerSentEventHttpMessageReader;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCodecsConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> CodecCustomizer <span class="hljs-title">myCodecCustomizer</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> (configurer) -&gt; {
			configurer.registerDefaults(<span class="hljs-keyword">false</span>);
			configurer.customCodecs().register(<span class="hljs-keyword">new</span> ServerSentEventHttpMessageReader());
			<span class="hljs-comment">// ...</span>
		};
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_4_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.codec.CodecCustomizer
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.http.codec.CodecConfigurer
<span class="hljs-keyword">import</span> org.springframework.http.codec.ServerSentEventHttpMessageReader

</span><span class="fold-block"><span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCodecsConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">myCodecCustomizer</span><span class="hljs-params">()</span></span>: CodecCustomizer {
		<span class="hljs-keyword">return</span> CodecCustomizer { configurer: CodecConfigurer -&gt;
			configurer.registerDefaults(<span class="hljs-literal">false</span>)
			configurer.customCodecs().register(ServerSentEventHttpMessageReader())
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also leverage <a class="xref page" href="../features/json.html#features.json.jackson.custom-serializers-and-deserializers">Boot’s custom JSON serializers and deserializers</a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="web.reactive.webflux.static-content"><a class="anchor" href="#web.reactive.webflux.static-content"></a>Static Content</h3>
<div class="paragraph">
<p>By default, Spring Boot serves static content from a directory called <code>/static</code> (or <code>/public</code> or <code>/resources</code> or <code>/META-INF/resources</code>) in the classpath.
It uses the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/resource/ResourceWebHandler.html"><code>ResourceWebHandler</code></a> from Spring WebFlux so that you can modify that behavior by adding your own <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/config/WebFluxConfigurer.html"><code>WebFluxConfigurer</code></a> and overriding the <code>addResourceHandlers</code> method.</p>
</div>
<div class="paragraph">
<p>By default, resources are mapped on <code>/**</code>, but you can tune that by setting the <code>spring.webflux.static-path-pattern</code> property.
For instance, relocating all resources to <code>/resources/**</code> can be achieved as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_5_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_5_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_5_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_properties" class="tabpanel" id="_tabs_5_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.webflux.static-path-pattern</span>=<span class="hljs-string">/resources/**</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_5_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">webflux:</span>
    <span class="hljs-attr">static-path-pattern:</span> <span class="hljs-string">"/resources/**"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also customize the static resource locations by using <code>spring.web.resources.static-locations</code>.
Doing so replaces the default values with a list of directory locations.
If you do so, the default welcome page detection switches to your custom locations.
So, if there is an <code>index.html</code> in any of your locations on startup, it is the home page of the application.</p>
</div>
<div class="paragraph">
<p>In addition to the “standard” static resource locations listed earlier, a special case is made for <a class="external" href="https://www.webjars.org/" target="_blank">Webjars content</a>.
By default, any resources with a path in <code>/webjars/**</code> are served from jar files if they are packaged in the Webjars format.
The path can be customized with the <code>spring.webflux.webjars-path-pattern</code> property.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Spring WebFlux applications do not strictly depend on the servlet API, so they cannot be deployed as war files and do not use the <code>src/main/webapp</code> directory.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="web.reactive.webflux.welcome-page"><a class="anchor" href="#web.reactive.webflux.welcome-page"></a>Welcome Page</h3>
<div class="paragraph">
<p>Spring Boot supports both static and templated welcome pages.
It first looks for an <code>index.html</code> file in the configured static content locations.
If one is not found, it then looks for an <code>index</code> template.
If either is found, it is automatically used as the welcome page of the application.</p>
</div>
<div class="paragraph">
<p>This only acts as a fallback for actual index routes defined by the application.
The ordering is defined by the order of <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/HandlerMapping.html"><code>HandlerMapping</code></a> beans which is by default the following:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;"/>
<col style="width: 50%;"/>
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>org.springframework.web.reactive.function.server.support.RouterFunctionMapping</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Endpoints declared with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/server/RouterFunction.html"><code>RouterFunction</code></a> beans</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerMapping</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Endpoints declared in <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Controller.html"><code>@Controller</code></a> beans</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>RouterFunctionMapping</code> for the Welcome Page</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The welcome page support</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="web.reactive.webflux.template-engines"><a class="anchor" href="#web.reactive.webflux.template-engines"></a>Template Engines</h3>
<div class="paragraph">
<p>As well as REST web services, you can also use Spring WebFlux to serve dynamic HTML content.
Spring WebFlux supports a variety of templating technologies, including Thymeleaf, FreeMarker, and Mustache.</p>
</div>
<div class="paragraph">
<p>Spring Boot includes auto-configuration support for the following templating engines:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a class="external" href="https://freemarker.apache.org/docs/" target="_blank">FreeMarker</a></p>
</li>
<li>
<p><a class="external" href="https://www.thymeleaf.org" target="_blank">Thymeleaf</a></p>
</li>
<li>
<p><a class="external" href="https://mustache.github.io/" target="_blank">Mustache</a></p>
</li>
</ul>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Not all FreeMarker features are supported with WebFlux.
For more details, check the description of each property.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>When you use one of these templating engines with the default configuration, your templates are picked up automatically from <code>src/main/resources/templates</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="web.reactive.webflux.error-handling"><a class="anchor" href="#web.reactive.webflux.error-handling"></a>Error Handling</h3>
<div class="paragraph">
<p>Spring Boot provides a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/server/WebExceptionHandler.html"><code>WebExceptionHandler</code></a> that handles all errors in a sensible way.
Its position in the processing order is immediately before the handlers provided by WebFlux, which are considered last.
For machine clients, it produces a JSON response with details of the error, the HTTP status, and the exception message.
For browser clients, there is a “whitelabel” error handler that renders the same data in HTML format.
You can also provide your own HTML templates to display errors (see the <a href="#web.reactive.webflux.error-handling.error-pages">next section</a>).</p>
</div>
<div class="paragraph">
<p>Before customizing error handling in Spring Boot directly, you can leverage the <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webflux/ann-rest-exceptions.html">RFC 9457 Problem Details</a> support in Spring WebFlux.
Spring WebFlux can produce custom error messages with the <code>application/problem+json</code> media type, like:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-json hljs" data-lang="json">{
	<span class="hljs-attr">"type"</span>: <span class="hljs-string">"https://example.org/problems/unknown-project"</span>,
	<span class="hljs-attr">"title"</span>: <span class="hljs-string">"Unknown project"</span>,
	<span class="hljs-attr">"status"</span>: <span class="hljs-number">404</span>,
	<span class="hljs-attr">"detail"</span>: <span class="hljs-string">"No project found for id 'spring-unknown'"</span>,
	<span class="hljs-attr">"instance"</span>: <span class="hljs-string">"/projects/spring-unknown"</span>
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This support can be enabled by setting <code>spring.webflux.problemdetails.enabled</code> to <code>true</code>.</p>
</div>
<div class="paragraph">
<p>The first step to customizing this feature often involves using the existing mechanism but replacing or augmenting the error contents.
For that, you can add a bean of type <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/reactive/error/ErrorAttributes.html"><code>ErrorAttributes</code></a>.</p>
</div>
<div class="paragraph">
<p>To change the error handling behavior, you can implement <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/reactive/error/ErrorWebExceptionHandler.html"><code>ErrorWebExceptionHandler</code></a> and register a bean definition of that type.
Because an <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/reactive/error/ErrorWebExceptionHandler.html"><code>ErrorWebExceptionHandler</code></a> is quite low-level, Spring Boot also provides a convenient <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/reactive/error/AbstractErrorWebExceptionHandler.html"><code>AbstractErrorWebExceptionHandler</code></a> to let you handle errors in a WebFlux functional way, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_6_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_6_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_6_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_java" class="tabpanel" id="_tabs_6_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> reactor.core.publisher.Mono;

<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.web.WebProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.web.reactive.error.AbstractErrorWebExceptionHandler;
<span class="hljs-keyword">import</span> org.springframework.boot.web.reactive.error.ErrorAttributes;
<span class="hljs-keyword">import</span> org.springframework.context.ApplicationContext;
<span class="hljs-keyword">import</span> org.springframework.http.HttpStatus;
<span class="hljs-keyword">import</span> org.springframework.http.MediaType;
<span class="hljs-keyword">import</span> org.springframework.http.codec.ServerCodecConfigurer;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.RouterFunction;
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.RouterFunctions;
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.ServerRequest;
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.ServerResponse;
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.ServerResponse.BodyBuilder;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyErrorWebExceptionHandler</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">AbstractErrorWebExceptionHandler</span> </span>{

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyErrorWebExceptionHandler</span><span class="hljs-params">(ErrorAttributes errorAttributes, WebProperties webProperties,
			ApplicationContext applicationContext, ServerCodecConfigurer serverCodecConfigurer)</span> </span>{
		<span class="hljs-keyword">super</span>(errorAttributes, webProperties.getResources(), applicationContext);
		setMessageReaders(serverCodecConfigurer.getReaders());
		setMessageWriters(serverCodecConfigurer.getWriters());
	}

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">protected</span> RouterFunction&lt;ServerResponse&gt; <span class="hljs-title">getRoutingFunction</span><span class="hljs-params">(ErrorAttributes errorAttributes)</span> </span>{
		<span class="hljs-keyword">return</span> RouterFunctions.route(<span class="hljs-keyword">this</span>::acceptsXml, <span class="hljs-keyword">this</span>::handleErrorAsXml);
	}

	<span class="hljs-function"><span class="hljs-keyword">private</span> <span class="hljs-keyword">boolean</span> <span class="hljs-title">acceptsXml</span><span class="hljs-params">(ServerRequest request)</span> </span>{
		<span class="hljs-keyword">return</span> request.headers().accept().contains(MediaType.APPLICATION_XML);
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> Mono&lt;ServerResponse&gt; <span class="hljs-title">handleErrorAsXml</span><span class="hljs-params">(ServerRequest request)</span> </span>{
		BodyBuilder builder = ServerResponse.status(HttpStatus.INTERNAL_SERVER_ERROR);
		<span class="hljs-comment">// ... additional builder calls</span>
		<span class="hljs-keyword">return</span> builder.build();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_6_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.web.WebProperties
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.web.reactive.error.AbstractErrorWebExceptionHandler
<span class="hljs-keyword">import</span> org.springframework.boot.web.reactive.error.ErrorAttributes
<span class="hljs-keyword">import</span> org.springframework.context.ApplicationContext
<span class="hljs-keyword">import</span> org.springframework.http.HttpStatus
<span class="hljs-keyword">import</span> org.springframework.http.MediaType
<span class="hljs-keyword">import</span> org.springframework.http.codec.ServerCodecConfigurer
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.RouterFunction
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.RouterFunctions
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.ServerRequest
<span class="hljs-keyword">import</span> org.springframework.web.reactive.function.server.ServerResponse
<span class="hljs-keyword">import</span> reactor.core.publisher.Mono

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyErrorWebExceptionHandler</span></span>(
		errorAttributes: ErrorAttributes, webProperties: WebProperties,
		applicationContext: ApplicationContext, serverCodecConfigurer: ServerCodecConfigurer
) : AbstractErrorWebExceptionHandler(errorAttributes, webProperties.resources, applicationContext) {

	<span class="hljs-keyword">init</span> {
		setMessageReaders(serverCodecConfigurer.readers)
		setMessageWriters(serverCodecConfigurer.writers)
	}

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getRoutingFunction</span><span class="hljs-params">(errorAttributes: <span class="hljs-type">ErrorAttributes</span>)</span></span>: RouterFunction&lt;ServerResponse&gt; {
		<span class="hljs-keyword">return</span> RouterFunctions.route(<span class="hljs-keyword">this</span>::acceptsXml, <span class="hljs-keyword">this</span>::handleErrorAsXml)
	}

	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">acceptsXml</span><span class="hljs-params">(request: <span class="hljs-type">ServerRequest</span>)</span></span>: <span class="hljs-built_in">Boolean</span> {
		<span class="hljs-keyword">return</span> request.headers().accept().contains(MediaType.APPLICATION_XML)
	}

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">handleErrorAsXml</span><span class="hljs-params">(request: <span class="hljs-type">ServerRequest</span>)</span></span>: Mono&lt;ServerResponse&gt; {
		<span class="hljs-keyword">val</span> builder = ServerResponse.status(HttpStatus.INTERNAL_SERVER_ERROR)
		<span class="hljs-comment">// ... additional builder calls</span>
		<span class="hljs-keyword">return</span> builder.build()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>For a more complete picture, you can also subclass <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/reactive/error/DefaultErrorWebExceptionHandler.html"><code>DefaultErrorWebExceptionHandler</code></a> directly and override specific methods.</p>
</div>
<div class="paragraph">
<p>In some cases, errors handled at the controller level are not recorded by web observations or the <a class="xref page" href="../actuator/metrics.html#actuator.metrics.supported.spring-webflux">metrics infrastructure</a>.
Applications can ensure that such exceptions are recorded with the observations by <a href="https://docs.spring.io/spring-framework/reference/6.2/integration/observability.html#observability.http-server.reactive">setting the handled exception on the observation context</a>.</p>
</div>
<div class="sect3">
<h4 id="web.reactive.webflux.error-handling.error-pages"><a class="anchor" href="#web.reactive.webflux.error-handling.error-pages"></a>Custom Error Pages</h4>
<div class="paragraph">
<p>If you want to display a custom HTML error page for a given status code, you can add views that resolve from <code>error/*</code>, for example by adding files to a <code>/error</code> directory.
Error pages can either be static HTML (that is, added under any of the static resource directories) or built with templates.
The name of the file should be the exact status code, a status code series mask, or <code>error</code> for a default if nothing else matches.
Note that the path to the default error view is <code>error/error</code>, whereas with Spring MVC the default error view is <code>error</code>.</p>
</div>
<div class="paragraph">
<p>For example, to map <code>404</code> to a static HTML file, your directory structure would be as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-none hljs">src/
 +- main/
     +- java/
     |   + &lt;source code&gt;
     +- resources/
         +- public/
             +- error/
             |   +- 404.html
             +- &lt;other public assets&gt;</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>To map all <code>5xx</code> errors by using a Mustache template, your directory structure would be as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-none hljs">src/
 +- main/
     +- java/
     |   + &lt;source code&gt;
     +- resources/
         +- templates/
             +- error/
             |   +- 5xx.mustache
             +- &lt;other templates&gt;</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="web.reactive.webflux.web-filters"><a class="anchor" href="#web.reactive.webflux.web-filters"></a>Web Filters</h3>
<div class="paragraph">
<p>Spring WebFlux provides a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/server/WebFilter.html"><code>WebFilter</code></a> interface that can be implemented to filter HTTP request-response exchanges.
<a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/server/WebFilter.html"><code>WebFilter</code></a> beans found in the application context will be automatically used to filter each exchange.</p>
</div>
<div class="paragraph">
<p>Where the order of the filters is important they can implement <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/Ordered.html"><code>Ordered</code></a> or be annotated with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/annotation/Order.html"><code>@Order</code></a>.
Spring Boot auto-configuration may configure web filters for you.
When it does so, the orders shown in the following table will be used:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;"/>
<col style="width: 50%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Web Filter</th>
<th class="tableblock halign-left valign-top">Order</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-security/site/docs/6.4.x/api/org/springframework/security/web/server/WebFilterChainProxy.html"><code>WebFilterChainProxy</code></a> (Spring Security)</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>-100</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/web/exchanges/reactive/HttpExchangesWebFilter.html"><code>HttpExchangesWebFilter</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Ordered.LOWEST_PRECEDENCE - 10</code></p></td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="web.reactive.reactive-server"><a class="anchor" href="#web.reactive.reactive-server"></a>Embedded Reactive Server Support</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot includes support for the following embedded reactive web servers: Reactor Netty, Tomcat, Jetty, and Undertow.
Most developers use the appropriate starter to obtain a fully configured instance.
By default, the embedded server listens for HTTP requests on port 8080.</p>
</div>
<div class="sect2">
<h3 id="web.reactive.reactive-server.customizing"><a class="anchor" href="#web.reactive.reactive-server.customizing"></a>Customizing Reactive Servers</h3>
<div class="paragraph">
<p>Common reactive web server settings can be configured by using Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> properties.
Usually, you would define the properties in your <code>application.properties</code> or <code>application.yaml</code> file.</p>
</div>
<div class="paragraph">
<p>Common server settings include:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Network settings: Listen port for incoming HTTP requests (<code>server.port</code>), interface address to bind to (<code>server.address</code>), and so on.</p>
</li>
<li>
<p>Error management: Location of the error page (<code>server.error.path</code>) and so on.</p>
</li>
<li>
<p><a class="xref page" href="../../how-to/webserver.html#howto.webserver.configure-ssl">SSL</a></p>
</li>
<li>
<p><a class="xref page" href="../../how-to/webserver.html#howto.webserver.enable-response-compression">HTTP compression</a></p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Spring Boot tries as much as possible to expose common settings, but this is not always possible.
For those cases, dedicated namespaces such as <code>server.netty.*</code> offer server-specific customizations.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
See the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/ServerProperties.html"><code>ServerProperties</code></a> class for a complete list.
</td>
</tr>
</tbody></table>
</div>
<div class="sect3">
<h4 id="web.reactive.reactive-server.customizing.programmatic"><a class="anchor" href="#web.reactive.reactive-server.customizing.programmatic"></a>Programmatic Customization</h4>
<div class="paragraph">
<p>If you need to programmatically configure your reactive web server, you can register a Spring bean that implements the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/WebServerFactoryCustomizer.html"><code>WebServerFactoryCustomizer</code></a> interface.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/WebServerFactoryCustomizer.html"><code>WebServerFactoryCustomizer</code></a> provides access to the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/reactive/server/ConfigurableReactiveWebServerFactory.html"><code>ConfigurableReactiveWebServerFactory</code></a>, which includes numerous customization setter methods.
The following example shows programmatically setting the port:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_7_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_7_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_7_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_java" class="tabpanel" id="_tabs_7_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.reactive.server.ConfigurableReactiveWebServerFactory;
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebServerFactoryCustomizer</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">WebServerFactoryCustomizer</span>&lt;<span class="hljs-title">ConfigurableReactiveWebServerFactory</span>&gt; </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">customize</span><span class="hljs-params">(ConfigurableReactiveWebServerFactory server)</span> </span>{
		server.setPort(<span class="hljs-number">9000</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_7_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer
<span class="hljs-keyword">import</span> org.springframework.boot.web.reactive.server.ConfigurableReactiveWebServerFactory
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebServerFactoryCustomizer</span> : <span class="hljs-type">WebServerFactoryCustomizer</span>&lt;<span class="hljs-type">ConfigurableReactiveWebServerFactory</span>&gt; </span>{

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">customize</span><span class="hljs-params">(server: <span class="hljs-type">ConfigurableReactiveWebServerFactory</span>)</span></span> {
		server.setPort(<span class="hljs-number">9000</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/jetty/JettyReactiveWebServerFactory.html"><code>JettyReactiveWebServerFactory</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/netty/NettyReactiveWebServerFactory.html"><code>NettyReactiveWebServerFactory</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/tomcat/TomcatReactiveWebServerFactory.html"><code>TomcatReactiveWebServerFactory</code></a>, and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/undertow/UndertowReactiveWebServerFactory.html"><code>UndertowReactiveWebServerFactory</code></a> are dedicated variants of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/reactive/server/ConfigurableReactiveWebServerFactory.html"><code>ConfigurableReactiveWebServerFactory</code></a> that have additional customization setter methods for Jetty, Reactor Netty, Tomcat, and Undertow respectively.
The following example shows how to customize <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/netty/NettyReactiveWebServerFactory.html"><code>NettyReactiveWebServerFactory</code></a> that provides access to Reactor Netty-specific configuration options:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_8_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_8_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_8_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_java" class="tabpanel" id="_tabs_8_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.time.Duration;

<span class="hljs-keyword">import</span> org.springframework.boot.web.embedded.netty.NettyReactiveWebServerFactory;
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyNettyWebServerFactoryCustomizer</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">WebServerFactoryCustomizer</span>&lt;<span class="hljs-title">NettyReactiveWebServerFactory</span>&gt; </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">customize</span><span class="hljs-params">(NettyReactiveWebServerFactory factory)</span> </span>{
		factory.addServerCustomizers((server) -&gt; server.idleTimeout(Duration.ofSeconds(<span class="hljs-number">20</span>)));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_8_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.embedded.netty.NettyReactiveWebServerFactory
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component
<span class="hljs-keyword">import</span> java.time.Duration

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyNettyWebServerFactoryCustomizer</span> : <span class="hljs-type">WebServerFactoryCustomizer</span>&lt;<span class="hljs-type">NettyReactiveWebServerFactory</span>&gt; </span>{

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">customize</span><span class="hljs-params">(factory: <span class="hljs-type">NettyReactiveWebServerFactory</span>)</span></span> {
		factory.addServerCustomizers({ server -&gt; server.idleTimeout(Duration.ofSeconds(<span class="hljs-number">20</span>)) })
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect3">
<h4 id="web.reactive.reactive-server.customizing.direct"><a class="anchor" href="#web.reactive.reactive-server.customizing.direct"></a>Customizing ConfigurableReactiveWebServerFactory Directly</h4>
<div class="paragraph">
<p>For more advanced use cases that require you to extend from <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/reactive/server/ReactiveWebServerFactory.html"><code>ReactiveWebServerFactory</code></a>, you can expose a bean of such type yourself.</p>
</div>
<div class="paragraph">
<p>Setters are provided for many configuration options.
Several protected method “hooks” are also provided should you need to do something more exotic.
See the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/reactive/server/ConfigurableReactiveWebServerFactory.html"><code>ConfigurableReactiveWebServerFactory</code></a> API documentation for details.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Auto-configured customizers are still applied on your custom factory, so use that option carefully.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="web.reactive.reactive-server-resources-configuration"><a class="anchor" href="#web.reactive.reactive-server-resources-configuration"></a>Reactive Server Resources Configuration</h2>
<div class="sectionbody">
<div class="paragraph">
<p>When auto-configuring a Reactor Netty or Jetty server, Spring Boot will create specific beans that will provide HTTP resources to the server instance: <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/ReactorResourceFactory.html"><code>ReactorResourceFactory</code></a> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/reactive/JettyResourceFactory.html"><code>JettyResourceFactory</code></a>.</p>
</div>
<div class="paragraph">
<p>By default, those resources will be also shared with the Reactor Netty and Jetty clients for optimal performances, given:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>the same technology is used for server and client</p>
</li>
<li>
<p>the client instance is built using the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.Builder.html"><code>WebClient.Builder</code></a> bean auto-configured by Spring Boot</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Developers can override the resource configuration for Jetty and Reactor Netty by providing a custom <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/ReactorResourceFactory.html"><code>ReactorResourceFactory</code></a> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/reactive/JettyResourceFactory.html"><code>JettyResourceFactory</code></a> bean - this will be applied to both clients and servers.</p>
</div>
<div class="paragraph">
<p>You can learn more about the resource configuration on the client side in the <a class="xref page" href="../io/rest-client.html#io.rest-client.webclient.runtime">WebClient Runtime</a> section.</p>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="servlet.html">Servlet Web Applications</a></span>
<span class="next"><a href="graceful-shutdown.html">Graceful Shutdown</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../reference/web/reactive.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="reactive.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../3.3/reference/web/reactive.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../4.0-SNAPSHOT/reference/web/reactive.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.5-SNAPSHOT/reference/web/reactive.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.4-SNAPSHOT/reference/web/reactive.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.3-SNAPSHOT/reference/web/reactive.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>