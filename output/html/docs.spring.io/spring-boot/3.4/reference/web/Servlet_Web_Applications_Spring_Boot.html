<!DOCTYPE html>
<html><head><title>Servlet Web Applications :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/reference/web/servlet.html"/><meta content="2025-06-04T16:01:12.459877" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Web</a>
<ul class="nav-list">
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Servlet Web Applications">
<div class="toc-menu"><h3>Servlet Web Applications</h3><ul><li data-level="1"><a href="#web.servlet.spring-mvc">The “Spring Web MVC Framework”</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.auto-configuration">Spring MVC Auto-configuration</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.conversion-service">Spring MVC Conversion Service</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.message-converters">HttpMessageConverters</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.message-codes">MessageCodesResolver</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.static-content">Static Content</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.welcome-page">Welcome Page</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.favicon">Custom Favicon</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.content-negotiation">Path Matching and Content Negotiation</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.binding-initializer">ConfigurableWebBindingInitializer</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.template-engines">Template Engines</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.error-handling">Error Handling</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.cors">CORS Support</a></li><li data-level="1"><a href="#web.servlet.jersey">JAX-RS and Jersey</a></li><li data-level="1"><a href="#web.servlet.embedded-container">Embedded Servlet Container Support</a></li><li data-level="2"><a href="#web.servlet.embedded-container.servlets-filters-listeners">Servlets, Filters, and Listeners</a></li><li data-level="2"><a href="#web.servlet.embedded-container.context-initializer">Servlet Context Initialization</a></li><li data-level="2"><a href="#web.servlet.embedded-container.application-context">The ServletWebServerApplicationContext</a></li><li data-level="2"><a href="#web.servlet.embedded-container.customizing">Customizing Embedded Servlet Containers</a></li><li data-level="2"><a href="#web.servlet.embedded-container.jsp-limitations">JSP Limitations</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/web/servlet.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../index.html">Spring Boot</a></li>
<li><a href="../index.html">Reference</a></li>
<li><a href="index.html">Web</a></li>
<li><a href="servlet.html">Servlet Web Applications</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../reference/web/servlet.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Servlet Web Applications</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Servlet Web Applications</h3><ul><li data-level="1"><a href="#web.servlet.spring-mvc">The “Spring Web MVC Framework”</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.auto-configuration">Spring MVC Auto-configuration</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.conversion-service">Spring MVC Conversion Service</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.message-converters">HttpMessageConverters</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.message-codes">MessageCodesResolver</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.static-content">Static Content</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.welcome-page">Welcome Page</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.favicon">Custom Favicon</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.content-negotiation">Path Matching and Content Negotiation</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.binding-initializer">ConfigurableWebBindingInitializer</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.template-engines">Template Engines</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.error-handling">Error Handling</a></li><li data-level="2"><a href="#web.servlet.spring-mvc.cors">CORS Support</a></li><li data-level="1"><a href="#web.servlet.jersey">JAX-RS and Jersey</a></li><li data-level="1"><a href="#web.servlet.embedded-container">Embedded Servlet Container Support</a></li><li data-level="2"><a href="#web.servlet.embedded-container.servlets-filters-listeners">Servlets, Filters, and Listeners</a></li><li data-level="2"><a href="#web.servlet.embedded-container.context-initializer">Servlet Context Initialization</a></li><li data-level="2"><a href="#web.servlet.embedded-container.application-context">The ServletWebServerApplicationContext</a></li><li data-level="2"><a href="#web.servlet.embedded-container.customizing">Customizing Embedded Servlet Containers</a></li><li data-level="2"><a href="#web.servlet.embedded-container.jsp-limitations">JSP Limitations</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>If you want to build servlet-based web applications, you can take advantage of Spring Boot’s auto-configuration for Spring MVC or Jersey.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="web.servlet.spring-mvc"><a class="anchor" href="#web.servlet.spring-mvc"></a>The “Spring Web MVC Framework”</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc.html">Spring Web MVC framework</a> (often referred to as “Spring MVC”) is a rich “model view controller” web framework.
Spring MVC lets you create special <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Controller.html"><code>@Controller</code></a> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/annotation/RestController.html"><code>@RestController</code></a> beans to handle incoming HTTP requests.
Methods in your controller are mapped to HTTP by using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/annotation/RequestMapping.html"><code>@RequestMapping</code></a> annotations.</p>
</div>
<div class="paragraph">
<p>The following code shows a typical <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/annotation/RestController.html"><code>@RestController</code></a> that serves JSON data:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_1_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_1_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_1_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_java" class="tabpanel" id="_tabs_1_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.List;

<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.DeleteMapping;
<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.GetMapping;
<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.PathVariable;
<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.RequestMapping;
<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.RestController;

</span><span class="fold-block"><span class="hljs-meta">@RestController</span>
<span class="hljs-meta">@RequestMapping</span>(<span class="hljs-string">"/users"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestController</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> UserRepository userRepository;

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> CustomerRepository customerRepository;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyRestController</span><span class="hljs-params">(UserRepository userRepository, CustomerRepository customerRepository)</span> </span>{
		<span class="hljs-keyword">this</span>.userRepository = userRepository;
		<span class="hljs-keyword">this</span>.customerRepository = customerRepository;
	}

	<span class="hljs-meta">@GetMapping</span>(<span class="hljs-string">"/{userId}"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> User <span class="hljs-title">getUser</span><span class="hljs-params">(@PathVariable Long userId)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.userRepository.findById(userId).get();
	}

	<span class="hljs-meta">@GetMapping</span>(<span class="hljs-string">"/{userId}/customers"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> List&lt;Customer&gt; <span class="hljs-title">getUserCustomers</span><span class="hljs-params">(@PathVariable Long userId)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.userRepository.findById(userId).map(<span class="hljs-keyword">this</span>.customerRepository::findByUser).get();
	}

	<span class="hljs-meta">@DeleteMapping</span>(<span class="hljs-string">"/{userId}"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">deleteUser</span><span class="hljs-params">(@PathVariable Long userId)</span> </span>{
		<span class="hljs-keyword">this</span>.userRepository.deleteById(userId);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_1_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.DeleteMapping
<span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.GetMapping
<span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.PathVariable
<span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.RequestMapping
<span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.RestController


</span><span class="fold-block"><span class="hljs-meta">@RestController</span>
<span class="hljs-meta">@RequestMapping(<span class="hljs-meta-string">"/users"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRestController</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> userRepository: UserRepository, <span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> customerRepository: CustomerRepository) {

	<span class="hljs-meta">@GetMapping(<span class="hljs-meta-string">"/{userId}"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getUser</span><span class="hljs-params">(<span class="hljs-meta">@PathVariable</span> userId: <span class="hljs-type">Long</span>)</span></span>: User {
		<span class="hljs-keyword">return</span> userRepository.findById(userId).<span class="hljs-keyword">get</span>()
	}

	<span class="hljs-meta">@GetMapping(<span class="hljs-meta-string">"/{userId}/customers"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getUserCustomers</span><span class="hljs-params">(<span class="hljs-meta">@PathVariable</span> userId: <span class="hljs-type">Long</span>)</span></span>: List&lt;Customer&gt; {
		<span class="hljs-keyword">return</span> userRepository.findById(userId).map(customerRepository::findByUser).<span class="hljs-keyword">get</span>()
	}

	<span class="hljs-meta">@DeleteMapping(<span class="hljs-meta-string">"/{userId}"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">deleteUser</span><span class="hljs-params">(<span class="hljs-meta">@PathVariable</span> userId: <span class="hljs-type">Long</span>)</span></span> {
		userRepository.deleteById(userId)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>“WebMvc.fn”, the functional variant, separates the routing configuration from the actual handling of the requests, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_2_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_2_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_2_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_java" class="tabpanel" id="_tabs_2_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.http.MediaType;
<span class="hljs-keyword">import</span> org.springframework.web.servlet.function.RequestPredicate;
<span class="hljs-keyword">import</span> org.springframework.web.servlet.function.RouterFunction;
<span class="hljs-keyword">import</span> org.springframework.web.servlet.function.ServerResponse;

<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.web.servlet.function.RequestPredicates.accept;
<span class="hljs-keyword">import</span> <span class="hljs-keyword">static</span> org.springframework.web.servlet.function.RouterFunctions.route;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRoutingConfiguration</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">static</span> <span class="hljs-keyword">final</span> RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> RouterFunction&lt;ServerResponse&gt; <span class="hljs-title">routerFunction</span><span class="hljs-params">(MyUserHandler userHandler)</span> </span>{
		<span class="hljs-keyword">return</span> route()
				.GET(<span class="hljs-string">"/{user}"</span>, ACCEPT_JSON, userHandler::getUser)
				.GET(<span class="hljs-string">"/{user}/customers"</span>, ACCEPT_JSON, userHandler::getUserCustomers)
				.DELETE(<span class="hljs-string">"/{user}"</span>, ACCEPT_JSON, userHandler::deleteUser)
				.build();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_2_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.http.MediaType
<span class="hljs-keyword">import</span> org.springframework.web.servlet.function.RequestPredicates.accept
<span class="hljs-keyword">import</span> org.springframework.web.servlet.function.RouterFunction
<span class="hljs-keyword">import</span> org.springframework.web.servlet.function.RouterFunctions
<span class="hljs-keyword">import</span> org.springframework.web.servlet.function.ServerResponse

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyRoutingConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">routerFunction</span><span class="hljs-params">(userHandler: <span class="hljs-type">MyUserHandler</span>)</span></span>: RouterFunction&lt;ServerResponse&gt; {
		<span class="hljs-keyword">return</span> RouterFunctions.route()
			.GET(<span class="hljs-string">"/{user}"</span>, ACCEPT_JSON, userHandler::getUser)
			.GET(<span class="hljs-string">"/{user}/customers"</span>, ACCEPT_JSON, userHandler::getUserCustomers)
			.DELETE(<span class="hljs-string">"/{user}"</span>, ACCEPT_JSON, userHandler::deleteUser)
			.build()
	}

	<span class="hljs-keyword">companion</span> <span class="hljs-keyword">object</span> {
		<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> ACCEPT_JSON = accept(MediaType.APPLICATION_JSON)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_3_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_3_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_3_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_java" class="tabpanel" id="_tabs_3_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.stereotype.Component;
<span class="hljs-keyword">import</span> org.springframework.web.servlet.function.ServerRequest;
<span class="hljs-keyword">import</span> org.springframework.web.servlet.function.ServerResponse;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyUserHandler</span> </span>{

	<span class="hljs-function"><span class="hljs-keyword">public</span> ServerResponse <span class="hljs-title">getUser</span><span class="hljs-params">(ServerRequest request)</span> </span>{
		...
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> ServerResponse <span class="hljs-title">getUserCustomers</span><span class="hljs-params">(ServerRequest request)</span> </span>{
		...
	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> ServerResponse <span class="hljs-title">deleteUser</span><span class="hljs-params">(ServerRequest request)</span> </span>{
		...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_3_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.stereotype.Component
<span class="hljs-keyword">import</span> org.springframework.web.servlet.function.ServerRequest
<span class="hljs-keyword">import</span> org.springframework.web.servlet.function.ServerResponse

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyUserHandler</span> </span>{

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getUser</span><span class="hljs-params">(request: <span class="hljs-type">ServerRequest</span>?)</span></span>: ServerResponse {
		...
	}

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getUserCustomers</span><span class="hljs-params">(request: <span class="hljs-type">ServerRequest</span>?)</span></span>: ServerResponse {
		...
	}

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">deleteUser</span><span class="hljs-params">(request: <span class="hljs-type">ServerRequest</span>?)</span></span>: ServerResponse {
		...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Spring MVC is part of the core Spring Framework, and detailed information is available in the <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc.html">reference documentation</a>.
There are also several guides that cover Spring MVC available at <a class="bare external" href="https://spring.io/guides" target="_blank">spring.io/guides</a>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can define as many <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/function/RouterFunction.html"><code>RouterFunction</code></a> beans as you like to modularize the definition of the router.
Beans can be ordered if you need to apply a precedence.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="web.servlet.spring-mvc.auto-configuration"><a class="anchor" href="#web.servlet.spring-mvc.auto-configuration"></a>Spring MVC Auto-configuration</h3>
<div class="paragraph">
<p>Spring Boot provides auto-configuration for Spring MVC that works well with most applications.
It replaces the need for <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/EnableWebMvc.html"><code>@EnableWebMvc</code></a> and the two cannot be used together.
In addition to Spring MVC’s defaults, the auto-configuration provides the following features:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Inclusion of <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/view/ContentNegotiatingViewResolver.html"><code>ContentNegotiatingViewResolver</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/view/BeanNameViewResolver.html"><code>BeanNameViewResolver</code></a> beans.</p>
</li>
<li>
<p>Support for serving static resources, including support for WebJars (covered <a href="#web.servlet.spring-mvc.static-content">later in this document</a>).</p>
</li>
<li>
<p>Automatic registration of <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/converter/Converter.html"><code>Converter</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/converter/GenericConverter.html"><code>GenericConverter</code></a>, and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/format/Formatter.html"><code>Formatter</code></a> beans.</p>
</li>
<li>
<p>Support for <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/http/HttpMessageConverters.html"><code>HttpMessageConverters</code></a> (covered <a href="#web.servlet.spring-mvc.message-converters">later in this document</a>).</p>
</li>
<li>
<p>Automatic registration of <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/validation/MessageCodesResolver.html"><code>MessageCodesResolver</code></a> (covered <a href="#web.servlet.spring-mvc.message-codes">later in this document</a>).</p>
</li>
<li>
<p>Static <code>index.html</code> support.</p>
</li>
<li>
<p>Automatic use of a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/support/ConfigurableWebBindingInitializer.html"><code>ConfigurableWebBindingInitializer</code></a> bean (covered <a href="#web.servlet.spring-mvc.binding-initializer">later in this document</a>).</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>If you want to keep those Spring Boot MVC customizations and make more <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc.html">MVC customizations</a> (interceptors, formatters, view controllers, and other features), you can add your own <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> class of type <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/WebMvcConfigurer.html"><code>WebMvcConfigurer</code></a> but <strong>without</strong> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/EnableWebMvc.html"><code>@EnableWebMvc</code></a>.</p>
</div>
<div class="paragraph">
<p>If you want to provide custom instances of <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/mvc/method/annotation/RequestMappingHandlerMapping.html"><code>RequestMappingHandlerMapping</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/mvc/method/annotation/RequestMappingHandlerAdapter.html"><code>RequestMappingHandlerAdapter</code></a>, or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/mvc/method/annotation/ExceptionHandlerExceptionResolver.html"><code>ExceptionHandlerExceptionResolver</code></a>, and still keep the Spring Boot MVC customizations, you can declare a bean of type <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/servlet/WebMvcRegistrations.html"><code>WebMvcRegistrations</code></a> and use it to provide custom instances of those components.
The custom instances will be subject to further initialization and configuration by Spring MVC.
To participate in, and if desired, override that subsequent processing, a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/WebMvcConfigurer.html"><code>WebMvcConfigurer</code></a> should be used.</p>
</div>
<div class="paragraph">
<p>If you do not want to use the auto-configuration and want to take complete control of Spring MVC, add your own <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> annotated with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/EnableWebMvc.html"><code>@EnableWebMvc</code></a>.
Alternatively, add your own <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a>-annotated <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/DelegatingWebMvcConfiguration.html"><code>DelegatingWebMvcConfiguration</code></a> as described in the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/EnableWebMvc.html"><code>@EnableWebMvc</code></a> API documentation.</p>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.spring-mvc.conversion-service"><a class="anchor" href="#web.servlet.spring-mvc.conversion-service"></a>Spring MVC Conversion Service</h3>
<div class="paragraph">
<p>Spring MVC uses a different <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/ConversionService.html"><code>ConversionService</code></a> to the one used to convert values from your <code>application.properties</code> or <code>application.yaml</code> file.
It means that <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/Period.html" target="_blank"><code>Period</code></a>, <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/Duration.html" target="_blank"><code>Duration</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/util/unit/DataSize.html"><code>DataSize</code></a> converters are not available and that <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/convert/DurationUnit.html"><code>@DurationUnit</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/convert/DataSizeUnit.html"><code>@DataSizeUnit</code></a> annotations will be ignored.</p>
</div>
<div class="paragraph">
<p>If you want to customize the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/convert/ConversionService.html"><code>ConversionService</code></a> used by Spring MVC, you can provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/WebMvcConfigurer.html"><code>WebMvcConfigurer</code></a> bean with an <code>addFormatters</code> method.
From this method you can register any converter that you like, or you can delegate to the static methods available on <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/convert/ApplicationConversionService.html"><code>ApplicationConversionService</code></a>.</p>
</div>
<div class="paragraph">
<p>Conversion can also be customized using the <code>spring.mvc.format.*</code> configuration properties.
When not configured, the following defaults are used:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 33.3333%;"/>
<col style="width: 33.3333%;"/>
<col style="width: 33.3334%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Property</th>
<th class="tableblock halign-left valign-top"><code>DateTimeFormatter</code></th>
<th class="tableblock halign-left valign-top">Formats</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring.mvc.format.date</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>ofLocalizedDate(FormatStyle.SHORT)</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java.util.Date</code> and <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDate.html" target="_blank"><code>LocalDate</code></a></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring.mvc.format.time</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>ofLocalizedTime(FormatStyle.SHORT)</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">java.time’s <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalTime.html" target="_blank"><code>LocalTime</code></a> and <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/OffsetTime.html" target="_blank"><code>OffsetTime</code></a></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring.mvc.format.date-time</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>ofLocalizedDateTime(FormatStyle.SHORT)</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">java.time’s <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" target="_blank"><code>LocalDateTime</code></a>, <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/OffsetDateTime.html" target="_blank"><code>OffsetDateTime</code></a>, and <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/ZonedDateTime.html" target="_blank"><code>ZonedDateTime</code></a></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="web.servlet.spring-mvc.message-converters"><a class="anchor" href="#web.servlet.spring-mvc.message-converters"></a>HttpMessageConverters</h3>
<div class="paragraph">
<p>Spring MVC uses the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/converter/HttpMessageConverter.html"><code>HttpMessageConverter</code></a> interface to convert HTTP requests and responses.
Sensible defaults are included out of the box.
For example, objects can be automatically converted to JSON (by using the Jackson library) or XML (by using the Jackson XML extension, if available, or by using JAXB if the Jackson XML extension is not available).
By default, strings are encoded in <code>UTF-8</code>.</p>
</div>
<div class="paragraph">
<p>Any <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/converter/HttpMessageConverter.html"><code>HttpMessageConverter</code></a> bean that is present in the context is added to the list of converters.
You can also override default converters in the same way.</p>
</div>
<div class="paragraph">
<p>If you need to add or customize converters, you can use Spring Boot’s <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/http/HttpMessageConverters.html"><code>HttpMessageConverters</code></a> class, as shown in the following listing:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_4_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_4_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_4_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_java" class="tabpanel" id="_tabs_4_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.http.HttpMessageConverters;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.http.converter.HttpMessageConverter;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyHttpMessageConvertersConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> HttpMessageConverters <span class="hljs-title">customConverters</span><span class="hljs-params">()</span> </span>{
		HttpMessageConverter&lt;?&gt; additional = <span class="hljs-keyword">new</span> AdditionalHttpMessageConverter();
		HttpMessageConverter&lt;?&gt; another = <span class="hljs-keyword">new</span> AnotherHttpMessageConverter();
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> HttpMessageConverters(additional, another);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_4_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.http.HttpMessageConverters
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.http.converter.HttpMessageConverter

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyHttpMessageConvertersConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">customConverters</span><span class="hljs-params">()</span></span>: HttpMessageConverters {
		<span class="hljs-keyword">val</span> additional: HttpMessageConverter&lt;*&gt; = AdditionalHttpMessageConverter()
		<span class="hljs-keyword">val</span> another: HttpMessageConverter&lt;*&gt; = AnotherHttpMessageConverter()
		<span class="hljs-keyword">return</span> HttpMessageConverters(additional, another)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>For further control, you can also sub-class <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/http/HttpMessageConverters.html"><code>HttpMessageConverters</code></a> and override its <code>postProcessConverters</code> and/or <code>postProcessPartConverters</code> methods.
This can be useful when you want to re-order or remove some of the converters that Spring MVC configures by default.</p>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.spring-mvc.message-codes"><a class="anchor" href="#web.servlet.spring-mvc.message-codes"></a>MessageCodesResolver</h3>
<div class="paragraph">
<p>Spring MVC has a strategy for generating error codes for rendering error messages from binding errors: <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/validation/MessageCodesResolver.html"><code>MessageCodesResolver</code></a>.
If you set the <code>spring.mvc.message-codes-resolver-format</code> property <code>PREFIX_ERROR_CODE</code> or <code>POSTFIX_ERROR_CODE</code>, Spring Boot creates one for you (see the enumeration in <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/validation/DefaultMessageCodesResolver.html#Format"><code>DefaultMessageCodesResolver.Format</code></a>).</p>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.spring-mvc.static-content"><a class="anchor" href="#web.servlet.spring-mvc.static-content"></a>Static Content</h3>
<div class="paragraph">
<p>By default, Spring Boot serves static content from a directory called <code>/static</code> (or <code>/public</code> or <code>/resources</code> or <code>/META-INF/resources</code>) in the classpath or from the root of the <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/ServletContext.html" target="_blank"><code>ServletContext</code></a>.
It uses the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/resource/ResourceHttpRequestHandler.html"><code>ResourceHttpRequestHandler</code></a> from Spring MVC so that you can modify that behavior by adding your own <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/WebMvcConfigurer.html"><code>WebMvcConfigurer</code></a> and overriding the <code>addResourceHandlers</code> method.</p>
</div>
<div class="paragraph">
<p>In a stand-alone web application, the default servlet from the container is not enabled.
It can be enabled using the <code>server.servlet.register-default-servlet</code> property.</p>
</div>
<div class="paragraph">
<p>The default servlet acts as a fallback, serving content from the root of the <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/ServletContext.html" target="_blank"><code>ServletContext</code></a> if Spring decides not to handle it.
Most of the time, this does not happen (unless you modify the default MVC configuration), because Spring can always handle requests through the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/DispatcherServlet.html"><code>DispatcherServlet</code></a>.</p>
</div>
<div class="paragraph">
<p>By default, resources are mapped on <code>/**</code>, but you can tune that with the <code>spring.mvc.static-path-pattern</code> property.
For instance, relocating all resources to <code>/resources/**</code> can be achieved as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_5_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_5_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_5_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_properties" class="tabpanel" id="_tabs_5_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.mvc.static-path-pattern</span>=<span class="hljs-string">/resources/**</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_5_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">mvc:</span>
    <span class="hljs-attr">static-path-pattern:</span> <span class="hljs-string">"/resources/**"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also customize the static resource locations by using the <code>spring.web.resources.static-locations</code> property (replacing the default values with a list of directory locations).
The root servlet context path, <code>"/"</code>, is automatically added as a location as well.</p>
</div>
<div class="paragraph">
<p>In addition to the “standard” static resource locations mentioned earlier, a special case is made for <a class="external" href="https://www.webjars.org/" target="_blank">Webjars content</a>.
By default, any resources with a path in <code>/webjars/**</code> are served from jar files if they are packaged in the Webjars format.
The path can be customized with the <code>spring.mvc.webjars-path-pattern</code> property.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Do not use the <code>src/main/webapp</code> directory if your application is packaged as a jar.
Although this directory is a common standard, it works <strong>only</strong> with war packaging, and it is silently ignored by most build tools if you generate a jar.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Spring Boot also supports the advanced resource handling features provided by Spring MVC, allowing use cases such as cache-busting static resources or using version agnostic URLs for Webjars.</p>
</div>
<div class="paragraph">
<p>To use version agnostic URLs for Webjars, add the <code>org.webjars:webjars-locator-lite</code> dependency.
Then declare your Webjar.
Using jQuery as an example, adding <code>"/webjars/jquery/jquery.min.js"</code> results in <code>"/webjars/jquery/x.y.z/jquery.min.js"</code> where <code>x.y.z</code> is the Webjar version.</p>
</div>
<div class="paragraph">
<p>To use cache busting, the following configuration configures a cache busting solution for all static resources, effectively adding a content hash, such as <code>&lt;link href="/css/spring-2a2d595e6ed9a0b24f027f2b63b134d6.css"/&gt;</code>, in URLs:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_6_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_6_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_6_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_properties" class="tabpanel" id="_tabs_6_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.web.resources.chain.strategy.content.enabled</span>=<span class="hljs-string">true</span>
<span class="hljs-meta">spring.web.resources.chain.strategy.content.paths</span>=<span class="hljs-string">/**</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_6_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">web:</span>
    <span class="hljs-attr">resources:</span>
      <span class="hljs-attr">chain:</span>
        <span class="hljs-attr">strategy:</span>
          <span class="hljs-attr">content:</span>
            <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span>
            <span class="hljs-attr">paths:</span> <span class="hljs-string">"/**"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Links to resources are rewritten in templates at runtime, thanks to a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/resource/ResourceUrlEncodingFilter.html"><code>ResourceUrlEncodingFilter</code></a> that is auto-configured for Thymeleaf and FreeMarker.
You should manually declare this filter when using JSPs.
Other template engines are currently not automatically supported but can be with custom template macros/helpers and the use of the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/resource/ResourceUrlProvider.html"><code>ResourceUrlProvider</code></a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>When loading resources dynamically with, for example, a JavaScript module loader, renaming files is not an option.
That is why other strategies are also supported and can be combined.
A "fixed" strategy adds a static version string in the URL without changing the file name, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_7_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_7_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_7_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_properties" class="tabpanel" id="_tabs_7_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.web.resources.chain.strategy.content.enabled</span>=<span class="hljs-string">true</span>
<span class="hljs-meta">spring.web.resources.chain.strategy.content.paths</span>=<span class="hljs-string">/**</span>
<span class="hljs-meta">spring.web.resources.chain.strategy.fixed.enabled</span>=<span class="hljs-string">true</span>
<span class="hljs-meta">spring.web.resources.chain.strategy.fixed.paths</span>=<span class="hljs-string">/js/lib/</span>
<span class="hljs-meta">spring.web.resources.chain.strategy.fixed.version</span>=<span class="hljs-string">v12</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_7_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">web:</span>
    <span class="hljs-attr">resources:</span>
      <span class="hljs-attr">chain:</span>
        <span class="hljs-attr">strategy:</span>
          <span class="hljs-attr">content:</span>
            <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span>
            <span class="hljs-attr">paths:</span> <span class="hljs-string">"/**"</span>
          <span class="hljs-attr">fixed:</span>
            <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span>
            <span class="hljs-attr">paths:</span> <span class="hljs-string">"/js/lib/"</span>
            <span class="hljs-attr">version:</span> <span class="hljs-string">"v12"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>With this configuration, JavaScript modules located under <code>"/js/lib/"</code> use a fixed versioning strategy (<code>"/v12/js/lib/mymodule.js"</code>), while other resources still use the content one (<code>&lt;link href="/css/spring-2a2d595e6ed9a0b24f027f2b63b134d6.css"/&gt;</code>).</p>
</div>
<div class="paragraph">
<p>See <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/WebProperties.Resources.html"><code>WebProperties.Resources</code></a> for more supported options.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<div class="paragraph">
<p>This feature has been thoroughly described in a dedicated <a class="external" href="https://spring.io/blog/2014/07/24/spring-framework-4-1-handling-static-web-resources" target="_blank">blog post</a> and in Spring Framework’s <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc/mvc-config/static-resources.html">reference documentation</a>.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.spring-mvc.welcome-page"><a class="anchor" href="#web.servlet.spring-mvc.welcome-page"></a>Welcome Page</h3>
<div class="paragraph">
<p>Spring Boot supports both static and templated welcome pages.
It first looks for an <code>index.html</code> file in the configured static content locations.
If one is not found, it then looks for an <code>index</code> template.
If either is found, it is automatically used as the welcome page of the application.</p>
</div>
<div class="paragraph">
<p>This only acts as a fallback for actual index routes defined by the application.
The ordering is defined by the order of <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/HandlerMapping.html"><code>HandlerMapping</code></a> beans which is by default the following:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;"/>
<col style="width: 50%;"/>
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>RouterFunctionMapping</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Endpoints declared with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/function/RouterFunction.html"><code>RouterFunction</code></a> beans</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>RequestMappingHandlerMapping</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Endpoints declared in <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Controller.html"><code>@Controller</code></a> beans</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>WelcomePageHandlerMapping</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The welcome page support</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="web.servlet.spring-mvc.favicon"><a class="anchor" href="#web.servlet.spring-mvc.favicon"></a>Custom Favicon</h3>
<div class="paragraph">
<p>As with other static resources, Spring Boot checks for a <code>favicon.ico</code> in the configured static content locations.
If such a file is present, it is automatically used as the favicon of the application.</p>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.spring-mvc.content-negotiation"><a class="anchor" href="#web.servlet.spring-mvc.content-negotiation"></a>Path Matching and Content Negotiation</h3>
<div class="paragraph">
<p>Spring MVC can map incoming HTTP requests to handlers by looking at the request path and matching it to the mappings defined in your application (for example, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/annotation/GetMapping.html"><code>@GetMapping</code></a> annotations on Controller methods).</p>
</div>
<div class="paragraph">
<p>Spring Boot chooses to disable suffix pattern matching by default, which means that requests like <code>"GET /projects/spring-boot.json"</code> will not be matched to <code>@GetMapping("/projects/spring-boot")</code> mappings.
This is considered as a <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc/mvc-controller/ann-requestmapping.html#mvc-ann-requestmapping-suffix-pattern-match">best practice for Spring MVC applications</a>.
This feature was mainly useful in the past for HTTP clients which did not send proper "Accept" request headers; we needed to make sure to send the correct Content Type to the client.
Nowadays, Content Negotiation is much more reliable.</p>
</div>
<div class="paragraph">
<p>There are other ways to deal with HTTP clients that do not consistently send proper "Accept" request headers.
Instead of using suffix matching, we can use a query parameter to ensure that requests like <code>"GET /projects/spring-boot?format=json"</code> will be mapped to <code>@GetMapping("/projects/spring-boot")</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_8_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_8_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_8_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_properties" class="tabpanel" id="_tabs_8_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.mvc.contentnegotiation.favor-parameter</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_8_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">mvc:</span>
    <span class="hljs-attr">contentnegotiation:</span>
      <span class="hljs-attr">favor-parameter:</span> <span class="hljs-literal">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Or if you prefer to use a different parameter name:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_9">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_9_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_9_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_9_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_9_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_9_properties" class="tabpanel" id="_tabs_9_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.mvc.contentnegotiation.favor-parameter</span>=<span class="hljs-string">true</span>
<span class="hljs-meta">spring.mvc.contentnegotiation.parameter-name</span>=<span class="hljs-string">myparam</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_9_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_9_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">mvc:</span>
    <span class="hljs-attr">contentnegotiation:</span>
      <span class="hljs-attr">favor-parameter:</span> <span class="hljs-literal">true</span>
      <span class="hljs-attr">parameter-name:</span> <span class="hljs-string">"myparam"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Most standard media types are supported out-of-the-box, but you can also define new ones:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_10">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_10_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_10_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_10_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_10_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_10_properties" class="tabpanel" id="_tabs_10_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.mvc.contentnegotiation.media-types.markdown</span>=<span class="hljs-string">text/markdown</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_10_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_10_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">mvc:</span>
    <span class="hljs-attr">contentnegotiation:</span>
      <span class="hljs-attr">media-types:</span>
        <span class="hljs-attr">markdown:</span> <span class="hljs-string">"text/markdown"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>As of Spring Framework 5.3, Spring MVC supports two strategies for matching request paths to controllers.
By default, Spring Boot uses the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/util/pattern/PathPatternParser.html"><code>PathPatternParser</code></a> strategy.
<a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/util/pattern/PathPatternParser.html"><code>PathPatternParser</code></a> is an <a class="external" href="https://spring.io/blog/2020/06/30/url-matching-with-pathpattern-in-spring-mvc" target="_blank">optimized implementation</a> but comes with some restrictions compared to the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/util/AntPathMatcher.html"><code>AntPathMatcher</code></a> strategy.
<a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/util/pattern/PathPatternParser.html"><code>PathPatternParser</code></a> restricts usage of <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc/mvc-controller/ann-requestmapping.html#mvc-ann-requestmapping-uri-templates">some path pattern variants</a>.
It is also incompatible with configuring the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/DispatcherServlet.html"><code>DispatcherServlet</code></a> with a path prefix (<code>spring.mvc.servlet.path</code>).</p>
</div>
<div class="paragraph">
<p>The strategy can be configured using the <code>spring.mvc.pathmatch.matching-strategy</code> configuration property, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_11">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_11_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_11_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_11_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_11_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_11_properties" class="tabpanel" id="_tabs_11_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.mvc.pathmatch.matching-strategy</span>=<span class="hljs-string">ant-path-matcher</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_11_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_11_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">mvc:</span>
    <span class="hljs-attr">pathmatch:</span>
      <span class="hljs-attr">matching-strategy:</span> <span class="hljs-string">"ant-path-matcher"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Spring MVC will throw a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/NoHandlerFoundException.html"><code>NoHandlerFoundException</code></a> if a handler is not found for a request.
Note that, by default, the <a href="#web.servlet.spring-mvc.static-content">serving of static content</a> is mapped to <code>/**</code> and will, therefore, provide a handler for all requests.
If no static content is available, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/resource/ResourceHttpRequestHandler.html"><code>ResourceHttpRequestHandler</code></a> will throw a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/resource/NoResourceFoundException.html"><code>NoResourceFoundException</code></a>.
For a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/NoHandlerFoundException.html"><code>NoHandlerFoundException</code></a> to be thrown, set <code>spring.mvc.static-path-pattern</code> to a more specific value such as <code>/resources/**</code> or set <code>spring.web.resources.add-mappings</code> to <code>false</code> to disable serving of static content entirely.</p>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.spring-mvc.binding-initializer"><a class="anchor" href="#web.servlet.spring-mvc.binding-initializer"></a>ConfigurableWebBindingInitializer</h3>
<div class="paragraph">
<p>Spring MVC uses a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/support/WebBindingInitializer.html"><code>WebBindingInitializer</code></a> to initialize a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/WebDataBinder.html"><code>WebDataBinder</code></a> for a particular request.
If you create your own <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/support/ConfigurableWebBindingInitializer.html"><code>ConfigurableWebBindingInitializer</code></a> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a>, Spring Boot automatically configures Spring MVC to use it.</p>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.spring-mvc.template-engines"><a class="anchor" href="#web.servlet.spring-mvc.template-engines"></a>Template Engines</h3>
<div class="paragraph">
<p>As well as REST web services, you can also use Spring MVC to serve dynamic HTML content.
Spring MVC supports a variety of templating technologies, including Thymeleaf, FreeMarker, and JSPs.
Also, many other templating engines include their own Spring MVC integrations.</p>
</div>
<div class="paragraph">
<p>Spring Boot includes auto-configuration support for the following templating engines:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a class="external" href="https://freemarker.apache.org/docs/" target="_blank">FreeMarker</a></p>
</li>
<li>
<p><a class="external" href="https://docs.groovy-lang.org/docs/next/html/documentation/template-engines.html#_the_markuptemplateengine" target="_blank">Groovy</a></p>
</li>
<li>
<p><a class="external" href="https://www.thymeleaf.org" target="_blank">Thymeleaf</a></p>
</li>
<li>
<p><a class="external" href="https://mustache.github.io/" target="_blank">Mustache</a></p>
</li>
</ul>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If possible, JSPs should be avoided.
There are several <a href="#web.servlet.embedded-container.jsp-limitations">known limitations</a> when using them with embedded servlet containers.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>When you use one of these templating engines with the default configuration, your templates are picked up automatically from <code>src/main/resources/templates</code>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Depending on how you run your application, your IDE may order the classpath differently.
Running your application in the IDE from its main method results in a different ordering than when you run your application by using Maven or Gradle or from its packaged jar.
This can cause Spring Boot to fail to find the expected template.
If you have this problem, you can reorder the classpath in the IDE to place the module’s classes and resources first.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.spring-mvc.error-handling"><a class="anchor" href="#web.servlet.spring-mvc.error-handling"></a>Error Handling</h3>
<div class="paragraph">
<p>By default, Spring Boot provides an <code>/error</code> mapping that handles all errors in a sensible way, and it is registered as a “global” error page in the servlet container.
For machine clients, it produces a JSON response with details of the error, the HTTP status, and the exception message.
For browser clients, there is a “whitelabel” error view that renders the same data in HTML format (to customize it, add a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/View.html"><code>View</code></a> that resolves to <code>error</code>).</p>
</div>
<div class="paragraph">
<p>There are a number of <code>server.error</code> properties that can be set if you want to customize the default error handling behavior.
See the <a class="xref page" href="../../appendix/application-properties/index.html#appendix.application-properties.server">Server Properties</a> section of the Appendix.</p>
</div>
<div class="paragraph">
<p>To replace the default behavior completely, you can implement <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/error/ErrorController.html"><code>ErrorController</code></a> and register a bean definition of that type or add a bean of type <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/error/ErrorAttributes.html"><code>ErrorAttributes</code></a> to use the existing mechanism but replace the contents.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/servlet/error/BasicErrorController.html"><code>BasicErrorController</code></a> can be used as a base class for a custom <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/error/ErrorController.html"><code>ErrorController</code></a>.
This is particularly useful if you want to add a handler for a new content type (the default is to handle <code>text/html</code> specifically and provide a fallback for everything else).
To do so, extend <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/servlet/error/BasicErrorController.html"><code>BasicErrorController</code></a>, add a public method with a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/annotation/RequestMapping.html"><code>@RequestMapping</code></a> that has a <code>produces</code> attribute, and create a bean of your new type.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>As of Spring Framework 6.0, <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc/mvc-ann-rest-exceptions.html">RFC 9457 Problem Details</a> is supported.
Spring MVC can produce custom error messages with the <code>application/problem+json</code> media type, like:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-json hljs" data-lang="json">{
	<span class="hljs-attr">"type"</span>: <span class="hljs-string">"https://example.org/problems/unknown-project"</span>,
	<span class="hljs-attr">"title"</span>: <span class="hljs-string">"Unknown project"</span>,
	<span class="hljs-attr">"status"</span>: <span class="hljs-number">404</span>,
	<span class="hljs-attr">"detail"</span>: <span class="hljs-string">"No project found for id 'spring-unknown'"</span>,
	<span class="hljs-attr">"instance"</span>: <span class="hljs-string">"/projects/spring-unknown"</span>
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This support can be enabled by setting <code>spring.mvc.problemdetails.enabled</code> to <code>true</code>.</p>
</div>
<div class="paragraph">
<p>You can also define a class annotated with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/annotation/ControllerAdvice.html"><code>@ControllerAdvice</code></a> to customize the JSON document to return for a particular controller and/or exception type, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_12">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_12_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_12_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_12_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_12_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_12_java" class="tabpanel" id="_tabs_12_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> jakarta.servlet.RequestDispatcher;
<span class="hljs-keyword">import</span> jakarta.servlet.http.HttpServletRequest;

<span class="hljs-keyword">import</span> org.springframework.http.HttpStatus;
<span class="hljs-keyword">import</span> org.springframework.http.ResponseEntity;
<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.ControllerAdvice;
<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.ExceptionHandler;
<span class="hljs-keyword">import</span> org.springframework.web.bind.annotation.ResponseBody;
<span class="hljs-keyword">import</span> org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

</span><span class="fold-block"><span class="hljs-meta">@ControllerAdvice</span>(basePackageClasses = SomeController<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">public</span> <span class="hljs-title">class</span> <span class="hljs-title">MyControllerAdvice</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">ResponseEntityExceptionHandler</span> </span>{

	<span class="hljs-meta">@ResponseBody</span>
	<span class="hljs-meta">@ExceptionHandler</span>(MyException<span class="hljs-class">.<span class="hljs-keyword">class</span>)
	<span class="hljs-title">public</span> <span class="hljs-title">ResponseEntity</span>&lt;?&gt; <span class="hljs-title">handleControllerException</span>(<span class="hljs-title">HttpServletRequest</span> <span class="hljs-title">request</span>, <span class="hljs-title">Throwable</span> <span class="hljs-title">ex</span>) </span>{
		HttpStatus status = getStatus(request);
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> ResponseEntity&lt;&gt;(<span class="hljs-keyword">new</span> MyErrorBody(status.value(), ex.getMessage()), status);
	}

	<span class="hljs-function"><span class="hljs-keyword">private</span> HttpStatus <span class="hljs-title">getStatus</span><span class="hljs-params">(HttpServletRequest request)</span> </span>{
		Integer code = (Integer) request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE);
		HttpStatus status = HttpStatus.resolve(code);
		<span class="hljs-keyword">return</span> (status != <span class="hljs-keyword">null</span>) ? status : HttpStatus.INTERNAL_SERVER_ERROR;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_12_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_12_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> jakarta.servlet.RequestDispatcher
<span class="hljs-keyword">import</span> jakarta.servlet.http.HttpServletRequest
<span class="hljs-keyword">import</span> org.springframework.http.HttpStatus
<span class="hljs-keyword">import</span> org.springframework.http.ResponseEntity
<span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.ControllerAdvice
<span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.ExceptionHandler
<span class="hljs-keyword">import</span> org.springframework.web.bind.<span class="hljs-keyword">annotation</span>.ResponseBody
<span class="hljs-keyword">import</span> org.springframework.web.servlet.mvc.method.<span class="hljs-keyword">annotation</span>.ResponseEntityExceptionHandler

</span><span class="fold-block"><span class="hljs-meta">@ControllerAdvice(basePackageClasses = [SomeController::class])</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyControllerAdvice</span> : <span class="hljs-type">ResponseEntityExceptionHandler</span></span>() {

	<span class="hljs-meta">@ResponseBody</span>
	<span class="hljs-meta">@ExceptionHandler(MyException::class)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">handleControllerException</span><span class="hljs-params">(request: <span class="hljs-type">HttpServletRequest</span>, ex: <span class="hljs-type">Throwable</span>)</span></span>: ResponseEntity&lt;*&gt; {
		<span class="hljs-keyword">val</span> status = getStatus(request)
		<span class="hljs-keyword">return</span> ResponseEntity(MyErrorBody(status.value(), ex.message), status)
	}

	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">getStatus</span><span class="hljs-params">(request: <span class="hljs-type">HttpServletRequest</span>)</span></span>: HttpStatus {
		<span class="hljs-keyword">val</span> code = request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE) <span class="hljs-keyword">as</span> <span class="hljs-built_in">Int</span>
		<span class="hljs-keyword">val</span> status = HttpStatus.resolve(code)
		<span class="hljs-keyword">return</span> status ?: HttpStatus.INTERNAL_SERVER_ERROR
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>In the preceding example, if <code>MyException</code> is thrown by a controller defined in the same package as <code>SomeController</code>, a JSON representation of the <code>MyErrorBody</code> POJO is used instead of the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/error/ErrorAttributes.html"><code>ErrorAttributes</code></a> representation.</p>
</div>
<div class="paragraph">
<p>In some cases, errors handled at the controller level are not recorded by web observations or the <a class="xref page" href="../actuator/metrics.html#actuator.metrics.supported.spring-mvc">metrics infrastructure</a>.
Applications can ensure that such exceptions are recorded with the observations by <a href="https://docs.spring.io/spring-framework/reference/6.2/integration/observability.html#observability.http-server.servlet">setting the handled exception on the observation context</a>.</p>
</div>
<div class="sect3">
<h4 id="web.servlet.spring-mvc.error-handling.error-pages"><a class="anchor" href="#web.servlet.spring-mvc.error-handling.error-pages"></a>Custom Error Pages</h4>
<div class="paragraph">
<p>If you want to display a custom HTML error page for a given status code, you can add a file to an <code>/error</code> directory.
Error pages can either be static HTML (that is, added under any of the static resource directories) or be built by using templates.
The name of the file should be the exact status code or a series mask.</p>
</div>
<div class="paragraph">
<p>For example, to map <code>404</code> to a static HTML file, your directory structure would be as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-none hljs">src/
 +- main/
     +- java/
     |   + &lt;source code&gt;
     +- resources/
         +- public/
             +- error/
             |   +- 404.html
             +- &lt;other public assets&gt;</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>To map all <code>5xx</code> errors by using a FreeMarker template, your directory structure would be as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-none hljs">src/
 +- main/
     +- java/
     |   + &lt;source code&gt;
     +- resources/
         +- templates/
             +- error/
             |   +- 5xx.ftlh
             +- &lt;other templates&gt;</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>For more complex mappings, you can also add beans that implement the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/servlet/error/ErrorViewResolver.html"><code>ErrorViewResolver</code></a> interface, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_13">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_13_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_13_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_13_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_13_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_13_java" class="tabpanel" id="_tabs_13_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.Map;

<span class="hljs-keyword">import</span> jakarta.servlet.http.HttpServletRequest;

<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver;
<span class="hljs-keyword">import</span> org.springframework.http.HttpStatus;
<span class="hljs-keyword">import</span> org.springframework.web.servlet.ModelAndView;

</span><span class="fold-block"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyErrorViewResolver</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">ErrorViewResolver</span> </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> ModelAndView <span class="hljs-title">resolveErrorView</span><span class="hljs-params">(HttpServletRequest request, HttpStatus status, Map&lt;String, Object&gt; model)</span> </span>{
		<span class="hljs-comment">// Use the request or status to optionally return a ModelAndView</span>
		<span class="hljs-keyword">if</span> (status == HttpStatus.INSUFFICIENT_STORAGE) {
			<span class="hljs-comment">// We could add custom model values here</span>
			<span class="hljs-keyword">new</span> ModelAndView(<span class="hljs-string">"myview"</span>);
		}
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">null</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_13_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_13_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> jakarta.servlet.http.HttpServletRequest
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver
<span class="hljs-keyword">import</span> org.springframework.http.HttpStatus
<span class="hljs-keyword">import</span> org.springframework.web.servlet.ModelAndView

</span><span class="fold-block"><span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyErrorViewResolver</span> : <span class="hljs-type">ErrorViewResolver {</span></span>

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">resolveErrorView</span><span class="hljs-params">(request: <span class="hljs-type">HttpServletRequest</span>, status: <span class="hljs-type">HttpStatus</span>,
			model: <span class="hljs-type">Map</span>&lt;<span class="hljs-type">String</span>, Any&gt;)</span></span>: ModelAndView? {
		<span class="hljs-comment">// Use the request or status to optionally return a ModelAndView</span>
		<span class="hljs-keyword">if</span> (status == HttpStatus.INSUFFICIENT_STORAGE) {
			<span class="hljs-comment">// We could add custom model values here</span>
			<span class="hljs-keyword">return</span> ModelAndView(<span class="hljs-string">"myview"</span>)
		}
		<span class="hljs-keyword">return</span> <span class="hljs-literal">null</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also use regular Spring MVC features such as <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc/mvc-servlet/exceptionhandlers.html"><code>@ExceptionHandler</code> methods</a> and <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc/mvc-controller/ann-advice.html"><code>@ControllerAdvice</code></a>.
The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/error/ErrorController.html"><code>ErrorController</code></a> then picks up any unhandled exceptions.</p>
</div>
</div>
<div class="sect3">
<h4 id="web.servlet.spring-mvc.error-handling.error-pages-without-spring-mvc"><a class="anchor" href="#web.servlet.spring-mvc.error-handling.error-pages-without-spring-mvc"></a>Mapping Error Pages Outside of Spring MVC</h4>
<div class="paragraph">
<p>For applications that do not use Spring MVC, you can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/ErrorPageRegistrar.html"><code>ErrorPageRegistrar</code></a> interface to directly register <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/ErrorPage.html"><code>ErrorPage</code></a> instances.
This abstraction works directly with the underlying embedded servlet container and works even if you do not have a Spring MVC <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/DispatcherServlet.html"><code>DispatcherServlet</code></a>.</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_14">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_14_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_14_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_14_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_14_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_14_java" class="tabpanel" id="_tabs_14_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.server.ErrorPage;
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.ErrorPageRegistrar;
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.ErrorPageRegistry;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.http.HttpStatus;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyErrorPagesConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> ErrorPageRegistrar <span class="hljs-title">errorPageRegistrar</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>::registerErrorPages;
	}

	<span class="hljs-function"><span class="hljs-keyword">private</span> <span class="hljs-keyword">void</span> <span class="hljs-title">registerErrorPages</span><span class="hljs-params">(ErrorPageRegistry registry)</span> </span>{
		registry.addErrorPages(<span class="hljs-keyword">new</span> ErrorPage(HttpStatus.BAD_REQUEST, <span class="hljs-string">"/400"</span>));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_14_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_14_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.server.ErrorPage
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.ErrorPageRegistrar
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.ErrorPageRegistry
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.http.HttpStatus

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyErrorPagesConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">errorPageRegistrar</span><span class="hljs-params">()</span></span>: ErrorPageRegistrar {
		<span class="hljs-keyword">return</span> ErrorPageRegistrar { registry: ErrorPageRegistry -&gt; registerErrorPages(registry) }
	}

	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">registerErrorPages</span><span class="hljs-params">(registry: <span class="hljs-type">ErrorPageRegistry</span>)</span></span> {
		registry.addErrorPages(ErrorPage(HttpStatus.BAD_REQUEST, <span class="hljs-string">"/400"</span>))
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If you register an <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/ErrorPage.html"><code>ErrorPage</code></a> with a path that ends up being handled by a <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a> (as is common with some non-Spring web frameworks, like Jersey and Wicket), then the <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a> has to be explicitly registered as an <code>ERROR</code> dispatcher, as shown in the following example:
</td>
</tr>
</tbody></table>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_15">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_15_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_15_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_15_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_15_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_15_java" class="tabpanel" id="_tabs_15_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.EnumSet;

<span class="hljs-keyword">import</span> jakarta.servlet.DispatcherType;

<span class="hljs-keyword">import</span> org.springframework.boot.web.servlet.FilterRegistrationBean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyFilterConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> FilterRegistrationBean&lt;MyFilter&gt; <span class="hljs-title">myFilter</span><span class="hljs-params">()</span> </span>{
		FilterRegistrationBean&lt;MyFilter&gt; registration = <span class="hljs-keyword">new</span> FilterRegistrationBean&lt;&gt;(<span class="hljs-keyword">new</span> MyFilter());
		<span class="hljs-comment">// ...</span>
		registration.setDispatcherTypes(EnumSet.allOf(DispatcherType<span class="hljs-class">.<span class="hljs-keyword">class</span>))</span>;
		<span class="hljs-keyword">return</span> registration;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_15_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_15_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> jakarta.servlet.DispatcherType
<span class="hljs-keyword">import</span> org.springframework.boot.web.servlet.FilterRegistrationBean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> java.util.EnumSet

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyFilterConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">myFilter</span><span class="hljs-params">()</span></span>: FilterRegistrationBean&lt;MyFilter&gt; {
		<span class="hljs-keyword">val</span> registration = FilterRegistrationBean(MyFilter())
		<span class="hljs-comment">// ...</span>
		registration.setDispatcherTypes(EnumSet.allOf(DispatcherType::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>))</span>
		<span class="hljs-keyword">return</span> registration
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Note that the default <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/FilterRegistrationBean.html"><code>FilterRegistrationBean</code></a> does not include the <code>ERROR</code> dispatcher type.</p>
</div>
</div>
<div class="sect3">
<h4 id="web.servlet.spring-mvc.error-handling.in-a-war-deployment"><a class="anchor" href="#web.servlet.spring-mvc.error-handling.in-a-war-deployment"></a>Error Handling in a WAR Deployment</h4>
<div class="paragraph">
<p>When deployed to a servlet container, Spring Boot uses its error page filter to forward a request with an error status to the appropriate error page.
This is necessary as the servlet specification does not provide an API for registering error pages.
Depending on the container that you are deploying your war file to and the technologies that your application uses, some additional configuration may be required.</p>
</div>
<div class="paragraph">
<p>The error page filter can only forward the request to the correct error page if the response has not already been committed.
By default, WebSphere Application Server 8.0 and later commits the response upon successful completion of a servlet’s service method.
You should disable this behavior by setting <code>com.ibm.ws.webcontainer.invokeFlushAfterService</code> to <code>false</code>.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.spring-mvc.cors"><a class="anchor" href="#web.servlet.spring-mvc.cors"></a>CORS Support</h3>
<div class="paragraph">
<p><a class="external" href="https://en.wikipedia.org/wiki/Cross-origin_resource_sharing" target="_blank">Cross-origin resource sharing</a> (CORS) is a <a class="external" href="https://www.w3.org/TR/cors/" target="_blank">W3C specification</a> implemented by <a class="external" href="https://caniuse.com/#feat=cors" target="_blank">most browsers</a> that lets you specify in a flexible way what kind of cross-domain requests are authorized, instead of using some less secure and less powerful approaches such as IFRAME or JSONP.</p>
</div>
<div class="paragraph">
<p>As of version 4.2, Spring MVC <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc-cors.html">supports CORS</a>.
Using <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc-cors.html#mvc-cors-controller">controller method CORS configuration</a> with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/annotation/CrossOrigin.html"><code>@CrossOrigin</code></a> annotations in your Spring Boot application does not require any specific configuration.
<a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc-cors.html#mvc-cors-global">Global CORS configuration</a> can be defined by registering a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/servlet/config/annotation/WebMvcConfigurer.html"><code>WebMvcConfigurer</code></a> bean with a customized <code>addCorsMappings(CorsRegistry)</code> method, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_16">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_16_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_16_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_16_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_16_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_16_java" class="tabpanel" id="_tabs_16_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.web.servlet.config.annotation.CorsRegistry;
<span class="hljs-keyword">import</span> org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCorsConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> WebMvcConfigurer <span class="hljs-title">corsConfigurer</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> WebMvcConfigurer() {

			<span class="hljs-meta">@Override</span>
			<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">addCorsMappings</span><span class="hljs-params">(CorsRegistry registry)</span> </span>{
				registry.addMapping(<span class="hljs-string">"/api/**"</span>);
			}

		};
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_16_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_16_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.web.servlet.config.<span class="hljs-keyword">annotation</span>.CorsRegistry
<span class="hljs-keyword">import</span> org.springframework.web.servlet.config.<span class="hljs-keyword">annotation</span>.WebMvcConfigurer

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCorsConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">corsConfigurer</span><span class="hljs-params">()</span></span>: WebMvcConfigurer {
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">object</span> : WebMvcConfigurer {
			<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">addCorsMappings</span><span class="hljs-params">(registry: <span class="hljs-type">CorsRegistry</span>)</span></span> {
				registry.addMapping(<span class="hljs-string">"/api/**"</span>)
			}
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="web.servlet.jersey"><a class="anchor" href="#web.servlet.jersey"></a>JAX-RS and Jersey</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you prefer the JAX-RS programming model for REST endpoints, you can use one of the available implementations instead of Spring MVC.
<a class="external" href="https://jersey.github.io/" target="_blank">Jersey</a> and <a class="external" href="https://cxf.apache.org/" target="_blank">Apache CXF</a> work quite well out of the box.
CXF requires you to register its <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Servlet.html" target="_blank"><code>Servlet</code></a> or <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a> as a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> in your application context.
Jersey has some native Spring support, so we also provide auto-configuration support for it in Spring Boot, together with a starter.</p>
</div>
<div class="paragraph">
<p>To get started with Jersey, include the <code>spring-boot-starter-jersey</code> as a dependency and then you need one <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> of type <a class="apiref external" href="https://javadoc.io/doc/org.glassfish.jersey.core/jersey-server/3.1.10/org/glassfish/jersey/server/ResourceConfig.html" target="_blank"><code>ResourceConfig</code></a> in which you register all the endpoints, as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.glassfish.jersey.server.ResourceConfig;

<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyJerseyConfig</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">ResourceConfig</span> </span>{

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyJerseyConfig</span><span class="hljs-params">()</span> </span>{
		register(MyEndpoint<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock warning">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-warning" title="Warning"></i>
</td>
<td class="content">
Jersey’s support for scanning executable archives is rather limited.
For example, it cannot scan for endpoints in a package found in a <a class="xref page" href="../../how-to/deployment/installing.html">fully executable jar file</a> or in <code>WEB-INF/classes</code> when running an executable war file.
To avoid this limitation, the <code>packages</code> method should not be used, and endpoints should be registered individually by using the <code>register</code> method, as shown in the preceding example.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>For more advanced customizations, you can also register an arbitrary number of beans that implement <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jersey/ResourceConfigCustomizer.html"><code>ResourceConfigCustomizer</code></a>.</p>
</div>
<div class="paragraph">
<p>All the registered endpoints should be a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a> with HTTP resource annotations (<code>@GET</code> and others), as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> jakarta.ws.rs.GET;
<span class="hljs-keyword">import</span> jakarta.ws.rs.Path;

<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-meta">@Path</span>(<span class="hljs-string">"/hello"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyEndpoint</span> </span>{

	<span class="hljs-meta">@GET</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">message</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-string">"Hello"</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Since the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/endpoint/annotation/Endpoint.html"><code>@Endpoint</code></a> is a Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html"><code>@Component</code></a>, its lifecycle is managed by Spring and you can use the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Autowired.html"><code>@Autowired</code></a> annotation to inject dependencies and use the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Value.html"><code>@Value</code></a> annotation to inject external configuration.
By default, the Jersey servlet is registered and mapped to <code>/*</code>.
You can change the mapping by adding <a class="apiref external" href="https://jakarta.ee/specifications/restful-ws/3.1/apidocs/jakarta/ws/rs/ApplicationPath.html" target="_blank"><code>@ApplicationPath</code></a> to your <a class="apiref external" href="https://javadoc.io/doc/org.glassfish.jersey.core/jersey-server/3.1.10/org/glassfish/jersey/server/ResourceConfig.html" target="_blank"><code>ResourceConfig</code></a>.</p>
</div>
<div class="paragraph">
<p>By default, Jersey is set up as a servlet in a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> of type <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/ServletRegistrationBean.html"><code>ServletRegistrationBean</code></a> named <code>jerseyServletRegistration</code>.
By default, the servlet is initialized lazily, but you can customize that behavior by setting <code>spring.jersey.servlet.load-on-startup</code>.
You can disable or override that bean by creating one of your own with the same name.
You can also use a filter instead of a servlet by setting <code>spring.jersey.type=filter</code> (in which case, the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> to replace or override is <code>jerseyFilterRegistration</code>).
The filter has an <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/annotation/Order.html"><code>@Order</code></a>, which you can set with <code>spring.jersey.filter.order</code>.
When using Jersey as a filter, a servlet that will handle any requests that are not intercepted by Jersey must be present.
If your application does not contain such a servlet, you may want to enable the default servlet by setting <code>server.servlet.register-default-servlet</code> to <code>true</code>.
Both the servlet and the filter registrations can be given init parameters by using <code>spring.jersey.init.*</code> to specify a map of properties.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="web.servlet.embedded-container"><a class="anchor" href="#web.servlet.embedded-container"></a>Embedded Servlet Container Support</h2>
<div class="sectionbody">
<div class="paragraph">
<p>For servlet application, Spring Boot includes support for embedded <a class="external" href="https://tomcat.apache.org/" target="_blank">Tomcat</a>, <a class="external" href="https://www.eclipse.org/jetty/" target="_blank">Jetty</a>, and <a class="external" href="https://github.com/undertow-io/undertow" target="_blank">Undertow</a> servers.
Most developers use the appropriate starter to obtain a fully configured instance.
By default, the embedded server listens for HTTP requests on port <code>8080</code>.</p>
</div>
<div class="sect2">
<h3 id="web.servlet.embedded-container.servlets-filters-listeners"><a class="anchor" href="#web.servlet.embedded-container.servlets-filters-listeners"></a>Servlets, Filters, and Listeners</h3>
<div class="paragraph">
<p>When using an embedded servlet container, you can register servlets, filters, and all the listeners (such as <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/http/HttpSessionListener.html" target="_blank"><code>HttpSessionListener</code></a>) from the servlet spec, either by using Spring beans or by scanning for servlet components.</p>
</div>
<div class="sect3">
<h4 id="web.servlet.embedded-container.servlets-filters-listeners.beans"><a class="anchor" href="#web.servlet.embedded-container.servlets-filters-listeners.beans"></a>Registering Servlets, Filters, and Listeners as Spring Beans</h4>
<div class="paragraph">
<p>Any <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Servlet.html" target="_blank"><code>Servlet</code></a>, <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a>, or servlet <code>*Listener</code> instance that is a Spring bean is registered with the embedded container.
This can be particularly convenient if you want to refer to a value from your <code>application.properties</code> during configuration.</p>
</div>
<div class="paragraph">
<p>By default, if the context contains only a single Servlet, it is mapped to <code>/</code>.
In the case of multiple servlet beans, the bean name is used as a path prefix.
Filters map to <code>/*</code>.</p>
</div>
<div class="paragraph">
<p>If convention-based mapping is not flexible enough, you can use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/ServletRegistrationBean.html"><code>ServletRegistrationBean</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/FilterRegistrationBean.html"><code>FilterRegistrationBean</code></a>, and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/ServletListenerRegistrationBean.html"><code>ServletListenerRegistrationBean</code></a> classes for complete control.</p>
</div>
<div class="paragraph">
<p>It is usually safe to leave filter beans unordered.
If a specific order is required, you should annotate the <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a> with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/annotation/Order.html"><code>@Order</code></a> or make it implement <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/Ordered.html"><code>Ordered</code></a>.
You cannot configure the order of a <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a> by annotating its bean method with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/annotation/Order.html"><code>@Order</code></a>.
If you cannot change the <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a> class to add <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/annotation/Order.html"><code>@Order</code></a> or implement <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/Ordered.html"><code>Ordered</code></a>, you must define a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/FilterRegistrationBean.html"><code>FilterRegistrationBean</code></a> for the <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a> and set the registration bean’s order using the <code>setOrder(int)</code> method.
Avoid configuring a filter that reads the request body at <code>Ordered.HIGHEST_PRECEDENCE</code>, since it might go against the character encoding configuration of your application.
If a servlet filter wraps the request, it should be configured with an order that is less than or equal to <code>OrderedFilter.REQUEST_WRAPPER_FILTER_MAX_ORDER</code>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
To see the order of every <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a> in your application, enable debug level logging for the <code>web</code> <a class="xref page" href="../features/logging.html#features.logging.log-groups">logging group</a> (<code>logging.level.web=debug</code>).
Details of the registered filters, including their order and URL patterns, will then be logged at startup.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock warning">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-warning" title="Warning"></i>
</td>
<td class="content">
Take care when registering <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a> beans since they are initialized very early in the application lifecycle.
If you need to register a <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a> that interacts with other beans, consider using a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/DelegatingFilterProxyRegistrationBean.html"><code>DelegatingFilterProxyRegistrationBean</code></a> instead.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.embedded-container.context-initializer"><a class="anchor" href="#web.servlet.embedded-container.context-initializer"></a>Servlet Context Initialization</h3>
<div class="paragraph">
<p>Embedded servlet containers do not directly execute the <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/ServletContainerInitializer.html" target="_blank"><code>ServletContainerInitializer</code></a> interface or Spring’s <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/WebApplicationInitializer.html"><code>WebApplicationInitializer</code></a> interface.
This is an intentional design decision intended to reduce the risk that third party libraries designed to run inside a war may break Spring Boot applications.</p>
</div>
<div class="paragraph">
<p>If you need to perform servlet context initialization in a Spring Boot application, you should register a bean that implements the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/ServletContextInitializer.html"><code>ServletContextInitializer</code></a> interface.
The single <code>onStartup</code> method provides access to the <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/ServletContext.html" target="_blank"><code>ServletContext</code></a> and, if necessary, can easily be used as an adapter to an existing <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/WebApplicationInitializer.html"><code>WebApplicationInitializer</code></a>.</p>
</div>
<div class="sect3">
<h4 id="web.servlet.embedded-container.context-initializer.scanning"><a class="anchor" href="#web.servlet.embedded-container.context-initializer.scanning"></a>Scanning for Servlets, Filters, and listeners</h4>
<div class="paragraph">
<p>When using an embedded container, automatic registration of classes annotated with <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/annotation/WebServlet.html" target="_blank"><code>@WebServlet</code></a>, <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/annotation/WebFilter.html" target="_blank"><code>@WebFilter</code></a>, and <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/annotation/WebListener.html" target="_blank"><code>@WebListener</code></a> can be enabled by using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/ServletComponentScan.html"><code>@ServletComponentScan</code></a>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/ServletComponentScan.html"><code>@ServletComponentScan</code></a> has no effect in a standalone container, where the container’s built-in discovery mechanisms are used instead.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.embedded-container.application-context"><a class="anchor" href="#web.servlet.embedded-container.application-context"></a>The ServletWebServerApplicationContext</h3>
<div class="paragraph">
<p>Under the hood, Spring Boot uses a different type of <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a> for embedded servlet container support.
The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/context/ServletWebServerApplicationContext.html"><code>ServletWebServerApplicationContext</code></a> is a special type of <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/context/WebApplicationContext.html"><code>WebApplicationContext</code></a> that bootstraps itself by searching for a single <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/server/ServletWebServerFactory.html"><code>ServletWebServerFactory</code></a> bean.
Usually a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/tomcat/TomcatServletWebServerFactory.html"><code>TomcatServletWebServerFactory</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/jetty/JettyServletWebServerFactory.html"><code>JettyServletWebServerFactory</code></a>, or <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/undertow/UndertowServletWebServerFactory.html"><code>UndertowServletWebServerFactory</code></a> has been auto-configured.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
You usually do not need to be aware of these implementation classes.
Most applications are auto-configured, and the appropriate <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/server/ServletWebServerFactory.html"><code>ServletWebServerFactory</code></a> are created on your behalf.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>In an embedded container setup, the <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/ServletContext.html" target="_blank"><code>ServletContext</code></a> is set as part of server startup which happens during application context initialization.
Because of this beans in the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a> cannot be reliably initialized with a <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/ServletContext.html" target="_blank"><code>ServletContext</code></a>.
One way to get around this is to inject <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html"><code>ApplicationContext</code></a> as a dependency of the bean and access the <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/ServletContext.html" target="_blank"><code>ServletContext</code></a> only when it is needed.
Another way is to use a callback once the server has started.
This can be done using an <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationListener.html"><code>ApplicationListener</code></a> which listens for the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/event/ApplicationStartedEvent.html"><code>ApplicationStartedEvent</code></a> as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> jakarta.servlet.ServletContext;

<span class="hljs-keyword">import</span> org.springframework.boot.context.event.ApplicationStartedEvent;
<span class="hljs-keyword">import</span> org.springframework.context.ApplicationContext;
<span class="hljs-keyword">import</span> org.springframework.context.ApplicationListener;
<span class="hljs-keyword">import</span> org.springframework.web.context.WebApplicationContext;

</span><span class="fold-block"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDemoBean</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">ApplicationListener</span>&lt;<span class="hljs-title">ApplicationStartedEvent</span>&gt; </span>{

	<span class="hljs-keyword">private</span> ServletContext servletContext;

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">onApplicationEvent</span><span class="hljs-params">(ApplicationStartedEvent event)</span> </span>{
		ApplicationContext applicationContext = event.getApplicationContext();
		<span class="hljs-keyword">this</span>.servletContext = ((WebApplicationContext) applicationContext).getServletContext();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.embedded-container.customizing"><a class="anchor" href="#web.servlet.embedded-container.customizing"></a>Customizing Embedded Servlet Containers</h3>
<div class="paragraph">
<p>Common servlet container settings can be configured by using Spring <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> properties.
Usually, you would define the properties in your <code>application.properties</code> or <code>application.yaml</code> file.</p>
</div>
<div class="paragraph">
<p>Common server settings include:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Network settings: Listen port for incoming HTTP requests (<code>server.port</code>), interface address to bind to (<code>server.address</code>), and so on.</p>
</li>
<li>
<p>Session settings: Whether the session is persistent (<code>server.servlet.session.persistent</code>), session timeout (<code>server.servlet.session.timeout</code>), location of session data (<code>server.servlet.session.store-dir</code>), and session-cookie configuration (<code>server.servlet.session.cookie.*</code>).</p>
</li>
<li>
<p>Error management: Location of the error page (<code>server.error.path</code>) and so on.</p>
</li>
<li>
<p><a class="xref page" href="../../how-to/webserver.html#howto.webserver.configure-ssl">SSL</a></p>
</li>
<li>
<p><a class="xref page" href="../../how-to/webserver.html#howto.webserver.enable-response-compression">HTTP compression</a></p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Spring Boot tries as much as possible to expose common settings, but this is not always possible.
For those cases, dedicated namespaces offer server-specific customizations (see <code>server.tomcat</code> and <code>server.undertow</code>).
For instance, <a class="xref page" href="../../how-to/webserver.html#howto.webserver.configure-access-logs">access logs</a> can be configured with specific features of the embedded servlet container.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
See the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/ServerProperties.html"><code>ServerProperties</code></a> class for a complete list.
</td>
</tr>
</tbody></table>
</div>
<div class="sect3">
<h4 id="web.servlet.embedded-container.customizing.samesite"><a class="anchor" href="#web.servlet.embedded-container.customizing.samesite"></a>SameSite Cookies</h4>
<div class="paragraph">
<p>The <code>SameSite</code> cookie attribute can be used by web browsers to control if and how cookies are submitted in cross-site requests.
The attribute is particularly relevant for modern web browsers which have started to change the default value that is used when the attribute is missing.</p>
</div>
<div class="paragraph">
<p>If you want to change the <code>SameSite</code> attribute of your session cookie, you can use the <code>server.servlet.session.cookie.same-site</code> property.
This property is supported by auto-configured Tomcat, Jetty and Undertow servers.
It is also used to configure Spring Session servlet based <a class="apiref" href="https://docs.spring.io/spring-session/docs/3.4.x/api/org/springframework/session/SessionRepository.html"><code>SessionRepository</code></a> beans.</p>
</div>
<div class="paragraph">
<p>For example, if you want your session cookie to have a <code>SameSite</code> attribute of <code>None</code>, you can add the following to your <code>application.properties</code> or <code>application.yaml</code> file:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_17">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_17_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_17_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_17_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_17_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_17_properties" class="tabpanel" id="_tabs_17_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.servlet.session.cookie.same-site</span>=<span class="hljs-string">none</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_17_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_17_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">servlet:</span>
    <span class="hljs-attr">session:</span>
      <span class="hljs-attr">cookie:</span>
        <span class="hljs-attr">same-site:</span> <span class="hljs-string">"none"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you want to change the <code>SameSite</code> attribute on other cookies added to your <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/http/HttpServletResponse.html" target="_blank"><code>HttpServletResponse</code></a>, you can use a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/server/CookieSameSiteSupplier.html"><code>CookieSameSiteSupplier</code></a>.
The <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/server/CookieSameSiteSupplier.html"><code>CookieSameSiteSupplier</code></a> is passed a <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/http/Cookie.html" target="_blank"><code>Cookie</code></a> and may return a <code>SameSite</code> value, or <code>null</code>.</p>
</div>
<div class="paragraph">
<p>There are a number of convenience factory and filter methods that you can use to quickly match specific cookies.
For example, adding the following bean will automatically apply a <code>SameSite</code> of <code>Lax</code> for all cookies with a name that matches the regular expression <code>myapp.*</code>.</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_18">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_18_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_18_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_18_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_18_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_18_java" class="tabpanel" id="_tabs_18_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.servlet.server.CookieSameSiteSupplier;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MySameSiteConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> CookieSameSiteSupplier <span class="hljs-title">applicationCookieSameSiteSupplier</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> CookieSameSiteSupplier.ofLax().whenHasNameMatching(<span class="hljs-string">"myapp.*"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_18_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_18_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.servlet.server.CookieSameSiteSupplier
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MySameSiteConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">applicationCookieSameSiteSupplier</span><span class="hljs-params">()</span></span>: CookieSameSiteSupplier {
		<span class="hljs-keyword">return</span> CookieSameSiteSupplier.ofLax().whenHasNameMatching(<span class="hljs-string">"myapp.*"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect3">
<h4 id="web.servlet.embedded-container.customizing.encoding"><a class="anchor" href="#web.servlet.embedded-container.customizing.encoding"></a>Character Encoding</h4>
<div class="paragraph">
<p>The character encoding behavior of the embedded servlet container for request and response handling can be configured using the <code>server.servlet.encoding.*</code> configuration properties.</p>
</div>
<div class="paragraph">
<p>When a request’s <code>Accept-Language</code> header indicates a locale for the request it will be automatically mapped to a charset by the servlet container.
Each container provides default locale to charset mappings and you should verify that they meet your application’s needs.
When they do not, use the <code>server.servlet.encoding.mapping</code> configuration property to customize the mappings, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_19">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_19_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_19_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_19_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_19_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_19_properties" class="tabpanel" id="_tabs_19_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.servlet.encoding.mapping.ko</span>=<span class="hljs-string">UTF-8</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_19_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_19_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">servlet:</span>
    <span class="hljs-attr">encoding:</span>
      <span class="hljs-attr">mapping:</span>
        <span class="hljs-attr">ko:</span> <span class="hljs-string">"UTF-8"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>In the preceding example, the <code>ko</code> (Korean) locale has been mapped to <code>UTF-8</code>.
This is equivalent to a <code>&lt;locale-encoding-mapping-list&gt;</code> entry in a <code>web.xml</code> file of a traditional war deployment.</p>
</div>
</div>
<div class="sect3">
<h4 id="web.servlet.embedded-container.customizing.programmatic"><a class="anchor" href="#web.servlet.embedded-container.customizing.programmatic"></a>Programmatic Customization</h4>
<div class="paragraph">
<p>If you need to programmatically configure your embedded servlet container, you can register a Spring bean that implements the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/WebServerFactoryCustomizer.html"><code>WebServerFactoryCustomizer</code></a> interface.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/WebServerFactoryCustomizer.html"><code>WebServerFactoryCustomizer</code></a> provides access to the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/server/ConfigurableServletWebServerFactory.html"><code>ConfigurableServletWebServerFactory</code></a>, which includes numerous customization setter methods.
The following example shows programmatically setting the port:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_20">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_20_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_20_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_20_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_20_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_20_java" class="tabpanel" id="_tabs_20_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer;
<span class="hljs-keyword">import</span> org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebServerFactoryCustomizer</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">WebServerFactoryCustomizer</span>&lt;<span class="hljs-title">ConfigurableServletWebServerFactory</span>&gt; </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">customize</span><span class="hljs-params">(ConfigurableServletWebServerFactory server)</span> </span>{
		server.setPort(<span class="hljs-number">9000</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_20_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_20_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer
<span class="hljs-keyword">import</span> org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebServerFactoryCustomizer</span> : <span class="hljs-type">WebServerFactoryCustomizer</span>&lt;<span class="hljs-type">ConfigurableServletWebServerFactory</span>&gt; </span>{

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">customize</span><span class="hljs-params">(server: <span class="hljs-type">ConfigurableServletWebServerFactory</span>)</span></span> {
		server.setPort(<span class="hljs-number">9000</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/tomcat/TomcatServletWebServerFactory.html"><code>TomcatServletWebServerFactory</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/jetty/JettyServletWebServerFactory.html"><code>JettyServletWebServerFactory</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/undertow/UndertowServletWebServerFactory.html"><code>UndertowServletWebServerFactory</code></a> are dedicated variants of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/server/ConfigurableServletWebServerFactory.html"><code>ConfigurableServletWebServerFactory</code></a> that have additional customization setter methods for Tomcat, Jetty and Undertow respectively.
The following example shows how to customize <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/tomcat/TomcatServletWebServerFactory.html"><code>TomcatServletWebServerFactory</code></a> that provides access to Tomcat-specific configuration options:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_21">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_21_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_21_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_21_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_21_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_21_java" class="tabpanel" id="_tabs_21_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.time.Duration;

<span class="hljs-keyword">import</span> org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyTomcatWebServerFactoryCustomizer</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">WebServerFactoryCustomizer</span>&lt;<span class="hljs-title">TomcatServletWebServerFactory</span>&gt; </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">customize</span><span class="hljs-params">(TomcatServletWebServerFactory server)</span> </span>{
		server.addConnectorCustomizers((connector) -&gt; connector.setAsyncTimeout(Duration.ofSeconds(<span class="hljs-number">20</span>).toMillis()));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_21_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_21_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component
<span class="hljs-keyword">import</span> java.time.Duration

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyTomcatWebServerFactoryCustomizer</span> : <span class="hljs-type">WebServerFactoryCustomizer</span>&lt;<span class="hljs-type">TomcatServletWebServerFactory</span>&gt; </span>{

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">customize</span><span class="hljs-params">(server: <span class="hljs-type">TomcatServletWebServerFactory</span>)</span></span> {
		server.addConnectorCustomizers({ connector -&gt; connector.asyncTimeout = Duration.ofSeconds(<span class="hljs-number">20</span>).toMillis() })
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect3">
<h4 id="web.servlet.embedded-container.customizing.direct"><a class="anchor" href="#web.servlet.embedded-container.customizing.direct"></a>Customizing ConfigurableServletWebServerFactory Directly</h4>
<div class="paragraph">
<p>For more advanced use cases that require you to extend from <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/server/ServletWebServerFactory.html"><code>ServletWebServerFactory</code></a>, you can expose a bean of such type yourself.</p>
</div>
<div class="paragraph">
<p>Setters are provided for many configuration options.
Several protected method “hooks” are also provided should you need to do something more exotic.
See the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/server/ConfigurableServletWebServerFactory.html"><code>ConfigurableServletWebServerFactory</code></a> API documentation for details.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Auto-configured customizers are still applied on your custom factory, so use that option carefully.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect2">
<h3 id="web.servlet.embedded-container.jsp-limitations"><a class="anchor" href="#web.servlet.embedded-container.jsp-limitations"></a>JSP Limitations</h3>
<div class="paragraph">
<p>When running a Spring Boot application that uses an embedded servlet container (and is packaged as an executable archive), there are some limitations in the JSP support.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>With Jetty and Tomcat, it should work if you use war packaging.
An executable war will work when launched with <code>java -jar</code>, and will also be deployable to any standard container.
JSPs are not supported when using an executable jar.</p>
</li>
<li>
<p>Undertow does not support JSPs.</p>
</li>
<li>
<p>Creating a custom <code>error.jsp</code> page does not override the default view for <a href="#web.servlet.spring-mvc.error-handling">error handling</a>.
<a href="#web.servlet.spring-mvc.error-handling.error-pages">Custom error pages</a> should be used instead.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="index.html">Web</a></span>
<span class="next"><a href="reactive.html">Reactive Web Applications</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../reference/web/servlet.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="servlet.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../3.3/reference/web/servlet.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../4.0-SNAPSHOT/reference/web/servlet.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.5-SNAPSHOT/reference/web/servlet.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.4-SNAPSHOT/reference/web/servlet.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.3-SNAPSHOT/reference/web/servlet.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>