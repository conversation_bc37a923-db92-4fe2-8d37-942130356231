<!DOCTYPE html>
<html><head><title>Working with NoSQL Technologies :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/reference/data/nosql.html"/><meta content="2025-06-04T16:01:42.438113" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="sql.html">SQL Databases</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Working with NoSQL Technologies">
<div class="toc-menu"><h3>Working with NoSQL Technologies</h3><ul><li data-level="1"><a href="#data.nosql.redis">Redis</a></li><li data-level="2"><a href="#data.nosql.redis.connecting">Connecting to Redis</a></li><li data-level="1"><a href="#data.nosql.mongodb">MongoDB</a></li><li data-level="2"><a href="#data.nosql.mongodb.connecting">Connecting to a MongoDB Database</a></li><li data-level="2"><a href="#data.nosql.mongodb.template">MongoTemplate</a></li><li data-level="2"><a href="#data.nosql.mongodb.repositories">Spring Data MongoDB Repositories</a></li><li data-level="1"><a href="#data.nosql.neo4j">Neo4j</a></li><li data-level="2"><a href="#data.nosql.neo4j.connecting">Connecting to a Neo4j Database</a></li><li data-level="2"><a href="#data.nosql.neo4j.repositories">Spring Data Neo4j Repositories</a></li><li data-level="1"><a href="#data.nosql.elasticsearch">Elasticsearch</a></li><li data-level="2"><a href="#data.nosql.elasticsearch.connecting-using-rest">Connecting to Elasticsearch Using REST clients</a></li><li data-level="2"><a href="#data.nosql.elasticsearch.connecting-using-spring-data">Connecting to Elasticsearch by Using Spring Data</a></li><li data-level="2"><a href="#data.nosql.elasticsearch.repositories">Spring Data Elasticsearch Repositories</a></li><li data-level="1"><a href="#data.nosql.cassandra">Cassandra</a></li><li data-level="2"><a href="#data.nosql.cassandra.connecting">Connecting to Cassandra</a></li><li data-level="2"><a href="#data.nosql.cassandra.repositories">Spring Data Cassandra Repositories</a></li><li data-level="1"><a href="#data.nosql.couchbase">Couchbase</a></li><li data-level="2"><a href="#data.nosql.couchbase.connecting">Connecting to Couchbase</a></li><li data-level="2"><a href="#data.nosql.couchbase.repositories">Spring Data Couchbase Repositories</a></li><li data-level="1"><a href="#data.nosql.ldap">LDAP</a></li><li data-level="2"><a href="#data.nosql.ldap.connecting">Connecting to an LDAP Server</a></li><li data-level="2"><a href="#data.nosql.ldap.repositories">Spring Data LDAP Repositories</a></li><li data-level="2"><a href="#data.nosql.ldap.embedded">Embedded In-memory LDAP Server</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/data/nosql.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../index.html">Spring Boot</a></li>
<li><a href="../index.html">Reference</a></li>
<li><a href="index.html">Data</a></li>
<li><a href="nosql.html">Working with NoSQL Technologies</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../reference/data/nosql.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Working with NoSQL Technologies</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Working with NoSQL Technologies</h3><ul><li data-level="1"><a href="#data.nosql.redis">Redis</a></li><li data-level="2"><a href="#data.nosql.redis.connecting">Connecting to Redis</a></li><li data-level="1"><a href="#data.nosql.mongodb">MongoDB</a></li><li data-level="2"><a href="#data.nosql.mongodb.connecting">Connecting to a MongoDB Database</a></li><li data-level="2"><a href="#data.nosql.mongodb.template">MongoTemplate</a></li><li data-level="2"><a href="#data.nosql.mongodb.repositories">Spring Data MongoDB Repositories</a></li><li data-level="1"><a href="#data.nosql.neo4j">Neo4j</a></li><li data-level="2"><a href="#data.nosql.neo4j.connecting">Connecting to a Neo4j Database</a></li><li data-level="2"><a href="#data.nosql.neo4j.repositories">Spring Data Neo4j Repositories</a></li><li data-level="1"><a href="#data.nosql.elasticsearch">Elasticsearch</a></li><li data-level="2"><a href="#data.nosql.elasticsearch.connecting-using-rest">Connecting to Elasticsearch Using REST clients</a></li><li data-level="2"><a href="#data.nosql.elasticsearch.connecting-using-spring-data">Connecting to Elasticsearch by Using Spring Data</a></li><li data-level="2"><a href="#data.nosql.elasticsearch.repositories">Spring Data Elasticsearch Repositories</a></li><li data-level="1"><a href="#data.nosql.cassandra">Cassandra</a></li><li data-level="2"><a href="#data.nosql.cassandra.connecting">Connecting to Cassandra</a></li><li data-level="2"><a href="#data.nosql.cassandra.repositories">Spring Data Cassandra Repositories</a></li><li data-level="1"><a href="#data.nosql.couchbase">Couchbase</a></li><li data-level="2"><a href="#data.nosql.couchbase.connecting">Connecting to Couchbase</a></li><li data-level="2"><a href="#data.nosql.couchbase.repositories">Spring Data Couchbase Repositories</a></li><li data-level="1"><a href="#data.nosql.ldap">LDAP</a></li><li data-level="2"><a href="#data.nosql.ldap.connecting">Connecting to an LDAP Server</a></li><li data-level="2"><a href="#data.nosql.ldap.repositories">Spring Data LDAP Repositories</a></li><li data-level="2"><a href="#data.nosql.ldap.embedded">Embedded In-memory LDAP Server</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>Spring Data provides additional projects that help you access a variety of NoSQL technologies, including:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a class="external" href="https://spring.io/projects/spring-data-cassandra" target="_blank">Cassandra</a></p>
</li>
<li>
<p><a class="external" href="https://spring.io/projects/spring-data-couchbase" target="_blank">Couchbase</a></p>
</li>
<li>
<p><a class="external" href="https://spring.io/projects/spring-data-elasticsearch" target="_blank">Elasticsearch</a></p>
</li>
<li>
<p><a class="external" href="https://spring.io/projects/spring-data-geode" target="_blank">Geode</a></p>
</li>
<li>
<p><a class="external" href="https://spring.io/projects/spring-data-ldap" target="_blank">LDAP</a></p>
</li>
<li>
<p><a class="external" href="https://spring.io/projects/spring-data-mongodb" target="_blank">MongoDB</a></p>
</li>
<li>
<p><a class="external" href="https://spring.io/projects/spring-data-neo4j" target="_blank">Neo4J</a></p>
</li>
<li>
<p><a class="external" href="https://spring.io/projects/spring-data-redis" target="_blank">Redis</a></p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Of these, Spring Boot provides auto-configuration for Cassandra, Couchbase, Elasticsearch, LDAP, MongoDB, Neo4J and Redis.
Additionally, <a class="external" href="https://github.com/spring-projects/spring-boot-data-geode" target="_blank">Spring Boot for Apache Geode</a> provides <a href="https://docs.spring.io/spring-boot-data-geode-build/2.0.x/reference/html5#geode-repositories">auto-configuration for Apache Geode</a>.
You can make use of the other projects, but you must configure them yourself.
See the appropriate reference documentation at <a class="bare external" href="https://spring.io/projects/spring-data" target="_blank">spring.io/projects/spring-data</a>.</p>
</div>
<div class="paragraph">
<p>Spring Boot also provides auto-configuration for the InfluxDB client but it is deprecated in favor of <a class="external" href="https://github.com/influxdata/influxdb-client-java" target="_blank">the new InfluxDB Java client</a> that provides its own Spring Boot integration.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.nosql.redis"><a class="anchor" href="#data.nosql.redis"></a>Redis</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="external" href="https://redis.io/" target="_blank">Redis</a> is a cache, message broker, and richly-featured key-value store.
Spring Boot offers basic auto-configuration for the <a class="external" href="https://github.com/lettuce-io/lettuce-core/" target="_blank">Lettuce</a> and <a class="external" href="https://github.com/xetorthio/jedis/" target="_blank">Jedis</a> client libraries and the abstractions on top of them provided by <a class="external" href="https://github.com/spring-projects/spring-data-redis" target="_blank">Spring Data Redis</a>.</p>
</div>
<div class="paragraph">
<p>There is a <code>spring-boot-starter-data-redis</code> starter for collecting the dependencies in a convenient way.
By default, it uses <a class="external" href="https://github.com/lettuce-io/lettuce-core/" target="_blank">Lettuce</a>.
That starter handles both traditional and reactive applications.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
We also provide a <code>spring-boot-starter-data-redis-reactive</code> starter for consistency with the other stores with reactive support.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="data.nosql.redis.connecting"><a class="anchor" href="#data.nosql.redis.connecting"></a>Connecting to Redis</h3>
<div class="paragraph">
<p>You can inject an auto-configured <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/connection/RedisConnectionFactory.html"><code>RedisConnectionFactory</code></a>, <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/core/StringRedisTemplate.html"><code>StringRedisTemplate</code></a>, or vanilla <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/core/RedisTemplate.html"><code>RedisTemplate</code></a> instance as you would any other Spring Bean.
The following listing shows an example of such a bean:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_1_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_1_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_1_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_java" class="tabpanel" id="_tabs_1_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.data.redis.core.StringRedisTemplate;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> StringRedisTemplate template;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(StringRedisTemplate template)</span> </span>{
		<span class="hljs-keyword">this</span>.template = template;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> Boolean <span class="hljs-title">someMethod</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.template.hasKey(<span class="hljs-string">"spring"</span>);
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_1_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.redis.core.StringRedisTemplate
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> template: StringRedisTemplate) {

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someMethod</span><span class="hljs-params">()</span></span>: <span class="hljs-built_in">Boolean</span> {
		<span class="hljs-keyword">return</span> template.hasKey(<span class="hljs-string">"spring"</span>)
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>By default, the instance tries to connect to a Redis server at <code>localhost:6379</code>.
You can specify custom connection details using <code>spring.data.redis.*</code> properties, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_2_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_2_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_2_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_properties" class="tabpanel" id="_tabs_2_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.data.redis.host</span>=<span class="hljs-string">localhost</span>
<span class="hljs-meta">spring.data.redis.port</span>=<span class="hljs-string">6379</span>
<span class="hljs-meta">spring.data.redis.database</span>=<span class="hljs-string">0</span>
<span class="hljs-meta">spring.data.redis.username</span>=<span class="hljs-string">user</span>
<span class="hljs-meta">spring.data.redis.password</span>=<span class="hljs-string">secret</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_2_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">data:</span>
    <span class="hljs-attr">redis:</span>
      <span class="hljs-attr">host:</span> <span class="hljs-string">"localhost"</span>
      <span class="hljs-attr">port:</span> <span class="hljs-number">6379</span>
      <span class="hljs-attr">database:</span> <span class="hljs-number">0</span>
      <span class="hljs-attr">username:</span> <span class="hljs-string">"user"</span>
      <span class="hljs-attr">password:</span> <span class="hljs-string">"secret"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also specify the url of the Redis server directly.
When setting the url, the host, port, username and password properties are ignored.
This is shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_3_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_3_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_3_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_properties" class="tabpanel" id="_tabs_3_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.data.redis.url</span>=<span class="hljs-string">redis://user:secret@localhost:6379</span>
<span class="hljs-meta">spring.data.redis.database</span>=<span class="hljs-string">0</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_3_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">data:</span>
    <span class="hljs-attr">redis:</span>
      <span class="hljs-attr">url:</span> <span class="hljs-string">"redis://user:secret@localhost:6379"</span>
      <span class="hljs-attr">database:</span> <span class="hljs-number">0</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can also register an arbitrary number of beans that implement <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/data/redis/LettuceClientConfigurationBuilderCustomizer.html"><code>LettuceClientConfigurationBuilderCustomizer</code></a> for more advanced customizations.
<a class="apiref external" href="https://javadoc.io/doc/io.lettuce/lettuce-core/6.4.2.RELEASE/io/lettuce/core/resource/ClientResources.html" target="_blank"><code>ClientResources</code></a> can also be customized using <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/data/redis/ClientResourcesBuilderCustomizer.html"><code>ClientResourcesBuilderCustomizer</code></a>.
If you use Jedis, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/data/redis/JedisClientConfigurationBuilderCustomizer.html"><code>JedisClientConfigurationBuilderCustomizer</code></a> is also available.
Alternatively, you can register a bean of type <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/connection/RedisStandaloneConfiguration.html"><code>RedisStandaloneConfiguration</code></a>, <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/connection/RedisSentinelConfiguration.html"><code>RedisSentinelConfiguration</code></a>, or <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/connection/RedisClusterConfiguration.html"><code>RedisClusterConfiguration</code></a> to take full control over the configuration.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If you add your own <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> of any of the auto-configured types, it replaces the default (except in the case of <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/core/RedisTemplate.html"><code>RedisTemplate</code></a>, when the exclusion is based on the bean name, <code>redisTemplate</code>, not its type).</p>
</div>
<div class="paragraph">
<p>By default, a pooled connection factory is auto-configured if <code>commons-pool2</code> is on the classpath.</p>
</div>
<div class="paragraph">
<p>The auto-configured <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/connection/RedisConnectionFactory.html"><code>RedisConnectionFactory</code></a> can be configured to use SSL for communication with the server by setting the properties as shown in this example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_4_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_4_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_4_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_properties" class="tabpanel" id="_tabs_4_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.data.redis.ssl.enabled</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_4_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">data:</span>
    <span class="hljs-attr">redis:</span>
      <span class="hljs-attr">ssl:</span>
        <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Custom SSL trust material can be configured in an <a class="xref page" href="../features/ssl.html">SSL bundle</a> and applied to the <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/connection/RedisConnectionFactory.html"><code>RedisConnectionFactory</code></a> as shown in this example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_5_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_5_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_5_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_properties" class="tabpanel" id="_tabs_5_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.data.redis.ssl.bundle</span>=<span class="hljs-string">example</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_5_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">data:</span>
    <span class="hljs-attr">redis:</span>
      <span class="hljs-attr">ssl:</span>
        <span class="hljs-attr">bundle:</span> <span class="hljs-string">"example"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.nosql.mongodb"><a class="anchor" href="#data.nosql.mongodb"></a>MongoDB</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="external" href="https://www.mongodb.com/" target="_blank">MongoDB</a> is an open-source NoSQL document database that uses a JSON-like schema instead of traditional table-based relational data.
Spring Boot offers several conveniences for working with MongoDB, including the <code>spring-boot-starter-data-mongodb</code> and <code>spring-boot-starter-data-mongodb-reactive</code> starters.</p>
</div>
<div class="sect2">
<h3 id="data.nosql.mongodb.connecting"><a class="anchor" href="#data.nosql.mongodb.connecting"></a>Connecting to a MongoDB Database</h3>
<div class="paragraph">
<p>To access MongoDB databases, you can inject an auto-configured <a class="apiref" href="https://docs.spring.io/spring-data/mongodb/docs/4.4.x/api/org/springframework/data/mongodb/MongoDatabaseFactory.html"><code>MongoDatabaseFactory</code></a>.
By default, the instance tries to connect to a MongoDB server at <code>mongodb://localhost/test</code>.
The following example shows how to connect to a MongoDB database:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_6_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_6_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_6_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_java" class="tabpanel" id="_tabs_6_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> com.mongodb.client.MongoCollection;
<span class="hljs-keyword">import</span> com.mongodb.client.MongoDatabase;
<span class="hljs-keyword">import</span> org.bson.Document;

<span class="hljs-keyword">import</span> org.springframework.data.mongodb.MongoDatabaseFactory;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> MongoDatabaseFactory mongo;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(MongoDatabaseFactory mongo)</span> </span>{
		<span class="hljs-keyword">this</span>.mongo = mongo;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> MongoCollection&lt;Document&gt; <span class="hljs-title">someMethod</span><span class="hljs-params">()</span> </span>{
		MongoDatabase db = <span class="hljs-keyword">this</span>.mongo.getMongoDatabase();
		<span class="hljs-keyword">return</span> db.getCollection(<span class="hljs-string">"users"</span>);
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_6_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> com.mongodb.client.MongoCollection
<span class="hljs-keyword">import</span> org.bson.Document
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.mongodb.MongoDatabaseFactory
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> mongo: MongoDatabaseFactory) {

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someMethod</span><span class="hljs-params">()</span></span>: MongoCollection&lt;Document&gt; {
		<span class="hljs-keyword">val</span> db = mongo.mongoDatabase
		<span class="hljs-keyword">return</span> db.getCollection(<span class="hljs-string">"users"</span>)
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you have defined your own <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-sync/com/mongodb/client/MongoClient.html" target="_blank"><code>MongoClient</code></a>, it will be used to auto-configure a suitable <a class="apiref" href="https://docs.spring.io/spring-data/mongodb/docs/4.4.x/api/org/springframework/data/mongodb/MongoDatabaseFactory.html"><code>MongoDatabaseFactory</code></a>.</p>
</div>
<div class="paragraph">
<p>The auto-configured <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-sync/com/mongodb/client/MongoClient.html" target="_blank"><code>MongoClient</code></a> is created using a <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-core/com/mongodb/MongoClientSettings.html" target="_blank"><code>MongoClientSettings</code></a> bean.
If you have defined your own <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-core/com/mongodb/MongoClientSettings.html" target="_blank"><code>MongoClientSettings</code></a>, it will be used without modification and the <code>spring.data.mongodb</code> properties will be ignored.
Otherwise a <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-core/com/mongodb/MongoClientSettings.html" target="_blank"><code>MongoClientSettings</code></a> will be auto-configured and will have the <code>spring.data.mongodb</code> properties applied to it.
In either case, you can declare one or more <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/mongo/MongoClientSettingsBuilderCustomizer.html"><code>MongoClientSettingsBuilderCustomizer</code></a> beans to fine-tune the <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-core/com/mongodb/MongoClientSettings.html" target="_blank"><code>MongoClientSettings</code></a> configuration.
Each will be called in order with the <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-core/com/mongodb/MongoClientSettings.Builder.html" target="_blank"><code>MongoClientSettings.Builder</code></a> that is used to build the <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-core/com/mongodb/MongoClientSettings.html" target="_blank"><code>MongoClientSettings</code></a>.</p>
</div>
<div class="paragraph">
<p>You can set the <code>spring.data.mongodb.uri</code> property to change the URL and configure additional settings such as the <em>replica set</em>, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_7_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_7_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_7_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_properties" class="tabpanel" id="_tabs_7_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.data.mongodb.uri</span>=<span class="hljs-string">mongodb://user:<EMAIL>:27017,mongoserver2.example.com:23456/test</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_7_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">data:</span>
    <span class="hljs-attr">mongodb:</span>
      <span class="hljs-attr">uri:</span> <span class="hljs-string">"mongodb://user:<EMAIL>:27017,mongoserver2.example.com:23456/test"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Alternatively, you can specify connection details using discrete properties.
For example, you might declare the following settings in your <code>application.properties</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_8_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_8_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_8_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_properties" class="tabpanel" id="_tabs_8_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.data.mongodb.host</span>=<span class="hljs-string">mongoserver1.example.com</span>
<span class="hljs-meta">spring.data.mongodb.port</span>=<span class="hljs-string">27017</span>
<span class="hljs-meta">spring.data.mongodb.additional-hosts[0]</span>=<span class="hljs-string">mongoserver2.example.com:23456</span>
<span class="hljs-meta">spring.data.mongodb.database</span>=<span class="hljs-string">test</span>
<span class="hljs-meta">spring.data.mongodb.username</span>=<span class="hljs-string">user</span>
<span class="hljs-meta">spring.data.mongodb.password</span>=<span class="hljs-string">secret</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_8_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">data:</span>
    <span class="hljs-attr">mongodb:</span>
      <span class="hljs-attr">host:</span> <span class="hljs-string">"mongoserver1.example.com"</span>
      <span class="hljs-attr">port:</span> <span class="hljs-number">27017</span>
      <span class="hljs-attr">additional-hosts:</span>
      <span class="hljs-bullet">-</span> <span class="hljs-string">"mongoserver2.example.com:23456"</span>
      <span class="hljs-attr">database:</span> <span class="hljs-string">"test"</span>
      <span class="hljs-attr">username:</span> <span class="hljs-string">"user"</span>
      <span class="hljs-attr">password:</span> <span class="hljs-string">"secret"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The auto-configured <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-sync/com/mongodb/client/MongoClient.html" target="_blank"><code>MongoClient</code></a> can be configured to use SSL for communication with the server by setting the properties as shown in this example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_9">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_9_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_9_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_9_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_9_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_9_properties" class="tabpanel" id="_tabs_9_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.data.mongodb.uri</span>=<span class="hljs-string">mongodb://user:<EMAIL>:27017,mongoserver2.example.com:23456/test</span>
<span class="hljs-meta">spring.data.mongodb.ssl.enabled</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_9_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_9_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">data:</span>
    <span class="hljs-attr">mongodb:</span>
      <span class="hljs-attr">uri:</span> <span class="hljs-string">"mongodb://user:<EMAIL>:27017,mongoserver2.example.com:23456/test"</span>
      <span class="hljs-attr">ssl:</span>
        <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Custom SSL trust material can be configured in an <a class="xref page" href="../features/ssl.html">SSL bundle</a> and applied to the <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-sync/com/mongodb/client/MongoClient.html" target="_blank"><code>MongoClient</code></a> as shown in this example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_10">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_10_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_10_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_10_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_10_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_10_properties" class="tabpanel" id="_tabs_10_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.data.mongodb.uri</span>=<span class="hljs-string">mongodb://user:<EMAIL>:27017,mongoserver2.example.com:23456/test</span>
<span class="hljs-meta">spring.data.mongodb.ssl.bundle</span>=<span class="hljs-string">example</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_10_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_10_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">data:</span>
    <span class="hljs-attr">mongodb:</span>
      <span class="hljs-attr">uri:</span> <span class="hljs-string">"mongodb://user:<EMAIL>:27017,mongoserver2.example.com:23456/test"</span>
      <span class="hljs-attr">ssl:</span>
        <span class="hljs-attr">bundle:</span> <span class="hljs-string">"example"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<div class="paragraph">
<p>If <code>spring.data.mongodb.port</code> is not specified, the default of <code>27017</code> is used.
You could delete this line from the example shown earlier.</p>
</div>
<div class="paragraph">
<p>You can also specify the port as part of the host address by using the <code>host:port</code> syntax.
This format should be used if you need to change the port of an <code>additional-hosts</code> entry.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you do not use Spring Data MongoDB, you can inject a <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-sync/com/mongodb/client/MongoClient.html" target="_blank"><code>MongoClient</code></a> bean instead of using <a class="apiref" href="https://docs.spring.io/spring-data/mongodb/docs/4.4.x/api/org/springframework/data/mongodb/MongoDatabaseFactory.html"><code>MongoDatabaseFactory</code></a>.
If you want to take complete control of establishing the MongoDB connection, you can also declare your own <a class="apiref" href="https://docs.spring.io/spring-data/mongodb/docs/4.4.x/api/org/springframework/data/mongodb/MongoDatabaseFactory.html"><code>MongoDatabaseFactory</code></a> or <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-sync/com/mongodb/client/MongoClient.html" target="_blank"><code>MongoClient</code></a> bean.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If you are using the reactive driver, Netty is required for SSL.
The auto-configuration configures this factory automatically if Netty is available and the factory to use has not been customized already.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="data.nosql.mongodb.template"><a class="anchor" href="#data.nosql.mongodb.template"></a>MongoTemplate</h3>
<div class="paragraph">
<p><a class="external" href="https://spring.io/projects/spring-data-mongodb" target="_blank">Spring Data MongoDB</a> provides a <a class="apiref" href="https://docs.spring.io/spring-data/mongodb/docs/4.4.x/api/org/springframework/data/mongodb/core/MongoTemplate.html"><code>MongoTemplate</code></a> class that is very similar in its design to Spring’s <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/JdbcTemplate.html"><code>JdbcTemplate</code></a>.
As with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jdbc/core/JdbcTemplate.html"><code>JdbcTemplate</code></a>, Spring Boot auto-configures a bean for you to inject the template, as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_11">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_11_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_11_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_11_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_11_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_11_java" class="tabpanel" id="_tabs_11_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> com.mongodb.client.MongoCollection;
<span class="hljs-keyword">import</span> org.bson.Document;

<span class="hljs-keyword">import</span> org.springframework.data.mongodb.core.MongoTemplate;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> MongoTemplate mongoTemplate;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(MongoTemplate mongoTemplate)</span> </span>{
		<span class="hljs-keyword">this</span>.mongoTemplate = mongoTemplate;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> MongoCollection&lt;Document&gt; <span class="hljs-title">someMethod</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.mongoTemplate.getCollection(<span class="hljs-string">"users"</span>);
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_11_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_11_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> com.mongodb.client.MongoCollection
<span class="hljs-keyword">import</span> org.bson.Document
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.mongodb.core.MongoTemplate
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> mongoTemplate: MongoTemplate) {

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someMethod</span><span class="hljs-params">()</span></span>: MongoCollection&lt;Document&gt; {
		<span class="hljs-keyword">return</span> mongoTemplate.getCollection(<span class="hljs-string">"users"</span>)
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>See the <a class="apiref" href="https://docs.spring.io/spring-data/mongodb/docs/4.4.x/api/org/springframework/data/mongodb/core/MongoOperations.html"><code>MongoOperations</code></a> API documentation for complete details.</p>
</div>
</div>
<div class="sect2">
<h3 id="data.nosql.mongodb.repositories"><a class="anchor" href="#data.nosql.mongodb.repositories"></a>Spring Data MongoDB Repositories</h3>
<div class="paragraph">
<p>Spring Data includes repository support for MongoDB.
As with the JPA repositories discussed earlier, the basic principle is that queries are constructed automatically, based on method names.</p>
</div>
<div class="paragraph">
<p>In fact, both Spring Data JPA and Spring Data MongoDB share the same common infrastructure.
You could take the JPA example from earlier and, assuming that <code>City</code> is now a MongoDB data class rather than a JPA <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/Entity.html" target="_blank"><code>@Entity</code></a>, it works in the same way, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_12">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_12_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_12_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_12_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_12_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_12_java" class="tabpanel" id="_tabs_12_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.data.domain.Page;
<span class="hljs-keyword">import</span> org.springframework.data.domain.Pageable;
<span class="hljs-keyword">import</span> org.springframework.data.repository.Repository;

</span><span class="fold-block"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">CityRepository</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">Repository</span>&lt;<span class="hljs-title">City</span>, <span class="hljs-title">Long</span>&gt; </span>{

	<span class="hljs-function">Page&lt;City&gt; <span class="hljs-title">findAll</span><span class="hljs-params">(Pageable pageable)</span></span>;

	<span class="hljs-function">City <span class="hljs-title">findByNameAndStateAllIgnoringCase</span><span class="hljs-params">(String name, String state)</span></span>;

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_12_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_12_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.domain.Page
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.domain.Pageable
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.repository.Repository

</span><span class="fold-block"><span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">CityRepository</span> :
	<span class="hljs-type">Repository</span>&lt;<span class="hljs-type">City?, Long?</span>&gt; </span>{
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">findAll</span><span class="hljs-params">(pageable: <span class="hljs-type">Pageable</span>?)</span></span>: Page&lt;City?&gt;?
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">findByNameAndStateAllIgnoringCase</span><span class="hljs-params">(name: <span class="hljs-type">String</span>?, state: <span class="hljs-type">String</span>?)</span></span>: City?
}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Repositories and documents are found through scanning.
By default, the <a class="xref page" href="../using/auto-configuration.html#using.auto-configuration.packages">auto-configuration packages</a> are scanned.
You can customize the locations to look for repositories and documents by using <a class="apiref" href="https://docs.spring.io/spring-data/mongodb/docs/4.4.x/api/org/springframework/data/mongodb/repository/config/EnableMongoRepositories.html"><code>@EnableMongoRepositories</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/domain/EntityScan.html"><code>@EntityScan</code></a> respectively.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
For complete details of Spring Data MongoDB, including its rich object mapping technologies, see its <a href="https://docs.spring.io/spring-data/mongodb/reference/4.4">reference documentation</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.nosql.neo4j"><a class="anchor" href="#data.nosql.neo4j"></a>Neo4j</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="external" href="https://neo4j.com/" target="_blank">Neo4j</a> is an open-source NoSQL graph database that uses a rich data model of nodes connected by first class relationships, which is better suited for connected big data than traditional RDBMS approaches.
Spring Boot offers several conveniences for working with Neo4j, including the <code>spring-boot-starter-data-neo4j</code> starter.</p>
</div>
<div class="sect2">
<h3 id="data.nosql.neo4j.connecting"><a class="anchor" href="#data.nosql.neo4j.connecting"></a>Connecting to a Neo4j Database</h3>
<div class="paragraph">
<p>To access a Neo4j server, you can inject an auto-configured <a class="apiref external" href="https://javadoc.io/doc/org.neo4j.driver/neo4j-java-driver/5.28.5/org/neo4j/driver/Driver.html" target="_blank"><code>Driver</code></a>.
By default, the instance tries to connect to a Neo4j server at <code>localhost:7687</code> using the Bolt protocol.
The following example shows how to inject a Neo4j <a class="apiref external" href="https://javadoc.io/doc/org.neo4j.driver/neo4j-java-driver/5.28.5/org/neo4j/driver/Driver.html" target="_blank"><code>Driver</code></a> that gives you access, amongst other things, to a <a class="apiref external" href="https://javadoc.io/doc/org.neo4j.driver/neo4j-java-driver/5.28.5/org/neo4j/driver/Session.html" target="_blank"><code>Session</code></a>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_13">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_13_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_13_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_13_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_13_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_13_java" class="tabpanel" id="_tabs_13_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.neo4j.driver.Driver;
<span class="hljs-keyword">import</span> org.neo4j.driver.Session;
<span class="hljs-keyword">import</span> org.neo4j.driver.Values;

<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> Driver driver;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(Driver driver)</span> </span>{
		<span class="hljs-keyword">this</span>.driver = driver;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">someMethod</span><span class="hljs-params">(String message)</span> </span>{
		<span class="hljs-keyword">try</span> (Session session = <span class="hljs-keyword">this</span>.driver.session()) {
			<span class="hljs-keyword">return</span> session.executeWrite(
					(transaction) -&gt; transaction
						.run(<span class="hljs-string">"CREATE (a:Greeting) SET a.message = $message RETURN a.message + ', from node ' + id(a)"</span>,
								Values.parameters(<span class="hljs-string">"message"</span>, message))
						.single()
						.get(<span class="hljs-number">0</span>)
						.asString());
		}
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_13_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_13_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.neo4j.driver.*
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> driver: Driver) {
</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>
</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someMethod</span><span class="hljs-params">(message: <span class="hljs-type">String</span>?)</span></span>: String {
		driver.session().use { session -&gt;
			<span class="hljs-keyword">return</span><span class="hljs-symbol">@someMethod</span> session.executeWrite { transaction: TransactionContext -&gt;
				transaction
					.run(
						<span class="hljs-string">"CREATE (a:Greeting) SET a.message = \$message RETURN a.message + ', from node ' + id(a)"</span>,
						Values.parameters(<span class="hljs-string">"message"</span>, message)
					)
					.single()[<span class="hljs-number">0</span>].asString()
			}
		}
	}
</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can configure various aspects of the driver using <code>spring.neo4j.*</code> properties.
The following example shows how to configure the uri and credentials to use:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_14">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_14_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_14_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_14_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_14_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_14_properties" class="tabpanel" id="_tabs_14_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.neo4j.uri</span>=<span class="hljs-string">bolt://my-server:7687</span>
<span class="hljs-meta">spring.neo4j.authentication.username</span>=<span class="hljs-string">neo4j</span>
<span class="hljs-meta">spring.neo4j.authentication.password</span>=<span class="hljs-string">secret</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_14_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_14_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">neo4j:</span>
    <span class="hljs-attr">uri:</span> <span class="hljs-string">"bolt://my-server:7687"</span>
    <span class="hljs-attr">authentication:</span>
      <span class="hljs-attr">username:</span> <span class="hljs-string">"neo4j"</span>
      <span class="hljs-attr">password:</span> <span class="hljs-string">"secret"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The auto-configured <a class="apiref external" href="https://javadoc.io/doc/org.neo4j.driver/neo4j-java-driver/5.28.5/org/neo4j/driver/Driver.html" target="_blank"><code>Driver</code></a> is created using <code>org.neo4j.driver.Config$ConfigBuilder</code>.
To fine-tune its configuration, declare one or more <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/neo4j/ConfigBuilderCustomizer.html"><code>ConfigBuilderCustomizer</code></a> beans.
Each will be called in order with the <code>org.neo4j.driver.Config$ConfigBuilder</code> that is used to build the <a class="apiref external" href="https://javadoc.io/doc/org.neo4j.driver/neo4j-java-driver/5.28.5/org/neo4j/driver/Driver.html" target="_blank"><code>Driver</code></a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="data.nosql.neo4j.repositories"><a class="anchor" href="#data.nosql.neo4j.repositories"></a>Spring Data Neo4j Repositories</h3>
<div class="paragraph">
<p>Spring Data includes repository support for Neo4j.
For complete details of Spring Data Neo4j, see the <a href="https://docs.spring.io/spring-data/neo4j/reference/7.4">reference documentation</a>.</p>
</div>
<div class="paragraph">
<p>Spring Data Neo4j shares the common infrastructure with Spring Data JPA as many other Spring Data modules do.
You could take the JPA example from earlier and define <code>City</code> as Spring Data Neo4j <a class="apiref" href="https://docs.spring.io/spring-data/neo4j/docs/7.4.x/api/org/springframework/data/neo4j/core/schema/Node.html"><code>@Node</code></a> rather than JPA <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/Entity.html" target="_blank"><code>@Entity</code></a> and the repository abstraction works in the same way, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_15">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_15_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_15_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_15_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_15_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_15_java" class="tabpanel" id="_tabs_15_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.Optional;

<span class="hljs-keyword">import</span> org.springframework.data.neo4j.repository.Neo4jRepository;

</span><span class="fold-block"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">CityRepository</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">Neo4jRepository</span>&lt;<span class="hljs-title">City</span>, <span class="hljs-title">Long</span>&gt; </span>{

	<span class="hljs-function">Optional&lt;City&gt; <span class="hljs-title">findOneByNameAndState</span><span class="hljs-params">(String name, String state)</span></span>;

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_15_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_15_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.neo4j.repository.Neo4jRepository
<span class="hljs-keyword">import</span> java.util.Optional

</span><span class="fold-block"><span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">CityRepository</span> : <span class="hljs-type">Neo4jRepository</span>&lt;<span class="hljs-type">City?, Long?</span>&gt; </span>{

	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">findOneByNameAndState</span><span class="hljs-params">(name: <span class="hljs-type">String</span>?, state: <span class="hljs-type">String</span>?)</span></span>: Optional&lt;City?&gt;?

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The <code>spring-boot-starter-data-neo4j</code> starter enables the repository support as well as transaction management.
Spring Boot supports both classic and reactive Neo4j repositories, using the <a class="apiref" href="https://docs.spring.io/spring-data/neo4j/docs/7.4.x/api/org/springframework/data/neo4j/core/Neo4jTemplate.html"><code>Neo4jTemplate</code></a> or <a class="apiref" href="https://docs.spring.io/spring-data/neo4j/docs/7.4.x/api/org/springframework/data/neo4j/core/ReactiveNeo4jTemplate.html"><code>ReactiveNeo4jTemplate</code></a> beans.
When Project Reactor is available on the classpath, the reactive style is also auto-configured.</p>
</div>
<div class="paragraph">
<p>Repositories and entities are found through scanning.
By default, the <a class="xref page" href="../using/auto-configuration.html#using.auto-configuration.packages">auto-configuration packages</a> are scanned.
You can customize the locations to look for repositories and entities by using <a class="apiref" href="https://docs.spring.io/spring-data/neo4j/docs/7.4.x/api/org/springframework/data/neo4j/repository/config/EnableNeo4jRepositories.html"><code>@EnableNeo4jRepositories</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/domain/EntityScan.html"><code>@EntityScan</code></a> respectively.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>In an application using the reactive style, a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/transaction/ReactiveTransactionManager.html"><code>ReactiveTransactionManager</code></a> is not auto-configured.
To enable transaction management, the following bean must be defined in your configuration:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_16">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_16_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_16_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_16_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_16_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_16_java" class="tabpanel" id="_tabs_16_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.neo4j.driver.Driver;

<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.data.neo4j.core.ReactiveDatabaseSelectionProvider;
<span class="hljs-keyword">import</span> org.springframework.data.neo4j.core.transaction.ReactiveNeo4jTransactionManager;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyNeo4jConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> ReactiveNeo4jTransactionManager <span class="hljs-title">reactiveTransactionManager</span><span class="hljs-params">(Driver driver,
			ReactiveDatabaseSelectionProvider databaseNameProvider)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> ReactiveNeo4jTransactionManager(driver, databaseNameProvider);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_16_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_16_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.neo4j.driver.Driver
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.neo4j.core.ReactiveDatabaseSelectionProvider
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.neo4j.core.transaction.ReactiveNeo4jTransactionManager

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyNeo4jConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">reactiveTransactionManager</span><span class="hljs-params">(driver: <span class="hljs-type">Driver</span>,
			databaseNameProvider: <span class="hljs-type">ReactiveDatabaseSelectionProvider</span>)</span></span>: ReactiveNeo4jTransactionManager {
		<span class="hljs-keyword">return</span> ReactiveNeo4jTransactionManager(driver, databaseNameProvider)
	}
}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.nosql.elasticsearch"><a class="anchor" href="#data.nosql.elasticsearch"></a>Elasticsearch</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="external" href="https://www.elastic.co/products/elasticsearch" target="_blank">Elasticsearch</a> is an open source, distributed, RESTful search and analytics engine.
Spring Boot offers basic auto-configuration for Elasticsearch clients.</p>
</div>
<div class="paragraph">
<p>Spring Boot supports several clients:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>The official low-level REST client</p>
</li>
<li>
<p>The official Java API client</p>
</li>
<li>
<p>The <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/client/elc/ReactiveElasticsearchClient.html"><code>ReactiveElasticsearchClient</code></a> provided by Spring Data Elasticsearch</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Spring Boot provides a dedicated starter, <code>spring-boot-starter-data-elasticsearch</code>.</p>
</div>
<div class="sect2">
<h3 id="data.nosql.elasticsearch.connecting-using-rest"><a class="anchor" href="#data.nosql.elasticsearch.connecting-using-rest"></a>Connecting to Elasticsearch Using REST clients</h3>
<div class="paragraph">
<p>Elasticsearch ships two different REST clients that you can use to query a cluster: the <a class="external" href="https://www.elastic.co/guide/en/elasticsearch/client/java-api-client/current/java-rest-low.html" target="_blank">low-level client</a> from the <code>org.elasticsearch.client:elasticsearch-rest-client</code> module and the <a class="external" href="https://www.elastic.co/guide/en/elasticsearch/client/java-api-client/current/index.html" target="_blank">Java API client</a> from the <code>co.elastic.clients:elasticsearch-java</code> module.
Additionally, Spring Boot provides support for a reactive client from the <code>org.springframework.data:spring-data-elasticsearch</code> module.
By default, the clients will target <code><a class="bare external" href="http://localhost:9200" target="_blank">localhost:9200</a></code>.
You can use <code>spring.elasticsearch.*</code> properties to further tune how the clients are configured, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_17">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_17_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_17_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_17_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_17_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_17_properties" class="tabpanel" id="_tabs_17_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.elasticsearch.uris</span>=<span class="hljs-string">https://search.example.com:9200</span>
<span class="hljs-meta">spring.elasticsearch.socket-timeout</span>=<span class="hljs-string">10s</span>
<span class="hljs-meta">spring.elasticsearch.username</span>=<span class="hljs-string">user</span>
<span class="hljs-meta">spring.elasticsearch.password</span>=<span class="hljs-string">secret</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_17_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_17_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">elasticsearch:</span>
    <span class="hljs-attr">uris:</span> <span class="hljs-string">"https://search.example.com:9200"</span>
    <span class="hljs-attr">socket-timeout:</span> <span class="hljs-string">"10s"</span>
    <span class="hljs-attr">username:</span> <span class="hljs-string">"user"</span>
    <span class="hljs-attr">password:</span> <span class="hljs-string">"secret"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="sect3">
<h4 id="data.nosql.elasticsearch.connecting-using-rest.restclient"><a class="anchor" href="#data.nosql.elasticsearch.connecting-using-rest.restclient"></a>Connecting to Elasticsearch Using RestClient</h4>
<div class="paragraph">
<p>If you have <code>elasticsearch-rest-client</code> on the classpath, Spring Boot will auto-configure and register a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a> bean.
In addition to the properties described previously, to fine-tune the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a> you can register an arbitrary number of beans that implement <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/elasticsearch/RestClientBuilderCustomizer.html"><code>RestClientBuilderCustomizer</code></a> for more advanced customizations.
To take full control over the clients' configuration, define a <a class="apiref external" href="https://artifacts.elastic.co/javadoc/org/elasticsearch/client/elasticsearch-rest-client/8.15.5/org/elasticsearch/client/RestClientBuilder.html" target="_blank"><code>RestClientBuilder</code></a> bean.</p>
</div>
<div class="paragraph">
<p>Additionally, if <code>elasticsearch-rest-client-sniffer</code> is on the classpath, a <a class="apiref external" href="https://artifacts.elastic.co/javadoc/org/elasticsearch/client/elasticsearch-rest-client-sniffer/8.15.5/org/elasticsearch/client/sniff/Sniffer.html" target="_blank"><code>Sniffer</code></a> is auto-configured to automatically discover nodes from a running Elasticsearch cluster and set them on the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a> bean.
You can further tune how <a class="apiref external" href="https://artifacts.elastic.co/javadoc/org/elasticsearch/client/elasticsearch-rest-client-sniffer/8.15.5/org/elasticsearch/client/sniff/Sniffer.html" target="_blank"><code>Sniffer</code></a> is configured, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_18">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_18_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_18_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_18_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_18_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_18_properties" class="tabpanel" id="_tabs_18_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.elasticsearch.restclient.sniffer.interval</span>=<span class="hljs-string">10m</span>
<span class="hljs-meta">spring.elasticsearch.restclient.sniffer.delay-after-failure</span>=<span class="hljs-string">30s</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_18_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_18_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">elasticsearch:</span>
    <span class="hljs-attr">restclient:</span>
      <span class="hljs-attr">sniffer:</span>
        <span class="hljs-attr">interval:</span> <span class="hljs-string">"10m"</span>
        <span class="hljs-attr">delay-after-failure:</span> <span class="hljs-string">"30s"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect3">
<h4 id="data.nosql.elasticsearch.connecting-using-rest.javaapiclient"><a class="anchor" href="#data.nosql.elasticsearch.connecting-using-rest.javaapiclient"></a>Connecting to Elasticsearch Using ElasticsearchClient</h4>
<div class="paragraph">
<p>If you have <code>co.elastic.clients:elasticsearch-java</code> on the classpath, Spring Boot will auto-configure and register an <a class="apiref external" href="https://artifacts.elastic.co/javadoc/co/elastic/clients/elasticsearch-java/8.15.5/co/elastic/clients/elasticsearch/ElasticsearchClient.html" target="_blank"><code>ElasticsearchClient</code></a> bean.</p>
</div>
<div class="paragraph">
<p>The <a class="apiref external" href="https://artifacts.elastic.co/javadoc/co/elastic/clients/elasticsearch-java/8.15.5/co/elastic/clients/elasticsearch/ElasticsearchClient.html" target="_blank"><code>ElasticsearchClient</code></a> uses a transport that depends upon the previously described <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a>.
Therefore, the properties described previously can be used to configure the <a class="apiref external" href="https://artifacts.elastic.co/javadoc/co/elastic/clients/elasticsearch-java/8.15.5/co/elastic/clients/elasticsearch/ElasticsearchClient.html" target="_blank"><code>ElasticsearchClient</code></a>.
Furthermore, you can define a <a class="apiref external" href="https://artifacts.elastic.co/javadoc/co/elastic/clients/elasticsearch-java/8.15.5/co/elastic/clients/transport/rest_client/RestClientOptions.html" target="_blank"><code>RestClientOptions</code></a> bean to take further control of the behavior of the transport.</p>
</div>
</div>
<div class="sect3">
<h4 id="data.nosql.elasticsearch.connecting-using-rest.reactiveclient"><a class="anchor" href="#data.nosql.elasticsearch.connecting-using-rest.reactiveclient"></a>Connecting to Elasticsearch using ReactiveElasticsearchClient</h4>
<div class="paragraph">
<p><a class="external" href="https://spring.io/projects/spring-data-elasticsearch" target="_blank">Spring Data Elasticsearch</a> ships <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/client/elc/ReactiveElasticsearchClient.html"><code>ReactiveElasticsearchClient</code></a> for querying Elasticsearch instances in a reactive fashion.
If you have Spring Data Elasticsearch and Reactor on the classpath, Spring Boot will auto-configure and register a <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/client/elc/ReactiveElasticsearchClient.html"><code>ReactiveElasticsearchClient</code></a>.</p>
</div>
<div class="paragraph">
<p>The <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/client/elc/ReactiveElasticsearchClient.html"><code>ReactiveElasticsearchClient</code></a> uses a transport that depends upon the previously described <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a>.
Therefore, the properties described previously can be used to configure the <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/client/elc/ReactiveElasticsearchClient.html"><code>ReactiveElasticsearchClient</code></a>.
Furthermore, you can define a <a class="apiref external" href="https://artifacts.elastic.co/javadoc/co/elastic/clients/elasticsearch-java/8.15.5/co/elastic/clients/transport/rest_client/RestClientOptions.html" target="_blank"><code>RestClientOptions</code></a> bean to take further control of the behavior of the transport.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="data.nosql.elasticsearch.connecting-using-spring-data"><a class="anchor" href="#data.nosql.elasticsearch.connecting-using-spring-data"></a>Connecting to Elasticsearch by Using Spring Data</h3>
<div class="paragraph">
<p>To connect to Elasticsearch, an <a class="apiref external" href="https://artifacts.elastic.co/javadoc/co/elastic/clients/elasticsearch-java/8.15.5/co/elastic/clients/elasticsearch/ElasticsearchClient.html" target="_blank"><code>ElasticsearchClient</code></a> bean must be defined,
auto-configured by Spring Boot or manually provided by the application (see previous sections).
With this configuration in place, an
<a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/client/elc/ElasticsearchTemplate.html"><code>ElasticsearchTemplate</code></a> can be injected like any other Spring bean,
as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_19">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_19_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_19_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_19_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_19_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_19_java" class="tabpanel" id="_tabs_19_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> ElasticsearchTemplate template;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(ElasticsearchTemplate template)</span> </span>{
		<span class="hljs-keyword">this</span>.template = template;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">boolean</span> <span class="hljs-title">someMethod</span><span class="hljs-params">(String id)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.template.exists(id, User<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_19_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_19_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> template: org.springframework.<span class="hljs-keyword">data</span>.elasticsearch.client.elc.ElasticsearchTemplate ) {

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someMethod</span><span class="hljs-params">(id: <span class="hljs-type">String</span>)</span></span>: <span class="hljs-built_in">Boolean</span> {
		<span class="hljs-keyword">return</span> template.exists(id, User::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>)</span>
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>In the presence of <code>spring-data-elasticsearch</code> and Reactor, Spring Boot can also auto-configure a <a href="#data.nosql.elasticsearch.connecting-using-rest.reactiveclient"><code>ReactiveElasticsearchClient</code></a> and a <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/client/elc/ReactiveElasticsearchTemplate.html"><code>ReactiveElasticsearchTemplate</code></a> as beans.
They are the reactive equivalent of the other REST clients.</p>
</div>
</div>
<div class="sect2">
<h3 id="data.nosql.elasticsearch.repositories"><a class="anchor" href="#data.nosql.elasticsearch.repositories"></a>Spring Data Elasticsearch Repositories</h3>
<div class="paragraph">
<p>Spring Data includes repository support for Elasticsearch.
As with the JPA repositories discussed earlier, the basic principle is that queries are constructed for you automatically based on method names.</p>
</div>
<div class="paragraph">
<p>In fact, both Spring Data JPA and Spring Data Elasticsearch share the same common infrastructure.
You could take the JPA example from earlier and, assuming that <code>City</code> is now an Elasticsearch <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/annotations/Document.html"><code>@Document</code></a> class rather than a JPA <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/Entity.html" target="_blank"><code>@Entity</code></a>, it works in the same way.</p>
</div>
<div class="paragraph">
<p>Repositories and documents are found through scanning.
By default, the <a class="xref page" href="../using/auto-configuration.html#using.auto-configuration.packages">auto-configuration packages</a> are scanned.
You can customize the locations to look for repositories and documents by using <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/repository/config/EnableElasticsearchRepositories.html"><code>@EnableElasticsearchRepositories</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/domain/EntityScan.html"><code>@EntityScan</code></a> respectively.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
For complete details of Spring Data Elasticsearch, see the <a href="https://docs.spring.io/spring-data/elasticsearch/reference/5.4">reference documentation</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Spring Boot supports both classic and reactive Elasticsearch repositories, using the <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/client/elc/ElasticsearchTemplate.html"><code>ElasticsearchTemplate</code></a> or <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/client/elc/ReactiveElasticsearchTemplate.html"><code>ReactiveElasticsearchTemplate</code></a> beans.
Most likely those beans are auto-configured by Spring Boot given the required dependencies are present.</p>
</div>
<div class="paragraph">
<p>If you wish to use your own template for backing the Elasticsearch repositories, you can add your own <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/client/elc/ElasticsearchTemplate.html"><code>ElasticsearchTemplate</code></a> or <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/core/ElasticsearchOperations.html"><code>ElasticsearchOperations</code></a> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a>, as long as it is named <code>"elasticsearchTemplate"</code>.
Same applies to <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/client/elc/ReactiveElasticsearchTemplate.html"><code>ReactiveElasticsearchTemplate</code></a> and <a class="apiref" href="https://docs.spring.io/spring-data/elasticsearch/docs/5.4.x/api/org/springframework/data/elasticsearch/core/ReactiveElasticsearchOperations.html"><code>ReactiveElasticsearchOperations</code></a>, with the bean name <code>"reactiveElasticsearchTemplate"</code>.</p>
</div>
<div class="paragraph">
<p>You can choose to disable the repositories support with the following property:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_20">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_20_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_20_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_20_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_20_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_20_properties" class="tabpanel" id="_tabs_20_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.data.elasticsearch.repositories.enabled</span>=<span class="hljs-string">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_20_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_20_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">data:</span>
    <span class="hljs-attr">elasticsearch:</span>
      <span class="hljs-attr">repositories:</span>
        <span class="hljs-attr">enabled:</span> <span class="hljs-literal">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.nosql.cassandra"><a class="anchor" href="#data.nosql.cassandra"></a>Cassandra</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="external" href="https://cassandra.apache.org/" target="_blank">Cassandra</a> is an open source, distributed database management system designed to handle large amounts of data across many commodity servers.
Spring Boot offers auto-configuration for Cassandra and the abstractions on top of it provided by <a class="external" href="https://spring.io/projects/spring-data-cassandra" target="_blank">Spring Data Cassandra</a>.
There is a <code>spring-boot-starter-data-cassandra</code> starter for collecting the dependencies in a convenient way.</p>
</div>
<div class="sect2">
<h3 id="data.nosql.cassandra.connecting"><a class="anchor" href="#data.nosql.cassandra.connecting"></a>Connecting to Cassandra</h3>
<div class="paragraph">
<p>You can inject an auto-configured <a class="apiref" href="https://docs.spring.io/spring-data/cassandra/docs/4.4.x/api/org/springframework/data/cassandra/core/CassandraTemplate.html"><code>CassandraTemplate</code></a> or a Cassandra <code>CqlSession</code> instance as you would with any other Spring Bean.
The <code>spring.cassandra.*</code> properties can be used to customize the connection.
Generally, you provide <code>keyspace-name</code> and <code>contact-points</code> as well the local datacenter name, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_21">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_21_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_21_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_21_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_21_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_21_properties" class="tabpanel" id="_tabs_21_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.cassandra.keyspace-name</span>=<span class="hljs-string">mykeyspace</span>
<span class="hljs-meta">spring.cassandra.contact-points</span>=<span class="hljs-string">cassandrahost1:9042,cassandrahost2:9042</span>
<span class="hljs-meta">spring.cassandra.local-datacenter</span>=<span class="hljs-string">datacenter1</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_21_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_21_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">cassandra:</span>
    <span class="hljs-attr">keyspace-name:</span> <span class="hljs-string">"mykeyspace"</span>
    <span class="hljs-attr">contact-points:</span> <span class="hljs-string">"cassandrahost1:9042,cassandrahost2:9042"</span>
    <span class="hljs-attr">local-datacenter:</span> <span class="hljs-string">"datacenter1"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If the port is the same for all your contact points you can use a shortcut and only specify the host names, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_22">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_22_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_22_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_22_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_22_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_22_properties" class="tabpanel" id="_tabs_22_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.cassandra.keyspace-name</span>=<span class="hljs-string">mykeyspace</span>
<span class="hljs-meta">spring.cassandra.contact-points</span>=<span class="hljs-string">cassandrahost1,cassandrahost2</span>
<span class="hljs-meta">spring.cassandra.local-datacenter</span>=<span class="hljs-string">datacenter1</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_22_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_22_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">cassandra:</span>
    <span class="hljs-attr">keyspace-name:</span> <span class="hljs-string">"mykeyspace"</span>
    <span class="hljs-attr">contact-points:</span> <span class="hljs-string">"cassandrahost1,cassandrahost2"</span>
    <span class="hljs-attr">local-datacenter:</span> <span class="hljs-string">"datacenter1"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Those two examples are identical as the port default to <code>9042</code>.
If you need to configure the port, use <code>spring.cassandra.port</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The auto-configured <code>CqlSession</code> can be configured to use SSL for communication with the server by setting the properties as shown in this example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_23">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_23_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_23_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_23_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_23_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_23_properties" class="tabpanel" id="_tabs_23_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.cassandra.keyspace-name</span>=<span class="hljs-string">mykeyspace</span>
<span class="hljs-meta">spring.cassandra.contact-points</span>=<span class="hljs-string">cassandrahost1,cassandrahost2</span>
<span class="hljs-meta">spring.cassandra.local-datacenter</span>=<span class="hljs-string">datacenter1</span>
<span class="hljs-meta">spring.cassandra.ssl.enabled</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_23_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_23_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">cassandra:</span>
    <span class="hljs-attr">keyspace-name:</span> <span class="hljs-string">"mykeyspace"</span>
    <span class="hljs-attr">contact-points:</span> <span class="hljs-string">"cassandrahost1,cassandrahost2"</span>
    <span class="hljs-attr">local-datacenter:</span> <span class="hljs-string">"datacenter1"</span>
    <span class="hljs-attr">ssl:</span>
      <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Custom SSL trust material can be configured in an <a class="xref page" href="../features/ssl.html">SSL bundle</a> and applied to the <code>CqlSession</code> as shown in this example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_24">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_24_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_24_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_24_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_24_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_24_properties" class="tabpanel" id="_tabs_24_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.cassandra.keyspace-name</span>=<span class="hljs-string">mykeyspace</span>
<span class="hljs-meta">spring.cassandra.contact-points</span>=<span class="hljs-string">cassandrahost1,cassandrahost2</span>
<span class="hljs-meta">spring.cassandra.local-datacenter</span>=<span class="hljs-string">datacenter1</span>
<span class="hljs-meta">spring.cassandra.ssl.bundle</span>=<span class="hljs-string">example</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_24_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_24_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">cassandra:</span>
    <span class="hljs-attr">keyspace-name:</span> <span class="hljs-string">"mykeyspace"</span>
    <span class="hljs-attr">contact-points:</span> <span class="hljs-string">"cassandrahost1,cassandrahost2"</span>
    <span class="hljs-attr">local-datacenter:</span> <span class="hljs-string">"datacenter1"</span>
    <span class="hljs-attr">ssl:</span>
      <span class="hljs-attr">bundle:</span> <span class="hljs-string">"example"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>The Cassandra driver has its own configuration infrastructure that loads an <code>application.conf</code> at the root of the classpath.</p>
</div>
<div class="paragraph">
<p>Spring Boot does not look for such a file by default but can load one using <code>spring.cassandra.config</code>.
If a property is both present in <code>spring.cassandra.*</code> and the configuration file, the value in <code>spring.cassandra.*</code> takes precedence.</p>
</div>
<div class="paragraph">
<p>For more advanced driver customizations, you can register an arbitrary number of beans that implement <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/cassandra/DriverConfigLoaderBuilderCustomizer.html"><code>DriverConfigLoaderBuilderCustomizer</code></a>.
The <code>CqlSession</code> can be customized with a bean of type <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/cassandra/CqlSessionBuilderCustomizer.html"><code>CqlSessionBuilderCustomizer</code></a>.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If you use <code>CqlSessionBuilder</code> to create multiple <code>CqlSession</code> beans, keep in mind the builder is mutable so make sure to inject a fresh copy for each session.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following code listing shows how to inject a Cassandra bean:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_25">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_25_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_25_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_25_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_25_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_25_java" class="tabpanel" id="_tabs_25_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.data.cassandra.core.CassandraTemplate;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> CassandraTemplate template;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(CassandraTemplate template)</span> </span>{
		<span class="hljs-keyword">this</span>.template = template;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">long</span> <span class="hljs-title">someMethod</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.template.count(User<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_25_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_25_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.cassandra.core.CassandraTemplate
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> template: CassandraTemplate) {

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someMethod</span><span class="hljs-params">()</span></span>: <span class="hljs-built_in">Long</span> {
		<span class="hljs-keyword">return</span> template.count(User::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>)</span>
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you add your own <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> of type <a class="apiref" href="https://docs.spring.io/spring-data/cassandra/docs/4.4.x/api/org/springframework/data/cassandra/core/CassandraTemplate.html"><code>CassandraTemplate</code></a>, it replaces the default.</p>
</div>
</div>
<div class="sect2">
<h3 id="data.nosql.cassandra.repositories"><a class="anchor" href="#data.nosql.cassandra.repositories"></a>Spring Data Cassandra Repositories</h3>
<div class="paragraph">
<p>Spring Data includes basic repository support for Cassandra.
Currently, this is more limited than the JPA repositories discussed earlier and needs <a class="apiref" href="https://docs.spring.io/spring-data/cassandra/docs/4.4.x/api/org/springframework/data/cassandra/repository/Query.html"><code>@Query</code></a> annotated finder methods.</p>
</div>
<div class="paragraph">
<p>Repositories and entities are found through scanning.
By default, the <a class="xref page" href="../using/auto-configuration.html#using.auto-configuration.packages">auto-configuration packages</a> are scanned.
You can customize the locations to look for repositories and entities by using <a class="apiref" href="https://docs.spring.io/spring-data/cassandra/docs/4.4.x/api/org/springframework/data/cassandra/repository/config/EnableCassandraRepositories.html"><code>@EnableCassandraRepositories</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/domain/EntityScan.html"><code>@EntityScan</code></a> respectively.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
For complete details of Spring Data Cassandra, see the <a href="https://docs.spring.io/spring-data/cassandra/reference/4.4">reference documentation</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.nosql.couchbase"><a class="anchor" href="#data.nosql.couchbase"></a>Couchbase</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="external" href="https://www.couchbase.com/" target="_blank">Couchbase</a> is an open-source, distributed, multi-model NoSQL document-oriented database that is optimized for interactive applications.
Spring Boot offers auto-configuration for Couchbase and the abstractions on top of it provided by <a class="external" href="https://github.com/spring-projects/spring-data-couchbase" target="_blank">Spring Data Couchbase</a>.
There are <code>spring-boot-starter-data-couchbase</code> and <code>spring-boot-starter-data-couchbase-reactive</code> starters for collecting the dependencies in a convenient way.</p>
</div>
<div class="sect2">
<h3 id="data.nosql.couchbase.connecting"><a class="anchor" href="#data.nosql.couchbase.connecting"></a>Connecting to Couchbase</h3>
<div class="paragraph">
<p>You can get a <a class="apiref external" href="https://javadoc.io/doc/com.couchbase.client/java-client/3.7.9/com/couchbase/client/java/Cluster.html" target="_blank"><code>Cluster</code></a> by adding the Couchbase SDK and some configuration.
The <code>spring.couchbase.*</code> properties can be used to customize the connection.
Generally, you provide the <a class="external" href="https://docs.couchbase.com/dotnet-sdk/current/howtos/managing-connections.html" target="_blank">connection string</a> and credentials for authentication. Basic authentication with username and password can be configured as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_26">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_26_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_26_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_26_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_26_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_26_properties" class="tabpanel" id="_tabs_26_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.couchbase.connection-string</span>=<span class="hljs-string">couchbase://*************</span>
<span class="hljs-meta">spring.couchbase.username</span>=<span class="hljs-string">user</span>
<span class="hljs-meta">spring.couchbase.password</span>=<span class="hljs-string">secret</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_26_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_26_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">couchbase:</span>
    <span class="hljs-attr">connection-string:</span> <span class="hljs-string">"couchbase://*************"</span>
    <span class="hljs-attr">username:</span> <span class="hljs-string">"user"</span>
    <span class="hljs-attr">password:</span> <span class="hljs-string">"secret"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p><a class="external" href="https://docs.couchbase.com/server/current/manage/manage-security/configure-client-certificates.html" target="_blank">Client certificates</a> can be used for authentication instead of username and password.
The location and password for a Java KeyStore containing client certificates can be configured as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_27">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_27_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_27_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_27_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_27_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_27_properties" class="tabpanel" id="_tabs_27_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.couchbase.connection-string</span>=<span class="hljs-string">couchbase://*************</span>
<span class="hljs-meta">spring.couchbase.env.ssl.enabled</span>=<span class="hljs-string">true</span>
<span class="hljs-meta">spring.couchbase.authentication.jks.location</span>=<span class="hljs-string">classpath:client.p12</span>
<span class="hljs-meta">spring.couchbase.authentication.jks.password</span>=<span class="hljs-string">secret</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_27_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_27_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">couchbase:</span>
    <span class="hljs-attr">connection-string:</span> <span class="hljs-string">"couchbase://*************"</span>
    <span class="hljs-attr">env:</span>
      <span class="hljs-attr">ssl:</span>
        <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span>
    <span class="hljs-attr">authentication:</span>
      <span class="hljs-attr">jks:</span>
        <span class="hljs-attr">location:</span> <span class="hljs-string">"classpath:client.p12"</span>
        <span class="hljs-attr">password:</span> <span class="hljs-string">"secret"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>PEM-encoded certificates and a private key can be configured as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_28">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_28_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_28_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_28_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_28_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_28_properties" class="tabpanel" id="_tabs_28_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.couchbase.connection-string</span>=<span class="hljs-string">couchbase://*************</span>
<span class="hljs-meta">spring.couchbase.env.ssl.enabled</span>=<span class="hljs-string">true</span>
<span class="hljs-meta">spring.couchbase.authentication.pem.certificates</span>=<span class="hljs-string">classpath:client.crt</span>
<span class="hljs-meta">spring.couchbase.authentication.pem.private-key</span>=<span class="hljs-string">classpath:client.key</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_28_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_28_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">couchbase:</span>
    <span class="hljs-attr">connection-string:</span> <span class="hljs-string">"couchbase://*************"</span>
    <span class="hljs-attr">env:</span>
      <span class="hljs-attr">ssl:</span>
        <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span>
    <span class="hljs-attr">authentication:</span>
      <span class="hljs-attr">pem:</span>
        <span class="hljs-attr">certificates:</span> <span class="hljs-string">"classpath:client.crt"</span>
        <span class="hljs-attr">private-key:</span> <span class="hljs-string">"classpath:client.key"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>It is also possible to customize some of the <a class="apiref external" href="https://javadoc.io/doc/com.couchbase.client/java-client/3.7.9/com/couchbase/client/java/env/ClusterEnvironment.html" target="_blank"><code>ClusterEnvironment</code></a> settings.
For instance, the following configuration changes the timeout to open a new <a class="apiref external" href="https://javadoc.io/doc/com.couchbase.client/java-client/3.7.9/com/couchbase/client/java/Bucket.html" target="_blank"><code>Bucket</code></a> and enables SSL support with a reference to a configured <a class="xref page" href="../features/ssl.html">SSL bundle</a>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_29">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_29_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_29_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_29_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_29_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_29_properties" class="tabpanel" id="_tabs_29_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.couchbase.env.timeouts.connect</span>=<span class="hljs-string">3s</span>
<span class="hljs-meta">spring.couchbase.env.ssl.bundle</span>=<span class="hljs-string">example</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_29_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_29_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">couchbase:</span>
    <span class="hljs-attr">env:</span>
      <span class="hljs-attr">timeouts:</span>
        <span class="hljs-attr">connect:</span> <span class="hljs-string">"3s"</span>
      <span class="hljs-attr">ssl:</span>
        <span class="hljs-attr">bundle:</span> <span class="hljs-string">"example"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Check the <code>spring.couchbase.env.*</code> properties for more details.
To take more control, one or more <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/couchbase/ClusterEnvironmentBuilderCustomizer.html"><code>ClusterEnvironmentBuilderCustomizer</code></a> beans can be used.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="data.nosql.couchbase.repositories"><a class="anchor" href="#data.nosql.couchbase.repositories"></a>Spring Data Couchbase Repositories</h3>
<div class="paragraph">
<p>Spring Data includes repository support for Couchbase.</p>
</div>
<div class="paragraph">
<p>Repositories and documents are found through scanning.
By default, the <a class="xref page" href="../using/auto-configuration.html#using.auto-configuration.packages">auto-configuration packages</a> are scanned.
You can customize the locations to look for repositories and documents by using <a class="apiref" href="https://docs.spring.io/spring-data/couchbase/docs/5.4.x/api/org/springframework/data/couchbase/repository/config/EnableCouchbaseRepositories.html"><code>@EnableCouchbaseRepositories</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/domain/EntityScan.html"><code>@EntityScan</code></a> respectively.</p>
</div>
<div class="paragraph">
<p>For complete details of Spring Data Couchbase, see the <a href="https://docs.spring.io/spring-data/couchbase/reference/5.4">reference documentation</a>.</p>
</div>
<div class="paragraph">
<p>You can inject an auto-configured <a class="apiref" href="https://docs.spring.io/spring-data/couchbase/docs/5.4.x/api/org/springframework/data/couchbase/core/CouchbaseTemplate.html"><code>CouchbaseTemplate</code></a> instance as you would with any other Spring Bean, provided a <a class="apiref" href="https://docs.spring.io/spring-data/couchbase/docs/5.4.x/api/org/springframework/data/couchbase/CouchbaseClientFactory.html"><code>CouchbaseClientFactory</code></a> bean is available.
This happens when a <a class="apiref external" href="https://javadoc.io/doc/com.couchbase.client/java-client/3.7.9/com/couchbase/client/java/Cluster.html" target="_blank"><code>Cluster</code></a> is available, as described above, and a bucket name has been specified:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_30">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_30_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_30_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_30_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_30_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_30_properties" class="tabpanel" id="_tabs_30_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.data.couchbase.bucket-name</span>=<span class="hljs-string">my-bucket</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_30_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_30_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">data:</span>
    <span class="hljs-attr">couchbase:</span>
      <span class="hljs-attr">bucket-name:</span> <span class="hljs-string">"my-bucket"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The following examples shows how to inject a <a class="apiref" href="https://docs.spring.io/spring-data/couchbase/docs/5.4.x/api/org/springframework/data/couchbase/core/CouchbaseTemplate.html"><code>CouchbaseTemplate</code></a> bean:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_31">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_31_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_31_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_31_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_31_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_31_java" class="tabpanel" id="_tabs_31_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.data.couchbase.core.CouchbaseTemplate;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> CouchbaseTemplate template;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(CouchbaseTemplate template)</span> </span>{
		<span class="hljs-keyword">this</span>.template = template;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">someMethod</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.template.getBucketName();
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_31_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_31_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.couchbase.core.CouchbaseTemplate
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> template: CouchbaseTemplate) {

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someMethod</span><span class="hljs-params">()</span></span>: String {
		<span class="hljs-keyword">return</span> template.bucketName
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>There are a few beans that you can define in your own configuration to override those provided by the auto-configuration:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>A <a class="apiref" href="https://docs.spring.io/spring-data/couchbase/docs/5.4.x/api/org/springframework/data/couchbase/core/mapping/CouchbaseMappingContext.html"><code>CouchbaseMappingContext</code></a> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> with a name of <code>couchbaseMappingContext</code>.</p>
</li>
<li>
<p>A <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/convert/CustomConversions.html"><code>CustomConversions</code></a> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> with a name of <code>couchbaseCustomConversions</code>.</p>
</li>
<li>
<p>A <a class="apiref" href="https://docs.spring.io/spring-data/couchbase/docs/5.4.x/api/org/springframework/data/couchbase/core/CouchbaseTemplate.html"><code>CouchbaseTemplate</code></a> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> with a name of <code>couchbaseTemplate</code>.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>To avoid hard-coding those names in your own config, you can reuse <a class="apiref" href="https://docs.spring.io/spring-data/couchbase/docs/5.4.x/api/org/springframework/data/couchbase/config/BeanNames.html"><code>BeanNames</code></a> provided by Spring Data Couchbase.
For instance, you can customize the converters to use, as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_32">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_32_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_32_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_32_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_32_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_32_java" class="tabpanel" id="_tabs_32_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.assertj.core.util.Arrays;

<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.data.couchbase.config.BeanNames;
<span class="hljs-keyword">import</span> org.springframework.data.couchbase.core.convert.CouchbaseCustomConversions;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCouchbaseConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>(BeanNames.COUCHBASE_CUSTOM_CONVERSIONS)
	<span class="hljs-function"><span class="hljs-keyword">public</span> CouchbaseCustomConversions <span class="hljs-title">myCustomConversions</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> CouchbaseCustomConversions(Arrays.asList(<span class="hljs-keyword">new</span> MyConverter()));
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_32_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_32_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.assertj.core.util.Arrays
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.couchbase.config.BeanNames
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.couchbase.core.convert.CouchbaseCustomConversions

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCouchbaseConfiguration</span> </span>{

	<span class="hljs-meta">@Bean(BeanNames.COUCHBASE_CUSTOM_CONVERSIONS)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">myCustomConversions</span><span class="hljs-params">()</span></span>: CouchbaseCustomConversions {
		<span class="hljs-keyword">return</span> CouchbaseCustomConversions(Arrays.asList(MyConverter()))
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="data.nosql.ldap"><a class="anchor" href="#data.nosql.ldap"></a>LDAP</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="external" href="https://en.wikipedia.org/wiki/Lightweight_Directory_Access_Protocol" target="_blank">LDAP</a> (Lightweight Directory Access Protocol) is an open, vendor-neutral, industry standard application protocol for accessing and maintaining distributed directory information services over an IP network.
Spring Boot offers auto-configuration for any compliant LDAP server as well as support for the embedded in-memory LDAP server from <a class="external" href="https://ldap.com/unboundid-ldap-sdk-for-java/" target="_blank">UnboundID</a>.</p>
</div>
<div class="paragraph">
<p>LDAP abstractions are provided by <a class="external" href="https://github.com/spring-projects/spring-data-ldap" target="_blank">Spring Data LDAP</a>.
There is a <code>spring-boot-starter-data-ldap</code> starter for collecting the dependencies in a convenient way.</p>
</div>
<div class="sect2">
<h3 id="data.nosql.ldap.connecting"><a class="anchor" href="#data.nosql.ldap.connecting"></a>Connecting to an LDAP Server</h3>
<div class="paragraph">
<p>To connect to an LDAP server, make sure you declare a dependency on the <code>spring-boot-starter-data-ldap</code> starter or <code>spring-ldap-core</code> and then declare the URLs of your server in your application.properties, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_33">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_33_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_33_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_33_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_33_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_33_properties" class="tabpanel" id="_tabs_33_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.ldap.urls</span>=<span class="hljs-string">ldap://myserver:1235</span>
<span class="hljs-meta">spring.ldap.username</span>=<span class="hljs-string">admin</span>
<span class="hljs-meta">spring.ldap.password</span>=<span class="hljs-string">secret</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_33_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_33_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">ldap:</span>
    <span class="hljs-attr">urls:</span> <span class="hljs-string">"ldap://myserver:1235"</span>
    <span class="hljs-attr">username:</span> <span class="hljs-string">"admin"</span>
    <span class="hljs-attr">password:</span> <span class="hljs-string">"secret"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you need to customize connection settings, you can use the <code>spring.ldap.base</code> and <code>spring.ldap.base-environment</code> properties.</p>
</div>
<div class="paragraph">
<p>An <a class="apiref" href="https://docs.spring.io/spring-ldap/docs/3.2.x/api/org/springframework/ldap/core/support/LdapContextSource.html"><code>LdapContextSource</code></a> is auto-configured based on these settings.
If a <a class="apiref" href="https://docs.spring.io/spring-ldap/docs/3.2.x/api/org/springframework/ldap/core/support/DirContextAuthenticationStrategy.html"><code>DirContextAuthenticationStrategy</code></a> bean is available, it is associated to the auto-configured <a class="apiref" href="https://docs.spring.io/spring-ldap/docs/3.2.x/api/org/springframework/ldap/core/support/LdapContextSource.html"><code>LdapContextSource</code></a>.
If you need to customize it, for instance to use a <a class="apiref" href="https://docs.spring.io/spring-ldap/docs/3.2.x/api/org/springframework/ldap/pool2/factory/PooledContextSource.html"><code>PooledContextSource</code></a>, you can still inject the auto-configured <a class="apiref" href="https://docs.spring.io/spring-ldap/docs/3.2.x/api/org/springframework/ldap/core/support/LdapContextSource.html"><code>LdapContextSource</code></a>.
Make sure to flag your customized <a class="apiref" href="https://docs.spring.io/spring-ldap/docs/3.2.x/api/org/springframework/ldap/core/ContextSource.html"><code>ContextSource</code></a> as <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Primary.html"><code>@Primary</code></a> so that the auto-configured <a class="apiref" href="https://docs.spring.io/spring-ldap/docs/3.2.x/api/org/springframework/ldap/core/LdapTemplate.html"><code>LdapTemplate</code></a> uses it.</p>
</div>
</div>
<div class="sect2">
<h3 id="data.nosql.ldap.repositories"><a class="anchor" href="#data.nosql.ldap.repositories"></a>Spring Data LDAP Repositories</h3>
<div class="paragraph">
<p>Spring Data includes repository support for LDAP.</p>
</div>
<div class="paragraph">
<p>Repositories and documents are found through scanning.
By default, the <a class="xref page" href="../using/auto-configuration.html#using.auto-configuration.packages">auto-configuration packages</a> are scanned.
You can customize the locations to look for repositories and documents by using <a class="apiref" href="https://docs.spring.io/spring-data/ldap/docs/3.4.x/api/org/springframework/data/ldap/repository/config/EnableLdapRepositories.html"><code>@EnableLdapRepositories</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/domain/EntityScan.html"><code>@EntityScan</code></a> respectively.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
For complete details of Spring Data LDAP, see the <a href="https://docs.spring.io/spring-data/ldap/reference/3.4">reference documentation</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>You can also inject an auto-configured <a class="apiref" href="https://docs.spring.io/spring-ldap/docs/3.2.x/api/org/springframework/ldap/core/LdapTemplate.html"><code>LdapTemplate</code></a> instance as you would with any other Spring Bean, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_34">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_34_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_34_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_34_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_34_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_34_java" class="tabpanel" id="_tabs_34_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.List;

<span class="hljs-keyword">import</span> org.springframework.ldap.core.LdapTemplate;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> LdapTemplate template;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(LdapTemplate template)</span> </span>{
		<span class="hljs-keyword">this</span>.template = template;
	}

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">public</span> List&lt;User&gt; <span class="hljs-title">someMethod</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.template.findAll(User<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_34_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_34_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.ldap.core.LdapTemplate
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> template: LdapTemplate) {

</span><span class="fold-block is-hidden-unfolded">	<span class="hljs-comment">// ...</span>

</span><span class="fold-block is-hidden-folded">	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">someMethod</span><span class="hljs-params">()</span></span>: List&lt;User&gt; {
		<span class="hljs-keyword">return</span> template.findAll(User::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>)</span>
	}

</span><span class="fold-block">}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="data.nosql.ldap.embedded"><a class="anchor" href="#data.nosql.ldap.embedded"></a>Embedded In-memory LDAP Server</h3>
<div class="paragraph">
<p>For testing purposes, Spring Boot supports auto-configuration of an in-memory LDAP server from <a class="external" href="https://ldap.com/unboundid-ldap-sdk-for-java/" target="_blank">UnboundID</a>.
To configure the server, add a dependency to <code>com.unboundid:unboundid-ldapsdk</code> and declare a <code>spring.ldap.embedded.base-dn</code> property, as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_35">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_35_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_35_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_35_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_35_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_35_properties" class="tabpanel" id="_tabs_35_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.ldap.embedded.base-dn</span>=<span class="hljs-string">dc=spring,dc=io</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_35_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_35_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">ldap:</span>
    <span class="hljs-attr">embedded:</span>
      <span class="hljs-attr">base-dn:</span> <span class="hljs-string">"dc=spring,dc=io"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>It is possible to define multiple base-dn values, however, since distinguished names usually contain commas, they must be defined using the correct notation.</p>
</div>
<div class="paragraph">
<p>In yaml files, you can use the yaml list notation. In properties files, you must include the index as part of the property name:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_36">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_36_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_36_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_36_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_36_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_36_properties" class="tabpanel" id="_tabs_36_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.ldap.embedded.base-dn[0]</span>=<span class="hljs-string">dc=spring,dc=io</span>
<span class="hljs-meta">spring.ldap.embedded.base-dn[1]</span>=<span class="hljs-string">dc=vmware,dc=com</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_36_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_36_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring.ldap.embedded.base-dn:</span>
<span class="hljs-bullet">-</span> <span class="hljs-string">"dc=spring,dc=io"</span>
<span class="hljs-bullet">-</span> <span class="hljs-string">"dc=vmware,dc=com"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>By default, the server starts on a random port and triggers the regular LDAP support.
There is no need to specify a <code>spring.ldap.urls</code> property.</p>
</div>
<div class="paragraph">
<p>If there is a <code>schema.ldif</code> file on your classpath, it is used to initialize the server.
If you want to load the initialization script from a different resource, you can also use the <code>spring.ldap.embedded.ldif</code> property.</p>
</div>
<div class="paragraph">
<p>By default, a standard schema is used to validate <code>LDIF</code> files.
You can turn off validation altogether by setting the <code>spring.ldap.embedded.validation.enabled</code> property.
If you have custom attributes, you can use <code>spring.ldap.embedded.validation.schema</code> to define your custom attribute types or object classes.</p>
</div>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="sql.html">SQL Databases</a></span>
<span class="next"><a href="../io/index.html">IO</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../reference/data/nosql.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="nosql.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../3.3/reference/data/nosql.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../4.0-SNAPSHOT/reference/data/nosql.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.5-SNAPSHOT/reference/data/nosql.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.4-SNAPSHOT/reference/data/nosql.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.3-SNAPSHOT/reference/data/nosql.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>