<!DOCTYPE html>

<html><head><title>Ollama Chat :: Spring AI Reference</title><link href="https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/ollama-chat.html"/><meta content="2025-06-04T18:21:18.067838" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../../../../../images/spring-ai_reference___img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>
<div class="body">
<div class="nav-container" data-component="ai" data-version="1.1.0">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring AI</span>
<span class="version">1.1.0-SNAPSHOT</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../index.html">Overview</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../concepts.html">AI Concepts</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link" href="../../getting-started.html">Getting Started</a>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Reference</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../chatclient.html">Chat Client API</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../advisors.html">Advisors</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../prompt.html">Prompts</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../structured-output-converter.html">Structured Output</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../multimodality.html">Multimodality</a>
</li>
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Models</a>
<ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../chatmodel.html">Chat Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="comparison.html">Chat Models Comparison</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="bedrock-converse.html">Amazon Bedrock Converse</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="anthropic-chat.html">Anthropic 3</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="azure-openai-chat.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="deepseek-chat.html">DeepSeek</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="dmr-chat.html">Docker Model Runner</a>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="google-vertexai.html">Google VertexAI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="vertexai-gemini-chat.html">VertexAI Gemini</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="groq-chat.html">Groq</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="huggingface.html">Hugging Face</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="mistralai-chat.html">Mistral AI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="minimax-chat.html">MiniMax</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="moonshot-chat.html">Moonshot AI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="nvidia-chat.html">NVIDIA</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="4">
<a class="nav-link" href="ollama-chat.html">Ollama</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="perplexity-chat.html">Perplexity AI</a>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">OCI Generative AI</span>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="oci-genai/cohere-chat.html">Cohere</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="openai-chat.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="qianfan-chat.html">QianFan</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="zhipuai-chat.html">ZhiPu AI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../embeddings.html">Embedding Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../bedrock.html">Amazon Bedrock</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="../embeddings/bedrock-cohere-embedding.html">Cohere</a>
</li>
<li class="nav-item" data-depth="5">
<a class="nav-link" href="../embeddings/bedrock-titan-embedding.html">Titan</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../embeddings/azure-openai-embeddings.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../embeddings/mistralai-embeddings.html">Mistral AI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../embeddings/minimax-embeddings.html">MiniMax</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../embeddings/oci-genai-embeddings.html">OCI GenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../embeddings/ollama-embeddings.html">Ollama</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../embeddings/onnx.html">(ONNX) Transformers</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../embeddings/openai-embeddings.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../embeddings/postgresml-embeddings.html">PostgresML</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../embeddings/qianfan-embeddings.html">QianFan</a>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">VertexAI</span>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="../embeddings/vertexai-embeddings-text.html">Text Embedding</a>
</li>
<li class="nav-item" data-depth="5">
<a class="nav-link" href="../embeddings/vertexai-embeddings-multimodal.html">Multimodal Embedding</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../embeddings/zhipuai-embeddings.html">ZhiPu AI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../imageclient.html">Image Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../image/azure-openai-image.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../image/openai-image.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../image/stabilityai-image.html">Stability</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../image/zhipuai-image.html">ZhiPuAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../image/qianfan-image.html">QianFan</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="#api/audio">Audio Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../audio/transcriptions.html">Transcription API</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="../audio/transcriptions/azure-openai-transcriptions.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="5">
<a class="nav-link" href="../audio/transcriptions/openai-transcriptions.html">OpenAI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../audio/speech.html">Text-To-Speech (TTS) API</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="../audio/speech/openai-speech.html">OpenAI</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="#api/moderation">Moderation Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../moderation/openai-moderation.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../moderation/mistral-ai-moderation.html">Mistral AI</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../chat-memory.html">Chat Memory</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../tools.html">Tool Calling</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../mcp/mcp-overview.html">Model Context Protocol (MCP)</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../mcp/mcp-client-boot-starter-docs.html">MCP Client Boot Starters</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../mcp/mcp-server-boot-starter-docs.html">MCP Server Boot Starters</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../mcp/mcp-helpers.html">MCP Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../retrieval-augmented-generation.html">Retrieval Augmented Generation (RAG)</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../etl-pipeline.html">ETL Pipeline</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../testing.html">Model Evaluation</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../vectordbs.html">Vector Databases</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/azure.html">Azure AI Service</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/azure-cosmos-db.html">Azure Cosmos DB</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/apache-cassandra.html">Apache Cassandra Vector Store</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/chroma.html">Chroma</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/couchbase.html">Couchbase</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/elasticsearch.html">Elasticsearch</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/gemfire.html">GemFire</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/mariadb.html">MariaDB Vector Store</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/milvus.html">Milvus</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/mongodb.html">MongoDB Atlas</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/neo4j.html">Neo4j</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/opensearch.html">OpenSearch</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/oracle.html">Oracle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/pgvector.html">PGvector</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/pinecone.html">Pinecone</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/qdrant.html">Qdrant</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/redis.html">Redis</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/hana.html">SAP Hana</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/typesense.html">Typesense</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../vectordbs/weaviate.html">Weaviate</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../observability/index.html">Observability</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../docker-compose.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Testing</span>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testcontainers.html">Testcontainers</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Guides</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="https://github.com/spring-ai-community/awesome-spring-ai">Awesome Spring AI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="prompt-engineering-patterns.html">Prompt Engineering Patterns</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../effective-agents.html">Building Effective Agents</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../cloud-bindings.html">Deploying to the Cloud</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../upgrade-notes.html">Upgrade Notes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../tools-migration.html">Migrating FunctionCallback to ToolCallback API</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Ollama Chat">
<div class="toc-menu"><h3>Ollama Chat</h3><ul><li data-level="1"><a href="#_prerequisites">Prerequisites</a></li><li data-level="1"><a href="#_auto_configuration">Auto-configuration</a></li><li data-level="2"><a href="#_base_properties">Base Properties</a></li><li data-level="2"><a href="#_chat_properties">Chat Properties</a></li><li data-level="1"><a href="#chat-options">Runtime Options</a></li><li data-level="1"><a href="#auto-pulling-models">Auto-pulling Models</a></li><li data-level="1"><a href="#_function_calling">Function Calling</a></li><li data-level="1"><a href="#_multimodal">Multimodal</a></li><li data-level="1"><a href="#_structured_outputs">Structured Outputs</a></li><li data-level="2"><a href="#_configuration">Configuration</a></li><li data-level="1"><a href="#_openai_api_compatibility">OpenAI API Compatibility</a></li><li data-level="1"><a href="#_huggingface_models">HuggingFace Models</a></li><li data-level="1"><a href="#_sample_controller">Sample Controller</a></li><li data-level="1"><a href="#_manual_configuration">Manual Configuration</a></li><li data-level="1"><a href="#low-level-api">Low-level OllamaApi Client</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-ai/edit/main/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/chat/ollama-chat.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-ai" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/questions/tagged/spring">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../index.html">Spring AI</a></li>
<li>Reference</li>
<li><a href="../index.html">Models</a></li>
<li><a href="../chatmodel.html">Chat Models</a></li>
<li><a href="ollama-chat.html">Ollama</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p>This version is still in development and is not considered stable yet. For the latest snapshot version, please use <a href="../../../api/chat/ollama-chat.html">Spring AI 1.0.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Ollama Chat</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Ollama Chat</h3><ul><li data-level="1"><a href="#_prerequisites">Prerequisites</a></li><li data-level="1"><a href="#_auto_configuration">Auto-configuration</a></li><li data-level="2"><a href="#_base_properties">Base Properties</a></li><li data-level="2"><a href="#_chat_properties">Chat Properties</a></li><li data-level="1"><a href="#chat-options">Runtime Options</a></li><li data-level="1"><a href="#auto-pulling-models">Auto-pulling Models</a></li><li data-level="1"><a href="#_function_calling">Function Calling</a></li><li data-level="1"><a href="#_multimodal">Multimodal</a></li><li data-level="1"><a href="#_structured_outputs">Structured Outputs</a></li><li data-level="2"><a href="#_configuration">Configuration</a></li><li data-level="1"><a href="#_openai_api_compatibility">OpenAI API Compatibility</a></li><li data-level="1"><a href="#_huggingface_models">HuggingFace Models</a></li><li data-level="1"><a href="#_sample_controller">Sample Controller</a></li><li data-level="1"><a href="#_manual_configuration">Manual Configuration</a></li><li data-level="1"><a href="#low-level-api">Low-level OllamaApi Client</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>With <a class="external" href="https://ollama.ai/" target="_blank">Ollama</a> you can run various Large Language Models (LLMs) locally and generate text from them.
Spring AI supports the Ollama chat completion capabilities with the <code>OllamaChatModel</code> API.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Ollama offers an OpenAI API compatible endpoint as well.
The <a href="#_openai_api_compatibility">OpenAI API compatibility</a> section explains how to use the <a class="xref page" href="openai-chat.html">Spring AI OpenAI</a> to connect to an Ollama server.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_prerequisites"><a class="anchor" href="#_prerequisites"></a>Prerequisites</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You first need access to an Ollama instance. There are a few options, including the following:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a class="external" href="https://ollama.com/download" target="_blank">Download and install Ollama</a> on your local machine.</p>
</li>
<li>
<p>Configure and <a class="xref page" href="../testcontainers.html">run Ollama via Testcontainers</a>.</p>
</li>
<li>
<p>Bind to an Ollama instance via <a class="xref page" href="../cloud-bindings.html">Kubernetes Service Bindings</a>.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>You can pull the models you want to use in your application from the <a class="external" href="https://ollama.com/library" target="_blank">Ollama model library</a>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-shellscript hljs" data-lang="shellscript">ollama pull &lt;model-name&gt;</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>You can also pull any of the thousands, free, <a class="external" href="https://huggingface.co/models?library=gguf&amp;sort=trending" target="_blank">GGUF Hugging Face Models</a>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-shellscript hljs" data-lang="shellscript">ollama pull hf.co/&lt;username&gt;/&lt;model-repository&gt;</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Alternatively, you can enable the option to download automatically any needed model: <a href="#auto-pulling-models">Auto-pulling Models</a>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_auto_configuration"><a class="anchor" href="#_auto_configuration"></a>Auto-configuration</h2>
<div class="sectionbody">
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>There has been a significant change in the Spring AI auto-configuration, starter modules' artifact names.
Please refer to the <a href="https://docs.spring.io/spring-ai/reference/upgrade-notes.html">upgrade notes</a> for more information.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Spring AI provides Spring Boot auto-configuration for the Ollama chat integration.
To enable it add the following dependency to your project’s Maven <code>pom.xml</code> or Gradle <code>build.gradle</code> build files:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Gradle|Maven" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_maven--panel" aria-selected="true" class="tab is-selected" data-sync-id="Maven" id="_tabs_1_maven" role="tab" tabindex="0">
<p>Maven</p>
</li>
<li aria-controls="_tabs_1_gradle--panel" class="tab" data-sync-id="Gradle" id="_tabs_1_gradle" role="tab" tabindex="-1">
<p>Gradle</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_maven" class="tabpanel" id="_tabs_1_maven--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
   <span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.ai<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
   <span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-ai-starter-model-ollama<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_gradle" class="tabpanel is-hidden" hidden="" id="_tabs_1_gradle--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-groovy hljs" data-lang="groovy">dependencies {
    implementation <span class="hljs-string">'org.springframework.ai:spring-ai-starter-model-ollama'</span>
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Refer to the <a class="xref page" href="../../getting-started.html#dependency-management">Dependency Management</a> section to add the Spring AI BOM to your build file.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="_base_properties"><a class="anchor" href="#_base_properties"></a>Base Properties</h3>
<div class="paragraph">
<p>The prefix <code>spring.ai.ollama</code> is the property prefix to configure the connection to Ollama.</p>
</div>
<table class="tableblock frame-all grid-all stripes-even stretch">
<colgroup>
<col style="width: 30%;"/>
<col style="width: 60%;"/>
<col style="width: 10%;"/>
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Property</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Description</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Default</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.base-url</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Base URL where Ollama API server is running.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code><a class="bare external" href="http://localhost:11434" target="_blank">localhost:11434</a></code></p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>Here are the properties for initializing the Ollama integration and <a href="#auto-pulling-models">auto-pulling models</a>.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 30%;"/>
<col style="width: 60%;"/>
<col style="width: 10%;"/>
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Property</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Description</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Default</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.init.pull-model-strategy</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Whether to pull models at startup-time and how.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>never</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.init.timeout</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">How long to wait for a model to be pulled.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>5m</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.init.max-retries</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Maximum number of retries for the model pull operation.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>0</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.init.chat.include</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Include this type of models in the initialization task.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>true</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.init.chat.additional-models</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Additional models to initialize besides the ones configured via default properties.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>[]</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="_chat_properties"><a class="anchor" href="#_chat_properties"></a>Chat Properties</h3>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>Enabling and disabling of the chat auto-configurations are now configured via top level properties with the prefix <code>spring.ai.model.chat</code>.</p>
</div>
<div class="paragraph">
<p>To enable, spring.ai.model.chat=ollama (It is enabled by default)</p>
</div>
<div class="paragraph">
<p>To disable, spring.ai.model.chat=none (or any value which doesn’t match ollama)</p>
</div>
<div class="paragraph">
<p>This change is done to allow configuration of multiple models.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The prefix <code>spring.ai.ollama.chat.options</code> is the property prefix that configures the Ollama chat model.
It includes the Ollama request (advanced) parameters such as the <code>model</code>, <code>keep-alive</code>, and <code>format</code> as well as the Ollama model <code>options</code> properties.</p>
</div>
<div class="paragraph">
<p>Here are the advanced request parameter for the Ollama chat model:</p>
</div>
<table class="tableblock frame-all grid-all stripes-even stretch">
<colgroup>
<col style="width: 30%;"/>
<col style="width: 60%;"/>
<col style="width: 10%;"/>
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Property</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Description</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Default</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.enabled (Removed and no longer valid)</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Enable Ollama chat model.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.model.chat</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Enable Ollama chat model.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">ollama</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.model</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The name of the <a class="external" href="https://github.com/ollama/ollama?tab=readme-ov-file#model-library" target="_blank">supported model</a> to use.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">mistral</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.format</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The format to return a response in. Currently, the only accepted value is <code>json</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.keep_alive</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Controls how long the model will stay loaded into memory following the request</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5m</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>The remaining <code>options</code> properties are based on the <a class="external" href="https://github.com/ollama/ollama/blob/main/docs/modelfile.md#valid-parameters-and-values" target="_blank">Ollama Valid Parameters and Values</a> and <a class="external" href="https://github.com/ollama/ollama/blob/main/api/types.go" target="_blank">Ollama Types</a>. The default values are based on the <a class="external" href="https://github.com/ollama/ollama/blob/b538dc3858014f94b099730a592751a5454cab0a/api/types.go#L364" target="_blank">Ollama Types Defaults</a>.</p>
</div>
<table class="tableblock frame-all grid-all stripes-even stretch">
<colgroup>
<col style="width: 30%;"/>
<col style="width: 60%;"/>
<col style="width: 10%;"/>
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Property</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Description</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Default</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.numa</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Whether to use NUMA.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.num-ctx</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Sets the size of the context window used to generate the next token.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2048</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.num-batch</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Prompt processing maximum batch size.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">512</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.num-gpu</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The number of layers to send to the GPU(s). On macOS it defaults to 1 to enable metal support, 0 to disable. 1 here indicates that NumGPU should be set dynamically</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-1</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.main-gpu</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">When using multiple GPUs this option controls which GPU is used for small tensors for which the overhead of splitting the computation across all GPUs is not worthwhile. The GPU in question will use slightly more VRAM to store a scratch buffer for temporary results.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.low-vram</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.f16-kv</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.logits-all</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Return logits for all the tokens, not just the last one. To enable completions to return logprobs, this must be true.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.vocab-only</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Load only the vocabulary, not the weights.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.use-mmap</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">By default, models are mapped into memory, which allows the system to load only the necessary parts of the model as needed. However, if the model is larger than your total amount of RAM or if your system is low on available memory, using mmap might increase the risk of pageouts, negatively impacting performance. Disabling mmap results in slower load times but may reduce pageouts if you’re not using mlock. Note that if the model is larger than the total amount of RAM, turning off mmap would prevent the model from loading at all.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">null</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.use-mlock</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Lock the model in memory, preventing it from being swapped out when memory-mapped. This can improve performance but trades away some of the advantages of memory-mapping by requiring more RAM to run and potentially slowing down load times as the model loads into RAM.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.num-thread</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Sets the number of threads to use during computation. By default, Ollama will detect this for optimal performance. It is recommended to set this value to the number of physical CPU cores your system has (as opposed to the logical number of cores). 0 = let the runtime decide</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.num-keep</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.seed</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-1</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.num-predict</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Maximum number of tokens to predict when generating text. (-1 = infinite generation, -2 = fill context)</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-1</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.top-k</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Reduces the probability of generating nonsense. A higher value (e.g., 100) will give more diverse answers, while a lower value (e.g., 10) will be more conservative.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">40</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.top-p</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0.9</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.min-p</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.tfs-z</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Tail-free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.typical-p</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.repeat-last-n</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.temperature</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The temperature of the model. Increasing the temperature will make the model answer more creatively.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0.8</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.repeat-penalty</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.1</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.presence-penalty</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.frequency-penalty</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.mirostat</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Enable Mirostat sampling for controlling perplexity. (default: 0, 0 = disabled, 1 = Mirostat, 2 = Mirostat 2.0)</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.mirostat-tau</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.mirostat-eta</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0.1</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.penalize-newline</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.stop</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Sets the stop sequences to use. When this pattern is encountered the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.functions</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">List of functions, identified by their names, to enable for function calling in a single prompt requests. Functions with those names must exist in the functionCallbacks registry.</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">spring.ai.ollama.chat.options.proxy-tool-calls</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">If true, the Spring AI will not handle the function calls internally, but will proxy them to the client. Then is the client’s responsibility to handle the function calls, dispatch them to the appropriate function, and return the results. If false (the default), the Spring AI will handle the function calls internally. Applicable only for chat models with function calling support</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
</tr>
</tbody>
</table>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
All properties prefixed with <code>spring.ai.ollama.chat.options</code> can be overridden at runtime by adding request-specific <a href="#chat-options">Runtime Options</a> to the <code>Prompt</code> call.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="chat-options"><a class="anchor" href="#chat-options"></a>Runtime Options</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The <a class="external" href="https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-ollama/src/main/java/org/springframework/ai/ollama/api/OllamaOptions.java" target="_blank">OllamaOptions.java</a> class provides model configurations, such as the model to use, the temperature,  etc.</p>
</div>
<div class="paragraph">
<p>On start-up, the default options can be configured with the <code>OllamaChatModel(api, options)</code> constructor or the <code>spring.ai.ollama.chat.options.*</code> properties.</p>
</div>
<div class="paragraph">
<p>At run-time, you can override the default options by adding new, request-specific options to the <code>Prompt</code> call.
For example, to override the default model and temperature for a specific request:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">ChatResponse response = chatModel.call(
    <span class="hljs-keyword">new</span> Prompt(
        <span class="hljs-string">"Generate the names of 5 famous pirates."</span>,
        OllamaOptions.builder()
            .model(OllamaModel.LLAMA3_1)
            .temperature(<span class="hljs-number">0.4</span>)
            .build()
    ));</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
In addition to the model specific <a class="external" href="https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-ollama/src/main/java/org/springframework/ai/ollama/api/OllamaOptions.java" target="_blank">OllamaOptions</a> you can use a portable <a class="external" href="https://github.com/spring-projects/spring-ai/blob/main/spring-ai-client-chat/src/main/java/org/springframework/ai/chat/prompt/ChatOptions.java" target="_blank">ChatOptions</a> instance, created with <a class="external" href="https://github.com/spring-projects/spring-ai/blob/main/spring-ai-client-chat/src/main/java/org/springframework/ai/chat/prompt/ChatOptionsBuilder.java" target="_blank">ChatOptionsBuilder#builder()</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="auto-pulling-models"><a class="anchor" href="#auto-pulling-models"></a>Auto-pulling Models</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring AI Ollama can automatically pull models when they are not available in your Ollama instance.
This feature is particularly useful for development and testing as well as for deploying your applications to new environments.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You can also pull, by name, any of the thousands, free, <a class="external" href="https://huggingface.co/models?library=gguf&amp;sort=trending" target="_blank">GGUF Hugging Face Models</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>There are three strategies for pulling models:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>always</code> (defined in <code>PullModelStrategy.ALWAYS</code>): Always pull the model, even if it’s already available. Useful to ensure you’re using the latest version of the model.</p>
</li>
<li>
<p><code>when_missing</code> (defined in <code>PullModelStrategy.WHEN_MISSING</code>): Only pull the model if it’s not already available. This may result in using an older version of the model.</p>
</li>
<li>
<p><code>never</code> (defined in <code>PullModelStrategy.NEVER</code>): Never pull the model automatically.</p>
</li>
</ul>
</div>
<div class="admonitionblock caution">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-caution" title="Caution"></i>
</td>
<td class="content">
Due to potential delays while downloading models, automatic pulling is not recommended for production environments. Instead, consider assessing and pre-downloading the necessary models in advance.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>All models defined via configuration properties and default options can be automatically pulled at startup time.
You can configure the pull strategy, timeout, and maximum number of retries using configuration properties:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">ai:</span>
    <span class="hljs-attr">ollama:</span>
      <span class="hljs-attr">init:</span>
        <span class="hljs-attr">pull-model-strategy:</span> <span class="hljs-string">always</span>
        <span class="hljs-attr">timeout:</span> <span class="hljs-string">60s</span>
        <span class="hljs-attr">max-retries:</span> <span class="hljs-number">1</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock caution">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-caution" title="Caution"></i>
</td>
<td class="content">
The application will not complete its initialization until all specified models are available in Ollama. Depending on the model size and internet connection speed, this may significantly slow down your application’s startup time.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>You can initialize additional models at startup, which is useful for models used dynamically at runtime:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">ai:</span>
    <span class="hljs-attr">ollama:</span>
      <span class="hljs-attr">init:</span>
        <span class="hljs-attr">pull-model-strategy:</span> <span class="hljs-string">always</span>
        <span class="hljs-attr">chat:</span>
          <span class="hljs-attr">additional-models:</span>
            <span class="hljs-bullet">-</span> <span class="hljs-string">llama3.2</span>
            <span class="hljs-bullet">-</span> <span class="hljs-string">qwen2.5</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>If you want to apply the pulling strategy only to specific types of models, you can exclude chat models from the initialization task:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">ai:</span>
    <span class="hljs-attr">ollama:</span>
      <span class="hljs-attr">init:</span>
        <span class="hljs-attr">pull-model-strategy:</span> <span class="hljs-string">always</span>
        <span class="hljs-attr">chat:</span>
          <span class="hljs-attr">include:</span> <span class="hljs-literal">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This configuration will apply the pulling strategy to all models except chat models.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_function_calling"><a class="anchor" href="#_function_calling"></a>Function Calling</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can register custom Java functions with the <code>OllamaChatModel</code> and have the Ollama model intelligently choose to output a JSON object containing arguments to call one or many of the registered functions.
This is a powerful technique to connect the LLM capabilities with external tools and APIs.
Read more about <a class="xref page" href="../tools.html">Tool Calling</a>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
You need Ollama 0.2.8 or newer to use the functional calling capabilities and Ollama 0.4.6 or newer to use them in streaming mode.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_multimodal"><a class="anchor" href="#_multimodal"></a>Multimodal</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Multimodality refers to a model’s ability to simultaneously understand and process information from various sources, including text, images, audio, and other data formats.</p>
</div>
<div class="paragraph">
<p>Some of the models available in Ollama with multimodality support are <a class="external" href="https://ollama.com/library/llava" target="_blank">LLaVA</a> and <a class="external" href="https://ollama.com/library/bakllava" target="_blank">BakLLaVA</a> (see the <a class="external" href="https://ollama.com/search?c=vision" target="_blank">full list</a>).
For further details, refer to the <a class="external" href="https://llava-vl.github.io/" target="_blank">LLaVA: Large Language and Vision Assistant</a>.</p>
</div>
<div class="paragraph">
<p>The Ollama <a class="external" href="https://github.com/ollama/ollama/blob/main/docs/api.md#parameters-1" target="_blank">Message API</a> provides an "images" parameter to incorporate a list of base64-encoded images with the message.</p>
</div>
<div class="paragraph">
<p>Spring AI’s <a class="external" href="https://github.com/spring-projects/spring-ai/blob/main/spring-ai-model/src/main/java/org/springframework/ai/chat/messages/Message.java" target="_blank">Message</a> interface facilitates multimodal AI models by introducing the <a class="external" href="https://github.com/spring-projects/spring-ai/blob/main/spring-ai-commons/src/main/java/org/springframework/ai/content/Media.java" target="_blank">Media</a> type.
This type encompasses data and details regarding media attachments in messages, utilizing Spring’s <code>org.springframework.util.MimeType</code> and a <code>org.springframework.core.io.Resource</code> for the raw media data.</p>
</div>
<div class="paragraph">
<p>Below is a straightforward code example excerpted from <a class="external" href="https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-ollama/src/test/java/org/springframework/ai/ollama/OllamaChatModelMultimodalIT.java" target="_blank">OllamaChatModelMultimodalIT.java</a>, illustrating the fusion of user text with an image.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">var</span> imageResource = <span class="hljs-keyword">new</span> ClassPathResource(<span class="hljs-string">"/multimodal.test.png"</span>);

<span class="hljs-keyword">var</span> userMessage = <span class="hljs-keyword">new</span> UserMessage(<span class="hljs-string">"Explain what do you see on this picture?"</span>,
        <span class="hljs-keyword">new</span> Media(MimeTypeUtils.IMAGE_PNG, <span class="hljs-keyword">this</span>.imageResource));

ChatResponse response = chatModel.call(<span class="hljs-keyword">new</span> Prompt(<span class="hljs-keyword">this</span>.userMessage,
        OllamaOptions.builder().model(OllamaModel.LLAVA)).build());</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The example shows a model taking as an input the <code>multimodal.test.png</code> image:</p>
</div>
<div class="imageblock text-left">
<div class="content">
<img alt="Multimodal Test Image" height="200" src="../../../../../../../images/spring-ai_reference_1.1-SNAPSHOT__images/multimodal.test.png" width="200"/>
</div>
</div>
<div class="paragraph">
<p>along with the text message "Explain what do you see on this picture?", and generating a response like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>The image shows a small metal basket filled with ripe bananas and red apples. The basket is placed on a surface,
which appears to be a table or countertop, as there's a hint of what seems like a kitchen cabinet or drawer in
the background. There's also a gold-colored ring visible behind the basket, which could indicate that this
photo was taken in an area with metallic decorations or fixtures. The overall setting suggests a home environment
where fruits are being displayed, possibly for convenience or aesthetic purposes.</pre>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_structured_outputs"><a class="anchor" href="#_structured_outputs"></a>Structured Outputs</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Ollama provides custom <a class="external" href="https://ollama.com/blog/structured-outputs" target="_blank">Structured Outputs</a> APIs that ensure your model generates responses conforming strictly to your provided <code>JSON Schema</code>.
In addition to the existing Spring AI model-agnostic <a class="xref page" href="../structured-output-converter.html">Structured Output Converter</a>, these APIs offer enhanced control and precision.</p>
</div>
<div class="sect2">
<h3 id="_configuration"><a class="anchor" href="#_configuration"></a>Configuration</h3>
<div class="paragraph">
<p>Spring AI allows you to configure your response format programmatically using the <code>OllamaOptions</code> builder.</p>
</div>
<div class="sect3">
<h4 id="_using_the_chat_options_builder"><a class="anchor" href="#_using_the_chat_options_builder"></a>Using the Chat Options Builder</h4>
<div class="paragraph">
<p>You can set the response format programmatically with the <code>OllamaOptions</code> builder as shown below:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">String jsonSchema = <span class="hljs-string">""</span><span class="hljs-string">"
        {
            "</span>type<span class="hljs-string">": "</span>object<span class="hljs-string">",
            "</span>properties<span class="hljs-string">": {
                "</span>steps<span class="hljs-string">": {
                    "</span>type<span class="hljs-string">": "</span>array<span class="hljs-string">",
                    "</span>items<span class="hljs-string">": {
                        "</span>type<span class="hljs-string">": "</span>object<span class="hljs-string">",
                        "</span>properties<span class="hljs-string">": {
                            "</span>explanation<span class="hljs-string">": { "</span>type<span class="hljs-string">": "</span>string<span class="hljs-string">" },
                            "</span>output<span class="hljs-string">": { "</span>type<span class="hljs-string">": "</span>string<span class="hljs-string">" }
                        },
                        "</span>required<span class="hljs-string">": ["</span>explanation<span class="hljs-string">", "</span>output<span class="hljs-string">"],
                        "</span>additionalProperties<span class="hljs-string">": false
                    }
                },
                "</span>final_answer<span class="hljs-string">": { "</span>type<span class="hljs-string">": "</span>string<span class="hljs-string">" }
            },
            "</span>required<span class="hljs-string">": ["</span>steps<span class="hljs-string">", "</span>final_answer<span class="hljs-string">"],
            "</span>additionalProperties<span class="hljs-string">": false
        }
        "</span><span class="hljs-string">""</span>;

Prompt prompt = <span class="hljs-keyword">new</span> Prompt(<span class="hljs-string">"how can I solve 8x + 7 = -23"</span>,
        OllamaOptions.builder()
            .model(OllamaModel.LLAMA3_2.getName())
            .format(<span class="hljs-keyword">new</span> ObjectMapper().readValue(jsonSchema, Map<span class="hljs-class">.<span class="hljs-keyword">class</span>))
            .<span class="hljs-title">build</span>())</span>;

ChatResponse response = <span class="hljs-keyword">this</span>.ollamaChatModel.call(<span class="hljs-keyword">this</span>.prompt);</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="_integrating_with_beanoutputconverter_utilities"><a class="anchor" href="#_integrating_with_beanoutputconverter_utilities"></a>Integrating with BeanOutputConverter Utilities</h4>
<div class="paragraph">
<p>You can leverage existing <a class="xref page" href="../structured-output-converter.html#_bean_output_converter">BeanOutputConverter</a> utilities to automatically generate the JSON Schema from your domain objects and later convert the structured response into domain-specific instances:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-function">record <span class="hljs-title">MathReasoning</span><span class="hljs-params">(
    @JsonProperty(required = <span class="hljs-keyword">true</span>, value = <span class="hljs-string">"steps"</span>)</span> Steps steps,
    @<span class="hljs-title">JsonProperty</span><span class="hljs-params">(required = <span class="hljs-keyword">true</span>, value = <span class="hljs-string">"final_answer"</span>)</span> String finalAnswer) </span>{

    <span class="hljs-function">record <span class="hljs-title">Steps</span><span class="hljs-params">(
        @JsonProperty(required = <span class="hljs-keyword">true</span>, value = <span class="hljs-string">"items"</span>)</span> Items[] items) </span>{

        <span class="hljs-function">record <span class="hljs-title">Items</span><span class="hljs-params">(
            @JsonProperty(required = <span class="hljs-keyword">true</span>, value = <span class="hljs-string">"explanation"</span>)</span> String explanation,
            @<span class="hljs-title">JsonProperty</span><span class="hljs-params">(required = <span class="hljs-keyword">true</span>, value = <span class="hljs-string">"output"</span>)</span> String output) </span>{
        }
    }
}

<span class="hljs-keyword">var</span> outputConverter = <span class="hljs-keyword">new</span> BeanOutputConverter&lt;&gt;(MathReasoning<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;

Prompt prompt = <span class="hljs-keyword">new</span> Prompt(<span class="hljs-string">"how can I solve 8x + 7 = -23"</span>,
        OllamaOptions.builder()
            .model(OllamaModel.LLAMA3_2.getName())
            .format(outputConverter.getJsonSchemaMap())
            .build());

ChatResponse response = <span class="hljs-keyword">this</span>.ollamaChatModel.call(<span class="hljs-keyword">this</span>.prompt);
String content = <span class="hljs-keyword">this</span>.response.getResult().getOutput().getText();

MathReasoning mathReasoning = <span class="hljs-keyword">this</span>.outputConverter.convert(<span class="hljs-keyword">this</span>.content);</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Ensure you use the <code>@JsonProperty(required = true,…​)</code>  annotation for generating a schema that accurately marks fields as <code>required</code>.
Although this is optional for JSON Schema, it’s recommended for the structured response to function correctly.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_openai_api_compatibility"><a class="anchor" href="#_openai_api_compatibility"></a>OpenAI API Compatibility</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Ollama is OpenAI API-compatible and you can use the <a class="xref page" href="openai-chat.html">Spring AI OpenAI</a> client to talk to Ollama and use tools.
For this, you need to configure the OpenAI base URL to your Ollama instance: <code>spring.ai.openai.chat.base-url=http://localhost:11434</code> and select one of the provided Ollama models: <code>spring.ai.openai.chat.options.model=mistral</code>.</p>
</div>
<div class="imageblock text-center">
<div class="content">
<img alt="Ollama OpenAI API compatibility" height="600" src="../../../../../../../images/spring-ai_reference_1.1-SNAPSHOT__images/spring-ai-ollama-over-openai.jpg" width="800"/>
</div>
</div>
<div class="paragraph">
<p>Check the <a class="external" href="https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-openai/src/test/java/org/springframework/ai/openai/chat/proxy/OllamaWithOpenAiChatModelIT.java" target="_blank">OllamaWithOpenAiChatModelIT.java</a> tests for examples of using Ollama over Spring AI OpenAI.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_huggingface_models"><a class="anchor" href="#_huggingface_models"></a>HuggingFace Models</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Ollama can access, out of the box, all <a class="external" href="https://huggingface.co/models?library=gguf&amp;sort=trending" target="_blank">GGUF Hugging Face </a> Chat Models.
You can pull any of these models by name: <code>ollama pull hf.co/&lt;username&gt;/&lt;model-repository&gt;</code> or configure the auto-pulling strategy: <a href="#auto-pulling-models">Auto-pulling Models</a>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-none hljs">spring.ai.ollama.chat.options.model=hf.co/bartowski/gemma-2-2b-it-GGUF
spring.ai.ollama.init.pull-model-strategy=always</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="ulist">
<ul>
<li>
<p><code>spring.ai.ollama.chat.options.model</code>: Specifies the <a class="external" href="https://huggingface.co/models?library=gguf&amp;sort=trending" target="_blank">Hugging Face GGUF model</a> to use.</p>
</li>
<li>
<p><code>spring.ai.ollama.init.pull-model-strategy=always</code>: (optional) Enables automatic model pulling at startup time.
For production, you should pre-download the models to avoid delays: <code>ollama pull hf.co/bartowski/gemma-2-2b-it-GGUF</code>.</p>
</li>
</ul>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_sample_controller"><a class="anchor" href="#_sample_controller"></a>Sample Controller</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="external" href="https://start.spring.io/" target="_blank">Create</a> a new Spring Boot project and add the <code>spring-ai-starter-model-ollama</code> to your pom (or gradle) dependencies.</p>
</div>
<div class="paragraph">
<p>Add a <code>application.yaml</code> file, under the <code>src/main/resources</code> directory, to enable and configure the Ollama chat model:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">ai:</span>
    <span class="hljs-attr">ollama:</span>
      <span class="hljs-attr">base-url:</span> <span class="hljs-string">http://localhost:11434</span>
      <span class="hljs-attr">chat:</span>
        <span class="hljs-attr">options:</span>
          <span class="hljs-attr">model:</span> <span class="hljs-string">mistral</span>
          <span class="hljs-attr">temperature:</span> <span class="hljs-number">0.7</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Replace the <code>base-url</code> with your Ollama server URL.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>This will create an <code>OllamaChatModel</code> implementation that you can inject into your classes.
Here is an example of a simple <code>@RestController</code> class that uses the chat model for text generations.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@RestController</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">ChatController</span> </span>{

    <span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> OllamaChatModel chatModel;

    <span class="hljs-meta">@Autowired</span>
    <span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">ChatController</span><span class="hljs-params">(OllamaChatModel chatModel)</span> </span>{
        <span class="hljs-keyword">this</span>.chatModel = chatModel;
    }

    <span class="hljs-meta">@GetMapping</span>(<span class="hljs-string">"/ai/generate"</span>)
    <span class="hljs-function"><span class="hljs-keyword">public</span> Map&lt;String,String&gt; <span class="hljs-title">generate</span><span class="hljs-params">(@RequestParam(value = <span class="hljs-string">"message"</span>, defaultValue = <span class="hljs-string">"Tell me a joke"</span>)</span> String message) </span>{
        <span class="hljs-keyword">return</span> Map.of(<span class="hljs-string">"generation"</span>, <span class="hljs-keyword">this</span>.chatModel.call(message));
    }

    <span class="hljs-meta">@GetMapping</span>(<span class="hljs-string">"/ai/generateStream"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> Flux&lt;ChatResponse&gt; <span class="hljs-title">generateStream</span><span class="hljs-params">(@RequestParam(value = <span class="hljs-string">"message"</span>, defaultValue = <span class="hljs-string">"Tell me a joke"</span>)</span> String message) </span>{
        Prompt prompt = <span class="hljs-keyword">new</span> Prompt(<span class="hljs-keyword">new</span> UserMessage(message));
        <span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.chatModel.stream(prompt);
    }

}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_manual_configuration"><a class="anchor" href="#_manual_configuration"></a>Manual Configuration</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you don’t want to use the Spring Boot auto-configuration, you can manually configure the <code>OllamaChatModel</code> in your application.
The <a class="external" href="https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-ollama/src/main/java/org/springframework/ai/ollama/OllamaChatModel.java" target="_blank">OllamaChatModel</a> implements the <code>ChatModel</code> and <code>StreamingChatModel</code> and uses the <a href="#low-level-api">Low-level OllamaApi Client</a> to connect to the Ollama service.</p>
</div>
<div class="paragraph">
<p>To use it, add the <code>spring-ai-ollama</code> dependency to your project’s Maven <code>pom.xml</code> or Gradle <code>build.gradle</code> build files:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Gradle|Maven" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_maven--panel" aria-selected="true" class="tab is-selected" data-sync-id="Maven" id="_tabs_2_maven" role="tab" tabindex="0">
<p>Maven</p>
</li>
<li aria-controls="_tabs_2_gradle--panel" class="tab" data-sync-id="Gradle" id="_tabs_2_gradle" role="tab" tabindex="-1">
<p>Gradle</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_maven" class="tabpanel" id="_tabs_2_maven--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.ai<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-ai-ollama<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_gradle" class="tabpanel is-hidden" hidden="" id="_tabs_2_gradle--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-groovy hljs" data-lang="groovy">dependencies {
    implementation <span class="hljs-string">'org.springframework.ai:spring-ai-ollama'</span>
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Refer to the <a class="xref page" href="../../getting-started.html#dependency-management">Dependency Management</a> section to add the Spring AI BOM to your build file.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The <code>spring-ai-ollama</code> dependency provides access also to the <code>OllamaEmbeddingModel</code>.
For more information about the <code>OllamaEmbeddingModel</code> refer to the <a href="../embeddings/ollama-embeddings.html">Ollama Embedding Model</a> section.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Next, create an <code>OllamaChatModel</code> instance and use it to send requests for text generation:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">var</span> ollamaApi = OllamaApi.builder().build();

<span class="hljs-keyword">var</span> chatModel = OllamaChatModel.builder()
                    .ollamaApi(ollamaApi)
                    .defaultOptions(
                        OllamaOptions.builder()
                            .model(OllamaModel.MISTRAL)
                            .temperature(<span class="hljs-number">0.9</span>)
                            .build())
                    .build();

ChatResponse response = <span class="hljs-keyword">this</span>.chatModel.call(
    <span class="hljs-keyword">new</span> Prompt(<span class="hljs-string">"Generate the names of 5 famous pirates."</span>));

<span class="hljs-comment">// Or with streaming responses</span>
Flux&lt;ChatResponse&gt; response = <span class="hljs-keyword">this</span>.chatModel.stream(
    <span class="hljs-keyword">new</span> Prompt(<span class="hljs-string">"Generate the names of 5 famous pirates."</span>));</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The <code>OllamaOptions</code> provides the configuration information for all chat requests.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="low-level-api"><a class="anchor" href="#low-level-api"></a>Low-level OllamaApi Client</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The <a class="external" href="https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-ollama/src/main/java/org/springframework/ai/ollama/api/OllamaApi.java" target="_blank">OllamaApi</a> provides a lightweight Java client for the Ollama Chat Completion API <a class="external" href="https://github.com/ollama/ollama/blob/main/docs/api.md#generate-a-chat-completion" target="_blank">Ollama Chat Completion API</a>.</p>
</div>
<div class="paragraph">
<p>The following class diagram illustrates the <code>OllamaApi</code> chat interfaces and building blocks:</p>
</div>
<div class="imageblock">
<div class="content">
<img alt="OllamaApi Chat Completion API Diagram" height="600" src="../../../../../../../images/spring-ai_reference_1.1-SNAPSHOT__images/ollama-chat-completion-api.jpg" width="800"/>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <code>OllamaApi</code> is a low-level API and is not recommended for direct use. Use the <code>OllamaChatModel</code> instead.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Here is a simple snippet showing how to use the API programmatically:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">OllamaApi ollamaApi = <span class="hljs-keyword">new</span> OllamaApi(<span class="hljs-string">"YOUR_HOST:YOUR_PORT"</span>);

<span class="hljs-comment">// Sync request</span>
<span class="hljs-keyword">var</span> request = ChatRequest.builder(<span class="hljs-string">"orca-mini"</span>)
    .stream(<span class="hljs-keyword">false</span>) <span class="hljs-comment">// not streaming</span>
    .messages(List.of(
            Message.builder(Role.SYSTEM)
                .content(<span class="hljs-string">"You are a geography teacher. You are talking to a student."</span>)
                .build(),
            Message.builder(Role.USER)
                .content(<span class="hljs-string">"What is the capital of Bulgaria and what is the size? "</span>
                        + <span class="hljs-string">"What is the national anthem?"</span>)
                .build()))
    .options(OllamaOptions.builder().temperature(<span class="hljs-number">0.9</span>).build())
    .build();

ChatResponse response = <span class="hljs-keyword">this</span>.ollamaApi.chat(<span class="hljs-keyword">this</span>.request);

<span class="hljs-comment">// Streaming request</span>
<span class="hljs-keyword">var</span> request2 = ChatRequest.builder(<span class="hljs-string">"orca-mini"</span>)
    .ttream(<span class="hljs-keyword">true</span>) <span class="hljs-comment">// streaming</span>
    .messages(List.of(Message.builder(Role.USER)
        .content(<span class="hljs-string">"What is the capital of Bulgaria and what is the size? "</span> + <span class="hljs-string">"What is the national anthem?"</span>)
        .build()))
    .options(OllamaOptions.builder().temperature(<span class="hljs-number">0.9</span>).build().toMap())
    .build();

Flux&lt;ChatResponse&gt; streamingResponse = <span class="hljs-keyword">this</span>.ollamaApi.streamingChat(<span class="hljs-keyword">this</span>.request2);</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="nvidia-chat.html">NVIDIA</a></span>
<span class="next"><a href="perplexity-chat.html">Perplexity AI</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../index.html">Spring AI</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../index.html">
      1.0.0
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../index.html">
      1.1.0-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../../../../../images/spring-ai_reference___img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../../../../../images/spring-ai_reference___img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../../../../../images/spring-ai_reference___img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>
</body></html>