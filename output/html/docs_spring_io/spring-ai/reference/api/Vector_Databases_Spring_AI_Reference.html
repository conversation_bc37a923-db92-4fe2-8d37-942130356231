<!DOCTYPE html>

<html><head><title>Vector Databases :: Spring AI Reference</title><link href="https://docs.spring.io/spring-ai/reference/api/vectordbs.html"/><meta content="2025-06-04T18:16:40.162019" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../../../images/spring-ai_reference___img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>
<div class="body">
<div class="nav-container" data-component="ai" data-version="1.0.0">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring AI</span>
<span class="version">1.0.0</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Overview</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../concepts.html">AI Concepts</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link" href="../getting-started.html">Getting Started</a>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Reference</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="chatclient.html">Chat Client API</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="advisors.html">Advisors</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="prompt.html">Prompts</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="structured-output-converter.html">Structured Output</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="multimodality.html">Multimodality</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="chatmodel.html">Chat Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/comparison.html">Chat Models Comparison</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/bedrock-converse.html">Amazon Bedrock Converse</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/anthropic-chat.html">Anthropic 3</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/azure-openai-chat.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/deepseek-chat.html">DeepSeek</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/dmr-chat.html">Docker Model Runner</a>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="chat/google-vertexai.html">Google VertexAI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="chat/vertexai-gemini-chat.html">VertexAI Gemini</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/groq-chat.html">Groq</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/huggingface.html">Hugging Face</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/mistralai-chat.html">Mistral AI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/minimax-chat.html">MiniMax</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/moonshot-chat.html">Moonshot AI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/nvidia-chat.html">NVIDIA</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/ollama-chat.html">Ollama</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/perplexity-chat.html">Perplexity AI</a>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">OCI Generative AI</span>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="chat/oci-genai/cohere-chat.html">Cohere</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/openai-chat.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/qianfan-chat.html">QianFan</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/zhipuai-chat.html">ZhiPu AI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="embeddings.html">Embedding Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="bedrock.html">Amazon Bedrock</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="embeddings/bedrock-cohere-embedding.html">Cohere</a>
</li>
<li class="nav-item" data-depth="5">
<a class="nav-link" href="embeddings/bedrock-titan-embedding.html">Titan</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/azure-openai-embeddings.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/mistralai-embeddings.html">Mistral AI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/minimax-embeddings.html">MiniMax</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/oci-genai-embeddings.html">OCI GenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/ollama-embeddings.html">Ollama</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/onnx.html">(ONNX) Transformers</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/openai-embeddings.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/postgresml-embeddings.html">PostgresML</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/qianfan-embeddings.html">QianFan</a>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">VertexAI</span>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="embeddings/vertexai-embeddings-text.html">Text Embedding</a>
</li>
<li class="nav-item" data-depth="5">
<a class="nav-link" href="embeddings/vertexai-embeddings-multimodal.html">Multimodal Embedding</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/zhipuai-embeddings.html">ZhiPu AI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="imageclient.html">Image Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/azure-openai-image.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/openai-image.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/stabilityai-image.html">Stability</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/zhipuai-image.html">ZhiPuAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/qianfan-image.html">QianFan</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="#api/audio">Audio Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="audio/transcriptions.html">Transcription API</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="audio/transcriptions/azure-openai-transcriptions.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="5">
<a class="nav-link" href="audio/transcriptions/openai-transcriptions.html">OpenAI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="audio/speech.html">Text-To-Speech (TTS) API</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="audio/speech/openai-speech.html">OpenAI</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="#api/moderation">Moderation Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="moderation/openai-moderation.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="moderation/mistral-ai-moderation.html">Mistral AI</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="chat-memory.html">Chat Memory</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="tools.html">Tool Calling</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="mcp/mcp-overview.html">Model Context Protocol (MCP)</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="mcp/mcp-client-boot-starter-docs.html">MCP Client Boot Starters</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="mcp/mcp-server-boot-starter-docs.html">MCP Server Boot Starters</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="mcp/mcp-helpers.html">MCP Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="retrieval-augmented-generation.html">Retrieval Augmented Generation (RAG)</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="etl-pipeline.html">ETL Pipeline</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="testing.html">Model Evaluation</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="vectordbs.html">Vector Databases</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/azure.html">Azure AI Service</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/azure-cosmos-db.html">Azure Cosmos DB</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/apache-cassandra.html">Apache Cassandra Vector Store</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/chroma.html">Chroma</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/couchbase.html">Couchbase</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/elasticsearch.html">Elasticsearch</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/gemfire.html">GemFire</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/mariadb.html">MariaDB Vector Store</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/milvus.html">Milvus</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/mongodb.html">MongoDB Atlas</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/neo4j.html">Neo4j</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/opensearch.html">OpenSearch</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/oracle.html">Oracle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/pgvector.html">PGvector</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/pinecone.html">Pinecone</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/qdrant.html">Qdrant</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/redis.html">Redis</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/hana.html">SAP Hana</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/typesense.html">Typesense</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/weaviate.html">Weaviate</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../observability/index.html">Observability</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="docker-compose.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Testing</span>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="testcontainers.html">Testcontainers</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Guides</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="https://github.com/spring-ai-community/awesome-spring-ai">Awesome Spring AI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="chat/prompt-engineering-patterns.html">Prompt Engineering Patterns</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="effective-agents.html">Building Effective Agents</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="cloud-bindings.html">Deploying to the Cloud</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../upgrade-notes.html">Upgrade Notes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="tools-migration.html">Migrating FunctionCallback to ToolCallback API</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Vector Databases">
<div class="toc-menu"><h3>Vector Databases</h3><ul><li data-level="1"><a href="#api-overview">API Overview</a></li><li data-level="1"><a href="#_schema_initialization">Schema Initialization</a></li><li data-level="1"><a href="#_batching_strategy">Batching Strategy</a></li><li data-level="2"><a href="#_default_implementation">Default Implementation</a></li><li data-level="2"><a href="#_working_with_auto_truncation">Working with Auto-Truncation</a></li><li data-level="2"><a href="#_custom_implementation">Custom Implementation</a></li><li data-level="1"><a href="#_vectorstore_implementations">VectorStore Implementations</a></li><li data-level="1"><a href="#_example_usage">Example Usage</a></li><li data-level="1"><a href="#metadata-filters">Metadata Filters</a></li><li data-level="2"><a href="#_filter_string">Filter String</a></li><li data-level="2"><a href="#_filter_expression">Filter.Expression</a></li><li data-level="1"><a href="#_deleting_documents_from_vector_store">Deleting Documents from Vector Store</a></li><li data-level="2"><a href="#_delete_by_document_ids">Delete by Document IDs</a></li><li data-level="2"><a href="#_delete_by_filter_expression">Delete by Filter Expression</a></li><li data-level="2"><a href="#_delete_by_string_filter_expression">Delete by String Filter Expression</a></li><li data-level="2"><a href="#_error_handling_when_calling_the_delete_api">Error Handling When Calling the Delete API</a></li><li data-level="2"><a href="#_document_versioning_use_case">Document Versioning Use Case</a></li><li data-level="2"><a href="#_performance_considerations_while_deleting_documents">Performance Considerations While Deleting Documents</a></li><li data-level="1"><a href="#_understanding_vectors">Understanding Vectors</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/vectordbs.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-ai" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/questions/tagged/spring">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../index.html">Spring AI</a></li>
<li>Reference</li>
<li><a href="vectordbs.html">Vector Databases</a></li>
</ul>
</nav>
</div><h1 class="page" id="page-title">Vector Databases</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Vector Databases</h3><ul><li data-level="1"><a href="#api-overview">API Overview</a></li><li data-level="1"><a href="#_schema_initialization">Schema Initialization</a></li><li data-level="1"><a href="#_batching_strategy">Batching Strategy</a></li><li data-level="2"><a href="#_default_implementation">Default Implementation</a></li><li data-level="2"><a href="#_working_with_auto_truncation">Working with Auto-Truncation</a></li><li data-level="2"><a href="#_custom_implementation">Custom Implementation</a></li><li data-level="1"><a href="#_vectorstore_implementations">VectorStore Implementations</a></li><li data-level="1"><a href="#_example_usage">Example Usage</a></li><li data-level="1"><a href="#metadata-filters">Metadata Filters</a></li><li data-level="2"><a href="#_filter_string">Filter String</a></li><li data-level="2"><a href="#_filter_expression">Filter.Expression</a></li><li data-level="1"><a href="#_deleting_documents_from_vector_store">Deleting Documents from Vector Store</a></li><li data-level="2"><a href="#_delete_by_document_ids">Delete by Document IDs</a></li><li data-level="2"><a href="#_delete_by_filter_expression">Delete by Filter Expression</a></li><li data-level="2"><a href="#_delete_by_string_filter_expression">Delete by String Filter Expression</a></li><li data-level="2"><a href="#_error_handling_when_calling_the_delete_api">Error Handling When Calling the Delete API</a></li><li data-level="2"><a href="#_document_versioning_use_case">Document Versioning Use Case</a></li><li data-level="2"><a href="#_performance_considerations_while_deleting_documents">Performance Considerations While Deleting Documents</a></li><li data-level="1"><a href="#_understanding_vectors">Understanding Vectors</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>A vector database is a specialized type of database that plays an essential role in AI applications.</p>
</div>
<div class="paragraph">
<p>In vector databases, queries differ from traditional relational databases.
Instead of exact matches, they perform similarity searches.
When given a vector as a query, a vector database returns vectors that are “similar” to the query vector.
Further details on how this similarity is calculated at a high-level is provided in a <a class="xref page" href="vectordbs/understand-vectordbs.html#vectordbs-similarity">Vector Similarity</a>.</p>
</div>
<div class="paragraph">
<p>Vector databases are used to integrate your data with AI models.
The first step in their usage is to load your data into a vector database.
Then, when a user query is to be sent to the AI model, a set of similar documents is first retrieved.
These documents then serve as the context for the user’s question and are sent to the AI model, along with the user’s query.
This technique is known as <a class="xref page" href="../concepts.html#concept-rag">Retrieval Augmented Generation (RAG)</a>.</p>
</div>
<div class="paragraph">
<p>The following sections describe the Spring AI interface for using multiple vector database implementations and some high-level sample usage.</p>
</div>
<div class="paragraph">
<p>The last section is intended to demystify the underlying approach of similarity searching in vector databases.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="api-overview"><a class="anchor" href="#api-overview"></a>API Overview</h2>
<div class="sectionbody">
<div class="paragraph">
<p>This section serves as a guide to the <code>VectorStore</code> interface and its associated classes within the Spring AI framework.</p>
</div>
<div class="paragraph">
<p>Spring AI offers an abstracted API for interacting with vector databases through the <code>VectorStore</code> interface.</p>
</div>
<div class="paragraph">
<p>Here is the <code>VectorStore</code> interface definition:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">VectorStore</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">DocumentWriter</span> </span>{

    <span class="hljs-function"><span class="hljs-keyword">default</span> String <span class="hljs-title">getName</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.getClass().getSimpleName();
	}

    <span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">add</span><span class="hljs-params">(List&lt;Document&gt; documents)</span></span>;

    <span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">delete</span><span class="hljs-params">(List&lt;String&gt; idList)</span></span>;

    <span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">delete</span><span class="hljs-params">(Filter.Expression filterExpression)</span></span>;

    <span class="hljs-function"><span class="hljs-keyword">default</span> <span class="hljs-keyword">void</span> <span class="hljs-title">delete</span><span class="hljs-params">(String filterExpression)</span> </span>{ ... };

    <span class="hljs-function">List&lt;Document&gt; <span class="hljs-title">similaritySearch</span><span class="hljs-params">(String query)</span></span>;

    <span class="hljs-function">List&lt;Document&gt; <span class="hljs-title">similaritySearch</span><span class="hljs-params">(SearchRequest request)</span></span>;

    <span class="hljs-keyword">default</span> &lt;T&gt; <span class="hljs-function">Optional&lt;T&gt; <span class="hljs-title">getNativeClient</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> Optional.empty();
	}
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>and the related <code>SearchRequest</code> builder:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">SearchRequest</span> </span>{

	<span class="hljs-keyword">public</span> <span class="hljs-keyword">static</span> <span class="hljs-keyword">final</span> <span class="hljs-keyword">double</span> SIMILARITY_THRESHOLD_ACCEPT_ALL = <span class="hljs-number">0.0</span>;

	<span class="hljs-keyword">public</span> <span class="hljs-keyword">static</span> <span class="hljs-keyword">final</span> <span class="hljs-keyword">int</span> DEFAULT_TOP_K = <span class="hljs-number">4</span>;

	<span class="hljs-keyword">private</span> String query = <span class="hljs-string">""</span>;

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">int</span> topK = DEFAULT_TOP_K;

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">double</span> similarityThreshold = SIMILARITY_THRESHOLD_ACCEPT_ALL;

	<span class="hljs-meta">@Nullable</span>
	<span class="hljs-keyword">private</span> Filter.Expression filterExpression;

    <span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">static</span> Builder <span class="hljs-title">from</span><span class="hljs-params">(SearchRequest originalSearchRequest)</span> </span>{
		<span class="hljs-keyword">return</span> builder().query(originalSearchRequest.getQuery())
			.topK(originalSearchRequest.getTopK())
			.similarityThreshold(originalSearchRequest.getSimilarityThreshold())
			.filterExpression(originalSearchRequest.getFilterExpression());
	}

	<span class="hljs-keyword">public</span> <span class="hljs-keyword">static</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Builder</span> </span>{

		<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> SearchRequest searchRequest = <span class="hljs-keyword">new</span> SearchRequest();

		<span class="hljs-function"><span class="hljs-keyword">public</span> Builder <span class="hljs-title">query</span><span class="hljs-params">(String query)</span> </span>{
			Assert.notNull(query, <span class="hljs-string">"Query can not be null."</span>);
			<span class="hljs-keyword">this</span>.searchRequest.query = query;
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> Builder <span class="hljs-title">topK</span><span class="hljs-params">(<span class="hljs-keyword">int</span> topK)</span> </span>{
			Assert.isTrue(topK &gt;= <span class="hljs-number">0</span>, <span class="hljs-string">"TopK should be positive."</span>);
			<span class="hljs-keyword">this</span>.searchRequest.topK = topK;
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> Builder <span class="hljs-title">similarityThreshold</span><span class="hljs-params">(<span class="hljs-keyword">double</span> threshold)</span> </span>{
			Assert.isTrue(threshold &gt;= <span class="hljs-number">0</span> &amp;&amp; threshold &lt;= <span class="hljs-number">1</span>, <span class="hljs-string">"Similarity threshold must be in [0,1] range."</span>);
			<span class="hljs-keyword">this</span>.searchRequest.similarityThreshold = threshold;
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> Builder <span class="hljs-title">similarityThresholdAll</span><span class="hljs-params">()</span> </span>{
			<span class="hljs-keyword">this</span>.searchRequest.similarityThreshold = <span class="hljs-number">0.0</span>;
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> Builder <span class="hljs-title">filterExpression</span><span class="hljs-params">(@Nullable Filter.Expression expression)</span> </span>{
			<span class="hljs-keyword">this</span>.searchRequest.filterExpression = expression;
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> Builder <span class="hljs-title">filterExpression</span><span class="hljs-params">(@Nullable String textExpression)</span> </span>{
			<span class="hljs-keyword">this</span>.searchRequest.filterExpression = (textExpression != <span class="hljs-keyword">null</span>)
					? <span class="hljs-keyword">new</span> FilterExpressionTextParser().parse(textExpression) : <span class="hljs-keyword">null</span>;
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>;
		}

		<span class="hljs-function"><span class="hljs-keyword">public</span> SearchRequest <span class="hljs-title">build</span><span class="hljs-params">()</span> </span>{
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.searchRequest;
		}

	}

	<span class="hljs-function"><span class="hljs-keyword">public</span> String <span class="hljs-title">getQuery</span><span class="hljs-params">()</span> </span>{...}
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">int</span> <span class="hljs-title">getTopK</span><span class="hljs-params">()</span> </span>{...}
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">double</span> <span class="hljs-title">getSimilarityThreshold</span><span class="hljs-params">()</span> </span>{...}
	<span class="hljs-keyword">public</span> Filter.<span class="hljs-function">Expression <span class="hljs-title">getFilterExpression</span><span class="hljs-params">()</span> </span>{...}
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>To insert data into the vector database, encapsulate it within a <code>Document</code> object.
The <code>Document</code> class encapsulates content from a data source, such as a PDF or Word document, and includes text represented as a string.
It also contains metadata in the form of key-value pairs, including details such as the filename.</p>
</div>
<div class="paragraph">
<p>Upon insertion into the vector database, the text content is transformed into a numerical array, or a <code>float[]</code>, known as vector embeddings, using an embedding model. Embedding models, such as <a class="external" href="https://en.wikipedia.org/wiki/Word2vec" target="_blank">Word2Vec</a>, <a class="external" href="https://en.wikipedia.org/wiki/GloVe_(machine_learning)" target="_blank">GLoVE</a>, and <a class="external" href="https://en.wikipedia.org/wiki/BERT_(language_model)" target="_blank">BERT</a>, or OpenAI’s <code>text-embedding-ada-002</code>, are used to convert words, sentences, or paragraphs into these vector embeddings.</p>
</div>
<div class="paragraph">
<p>The vector database’s role is to store and facilitate similarity searches for these embeddings. It does not generate the embeddings itself. For creating vector embeddings, the <code>EmbeddingModel</code> should be utilized.</p>
</div>
<div class="paragraph">
<p>The <code>similaritySearch</code> methods in the interface allow for retrieving documents similar to a given query string. These methods can be fine-tuned by using the following parameters:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>k</code>: An integer that specifies the maximum number of similar documents to return. This is often referred to as a 'top K' search, or 'K nearest neighbors' (KNN).</p>
</li>
<li>
<p><code>threshold</code>: A double value ranging from 0 to 1, where values closer to 1 indicate higher similarity. By default, if you set a threshold of 0.75, for instance, only documents with a similarity above this value are returned.</p>
</li>
<li>
<p><code>Filter.Expression</code>: A class used for passing a fluent DSL (Domain-Specific Language) expression that functions similarly to a 'where' clause in SQL, but it applies exclusively to the metadata key-value pairs of a <code>Document</code>.</p>
</li>
<li>
<p><code>filterExpression</code>: An external DSL based on ANTLR4 that accepts filter expressions as strings. For example, with metadata keys like country, year, and <code>isActive</code>, you could use an expression such as: <code>country == 'UK' &amp;&amp; year &gt;= 2020 &amp;&amp; isActive == true.</code></p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Find more information on the <code>Filter.Expression</code> in the <a href="#metadata-filters">Metadata Filters</a> section.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_schema_initialization"><a class="anchor" href="#_schema_initialization"></a>Schema Initialization</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Some vector stores require their backend schema to be initialized before usage.
It will not be initialized for you by default.
You must opt-in, by passing a <code>boolean</code> for the appropriate constructor argument or, if using Spring Boot, setting the appropriate <code>initialize-schema</code> property to <code>true</code> in <code>application.properties</code> or <code>application.yml</code>.
Check the documentation for the vector store you are using for the specific property name.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_batching_strategy"><a class="anchor" href="#_batching_strategy"></a>Batching Strategy</h2>
<div class="sectionbody">
<div class="paragraph">
<p>When working with vector stores, it’s often necessary to embed large numbers of documents.
While it might seem straightforward to make a single call to embed all documents at once, this approach can lead to issues.
Embedding models process text as tokens and have a maximum token limit, often referred to as the context window size.
This limit restricts the amount of text that can be processed in a single embedding request.
Attempting to embed too many tokens in one call can result in errors or truncated embeddings.</p>
</div>
<div class="paragraph">
<p>To address this token limit, Spring AI implements a batching strategy.
This approach breaks down large sets of documents into smaller batches that fit within the embedding model’s maximum context window.
Batching not only solves the token limit issue but can also lead to improved performance and more efficient use of API rate limits.</p>
</div>
<div class="paragraph">
<p>Spring AI provides this functionality through the <code>BatchingStrategy</code> interface, which allows for processing documents in sub-batches based on their token counts.</p>
</div>
<div class="paragraph">
<p>The core <code>BatchingStrategy</code> interface is defined as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">BatchingStrategy</span> </span>{
    List&lt;List&lt;Document&gt;&gt; batch(List&lt;Document&gt; documents);
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This interface defines a single method, <code>batch</code>, which takes a list of documents and returns a list of document batches.</p>
</div>
<div class="sect2">
<h3 id="_default_implementation"><a class="anchor" href="#_default_implementation"></a>Default Implementation</h3>
<div class="paragraph">
<p>Spring AI provides a default implementation called <code>TokenCountBatchingStrategy</code>.
This strategy batches documents based on their token counts, ensuring that each batch does not exceed a calculated maximum input token count.</p>
</div>
<div class="paragraph">
<p>Key features of <code>TokenCountBatchingStrategy</code>:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Uses <a class="external" href="https://platform.openai.com/docs/guides/embeddings/embedding-models" target="_blank">OpenAI’s max input token count</a> (8191) as the default upper limit.</p>
</li>
<li>
<p>Incorporates a reserve percentage (default 10%) to provide a buffer for potential overhead.</p>
</li>
<li>
<p>Calculates the actual max input token count as: <code>actualMaxInputTokenCount = originalMaxInputTokenCount * (1 - RESERVE_PERCENTAGE)</code></p>
</li>
</ol>
</div>
<div class="paragraph">
<p>The strategy estimates the token count for each document, groups them into batches without exceeding the max input token count, and throws an exception if a single document exceeds this limit.</p>
</div>
<div class="paragraph">
<p>You can also customize the <code>TokenCountBatchingStrategy</code> to better suit your specific requirements. This can be done by creating a new instance with custom parameters in a Spring Boot <code>@Configuration</code> class.</p>
</div>
<div class="paragraph">
<p>Here’s an example of how to create a custom <code>TokenCountBatchingStrategy</code> bean:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@Configuration</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">EmbeddingConfig</span> </span>{
    <span class="hljs-meta">@Bean</span>
    <span class="hljs-function"><span class="hljs-keyword">public</span> BatchingStrategy <span class="hljs-title">customTokenCountBatchingStrategy</span><span class="hljs-params">()</span> </span>{
        <span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> TokenCountBatchingStrategy(
            EncodingType.CL100K_BASE,  <span class="hljs-comment">// Specify the encoding type</span>
            <span class="hljs-number">8000</span>,                      <span class="hljs-comment">// Set the maximum input token count</span>
            <span class="hljs-number">0.1</span>                        <span class="hljs-comment">// Set the reserve percentage</span>
        );
    }
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>In this configuration:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p><code>EncodingType.CL100K_BASE</code>: Specifies the encoding type used for tokenization. This encoding type is used by the <code>JTokkitTokenCountEstimator</code> to accurately estimate token counts.</p>
</li>
<li>
<p><code>8000</code>: Sets the maximum input token count. This value should be less than or equal to the maximum context window size of your embedding model.</p>
</li>
<li>
<p><code>0.1</code>: Sets the reserve percentage. The percentage of tokens to reserve from the max input token count. This creates a buffer for potential token count increases during processing.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>By default, this constructor uses <code>Document.DEFAULT_CONTENT_FORMATTER</code> for content formatting and <code>MetadataMode.NONE</code> for metadata handling. If you need to customize these parameters, you can use the full constructor with additional parameters.</p>
</div>
<div class="paragraph">
<p>Once defined, this custom <code>TokenCountBatchingStrategy</code> bean will be automatically used by the <code>EmbeddingModel</code> implementations in your application, replacing the default strategy.</p>
</div>
<div class="paragraph">
<p>The <code>TokenCountBatchingStrategy</code> internally uses a <code>TokenCountEstimator</code> (specifically, <code>JTokkitTokenCountEstimator</code>) to calculate token counts for efficient batching. This ensures accurate token estimation based on the specified encoding type.</p>
</div>
<div class="paragraph">
<p>Additionally, <code>TokenCountBatchingStrategy</code> provides flexibility by allowing you to pass in your own implementation of the <code>TokenCountEstimator</code> interface. This feature enables you to use custom token counting strategies tailored to your specific needs. For example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">TokenCountEstimator customEstimator = <span class="hljs-keyword">new</span> YourCustomTokenCountEstimator();
TokenCountBatchingStrategy strategy = <span class="hljs-keyword">new</span> TokenCountBatchingStrategy(
		<span class="hljs-keyword">this</span>.customEstimator,
    <span class="hljs-number">8000</span>,  <span class="hljs-comment">// maxInputTokenCount</span>
    <span class="hljs-number">0.1</span>,   <span class="hljs-comment">// reservePercentage</span>
    Document.DEFAULT_CONTENT_FORMATTER,
    MetadataMode.NONE
);</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_working_with_auto_truncation"><a class="anchor" href="#_working_with_auto_truncation"></a>Working with Auto-Truncation</h3>
<div class="paragraph">
<p>Some embedding models, such as Vertex AI text embedding, support an <code>auto_truncate</code> feature. When enabled, the model silently truncates text inputs that exceed the maximum size and continues processing; when disabled, it throws an explicit error for inputs that are too large.</p>
</div>
<div class="paragraph">
<p>When using auto-truncation with the batching strategy, you must configure your batching strategy with a much higher input token count than the model’s actual maximum. This prevents the batching strategy from raising exceptions for large documents, allowing the embedding model to handle truncation internally.</p>
</div>
<div class="sect3">
<h4 id="_configuration_for_auto_truncation"><a class="anchor" href="#_configuration_for_auto_truncation"></a>Configuration for Auto-Truncation</h4>
<div class="paragraph">
<p>When enabling auto-truncation, set your batching strategy’s maximum input token count much higher than the model’s actual limit. This prevents the batching strategy from raising exceptions for large documents, allowing the embedding model to handle truncation internally.</p>
</div>
<div class="paragraph">
<p>Here’s an example configuration for using Vertex AI with auto-truncation and custom <code>BatchingStrategy</code> and then using them in the PgVectorStore:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@Configuration</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">AutoTruncationEmbeddingConfig</span> </span>{

    <span class="hljs-meta">@Bean</span>
    <span class="hljs-function"><span class="hljs-keyword">public</span> VertexAiTextEmbeddingModel <span class="hljs-title">vertexAiEmbeddingModel</span><span class="hljs-params">(
            VertexAiEmbeddingConnectionDetails connectionDetails)</span> </span>{

        VertexAiTextEmbeddingOptions options = VertexAiTextEmbeddingOptions.builder()
                .model(VertexAiTextEmbeddingOptions.DEFAULT_MODEL_NAME)
                .autoTruncate(<span class="hljs-keyword">true</span>)  <span class="hljs-comment">// Enable auto-truncation</span>
                .build();

        <span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> VertexAiTextEmbeddingModel(connectionDetails, options);
    }

    <span class="hljs-meta">@Bean</span>
    <span class="hljs-function"><span class="hljs-keyword">public</span> BatchingStrategy <span class="hljs-title">batchingStrategy</span><span class="hljs-params">()</span> </span>{
        <span class="hljs-comment">// Only use a high token limit if auto-truncation is enabled in your embedding model.</span>
        <span class="hljs-comment">// Set a much higher token count than the model actually supports</span>
        <span class="hljs-comment">// (e.g., 132,900 when Vertex AI supports only up to 20,000)</span>
        <span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> TokenCountBatchingStrategy(
                EncodingType.CL100K_BASE,
                <span class="hljs-number">132900</span>,  <span class="hljs-comment">// Artificially high limit</span>
                <span class="hljs-number">0.1</span>      <span class="hljs-comment">// 10% reserve</span>
        );
    }

    <span class="hljs-meta">@Bean</span>
    <span class="hljs-function"><span class="hljs-keyword">public</span> VectorStore <span class="hljs-title">vectorStore</span><span class="hljs-params">(JdbcTemplate jdbcTemplate, EmbeddingModel embeddingModel, BatchingStrategy batchingStrategy)</span> </span>{
        <span class="hljs-keyword">return</span> PgVectorStore.builder(jdbcTemplate, embeddingModel)
            <span class="hljs-comment">// other properties omitted here</span>
            .build();
    }
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>In this configuration:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>The embedding model has auto-truncation enabled, allowing it to handle oversized inputs gracefully.</p>
</li>
<li>
<p>The batching strategy uses an artificially high token limit (132,900) that’s much larger than the actual model limit (20,000).</p>
</li>
<li>
<p>The vector store uses the configured embedding model and the custom <code>BatchingStrategy</code> bean.</p>
</li>
</ol>
</div>
</div>
<div class="sect3">
<h4 id="_why_this_works"><a class="anchor" href="#_why_this_works"></a>Why This Works</h4>
<div class="paragraph">
<p>This approach works because:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>The <code>TokenCountBatchingStrategy</code> checks if any single document exceeds the configured maximum and throws an <code>IllegalArgumentException</code> if it does.</p>
</li>
<li>
<p>By setting a very high limit in the batching strategy, we ensure that this check never fails.</p>
</li>
<li>
<p>Documents or batches exceeding the model’s limit are silently truncated and processed by the embedding model’s auto-truncation feature.</p>
</li>
</ol>
</div>
</div>
<div class="sect3">
<h4 id="_best_practices"><a class="anchor" href="#_best_practices"></a>Best Practices</h4>
<div class="paragraph">
<p>When using auto-truncation:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Set the batching strategy’s max input token count to be at least 5-10x larger than the model’s actual limit to avoid premature exceptions from the batching strategy.</p>
</li>
<li>
<p>Monitor your logs for truncation warnings from the embedding model (note: not all models log truncation events).</p>
</li>
<li>
<p>Consider the implications of silent truncation on your embedding quality.</p>
</li>
<li>
<p>Test with sample documents to ensure truncated embeddings still meet your requirements.</p>
</li>
<li>
<p>Document this configuration for future maintainers, as it is non-standard.</p>
</li>
</ul>
</div>
<div class="admonitionblock caution">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-caution" title="Caution"></i>
</td>
<td class="content">
While auto-truncation prevents errors, it can result in incomplete embeddings. Important information at the end of long documents may be lost. If your application requires all content to be embedded, split documents into smaller chunks before embedding.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect3">
<h4 id="_spring_boot_auto_configuration"><a class="anchor" href="#_spring_boot_auto_configuration"></a>Spring Boot Auto-Configuration</h4>
<div class="paragraph">
<p>If you’re using Spring Boot auto-configuration, you must provide a custom <code>BatchingStrategy</code> bean to override the default one that comes with Spring AI:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@Bean</span>
<span class="hljs-function"><span class="hljs-keyword">public</span> BatchingStrategy <span class="hljs-title">customBatchingStrategy</span><span class="hljs-params">()</span> </span>{
    <span class="hljs-comment">// This bean will override the default BatchingStrategy</span>
    <span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> TokenCountBatchingStrategy(
            EncodingType.CL100K_BASE,
            <span class="hljs-number">132900</span>,  <span class="hljs-comment">// Much higher than model's actual limit</span>
            <span class="hljs-number">0.1</span>
    );
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The presence of this bean in your application context will automatically replace the default batching strategy used by all vector stores.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_custom_implementation"><a class="anchor" href="#_custom_implementation"></a>Custom Implementation</h3>
<div class="paragraph">
<p>While <code>TokenCountBatchingStrategy</code> provides a robust default implementation, you can customize the batching strategy to fit your specific needs.
This can be done through Spring Boot’s auto-configuration.</p>
</div>
<div class="paragraph">
<p>To customize the batching strategy, define a <code>BatchingStrategy</code> bean in your Spring Boot application:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@Configuration</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">EmbeddingConfig</span> </span>{
    <span class="hljs-meta">@Bean</span>
    <span class="hljs-function"><span class="hljs-keyword">public</span> BatchingStrategy <span class="hljs-title">customBatchingStrategy</span><span class="hljs-params">()</span> </span>{
        <span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> CustomBatchingStrategy();
    }
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This custom <code>BatchingStrategy</code> will then be automatically used by the <code>EmbeddingModel</code> implementations in your application.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Vector stores supported by Spring AI are configured to use the default <code>TokenCountBatchingStrategy</code>.
SAP Hana vector store is not currently configured for batching.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_vectorstore_implementations"><a class="anchor" href="#_vectorstore_implementations"></a>VectorStore Implementations</h2>
<div class="sectionbody">
<div class="paragraph">
<p>These are the available implementations of the <code>VectorStore</code> interface:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a class="xref page" href="vectordbs/azure.html">Azure Vector Search</a> - The <a class="external" href="https://learn.microsoft.com/en-us/azure/search/vector-search-overview" target="_blank">Azure</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/apache-cassandra.html">Apache Cassandra</a> - The <a class="external" href="https://cassandra.apache.org/doc/latest/cassandra/vector-search/overview.html" target="_blank">Apache Cassandra</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/chroma.html">Chroma Vector Store</a> - The <a class="external" href="https://www.trychroma.com/" target="_blank">Chroma</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/elasticsearch.html">Elasticsearch Vector Store</a> - The <a class="external" href="https://www.elastic.co/" target="_blank">Elasticsearch</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/gemfire.html">GemFire Vector Store</a> - The <a class="external" href="https://tanzu.vmware.com/content/blog/vmware-gemfire-vector-database-extension" target="_blank">GemFire</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/mariadb.html">MariaDB Vector Store</a> - The <a class="external" href="https://mariadb.com/" target="_blank">MariaDB</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/milvus.html">Milvus Vector Store</a> - The <a class="external" href="https://milvus.io/" target="_blank">Milvus</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/mongodb.html">MongoDB Atlas Vector Store</a> - The <a class="external" href="https://www.mongodb.com/atlas/database" target="_blank">MongoDB Atlas</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/neo4j.html">Neo4j Vector Store</a> - The <a class="external" href="https://neo4j.com/" target="_blank">Neo4j</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/opensearch.html">OpenSearch Vector Store</a> - The <a class="external" href="https://opensearch.org/platform/search/vector-database.html" target="_blank">OpenSearch</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/oracle.html">Oracle Vector Store</a> - The <a class="external" href="https://docs.oracle.com/en/database/oracle/oracle-database/23/vecse/overview-ai-vector-search.html" target="_blank">Oracle Database</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/pgvector.html">PgVector Store</a> - The <a class="external" href="https://github.com/pgvector/pgvector" target="_blank">PostgreSQL/PGVector</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/pinecone.html">Pinecone Vector Store</a> - <a class="external" href="https://www.pinecone.io/" target="_blank">PineCone</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/qdrant.html">Qdrant Vector Store</a> - <a class="external" href="https://www.qdrant.tech/" target="_blank">Qdrant</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/redis.html">Redis Vector Store</a> - The <a class="external" href="https://redis.io/" target="_blank">Redis</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/hana.html">SAP Hana Vector Store</a> - The <a class="external" href="https://news.sap.com/2024/04/sap-hana-cloud-vector-engine-ai-with-business-context/" target="_blank">SAP HANA</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/typesense.html">Typesense Vector Store</a> - The <a class="external" href="https://typesense.org/docs/0.24.0/api/vector-search.html" target="_blank">Typesense</a> vector store.</p>
</li>
<li>
<p><a class="xref page" href="vectordbs/weaviate.html">Weaviate Vector Store</a> - The <a class="external" href="https://weaviate.io/" target="_blank">Weaviate</a> vector store.</p>
</li>
<li>
<p><a class="external" href="https://github.com/spring-projects/spring-ai/blob/main/spring-ai-vector-store/src/main/java/org/springframework/ai/vectorstore/SimpleVectorStore.java" target="_blank">SimpleVectorStore</a> - A simple implementation of persistent vector storage, good for educational purposes.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>More implementations may be supported in future releases.</p>
</div>
<div class="paragraph">
<p>If you have a vector database that needs to be supported by Spring AI, open an issue on GitHub or, even better, submit a pull request with an implementation.</p>
</div>
<div class="paragraph">
<p>Information on each of the <code>VectorStore</code> implementations can be found in the subsections of this chapter.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_example_usage"><a class="anchor" href="#_example_usage"></a>Example Usage</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To compute the embeddings for a vector database, you need to pick an embedding model that matches the higher-level AI model being used.</p>
</div>
<div class="paragraph">
<p>For example, with OpenAI’s ChatGPT, we use the <code>OpenAiEmbeddingModel</code> and a model named <code>text-embedding-ada-002</code>.</p>
</div>
<div class="paragraph">
<p>The Spring Boot starter’s auto-configuration for OpenAI makes an implementation of <code>EmbeddingModel</code> available in the Spring application context for dependency injection.</p>
</div>
<div class="paragraph">
<p>The general usage of loading data into a vector store is something you would do in a batch-like job, by first loading data into Spring AI’s <code>Document</code> class and then calling the <code>save</code> method.</p>
</div>
<div class="paragraph">
<p>Given a <code>String</code> reference to a source file that represents a JSON file with data we want to load into the vector database, we use Spring AI’s <code>JsonReader</code> to load specific fields in the JSON, which splits them up into small pieces and then passes those small pieces to the vector store implementation.
The <code>VectorStore</code> implementation computes the embeddings and stores the JSON and the embedding in the vector database:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">  <span class="hljs-meta">@Autowired</span>
  VectorStore vectorStore;

  <span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">load</span><span class="hljs-params">(String sourceFile)</span> </span>{
            JsonReader jsonReader = <span class="hljs-keyword">new</span> JsonReader(<span class="hljs-keyword">new</span> FileSystemResource(sourceFile),
                    <span class="hljs-string">"price"</span>, <span class="hljs-string">"name"</span>, <span class="hljs-string">"shortDescription"</span>, <span class="hljs-string">"description"</span>, <span class="hljs-string">"tags"</span>);
            List&lt;Document&gt; documents = jsonReader.get();
            <span class="hljs-keyword">this</span>.vectorStore.add(documents);
  }</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Later, when a user question is passed into the AI model, a similarity search is done to retrieve similar documents, which are then "'stuffed'" into the prompt as context for the user’s question.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">   String question = &lt;question from user&gt;
   List&lt;Document&gt; similarDocuments = store.similaritySearch(<span class="hljs-keyword">this</span>.question);</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Additional options can be passed into the <code>similaritySearch</code> method to define how many documents to retrieve and a threshold of the similarity search.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="metadata-filters"><a class="anchor" href="#metadata-filters"></a>Metadata Filters</h2>
<div class="sectionbody">
<div class="paragraph">
<p>This section describes various filters that you can use against the results of a query.</p>
</div>
<div class="sect2">
<h3 id="_filter_string"><a class="anchor" href="#_filter_string"></a>Filter String</h3>
<div class="paragraph">
<p>You can pass in an SQL-like filter expressions as a <code>String</code> to one of the <code>similaritySearch</code> overloads.</p>
</div>
<div class="paragraph">
<p>Consider the following examples:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>"country == 'BG'"</code></p>
</li>
<li>
<p><code>"genre == 'drama' &amp;&amp; year &gt;= 2020"</code></p>
</li>
<li>
<p><code>"genre in ['comedy', 'documentary', 'drama']"</code></p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_filter_expression"><a class="anchor" href="#_filter_expression"></a>Filter.Expression</h3>
<div class="paragraph">
<p>You can create an instance of <code>Filter.Expression</code> with a <code>FilterExpressionBuilder</code> that exposes a fluent API.
A simple example is as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">FilterExpressionBuilder b = <span class="hljs-keyword">new</span> FilterExpressionBuilder();
Expression expression = <span class="hljs-keyword">this</span>.b.eq(<span class="hljs-string">"country"</span>, <span class="hljs-string">"BG"</span>).build();</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>You can build up sophisticated expressions by using the following operators:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-text hljs" data-lang="text">EQUALS: '=='
MINUS : '-'
PLUS: '+'
GT: '&gt;'
GE: '&gt;='
LT: '&lt;'
LE: '&lt;='
NE: '!='</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>You can combine expressions by using the following operators:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-text hljs" data-lang="text">AND: 'AND' | 'and' | '&amp;&amp;';
OR: 'OR' | 'or' | '||';</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Considering the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">Expression exp = b.and(b.eq(<span class="hljs-string">"genre"</span>, <span class="hljs-string">"drama"</span>), b.gte(<span class="hljs-string">"year"</span>, <span class="hljs-number">2020</span>)).build();</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>You can also use the following operators:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-text hljs" data-lang="text">IN: 'IN' | 'in';
NIN: 'NIN' | 'nin';
NOT: 'NOT' | 'not';</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Consider the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">Expression exp = b.and(b.in(<span class="hljs-string">"genre"</span>, <span class="hljs-string">"drama"</span>, <span class="hljs-string">"documentary"</span>), b.not(b.lt(<span class="hljs-string">"year"</span>, <span class="hljs-number">2020</span>))).build();</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_deleting_documents_from_vector_store"><a class="anchor" href="#_deleting_documents_from_vector_store"></a>Deleting Documents from Vector Store</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The Vector Store interface provides multiple methods for deleting documents, allowing you to remove data either by specific document IDs or using filter expressions.</p>
</div>
<div class="sect2">
<h3 id="_delete_by_document_ids"><a class="anchor" href="#_delete_by_document_ids"></a>Delete by Document IDs</h3>
<div class="paragraph">
<p>The simplest way to delete documents is by providing a list of document IDs:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">delete</span><span class="hljs-params">(List&lt;String&gt; idList)</span></span>;</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This method removes all documents whose IDs match those in the provided list.
If any ID in the list doesn’t exist in the store, it will be ignored.</p>
</div>
<div class="listingblock">
<div class="title">Example usage</div>
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-comment">// Create and add document</span>
Document document = <span class="hljs-keyword">new</span> Document(<span class="hljs-string">"The World is Big"</span>,
    Map.of(<span class="hljs-string">"country"</span>, <span class="hljs-string">"Netherlands"</span>));
vectorStore.add(List.of(document));

<span class="hljs-comment">// Delete document by ID</span>
vectorStore.delete(List.of(document.getId()));</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_delete_by_filter_expression"><a class="anchor" href="#_delete_by_filter_expression"></a>Delete by Filter Expression</h3>
<div class="paragraph">
<p>For more complex deletion criteria, you can use filter expressions:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">delete</span><span class="hljs-params">(Filter.Expression filterExpression)</span></span>;</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This method accepts a <code>Filter.Expression</code> object that defines the criteria for which documents should be deleted.
It’s particularly useful when you need to delete documents based on their metadata properties.</p>
</div>
<div class="listingblock">
<div class="title">Example usage</div>
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-comment">// Create test documents with different metadata</span>
Document bgDocument = <span class="hljs-keyword">new</span> Document(<span class="hljs-string">"The World is Big"</span>,
    Map.of(<span class="hljs-string">"country"</span>, <span class="hljs-string">"Bulgaria"</span>));
Document nlDocument = <span class="hljs-keyword">new</span> Document(<span class="hljs-string">"The World is Big"</span>,
    Map.of(<span class="hljs-string">"country"</span>, <span class="hljs-string">"Netherlands"</span>));

<span class="hljs-comment">// Add documents to the store</span>
vectorStore.add(List.of(bgDocument, nlDocument));

<span class="hljs-comment">// Delete documents from Bulgaria using filter expression</span>
Filter.Expression filterExpression = <span class="hljs-keyword">new</span> Filter.Expression(
    Filter.ExpressionType.EQ,
    <span class="hljs-keyword">new</span> Filter.Key(<span class="hljs-string">"country"</span>),
    <span class="hljs-keyword">new</span> Filter.Value(<span class="hljs-string">"Bulgaria"</span>)
);
vectorStore.delete(filterExpression);

<span class="hljs-comment">// Verify deletion with search</span>
SearchRequest request = SearchRequest.builder()
    .query(<span class="hljs-string">"World"</span>)
    .filterExpression(<span class="hljs-string">"country == 'Bulgaria'"</span>)
    .build();
List&lt;Document&gt; results = vectorStore.similaritySearch(request);
<span class="hljs-comment">// results will be empty as Bulgarian document was deleted</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_delete_by_string_filter_expression"><a class="anchor" href="#_delete_by_string_filter_expression"></a>Delete by String Filter Expression</h3>
<div class="paragraph">
<p>For convenience, you can also delete documents using a string-based filter expression:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">delete</span><span class="hljs-params">(String filterExpression)</span></span>;</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This method converts the provided string filter into a <code>Filter.Expression</code> object internally.
It’s useful when you have filter criteria in string format.</p>
</div>
<div class="listingblock">
<div class="title">Example usage</div>
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-comment">// Create and add documents</span>
Document bgDocument = <span class="hljs-keyword">new</span> Document(<span class="hljs-string">"The World is Big"</span>,
    Map.of(<span class="hljs-string">"country"</span>, <span class="hljs-string">"Bulgaria"</span>));
Document nlDocument = <span class="hljs-keyword">new</span> Document(<span class="hljs-string">"The World is Big"</span>,
    Map.of(<span class="hljs-string">"country"</span>, <span class="hljs-string">"Netherlands"</span>));
vectorStore.add(List.of(bgDocument, nlDocument));

<span class="hljs-comment">// Delete Bulgarian documents using string filter</span>
vectorStore.delete(<span class="hljs-string">"country == 'Bulgaria'"</span>);

<span class="hljs-comment">// Verify remaining documents</span>
SearchRequest request = SearchRequest.builder()
    .query(<span class="hljs-string">"World"</span>)
    .topK(<span class="hljs-number">5</span>)
    .build();
List&lt;Document&gt; results = vectorStore.similaritySearch(request);
<span class="hljs-comment">// results will only contain the Netherlands document</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_error_handling_when_calling_the_delete_api"><a class="anchor" href="#_error_handling_when_calling_the_delete_api"></a>Error Handling When Calling the Delete API</h3>
<div class="paragraph">
<p>All deletion methods may throw exceptions in case of errors:</p>
</div>
<div class="paragraph">
<p>The best practice is to wrap delete operations in try-catch blocks:</p>
</div>
<div class="listingblock">
<div class="title">Example usage</div>
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">try</span> {
    vectorStore.delete(<span class="hljs-string">"country == 'Bulgaria'"</span>);
}
<span class="hljs-keyword">catch</span> (Exception  e) {
    logger.error(<span class="hljs-string">"Invalid filter expression"</span>, e);
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_document_versioning_use_case"><a class="anchor" href="#_document_versioning_use_case"></a>Document Versioning Use Case</h3>
<div class="paragraph">
<p>A common scenario is managing document versions where you need to upload a new version of a document while removing the old version. Here’s how to handle this using filter expressions:</p>
</div>
<div class="listingblock">
<div class="title">Example usage</div>
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-comment">// Create initial document (v1) with version metadata</span>
Document documentV1 = <span class="hljs-keyword">new</span> Document(
    <span class="hljs-string">"AI and Machine Learning Best Practices"</span>,
    Map.of(
        <span class="hljs-string">"docId"</span>, <span class="hljs-string">"AIML-001"</span>,
        <span class="hljs-string">"version"</span>, <span class="hljs-string">"1.0"</span>,
        <span class="hljs-string">"lastUpdated"</span>, <span class="hljs-string">"2024-01-01"</span>
    )
);

<span class="hljs-comment">// Add v1 to the vector store</span>
vectorStore.add(List.of(documentV1));

<span class="hljs-comment">// Create updated version (v2) of the same document</span>
Document documentV2 = <span class="hljs-keyword">new</span> Document(
    <span class="hljs-string">"AI and Machine Learning Best Practices - Updated"</span>,
    Map.of(
        <span class="hljs-string">"docId"</span>, <span class="hljs-string">"AIML-001"</span>,
        <span class="hljs-string">"version"</span>, <span class="hljs-string">"2.0"</span>,
        <span class="hljs-string">"lastUpdated"</span>, <span class="hljs-string">"2024-02-01"</span>
    )
);

<span class="hljs-comment">// First, delete the old version using filter expression</span>
Filter.Expression deleteOldVersion = <span class="hljs-keyword">new</span> Filter.Expression(
    Filter.ExpressionType.AND,
    Arrays.asList(
        <span class="hljs-keyword">new</span> Filter.Expression(
            Filter.ExpressionType.EQ,
            <span class="hljs-keyword">new</span> Filter.Key(<span class="hljs-string">"docId"</span>),
            <span class="hljs-keyword">new</span> Filter.Value(<span class="hljs-string">"AIML-001"</span>)
        ),
        <span class="hljs-keyword">new</span> Filter.Expression(
            Filter.ExpressionType.EQ,
            <span class="hljs-keyword">new</span> Filter.Key(<span class="hljs-string">"version"</span>),
            <span class="hljs-keyword">new</span> Filter.Value(<span class="hljs-string">"1.0"</span>)
        )
    )
);
vectorStore.delete(deleteOldVersion);

<span class="hljs-comment">// Add the new version</span>
vectorStore.add(List.of(documentV2));

<span class="hljs-comment">// Verify only v2 exists</span>
SearchRequest request = SearchRequest.builder()
    .query(<span class="hljs-string">"AI and Machine Learning"</span>)
    .filterExpression(<span class="hljs-string">"docId == 'AIML-001'"</span>)
    .build();
List&lt;Document&gt; results = vectorStore.similaritySearch(request);
<span class="hljs-comment">// results will contain only v2 of the document</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>You can also accomplish the same using the string filter expression:</p>
</div>
<div class="listingblock">
<div class="title">Example usage</div>
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-comment">// Delete old version using string filter</span>
vectorStore.delete(<span class="hljs-string">"docId == 'AIML-001' AND version == '1.0'"</span>);

<span class="hljs-comment">// Add new version</span>
vectorStore.add(List.of(documentV2));</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_performance_considerations_while_deleting_documents"><a class="anchor" href="#_performance_considerations_while_deleting_documents"></a>Performance Considerations While Deleting Documents</h3>
<div class="ulist">
<ul>
<li>
<p>Deleting by ID list is generally faster when you know exactly which documents to remove.</p>
</li>
<li>
<p>Filter-based deletion may require scanning the index to find matching documents; however, this is vector store implementation-specific.</p>
</li>
<li>
<p>Large deletion operations should be batched to avoid overwhelming the system.</p>
</li>
<li>
<p>Consider using filter expressions when deleting based on document properties rather than collecting IDs first.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_understanding_vectors"><a class="anchor" href="#_understanding_vectors"></a>Understanding Vectors</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a class="xref page" href="vectordbs/understand-vectordbs.html">Understanding Vectors</a></p>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="testing.html">Model Evaluation</a></span>
<span class="next"><a href="vectordbs/azure.html">Azure AI Service</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../index.html">Spring AI</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../index.html">
      1.0.0
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../1.1-SNAPSHOT/index.html">
      1.1.0-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../../../images/spring-ai_reference___img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../../../images/spring-ai_reference___img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../../../images/spring-ai_reference___img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>
</body></html>