<!DOCTYPE html>

<html><head><title>Prompts :: Spring AI Reference</title><link href="https://docs.spring.io/spring-ai/reference/api/prompt.html"/><meta content="2025-06-04T18:18:52.075827" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../../../images/spring-ai_reference___img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>
<div class="body">
<div class="nav-container" data-component="ai" data-version="1.0.0">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring AI</span>
<span class="version">1.0.0</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Overview</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../concepts.html">AI Concepts</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link" href="../getting-started.html">Getting Started</a>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Reference</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="chatclient.html">Chat Client API</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="advisors.html">Advisors</a>
</li>
</ul>
</li>
<li class="nav-item is-current-page is-active" data-depth="2">
<a class="nav-link" href="prompt.html">Prompts</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="structured-output-converter.html">Structured Output</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="multimodality.html">Multimodality</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="chatmodel.html">Chat Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/comparison.html">Chat Models Comparison</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/bedrock-converse.html">Amazon Bedrock Converse</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/anthropic-chat.html">Anthropic 3</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/azure-openai-chat.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/deepseek-chat.html">DeepSeek</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/dmr-chat.html">Docker Model Runner</a>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="chat/google-vertexai.html">Google VertexAI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="chat/vertexai-gemini-chat.html">VertexAI Gemini</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/groq-chat.html">Groq</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/huggingface.html">Hugging Face</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/mistralai-chat.html">Mistral AI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/minimax-chat.html">MiniMax</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/moonshot-chat.html">Moonshot AI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/nvidia-chat.html">NVIDIA</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/ollama-chat.html">Ollama</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/perplexity-chat.html">Perplexity AI</a>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">OCI Generative AI</span>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="chat/oci-genai/cohere-chat.html">Cohere</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/openai-chat.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/qianfan-chat.html">QianFan</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/zhipuai-chat.html">ZhiPu AI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="embeddings.html">Embedding Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="bedrock.html">Amazon Bedrock</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="embeddings/bedrock-cohere-embedding.html">Cohere</a>
</li>
<li class="nav-item" data-depth="5">
<a class="nav-link" href="embeddings/bedrock-titan-embedding.html">Titan</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/azure-openai-embeddings.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/mistralai-embeddings.html">Mistral AI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/minimax-embeddings.html">MiniMax</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/oci-genai-embeddings.html">OCI GenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/ollama-embeddings.html">Ollama</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/onnx.html">(ONNX) Transformers</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/openai-embeddings.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/postgresml-embeddings.html">PostgresML</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/qianfan-embeddings.html">QianFan</a>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">VertexAI</span>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="embeddings/vertexai-embeddings-text.html">Text Embedding</a>
</li>
<li class="nav-item" data-depth="5">
<a class="nav-link" href="embeddings/vertexai-embeddings-multimodal.html">Multimodal Embedding</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/zhipuai-embeddings.html">ZhiPu AI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="imageclient.html">Image Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/azure-openai-image.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/openai-image.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/stabilityai-image.html">Stability</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/zhipuai-image.html">ZhiPuAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/qianfan-image.html">QianFan</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="#api/audio">Audio Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="audio/transcriptions.html">Transcription API</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="audio/transcriptions/azure-openai-transcriptions.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="5">
<a class="nav-link" href="audio/transcriptions/openai-transcriptions.html">OpenAI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="audio/speech.html">Text-To-Speech (TTS) API</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="audio/speech/openai-speech.html">OpenAI</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="#api/moderation">Moderation Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="moderation/openai-moderation.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="moderation/mistral-ai-moderation.html">Mistral AI</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="chat-memory.html">Chat Memory</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="tools.html">Tool Calling</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="mcp/mcp-overview.html">Model Context Protocol (MCP)</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="mcp/mcp-client-boot-starter-docs.html">MCP Client Boot Starters</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="mcp/mcp-server-boot-starter-docs.html">MCP Server Boot Starters</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="mcp/mcp-helpers.html">MCP Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="retrieval-augmented-generation.html">Retrieval Augmented Generation (RAG)</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="etl-pipeline.html">ETL Pipeline</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="testing.html">Model Evaluation</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="vectordbs.html">Vector Databases</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/azure.html">Azure AI Service</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/azure-cosmos-db.html">Azure Cosmos DB</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/apache-cassandra.html">Apache Cassandra Vector Store</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/chroma.html">Chroma</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/couchbase.html">Couchbase</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/elasticsearch.html">Elasticsearch</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/gemfire.html">GemFire</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/mariadb.html">MariaDB Vector Store</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/milvus.html">Milvus</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/mongodb.html">MongoDB Atlas</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/neo4j.html">Neo4j</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/opensearch.html">OpenSearch</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/oracle.html">Oracle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/pgvector.html">PGvector</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/pinecone.html">Pinecone</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/qdrant.html">Qdrant</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/redis.html">Redis</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/hana.html">SAP Hana</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/typesense.html">Typesense</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/weaviate.html">Weaviate</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../observability/index.html">Observability</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="docker-compose.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Testing</span>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="testcontainers.html">Testcontainers</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Guides</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="https://github.com/spring-ai-community/awesome-spring-ai">Awesome Spring AI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="chat/prompt-engineering-patterns.html">Prompt Engineering Patterns</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="effective-agents.html">Building Effective Agents</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="cloud-bindings.html">Deploying to the Cloud</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../upgrade-notes.html">Upgrade Notes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="tools-migration.html">Migrating FunctionCallback to ToolCallback API</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Prompts">
<div class="toc-menu"><h3>Prompts</h3><ul><li data-level="1"><a href="#_api_overview">API Overview</a></li><li data-level="2"><a href="#_prompt">Prompt</a></li><li data-level="2"><a href="#_message">Message</a></li><li data-level="2"><a href="#_prompttemplate">PromptTemplate</a></li><li data-level="1"><a href="#_example_usage">Example Usage</a></li><li data-level="2"><a href="#_using_a_custom_template_renderer">Using a custom template renderer</a></li><li data-level="2"><a href="#_using_resources_instead_of_raw_strings">Using resources instead of raw Strings</a></li><li data-level="1"><a href="#_prompt_engineering">Prompt Engineering</a></li><li data-level="2"><a href="#_creating_effective_prompts">Creating effective prompts</a></li><li data-level="1"><a href="#_tokens">Tokens</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/prompt.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-ai" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/questions/tagged/spring">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../index.html">Spring AI</a></li>
<li>Reference</li>
<li><a href="prompt.html">Prompts</a></li>
</ul>
</nav>
</div><h1 class="page" id="page-title">Prompts</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Prompts</h3><ul><li data-level="1"><a href="#_api_overview">API Overview</a></li><li data-level="2"><a href="#_prompt">Prompt</a></li><li data-level="2"><a href="#_message">Message</a></li><li data-level="2"><a href="#_prompttemplate">PromptTemplate</a></li><li data-level="1"><a href="#_example_usage">Example Usage</a></li><li data-level="2"><a href="#_using_a_custom_template_renderer">Using a custom template renderer</a></li><li data-level="2"><a href="#_using_resources_instead_of_raw_strings">Using resources instead of raw Strings</a></li><li data-level="1"><a href="#_prompt_engineering">Prompt Engineering</a></li><li data-level="2"><a href="#_creating_effective_prompts">Creating effective prompts</a></li><li data-level="1"><a href="#_tokens">Tokens</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>Prompts are the inputs that guide an AI model to generate specific outputs.
The design and phrasing of these prompts significantly influence the model’s responses.</p>
</div>
<div class="paragraph">
<p>At the lowest level of interaction with AI models in Spring AI, handling prompts in Spring AI is somewhat similar to managing the "View" in Spring MVC.
This involves creating extensive text with placeholders for dynamic content.
These placeholders are then replaced based on user requests or other code in the application.
Another analogy is a SQL statement that contain placeholders for certain expressions.</p>
</div>
<div class="paragraph">
<p>As Spring AI evolves, it will introduce higher levels of abstraction for interacting with AI models.
The foundational classes described in this section can be likened to JDBC in terms of their role and functionality.
The <code>ChatModel</code> class, for instance, is analogous to the core JDBC library in the JDK.
The <code>ChatClient</code> class can be likened to the <code>JdbcClient</code>, built on top of <code>ChatModel</code> and providing more advanced constructs via <code>Advisor</code>
to consider past interactions with the model, augment the prompt with additional contextual documents, and introduce agentic behavior.</p>
</div>
<div class="paragraph">
<p>The structure of prompts has evolved over time within the AI field.
Initially, prompts were simple strings.
Over time, they grew to include placeholders for specific inputs, like "USER:", which the AI model recognizes.
OpenAI have introduced even more structure to prompts by categorizing multiple message strings into distinct roles before they are processed by the AI model.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_api_overview"><a class="anchor" href="#_api_overview"></a>API Overview</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="_prompt"><a class="anchor" href="#_prompt"></a>Prompt</h3>
<div class="paragraph">
<p>It is common to use the <code>call()</code> method of <code>ChatModel</code> that takes a <code>Prompt</code> instance and returns a <code>ChatResponse</code>.</p>
</div>
<div class="paragraph">
<p>The <code>Prompt</code> class functions as a container for an organized series of <code>Message</code> objects and a request <code>ChatOptions</code>.
Every <code>Message</code> embodies a unique role within the prompt, differing in its content and intent.
These roles can encompass a variety of elements, from user inquiries to AI-generated responses to relevant background information.
This arrangement enables intricate and detailed interactions with AI models, as the prompt is constructed from multiple messages, each assigned a specific role to play in the dialogue.</p>
</div>
<div class="paragraph">
<p>Below is a truncated version of the Prompt class, with constructors and utility methods omitted for brevity:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Prompt</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">ModelRequest</span>&lt;<span class="hljs-title">List</span>&lt;<span class="hljs-title">Message</span>&gt;&gt; </span>{

    <span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> List&lt;Message&gt; messages;

    <span class="hljs-keyword">private</span> ChatOptions chatOptions;
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_message"><a class="anchor" href="#_message"></a>Message</h3>
<div class="paragraph">
<p>The <code>Message</code> interface encapsulates a <code>Prompt</code> textual content, a collection of metadata attributes, and a categorization known as <code>MessageType</code>.</p>
</div>
<div class="paragraph">
<p>The interface is defined as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">Content</span> </span>{

	<span class="hljs-function">String <span class="hljs-title">getContent</span><span class="hljs-params">()</span></span>;

	<span class="hljs-function">Map&lt;String, Object&gt; <span class="hljs-title">getMetadata</span><span class="hljs-params">()</span></span>;
}

<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">Message</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">Content</span> </span>{

	<span class="hljs-function">MessageType <span class="hljs-title">getMessageType</span><span class="hljs-params">()</span></span>;
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The multimodal message types implement also the <code>MediaContent</code> interface providing a list of <code>Media</code> content objects.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">MediaContent</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">Content</span> </span>{

	<span class="hljs-function">Collection&lt;Media&gt; <span class="hljs-title">getMedia</span><span class="hljs-params">()</span></span>;

}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Various implementations of the <code>Message</code> interface correspond to different categories of messages that an AI model can process.
The Models distinguish between message categories based on conversational roles.</p>
</div>
<div class="imageblock text-center">
<div class="content">
<img alt="Spring AI Message API" src="../../../../../images/spring-ai_reference__images/spring-ai-message-api.jpg" width="800"/>
</div>
</div>
<div class="paragraph">
<p>These roles are effectively mapped by the <code>MessageType</code>, as discussed below.</p>
</div>
<div class="sect3">
<h4 id="_roles"><a class="anchor" href="#_roles"></a>Roles</h4>
<div class="paragraph">
<p>Each message is assigned a specific role.
These roles categorize the messages, clarifying the context and purpose of each segment of the prompt for the AI model.
This structured approach enhances the nuance and effectiveness of communication with the AI, as each part of the prompt plays a distinct and defined role in the interaction.</p>
</div>
<div class="paragraph">
<p>The primary roles are:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>System Role: Guides the AI’s behavior and response style, setting parameters or rules for how the AI interprets and replies to the input. It’s akin to providing instructions to the AI before initiating a conversation.</p>
</li>
<li>
<p>User Role: Represents the user’s input – their questions, commands, or statements to the AI. This role is fundamental as it forms the basis of the AI’s response.</p>
</li>
<li>
<p>Assistant Role: The AI’s response to the user’s input.
More than just an answer or reaction, it’s crucial for maintaining the flow of the conversation.
By tracking the AI’s previous responses (its 'Assistant Role' messages), the system ensures coherent and contextually relevant interactions.
The Assistant message may contain Function Tool Call request information as well.
It’s like a special feature in the AI, used when needed to perform specific functions such as calculations, fetching data, or other tasks beyond just talking.</p>
</li>
<li>
<p>Tool/Function Role: The Tool/Function Role focuses on returning additional information in response to Tool Call Assistant Messages.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Roles are represented as an enumeration in Spring AI as shown below</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">public</span> <span class="hljs-keyword">enum</span> MessageType {

	USER(<span class="hljs-string">"user"</span>),

	ASSISTANT(<span class="hljs-string">"assistant"</span>),

	SYSTEM(<span class="hljs-string">"system"</span>),

	TOOL(<span class="hljs-string">"tool"</span>);

    ...
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_prompttemplate"><a class="anchor" href="#_prompttemplate"></a>PromptTemplate</h3>
<div class="paragraph">
<p>A key component for prompt templating in Spring AI is the <code>PromptTemplate</code> class, designed to facilitate the creation of structured prompts that are then sent to the AI model for processing</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">PromptTemplate</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">PromptTemplateActions</span>, <span class="hljs-title">PromptTemplateMessageActions</span> </span>{

    <span class="hljs-comment">// Other methods to be discussed later</span>
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This class uses the <code>TemplateRenderer</code> API to render templates. By default, Spring AI uses the <code>StTemplateRenderer</code> implementation, which is based on the open-source <a class="external" href="https://www.stringtemplate.org/" target="_blank">StringTemplate</a> engine developed by Terence Parr. Template variables are identified by the <code>{}</code> syntax, but you can configure the delimiters to use other syntax as well.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">TemplateRenderer</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">BiFunction</span>&lt;<span class="hljs-title">String</span>, <span class="hljs-title">Map</span>&lt;<span class="hljs-title">String</span>, <span class="hljs-title">Object</span>&gt;, <span class="hljs-title">String</span>&gt; </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function">String <span class="hljs-title">apply</span><span class="hljs-params">(String template, Map&lt;String, Object&gt; variables)</span></span>;

}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Spring AI uses the <code>TemplateRenderer</code> interface to handle the actual substitution of variables into the template string.
The default implementation uses <a href="#StringTemplate">[StringTemplate]</a>.
You can provide your own implementation of <code>TemplateRenderer</code> if you need custom logic.
For scenarios where no template rendering is required (e.g., the template string is already complete), you can use the provided <code>NoOpTemplateRenderer</code>.</p>
</div>
<div class="listingblock">
<div class="title">Example using a custom StringTemplate renderer with '&lt;' and '&gt;' delimiters</div>
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">PromptTemplate promptTemplate = PromptTemplate.builder()
    .renderer(StTemplateRenderer.builder().startDelimiterToken(<span class="hljs-string">'&lt;'</span>).endDelimiterToken(<span class="hljs-string">'&gt;'</span>).build())
    .template(<span class="hljs-string">""</span><span class="hljs-string">"
            Tell me the names of 5 movies whose soundtrack was composed by &lt;composer&gt;.
            "</span><span class="hljs-string">""</span>)
    .build();

String prompt = promptTemplate.render(Map.of(<span class="hljs-string">"composer"</span>, <span class="hljs-string">"John Williams"</span>));</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The interfaces implemented by this class support different aspects of prompt creation:</p>
</div>
<div class="paragraph">
<p><code>PromptTemplateStringActions</code> focuses on creating and rendering prompt strings, representing the most basic form of prompt generation.</p>
</div>
<div class="paragraph">
<p><code>PromptTemplateMessageActions</code> is tailored for prompt creation through the generation and manipulation of <code>Message</code> objects.</p>
</div>
<div class="paragraph">
<p><code>PromptTemplateActions</code> is designed to return the <code>Prompt</code> object, which can be passed to <code>ChatModel</code> for generating a response.</p>
</div>
<div class="paragraph">
<p>While these interfaces might not be used extensively in many projects, they show the different approaches to prompt creation.</p>
</div>
<div class="paragraph">
<p>The implemented interfaces are</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">PromptTemplateStringActions</span> </span>{

	<span class="hljs-function">String <span class="hljs-title">render</span><span class="hljs-params">()</span></span>;

	<span class="hljs-function">String <span class="hljs-title">render</span><span class="hljs-params">(Map&lt;String, Object&gt; model)</span></span>;

}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The method <code>String render()</code>: Renders a prompt template into a final string format without external input, suitable for templates without placeholders or dynamic content.</p>
</div>
<div class="paragraph">
<p>The method <code>String render(Map&lt;String, Object&gt; model)</code>: Enhances rendering functionality to include dynamic content. It uses a <code>Map&lt;String, Object&gt;</code> where map keys are placeholder names in the prompt template, and values are the dynamic content to be inserted.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">PromptTemplateMessageActions</span> </span>{

	<span class="hljs-function">Message <span class="hljs-title">createMessage</span><span class="hljs-params">()</span></span>;

    <span class="hljs-function">Message <span class="hljs-title">createMessage</span><span class="hljs-params">(List&lt;Media&gt; mediaList)</span></span>;

	<span class="hljs-function">Message <span class="hljs-title">createMessage</span><span class="hljs-params">(Map&lt;String, Object&gt; model)</span></span>;

}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The method <code>Message createMessage()</code>: Creates a <code>Message</code> object without additional data, used for static or predefined message content.</p>
</div>
<div class="paragraph">
<p>The method <code>Message createMessage(List&lt;Media&gt; mediaList)</code>: Creates a <code>Message</code> object with static textual and media content.</p>
</div>
<div class="paragraph">
<p>The method <code>Message createMessage(Map&lt;String, Object&gt; model)</code>: Extends message creation to integrate dynamic content, accepting a <code>Map&lt;String, Object&gt;</code> where each entry represents a placeholder in the message template and its corresponding dynamic value.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">PromptTemplateActions</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">PromptTemplateStringActions</span> </span>{

	<span class="hljs-function">Prompt <span class="hljs-title">create</span><span class="hljs-params">()</span></span>;

	<span class="hljs-function">Prompt <span class="hljs-title">create</span><span class="hljs-params">(ChatOptions modelOptions)</span></span>;

	<span class="hljs-function">Prompt <span class="hljs-title">create</span><span class="hljs-params">(Map&lt;String, Object&gt; model)</span></span>;

	<span class="hljs-function">Prompt <span class="hljs-title">create</span><span class="hljs-params">(Map&lt;String, Object&gt; model, ChatOptions modelOptions)</span></span>;

}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The method <code>Prompt create()</code>: Generates a <code>Prompt</code> object without external data inputs, ideal for static or predefined prompts.</p>
</div>
<div class="paragraph">
<p>The method <code>Prompt create(ChatOptions modelOptions)</code>: Generates a <code>Prompt</code> object without external data inputs and with specific options for the chat request.</p>
</div>
<div class="paragraph">
<p>The method <code>Prompt create(Map&lt;String, Object&gt; model)</code>: Expands prompt creation capabilities to include dynamic content, taking a <code>Map&lt;String, Object&gt;</code> where each map entry is a placeholder in the prompt template and its associated dynamic value.</p>
</div>
<div class="paragraph">
<p>The method <code>Prompt create(Map&lt;String, Object&gt; model, ChatOptions modelOptions)</code>: Expands prompt creation capabilities to include dynamic content, taking a <code>Map&lt;String, Object&gt;</code> where each map entry is a placeholder in the prompt template and its associated dynamic value, and specific options for the chat request.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_example_usage"><a class="anchor" href="#_example_usage"></a>Example Usage</h2>
<div class="sectionbody">
<div class="paragraph">
<p>A simple example taken from the <a class="external" href="https://github.com/Azure-Samples/spring-ai-azure-workshop/blob/main/2-README-prompt-templating.md" target="_blank">AI Workshop on PromptTemplates</a> is shown below.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">PromptTemplate promptTemplate = <span class="hljs-keyword">new</span> PromptTemplate(<span class="hljs-string">"Tell me a {adjective} joke about {topic}"</span>);

Prompt prompt = promptTemplate.create(Map.of(<span class="hljs-string">"adjective"</span>, adjective, <span class="hljs-string">"topic"</span>, topic));

<span class="hljs-keyword">return</span> chatModel.call(prompt).getResult();</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Another example taken from the <a class="external" href="https://github.com/Azure-Samples/spring-ai-azure-workshop/blob/main/3-README-prompt-roles.md" target="_blank">AI Workshop on Roles</a> is shown below.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">String userText = <span class="hljs-string">""</span><span class="hljs-string">"
    Tell me about three famous pirates from the Golden Age of Piracy and why they did.
    Write at least a sentence for each pirate.
    "</span><span class="hljs-string">""</span>;

Message userMessage = <span class="hljs-keyword">new</span> UserMessage(userText);

String systemText = <span class="hljs-string">""</span><span class="hljs-string">"
  You are a helpful AI assistant that helps people find information.
  Your name is {name}
  You should reply to the user's request with your name and also in the style of a {voice}.
  "</span><span class="hljs-string">""</span>;

SystemPromptTemplate systemPromptTemplate = <span class="hljs-keyword">new</span> SystemPromptTemplate(systemText);
Message systemMessage = systemPromptTemplate.createMessage(Map.of(<span class="hljs-string">"name"</span>, name, <span class="hljs-string">"voice"</span>, voice));

Prompt prompt = <span class="hljs-keyword">new</span> Prompt(List.of(userMessage, systemMessage));

List&lt;Generation&gt; response = chatModel.call(prompt).getResults();</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This shows how you can build up the <code>Prompt</code> instance by using the <code>SystemPromptTemplate</code> to create a <code>Message</code> with the system role passing in placeholder values.
The message with the role <code>user</code> is then combined with the message of the role <code>system</code> to form the prompt.
The prompt is then passed to the ChatModel to get a generative response.</p>
</div>
<div class="sect2">
<h3 id="_using_a_custom_template_renderer"><a class="anchor" href="#_using_a_custom_template_renderer"></a>Using a custom template renderer</h3>
<div class="paragraph">
<p>You can use a custom template renderer by implementing the <code>TemplateRenderer</code> interface and passing it to the <code>PromptTemplate</code> constructor. You can also keep using the default <code>StTemplateRenderer</code>, but with a custom configuration.</p>
</div>
<div class="paragraph">
<p>By default, template variables are identified by the <code>{}</code> syntax. If you’re planning to include JSON in your prompt, you might want to use a different syntax to avoid conflicts with JSON syntax. For example, you can use the <code>&lt;</code> and <code>&gt;</code> delimiters.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">PromptTemplate promptTemplate = PromptTemplate.builder()
    .renderer(StTemplateRenderer.builder().startDelimiterToken(<span class="hljs-string">'&lt;'</span>).endDelimiterToken(<span class="hljs-string">'&gt;'</span>).build())
    .template(<span class="hljs-string">""</span><span class="hljs-string">"
            Tell me the names of 5 movies whose soundtrack was composed by &lt;composer&gt;.
            "</span><span class="hljs-string">""</span>)
    .build();

String prompt = promptTemplate.render(Map.of(<span class="hljs-string">"composer"</span>, <span class="hljs-string">"John Williams"</span>));</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_using_resources_instead_of_raw_strings"><a class="anchor" href="#_using_resources_instead_of_raw_strings"></a>Using resources instead of raw Strings</h3>
<div class="paragraph">
<p>Spring AI supports the <code>org.springframework.core.io.Resource</code> abstraction, so you can put prompt data in a file that can directly be used in a <code>PromptTemplate</code>.
For example, you can define a field in your Spring managed component to retrieve the <code>Resource</code>.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@Value</span>(<span class="hljs-string">"classpath:/prompts/system-message.st"</span>)
<span class="hljs-keyword">private</span> Resource systemResource;</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>and then pass that resource to the <code>SystemPromptTemplate</code> directly.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">SystemPromptTemplate systemPromptTemplate = <span class="hljs-keyword">new</span> SystemPromptTemplate(systemResource);</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_prompt_engineering"><a class="anchor" href="#_prompt_engineering"></a>Prompt Engineering</h2>
<div class="sectionbody">
<div class="paragraph">
<p>In generative AI, the creation of prompts is a crucial task for developers.
The quality and structure of these prompts significantly influence the effectiveness of the AI’s output.
Investing time and effort in designing thoughtful prompts can greatly improve the results from the AI.</p>
</div>
<div class="paragraph">
<p>Sharing and discussing prompts is a common practice in the AI community.
This collaborative approach not only creates a shared learning environment but also leads to the identification and use of highly effective prompts.</p>
</div>
<div class="paragraph">
<p>Research in this area often involves analyzing and comparing different prompts to assess their effectiveness in various situations.
For example, a significant study demonstrated that starting a prompt with "Take a deep breath and work on this problem step by step" significantly enhanced problem-solving efficiency.
This highlights the impact that well-chosen language can have on generative AI systems' performance.</p>
</div>
<div class="paragraph">
<p>Grasping the most effective use of prompts, particularly with the rapid advancement of AI technologies, is a continuous challenge.
You should recognize the importance of prompt engineering and consider using insights from the community and research to improve prompt creation strategies.</p>
</div>
<div class="sect2">
<h3 id="_creating_effective_prompts"><a class="anchor" href="#_creating_effective_prompts"></a>Creating effective prompts</h3>
<div class="paragraph">
<p>When developing prompts, it’s important to integrate several key components to ensure clarity and effectiveness:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><strong>Instructions</strong>: Offer clear and direct instructions to the AI, similar to how you would communicate with a person. This clarity is essential for helping the AI "understand" what is expected.</p>
</li>
<li>
<p><strong>External Context</strong>: Include relevant background information or specific guidance for the AI’s response when necessary. This "external context" frames the prompt and aids the AI in grasping the overall scenario.</p>
</li>
<li>
<p><strong>User Input</strong>: This is the straightforward part - the user’s direct request or question forming the core of the prompt.</p>
</li>
<li>
<p><strong>Output Indicator</strong>: This aspect can be tricky. It involves specifying the desired format for the AI’s response, such as JSON. However, be aware that the AI might not always adhere strictly to this format. For instance, it might prepend a phrase like "here is your JSON" before the actual JSON data, or sometimes generate a JSON-like structure that is not accurate.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Providing the AI with examples of the anticipated question and answer format can be highly beneficial when crafting prompts.
This practice helps the AI "understand" the structure and intent of your query, leading to more precise and relevant responses.
While this documentation does not delve deeply into these techniques, they provide a starting point for further exploration in AI prompt engineering.</p>
</div>
<div class="paragraph">
<p>Following is a list of resources for further investigation.</p>
</div>
<div class="sect3">
<h4 id="_simple_techniques"><a class="anchor" href="#_simple_techniques"></a>Simple Techniques</h4>
<div class="ulist">
<ul>
<li>
<p><strong><a class="external" href="https://www.promptingguide.ai/introduction/examples.en#text-summarization" target="_blank">Text Summarization</a></strong>:<br/>
Reduces extensive text into concise summaries, capturing key points and main ideas while omitting less critical details.</p>
</li>
<li>
<p><strong><a class="external" href="https://www.promptingguide.ai/introduction/examples.en#question-answering" target="_blank">Question Answering</a></strong>:<br/>
Focuses on deriving specific answers from provided text, based on user-posed questions. It’s about pinpointing and extracting relevant information in response to queries.</p>
</li>
<li>
<p><strong><a class="external" href="https://www.promptingguide.ai/introduction/examples.en#text-classification" target="_blank">Text Classification</a></strong>:<br/>
Systematically categorizes text into predefined categories or groups, analyzing the text and assigning it to the most fitting category based on its content.</p>
</li>
<li>
<p><strong><a class="external" href="https://www.promptingguide.ai/introduction/examples.en#conversation" target="_blank">Conversation</a></strong>:<br/>
Creates interactive dialogues where the AI can engage in back-and-forth communication with users, simulating a natural conversation flow.</p>
</li>
<li>
<p><strong><a class="external" href="https://www.promptingguide.ai/introduction/examples.en#code-generation" target="_blank">Code Generation</a></strong>:<br/>
Generates functional code snippets based on specific user requirements or descriptions, translating natural language instructions into executable code.</p>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="_advanced_techniques"><a class="anchor" href="#_advanced_techniques"></a>Advanced Techniques</h4>
<div class="ulist">
<ul>
<li>
<p><strong><a class="external" href="https://www.promptingguide.ai/techniques/zeroshot" target="_blank">Zero-shot</a>, <a class="external" href="https://www.promptingguide.ai/techniques/fewshot" target="_blank">Few-shot Learning</a></strong>:<br/>
Enables the model to make accurate predictions or responses with minimal to no prior examples of the specific problem type, understanding and acting on new tasks using learned generalizations.</p>
</li>
<li>
<p><strong><a class="external" href="https://www.promptingguide.ai/techniques/cot" target="_blank">Chain-of-Thought</a></strong>:<br/>
Links multiple AI responses to create a coherent and contextually aware conversation. It helps the AI maintain the thread of the discussion, ensuring relevance and continuity.</p>
</li>
<li>
<p><strong><a class="external" href="https://www.promptingguide.ai/techniques/react" target="_blank">ReAct (Reason + Act)</a></strong>:<br/>
In this method, the AI first analyzes (reasons about) the input, then determines the most appropriate course of action or response. It combines understanding with decision-making.</p>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="_microsoft_guidance"><a class="anchor" href="#_microsoft_guidance"></a>Microsoft Guidance</h4>
<div class="ulist">
<ul>
<li>
<p><strong><a class="external" href="https://github.com/microsoft/guidance" target="_blank">Framework for Prompt Creation and Optimization</a></strong>:<br/>
Microsoft offers a structured approach to developing and refining prompts. This framework guides users in creating effective prompts that elicit the desired responses from AI models, optimizing the interaction for clarity and efficiency.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_tokens"><a class="anchor" href="#_tokens"></a>Tokens</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Tokens are essential in how AI models process text, acting as a bridge that converts words (as we understand them) into a format that AI models can process.
This conversion occurs in two stages: words are transformed into tokens upon input, and these tokens are then converted back into words in the output.</p>
</div>
<div class="paragraph">
<p>Tokenization, the process of breaking down text into tokens, is fundamental to how AI models comprehend and process language.
The AI model works with this tokenized format to understand and respond to prompts.</p>
</div>
<div class="paragraph">
<p>To better understand tokens, think of them as portions of words. Typically, a token represents about three-quarters of a word. For instance, the complete works of Shakespeare, totaling roughly 900,000 words, would translate to around 1.2 million tokens.</p>
</div>
<div class="paragraph">
<p>Experiment with the <a class="external" href="https://platform.openai.com/tokenizer" target="_blank">OpenAI Tokenizer UI</a> to see how words are converted into tokens.</p>
</div>
<div class="paragraph">
<p>Tokens have practical implications beyond their technical role in AI processing, especially regarding billing and model capabilities:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Billing: AI model services often bill based on token usage. Both the input (prompt) and the output (response) contribute to the total token count, making shorter prompts more cost-effective.</p>
</li>
<li>
<p>Model Limits: Different AI models have varying token limits, defining their "context window" – the maximum amount of information they can process at a time. For example, GPT-3’s limit is 4K tokens, while other models like Claude 2 and Meta Llama 2 have limits of 100K tokens, and some research models can handle up to 1 million tokens.</p>
</li>
<li>
<p>Context Window: A model’s token limit determines its context window. Inputs exceeding this limit are not processed by the model. It’s crucial to send only the minimal effective set of information for processing. For example, when inquiring about "Hamlet," there’s no need to include tokens from all of Shakespeare’s other works.</p>
</li>
<li>
<p>Response Metadata: The metadata of a response from an AI model includes the number of tokens used, a vital piece of information for managing usage and costs.</p>
</li>
</ul>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="advisors.html">Advisors</a></span>
<span class="next"><a href="structured-output-converter.html">Structured Output</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../index.html">Spring AI</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../index.html">
      1.0.0
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../1.1-SNAPSHOT/index.html">
      1.1.0-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../../../images/spring-ai_reference___img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../../../images/spring-ai_reference___img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../../../images/spring-ai_reference___img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>
</body></html>