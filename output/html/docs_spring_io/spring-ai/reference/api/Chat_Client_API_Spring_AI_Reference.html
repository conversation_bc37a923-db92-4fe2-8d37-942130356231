<!DOCTYPE html>

<html><head><title>Chat Client API :: Spring AI Reference</title><link href="https://docs.spring.io/spring-ai/reference/api/chatclient.html"/><meta content="2025-06-04T18:18:26.173207" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../../../images/spring-ai_reference___img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>
<div class="body">
<div class="nav-container" data-component="ai" data-version="1.0.0">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring AI</span>
<span class="version">1.0.0</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Overview</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../concepts.html">AI Concepts</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link" href="../getting-started.html">Getting Started</a>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Reference</span>
<ul class="nav-list">
<li class="nav-item is-current-page is-active" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="chatclient.html">Chat Client API</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="advisors.html">Advisors</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="prompt.html">Prompts</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="structured-output-converter.html">Structured Output</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="multimodality.html">Multimodality</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="chatmodel.html">Chat Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/comparison.html">Chat Models Comparison</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/bedrock-converse.html">Amazon Bedrock Converse</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/anthropic-chat.html">Anthropic 3</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/azure-openai-chat.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/deepseek-chat.html">DeepSeek</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/dmr-chat.html">Docker Model Runner</a>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="chat/google-vertexai.html">Google VertexAI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="chat/vertexai-gemini-chat.html">VertexAI Gemini</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/groq-chat.html">Groq</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/huggingface.html">Hugging Face</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/mistralai-chat.html">Mistral AI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/minimax-chat.html">MiniMax</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/moonshot-chat.html">Moonshot AI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/nvidia-chat.html">NVIDIA</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/ollama-chat.html">Ollama</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/perplexity-chat.html">Perplexity AI</a>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">OCI Generative AI</span>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="chat/oci-genai/cohere-chat.html">Cohere</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/openai-chat.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/qianfan-chat.html">QianFan</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="chat/zhipuai-chat.html">ZhiPu AI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="embeddings.html">Embedding Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="bedrock.html">Amazon Bedrock</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="embeddings/bedrock-cohere-embedding.html">Cohere</a>
</li>
<li class="nav-item" data-depth="5">
<a class="nav-link" href="embeddings/bedrock-titan-embedding.html">Titan</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/azure-openai-embeddings.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/mistralai-embeddings.html">Mistral AI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/minimax-embeddings.html">MiniMax</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/oci-genai-embeddings.html">OCI GenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/ollama-embeddings.html">Ollama</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/onnx.html">(ONNX) Transformers</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/openai-embeddings.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/postgresml-embeddings.html">PostgresML</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/qianfan-embeddings.html">QianFan</a>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">VertexAI</span>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="embeddings/vertexai-embeddings-text.html">Text Embedding</a>
</li>
<li class="nav-item" data-depth="5">
<a class="nav-link" href="embeddings/vertexai-embeddings-multimodal.html">Multimodal Embedding</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="embeddings/zhipuai-embeddings.html">ZhiPu AI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="imageclient.html">Image Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/azure-openai-image.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/openai-image.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/stabilityai-image.html">Stability</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/zhipuai-image.html">ZhiPuAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="image/qianfan-image.html">QianFan</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="#api/audio">Audio Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="audio/transcriptions.html">Transcription API</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="audio/transcriptions/azure-openai-transcriptions.html">Azure OpenAI</a>
</li>
<li class="nav-item" data-depth="5">
<a class="nav-link" href="audio/transcriptions/openai-transcriptions.html">OpenAI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="4">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="audio/speech.html">Text-To-Speech (TTS) API</a>
<ul class="nav-list">
<li class="nav-item" data-depth="5">
<a class="nav-link" href="audio/speech/openai-speech.html">OpenAI</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="#api/moderation">Moderation Models</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="moderation/openai-moderation.html">OpenAI</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="moderation/mistral-ai-moderation.html">Mistral AI</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="chat-memory.html">Chat Memory</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="tools.html">Tool Calling</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="mcp/mcp-overview.html">Model Context Protocol (MCP)</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="mcp/mcp-client-boot-starter-docs.html">MCP Client Boot Starters</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="mcp/mcp-server-boot-starter-docs.html">MCP Server Boot Starters</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="mcp/mcp-helpers.html">MCP Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="retrieval-augmented-generation.html">Retrieval Augmented Generation (RAG)</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="etl-pipeline.html">ETL Pipeline</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="testing.html">Model Evaluation</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="vectordbs.html">Vector Databases</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/azure.html">Azure AI Service</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/azure-cosmos-db.html">Azure Cosmos DB</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/apache-cassandra.html">Apache Cassandra Vector Store</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/chroma.html">Chroma</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/couchbase.html">Couchbase</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/elasticsearch.html">Elasticsearch</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/gemfire.html">GemFire</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/mariadb.html">MariaDB Vector Store</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/milvus.html">Milvus</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/mongodb.html">MongoDB Atlas</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/neo4j.html">Neo4j</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/opensearch.html">OpenSearch</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/oracle.html">Oracle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/pgvector.html">PGvector</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/pinecone.html">Pinecone</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/qdrant.html">Qdrant</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/redis.html">Redis</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/hana.html">SAP Hana</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/typesense.html">Typesense</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="vectordbs/weaviate.html">Weaviate</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../observability/index.html">Observability</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="docker-compose.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Testing</span>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="testcontainers.html">Testcontainers</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Guides</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="https://github.com/spring-ai-community/awesome-spring-ai">Awesome Spring AI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="chat/prompt-engineering-patterns.html">Prompt Engineering Patterns</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="effective-agents.html">Building Effective Agents</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="cloud-bindings.html">Deploying to the Cloud</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../upgrade-notes.html">Upgrade Notes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="tools-migration.html">Migrating FunctionCallback to ToolCallback API</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Chat Client API">
<div class="toc-menu"><h3>Chat Client API</h3><ul><li data-level="1"><a href="#_creating_a_chatclient">Creating a ChatClient</a></li><li data-level="2"><a href="#_using_an_autoconfigured_chatclient_builder">Using an autoconfigured ChatClient.Builder</a></li><li data-level="2"><a href="#_working_with_multiple_chat_models">Working with Multiple Chat Models</a></li><li data-level="1"><a href="#_chatclient_fluent_api">ChatClient Fluent API</a></li><li data-level="1"><a href="#_chatclient_responses">ChatClient Responses</a></li><li data-level="2"><a href="#_returning_a_chatresponse">Returning a ChatResponse</a></li><li data-level="2"><a href="#_returning_an_entity">Returning an Entity</a></li><li data-level="2"><a href="#_streaming_responses">Streaming Responses</a></li><li data-level="1"><a href="#_prompt_templates">Prompt Templates</a></li><li data-level="1"><a href="#_call_return_values">call() return values</a></li><li data-level="1"><a href="#_stream_return_values">stream() return values</a></li><li data-level="1"><a href="#_using_defaults">Using Defaults</a></li><li data-level="2"><a href="#_default_system_text">Default System Text</a></li><li data-level="2"><a href="#_default_system_text_with_parameters">Default System Text with parameters</a></li><li data-level="2"><a href="#_other_defaults">Other defaults</a></li><li data-level="1"><a href="#_advisors">Advisors</a></li><li data-level="2"><a href="#_advisor_configuration_in_chatclient">Advisor Configuration in ChatClient</a></li><li data-level="2"><a href="#_retrieval_augmented_generation">Retrieval Augmented Generation</a></li><li data-level="2"><a href="#_logging">Logging</a></li><li data-level="1"><a href="#_chat_memory">Chat Memory</a></li><li data-level="1"><a href="#_implementation_notes">Implementation Notes</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/chatclient.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-ai" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/questions/tagged/spring">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../index.html">Spring AI</a></li>
<li>Reference</li>
<li><a href="chatclient.html">Chat Client API</a></li>
</ul>
</nav>
</div><h1 class="page" id="page-title">Chat Client API</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Chat Client API</h3><ul><li data-level="1"><a href="#_creating_a_chatclient">Creating a ChatClient</a></li><li data-level="2"><a href="#_using_an_autoconfigured_chatclient_builder">Using an autoconfigured ChatClient.Builder</a></li><li data-level="2"><a href="#_working_with_multiple_chat_models">Working with Multiple Chat Models</a></li><li data-level="1"><a href="#_chatclient_fluent_api">ChatClient Fluent API</a></li><li data-level="1"><a href="#_chatclient_responses">ChatClient Responses</a></li><li data-level="2"><a href="#_returning_a_chatresponse">Returning a ChatResponse</a></li><li data-level="2"><a href="#_returning_an_entity">Returning an Entity</a></li><li data-level="2"><a href="#_streaming_responses">Streaming Responses</a></li><li data-level="1"><a href="#_prompt_templates">Prompt Templates</a></li><li data-level="1"><a href="#_call_return_values">call() return values</a></li><li data-level="1"><a href="#_stream_return_values">stream() return values</a></li><li data-level="1"><a href="#_using_defaults">Using Defaults</a></li><li data-level="2"><a href="#_default_system_text">Default System Text</a></li><li data-level="2"><a href="#_default_system_text_with_parameters">Default System Text with parameters</a></li><li data-level="2"><a href="#_other_defaults">Other defaults</a></li><li data-level="1"><a href="#_advisors">Advisors</a></li><li data-level="2"><a href="#_advisor_configuration_in_chatclient">Advisor Configuration in ChatClient</a></li><li data-level="2"><a href="#_retrieval_augmented_generation">Retrieval Augmented Generation</a></li><li data-level="2"><a href="#_logging">Logging</a></li><li data-level="1"><a href="#_chat_memory">Chat Memory</a></li><li data-level="1"><a href="#_implementation_notes">Implementation Notes</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>The <code>ChatClient</code> offers a fluent API for communicating with an AI Model.
It supports both a synchronous and streaming programming model.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>See the <a href="#_implementation_notes">Implementation Notes</a> at the bottom of this document related to the combined use of imperative and reactive programming models in <code>ChatClient</code></p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The fluent API has methods for building up the constituent parts of a <a class="xref page" href="prompt.html#_prompt">Prompt</a> that is passed to the AI model as input.
The <code>Prompt</code> contains the instructional text to guide the AI model’s output and behavior. From the API point of view, prompts consist of a collection of messages.</p>
</div>
<div class="paragraph">
<p>The AI model processes two main types of messages: user messages, which are direct inputs from the user, and system messages, which are generated by the system to guide the conversation.</p>
</div>
<div class="paragraph">
<p>These messages often contain placeholders that are substituted at runtime based on user input to customize the response of the AI model to the user input.</p>
</div>
<div class="paragraph">
<p>There are also Prompt options that can be specified, such as the name of the AI Model to use and the temperature setting that controls the randomness or creativity of the generated output.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_creating_a_chatclient"><a class="anchor" href="#_creating_a_chatclient"></a>Creating a ChatClient</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The <code>ChatClient</code> is created using a <code>ChatClient.Builder</code> object.
You can obtain an autoconfigured <code>ChatClient.Builder</code> instance for any <a class="xref page" href="chatmodel.html">ChatModel</a> Spring Boot autoconfiguration or create one programmatically.</p>
</div>
<div class="sect2">
<h3 id="_using_an_autoconfigured_chatclient_builder"><a class="anchor" href="#_using_an_autoconfigured_chatclient_builder"></a>Using an autoconfigured ChatClient.Builder</h3>
<div class="paragraph">
<p>In the most simple use case, Spring AI provides Spring Boot autoconfiguration, creating a prototype <code>ChatClient.Builder</code> bean for you to inject into your class.
Here is a simple example of retrieving a <code>String</code> response to a simple user request.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@RestController</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyController</span> </span>{

    <span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> ChatClient chatClient;

    <span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyController</span><span class="hljs-params">(ChatClient.Builder chatClientBuilder)</span> </span>{
        <span class="hljs-keyword">this</span>.chatClient = chatClientBuilder.build();
    }

    <span class="hljs-meta">@GetMapping</span>(<span class="hljs-string">"/ai"</span>)
    <span class="hljs-function">String <span class="hljs-title">generation</span><span class="hljs-params">(String userInput)</span> </span>{
        <span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.chatClient.prompt()
            .user(userInput)
            .call()
            .content();
    }
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>In this simple example, the user input sets the contents of the user message.
The <code>call()</code> method sends a request to the AI model, and the <code>content()</code> method returns the AI model’s response as a <code>String</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="_working_with_multiple_chat_models"><a class="anchor" href="#_working_with_multiple_chat_models"></a>Working with Multiple Chat Models</h3>
<div class="paragraph">
<p>There are several scenarios where you might need to work with multiple chat models in a single application:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Using different models for different types of tasks (e.g., a powerful model for complex reasoning and a faster, cheaper model for simpler tasks)</p>
</li>
<li>
<p>Implementing fallback mechanisms when one model service is unavailable</p>
</li>
<li>
<p>A/B testing different models or configurations</p>
</li>
<li>
<p>Providing users with a choice of models based on their preferences</p>
</li>
<li>
<p>Combining specialized models (one for code generation, another for creative content, etc.)</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>By default, Spring AI autoconfigures a single <code>ChatClient.Builder</code> bean. However, you may need to work with multiple chat models in your application. Here’s how to handle this scenario:</p>
</div>
<div class="paragraph">
<p>In all cases, you need to disable the <code>ChatClient.Builder</code> autoconfiguration by setting the property <code>spring.ai.chat.client.enabled=false</code>.</p>
</div>
<div class="paragraph">
<p>This allows you to create multiple <code>ChatClient</code> instances manually.</p>
</div>
<div class="sect3">
<h4 id="_multiple_chatclients_with_a_single_model_type"><a class="anchor" href="#_multiple_chatclients_with_a_single_model_type"></a>Multiple ChatClients with a Single Model Type</h4>
<div class="paragraph">
<p>This section covers a common use case where you need to create multiple ChatClient instances that all use the same underlying model type but with different configurations.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-comment">// Create ChatClient instances programmatically</span>
ChatModel myChatModel = ... <span class="hljs-comment">// already autoconfigured by Spring Boot</span>
ChatClient chatClient = ChatClient.create(myChatModel);

<span class="hljs-comment">// Or use the builder for more control</span>
ChatClient.Builder builder = ChatClient.builder(myChatModel);
ChatClient customChatClient = builder
    .defaultSystemPrompt(<span class="hljs-string">"You are a helpful assistant."</span>)
    .build();</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="_chatclients_for_different_model_types"><a class="anchor" href="#_chatclients_for_different_model_types"></a>ChatClients for Different Model Types</h4>
<div class="paragraph">
<p>When working with multiple AI models, you can define separate <code>ChatClient</code> beans for each model:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.ai.chat.ChatClient;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">ChatClientConfig</span> </span>{

    <span class="hljs-meta">@Bean</span>
    <span class="hljs-function"><span class="hljs-keyword">public</span> ChatClient <span class="hljs-title">openAiChatClient</span><span class="hljs-params">(OpenAiChatModel chatModel)</span> </span>{
        <span class="hljs-keyword">return</span> ChatClient.create(chatModel);
    }

    <span class="hljs-meta">@Bean</span>
    <span class="hljs-function"><span class="hljs-keyword">public</span> ChatClient <span class="hljs-title">anthropicChatClient</span><span class="hljs-params">(AnthropicChatModel chatModel)</span> </span>{
        <span class="hljs-keyword">return</span> ChatClient.create(chatModel);
    }
}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>You can then inject these beans into your application components using the <code>@Qualifier</code> annotation:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@Configuration</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">ChatClientExample</span> </span>{

    <span class="hljs-meta">@Bean</span>
    <span class="hljs-function">CommandLineRunner <span class="hljs-title">cli</span><span class="hljs-params">(
            @Qualifier(<span class="hljs-string">"openAiChatClient"</span>)</span> ChatClient openAiChatClient,
            @<span class="hljs-title">Qualifier</span><span class="hljs-params">(<span class="hljs-string">"anthropicChatClient"</span>)</span> ChatClient anthropicChatClient) </span>{

        <span class="hljs-keyword">return</span> args -&gt; {
            <span class="hljs-keyword">var</span> scanner = <span class="hljs-keyword">new</span> Scanner(System.in);
            ChatClient chat;

            <span class="hljs-comment">// Model selection</span>
            System.out.println(<span class="hljs-string">"\nSelect your AI model:"</span>);
            System.out.println(<span class="hljs-string">"1. OpenAI"</span>);
            System.out.println(<span class="hljs-string">"2. Anthropic"</span>);
            System.out.print(<span class="hljs-string">"Enter your choice (1 or 2): "</span>);

            String choice = scanner.nextLine().trim();

            <span class="hljs-keyword">if</span> (choice.equals(<span class="hljs-string">"1"</span>)) {
                chat = openAiChatClient;
                System.out.println(<span class="hljs-string">"Using OpenAI model"</span>);
            } <span class="hljs-keyword">else</span> {
                chat = anthropicChatClient;
                System.out.println(<span class="hljs-string">"Using Anthropic model"</span>);
            }

            <span class="hljs-comment">// Use the selected chat client</span>
            System.out.print(<span class="hljs-string">"\nEnter your question: "</span>);
            String input = scanner.nextLine();
            String response = chat.prompt(input).call().content();
            System.out.println(<span class="hljs-string">"ASSISTANT: "</span> + response);

            scanner.close();
        };
    }
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="_multiple_openai_compatible_api_endpoints"><a class="anchor" href="#_multiple_openai_compatible_api_endpoints"></a>Multiple OpenAI-Compatible API Endpoints</h4>
<div class="paragraph">
<p>The <code>OpenAiApi</code> and <code>OpenAiChatModel</code> classes provide a <code>mutate()</code> method that allows you to create variations of existing instances with different properties. This is particularly useful when you need to work with multiple OpenAI-compatible APIs.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@Service</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MultiModelService</span> </span>{

    <span class="hljs-keyword">private</span> <span class="hljs-keyword">static</span> <span class="hljs-keyword">final</span> Logger logger = LoggerFactory.getLogger(MultiModelService<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;

    <span class="hljs-meta">@Autowired</span>
    <span class="hljs-keyword">private</span> OpenAiChatModel baseChatModel;

    <span class="hljs-meta">@Autowired</span>
    <span class="hljs-keyword">private</span> OpenAiApi baseOpenAiApi;

    <span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">multiClientFlow</span><span class="hljs-params">()</span> </span>{
        <span class="hljs-keyword">try</span> {
            <span class="hljs-comment">// Derive a new OpenAiApi for Groq (Llama3)</span>
            OpenAiApi groqApi = baseOpenAiApi.mutate()
                .baseUrl(<span class="hljs-string">"https://api.groq.com/openai"</span>)
                .apiKey(System.getenv(<span class="hljs-string">"GROQ_API_KEY"</span>))
                .build();

            <span class="hljs-comment">// Derive a new OpenAiApi for OpenAI GPT-4</span>
            OpenAiApi gpt4Api = baseOpenAiApi.mutate()
                .baseUrl(<span class="hljs-string">"https://api.openai.com"</span>)
                .apiKey(System.getenv(<span class="hljs-string">"OPENAI_API_KEY"</span>))
                .build();

            <span class="hljs-comment">// Derive a new OpenAiChatModel for Groq</span>
            OpenAiChatModel groqModel = baseChatModel.mutate()
                .openAiApi(groqApi)
                .defaultOptions(OpenAiChatOptions.builder().model(<span class="hljs-string">"llama3-70b-8192"</span>).temperature(<span class="hljs-number">0.5</span>).build())
                .build();

            <span class="hljs-comment">// Derive a new OpenAiChatModel for GPT-4</span>
            OpenAiChatModel gpt4Model = baseChatModel.mutate()
                .openAiApi(gpt4Api)
                .defaultOptions(OpenAiChatOptions.builder().model(<span class="hljs-string">"gpt-4"</span>).temperature(<span class="hljs-number">0.7</span>).build())
                .build();

            <span class="hljs-comment">// Simple prompt for both models</span>
            String prompt = <span class="hljs-string">"What is the capital of France?"</span>;

            String groqResponse = ChatClient.builder(groqModel).build().prompt(prompt).call().content();
            String gpt4Response = ChatClient.builder(gpt4Model).build().prompt(prompt).call().content();

            logger.info(<span class="hljs-string">"Groq (Llama3) response: {}"</span>, groqResponse);
            logger.info(<span class="hljs-string">"OpenAI GPT-4 response: {}"</span>, gpt4Response);
        }
        <span class="hljs-keyword">catch</span> (Exception e) {
            logger.error(<span class="hljs-string">"Error in multi-client flow"</span>, e);
        }
    }
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_chatclient_fluent_api"><a class="anchor" href="#_chatclient_fluent_api"></a>ChatClient Fluent API</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The <code>ChatClient</code> fluent API allows you to create a prompt in three distinct ways using an overloaded <code>prompt</code> method to initiate the fluent API:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>prompt()</code>: This method with no arguments lets you start using the fluent API, allowing you to build up user, system, and other parts of the prompt.</p>
</li>
<li>
<p><code>prompt(Prompt prompt)</code>: This method accepts a <code>Prompt</code> argument, letting you pass in a <code>Prompt</code> instance that you have created using the Prompt’s non-fluent APIs.</p>
</li>
<li>
<p><code>prompt(String content)</code>: This is a convenience method similar to the previous overload. It takes the user’s text content.</p>
</li>
</ul>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_chatclient_responses"><a class="anchor" href="#_chatclient_responses"></a>ChatClient Responses</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The <code>ChatClient</code> API offers several ways to format the response from the AI Model using the fluent API.</p>
</div>
<div class="sect2">
<h3 id="_returning_a_chatresponse"><a class="anchor" href="#_returning_a_chatresponse"></a>Returning a ChatResponse</h3>
<div class="paragraph">
<p>The response from the AI model is a rich structure defined by the type <code><a class="xref page" href="chatmodel.html#ChatResponse">ChatResponse</a></code>.
It includes metadata about how the response was generated and can also contain multiple responses, known as <a class="xref page" href="chatmodel.html#Generation">Generation</a>s, each with its own metadata.
The metadata includes the number of tokens (each token is approximately 3/4 of a word) used to create the response.
This information is important because hosted AI models charge based on the number of tokens used per request.</p>
</div>
<div class="paragraph">
<p>An example to return the <code>ChatResponse</code> object that contains the metadata is shown below by invoking <code>chatResponse()</code> after the <code>call()</code> method.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">ChatResponse chatResponse = chatClient.prompt()
    .user(<span class="hljs-string">"Tell me a joke"</span>)
    .call()
    .chatResponse();</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_returning_an_entity"><a class="anchor" href="#_returning_an_entity"></a>Returning an Entity</h3>
<div class="paragraph">
<p>You often want to return an entity class that is mapped from the returned <code>String</code>.
The <code>entity()</code> method provides this functionality.</p>
</div>
<div class="paragraph">
<p>For example, given the Java record:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-function">record <span class="hljs-title">ActorFilms</span><span class="hljs-params">(String actor, List&lt;String&gt; movies)</span> </span>{}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>You can easily map the AI model’s output to this record using the <code>entity()</code> method, as shown below:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">ActorFilms actorFilms = chatClient.prompt()
    .user(<span class="hljs-string">"Generate the filmography for a random actor."</span>)
    .call()
    .entity(ActorFilms<span class="hljs-class">.<span class="hljs-keyword">class</span>)</span>;</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>There is also an overloaded <code>entity</code> method with the signature <code>entity(ParameterizedTypeReference&lt;T&gt; type)</code> that lets you specify types such as generic Lists:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">List&lt;ActorFilms&gt; actorFilms = chatClient.prompt()
    .user(<span class="hljs-string">"Generate the filmography of 5 movies for Tom Hanks and Bill Murray."</span>)
    .call()
    .entity(<span class="hljs-keyword">new</span> ParameterizedTypeReference&lt;List&lt;ActorFilms&gt;&gt;() {});</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_streaming_responses"><a class="anchor" href="#_streaming_responses"></a>Streaming Responses</h3>
<div class="paragraph">
<p>The <code>stream()</code> method lets you get an asynchronous response as shown below:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">Flux&lt;String&gt; output = chatClient.prompt()
    .user(<span class="hljs-string">"Tell me a joke"</span>)
    .stream()
    .content();</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>You can also stream the <code>ChatResponse</code> using the method <code>Flux&lt;ChatResponse&gt; chatResponse()</code>.</p>
</div>
<div class="paragraph">
<p>In the future, we will offer a convenience method that will let you return a Java entity with the reactive <code>stream()</code> method.
In the meantime, you should use the <a class="xref page" href="structured-output-converter.html#StructuredOutputConverter">Structured Output Converter</a> to convert the aggregated response explicity as shown below.
This also demonstrates the use of parameters in the fluent API that will be discussed in more detail in a later section of the documentation.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-keyword">var</span> converter = <span class="hljs-keyword">new</span> BeanOutputConverter&lt;&gt;(<span class="hljs-keyword">new</span> ParameterizedTypeReference&lt;List&lt;ActorsFilms&gt;&gt;() {});

Flux&lt;String&gt; flux = <span class="hljs-keyword">this</span>.chatClient.prompt()
    .user(u -&gt; u.text(<span class="hljs-string">""</span><span class="hljs-string">"
                        Generate the filmography for a random actor.
                        {format}
                      "</span><span class="hljs-string">""</span>)
            .param(<span class="hljs-string">"format"</span>, <span class="hljs-keyword">this</span>.converter.getFormat()))
    .stream()
    .content();

String content = <span class="hljs-keyword">this</span>.flux.collectList().block().stream().collect(Collectors.joining());

List&lt;ActorFilms&gt; actorFilms = <span class="hljs-keyword">this</span>.converter.convert(<span class="hljs-keyword">this</span>.content);</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_prompt_templates"><a class="anchor" href="#_prompt_templates"></a>Prompt Templates</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The <code>ChatClient</code> fluent API lets you provide user and system text as templates with variables that are replaced at runtime.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">String answer = ChatClient.create(chatModel).prompt()
    .user(u -&gt; u
            .text(<span class="hljs-string">"Tell me the names of 5 movies whose soundtrack was composed by {composer}"</span>)
            .param(<span class="hljs-string">"composer"</span>, <span class="hljs-string">"John Williams"</span>))
    .call()
    .content();</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Internally, the ChatClient uses the <code>PromptTemplate</code> class to handle the user and system text and replace the variables with the values provided at runtime relying on a given <code>TemplateRenderer</code> implementation.
By default, Spring AI uses the <code>StTemplateRenderer</code> implementation, which is based on the open-source <a class="external" href="https://www.stringtemplate.org/" target="_blank">StringTemplate</a> engine developed by Terence Parr.</p>
</div>
<div class="paragraph">
<p>Spring AI also provides a <code>NoOpTemplateRenderer</code> for cases where no template processing is desired.</p>
</div>
<div class="paragraph">
<p>Spring AI also provides a <code>NoOpTemplateRenderer</code>.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <code>TemplateRenderer</code> configured directly on the <code>ChatClient</code> (via <code>.templateRenderer()</code>) applies only to the prompt content defined directly in the <code>ChatClient</code> builder chain (e.g., via <code>.user()</code>, <code>.system()</code>).
It does <strong>not</strong> affect templates used internally by <a class="xref page" href="retrieval-augmented-generation.html#_questionansweradvisor">Advisors</a> like <code>QuestionAnswerAdvisor</code>, which have their own template customization mechanisms (see <a class="xref page" href="retrieval-augmented-generation.html#_custom_template">Custom Advisor Templates</a>).
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>If you’d rather use a different template engine, you can provide a custom implementation of the <code>TemplateRenderer</code> interface directly to the ChatClient. You can also keep using the default <code>StTemplateRenderer</code>, but with a custom configuration.</p>
</div>
<div class="paragraph">
<p>For example, by default, template variables are identified by the <code>{}</code> syntax. If you’re planning to include JSON in your prompt, you might want to use a different syntax to avoid conflicts with JSON syntax. For example, you can use the <code>&lt;</code> and <code>&gt;</code> delimiters.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">String answer = ChatClient.create(chatModel).prompt()
    .user(u -&gt; u
            .text(<span class="hljs-string">"Tell me the names of 5 movies whose soundtrack was composed by &lt;composer&gt;"</span>)
            .param(<span class="hljs-string">"composer"</span>, <span class="hljs-string">"John Williams"</span>))
    .templateRenderer(StTemplateRenderer.builder().startDelimiterToken(<span class="hljs-string">'&lt;'</span>).endDelimiterToken(<span class="hljs-string">'&gt;'</span>).build())
    .call()
    .content();</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_call_return_values"><a class="anchor" href="#_call_return_values"></a>call() return values</h2>
<div class="sectionbody">
<div class="paragraph">
<p>After specifying the <code>call()</code> method on <code>ChatClient</code>, there are a few different options for the response type.</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>String content()</code>: returns the String content of the response</p>
</li>
<li>
<p><code>ChatResponse chatResponse()</code>: returns the <code>ChatResponse</code> object that contains multiple generations and also metadata about the response, for example how many token were used to create the response.</p>
</li>
<li>
<p><code>ChatClientResponse chatClientResponse()</code>: returns a <code>ChatClientResponse</code> object that contains the <code>ChatResponse</code> object and the ChatClient execution context, giving you access to additional data used during the execution of advisors (e.g. the relevant documents retrieved in a RAG flow).</p>
</li>
<li>
<p><code>entity()</code> to return a Java type</p>
<div class="ulist">
<ul>
<li>
<p><code>entity(ParameterizedTypeReference&lt;T&gt; type)</code>: used to return a <code>Collection</code> of entity types.</p>
</li>
<li>
<p><code>entity(Class&lt;T&gt; type)</code>:  used to return a specific entity type.</p>
</li>
<li>
<p><code>entity(StructuredOutputConverter&lt;T&gt; structuredOutputConverter)</code>: used to specify an instance of a <code>StructuredOutputConverter</code> to convert a <code>String</code> to an entity type.</p>
</li>
</ul>
</div>
</li>
</ul>
</div>
<div class="paragraph">
<p>You can also invoke the <code>stream()</code> method instead of <code>call()</code>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_stream_return_values"><a class="anchor" href="#_stream_return_values"></a>stream() return values</h2>
<div class="sectionbody">
<div class="paragraph">
<p>After specifying the <code>stream()</code> method on <code>ChatClient</code>, there are a few options for the response type:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>Flux&lt;String&gt; content()</code>: Returns a <code>Flux</code> of the string being generated by the AI model.</p>
</li>
<li>
<p><code>Flux&lt;ChatResponse&gt; chatResponse()</code>: Returns a <code>Flux</code> of the <code>ChatResponse</code> object, which contains additional metadata about the response.</p>
</li>
<li>
<p><code>Flux&lt;ChatClientResponse&gt; chatClientResponse()</code>: returns a <code>Flux</code> of the <code>ChatClientResponse</code> object that contains the <code>ChatResponse</code> object and the ChatClient execution context, giving you access to additional data used during the execution of advisors (e.g. the relevant documents retrieved in a RAG flow).</p>
</li>
</ul>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_using_defaults"><a class="anchor" href="#_using_defaults"></a>Using Defaults</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Creating a <code>ChatClient</code> with a default system text in an <code>@Configuration</code> class simplifies runtime code.
By setting defaults, you only need to specify the user text when calling <code>ChatClient</code>, eliminating the need to set a system text for each request in your runtime code path.</p>
</div>
<div class="sect2">
<h3 id="_default_system_text"><a class="anchor" href="#_default_system_text"></a>Default System Text</h3>
<div class="paragraph">
<p>In the following example, we will configure the system text to always reply in a pirate’s voice.
To avoid repeating the system text in runtime code, we will create a <code>ChatClient</code> instance in a <code>@Configuration</code> class.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@Configuration</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Config</span> </span>{

    <span class="hljs-meta">@Bean</span>
    <span class="hljs-function">ChatClient <span class="hljs-title">chatClient</span><span class="hljs-params">(ChatClient.Builder builder)</span> </span>{
        <span class="hljs-keyword">return</span> builder.defaultSystem(<span class="hljs-string">"You are a friendly chat bot that answers question in the voice of a Pirate"</span>)
                .build();
    }

}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>and a <code>@RestController</code> to invoke it:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@RestController</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">AIController</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> ChatClient chatClient;

	AIController(ChatClient chatClient) {
		<span class="hljs-keyword">this</span>.chatClient = chatClient;
	}

	<span class="hljs-meta">@GetMapping</span>(<span class="hljs-string">"/ai/simple"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> Map&lt;String, String&gt; <span class="hljs-title">completion</span><span class="hljs-params">(@RequestParam(value = <span class="hljs-string">"message"</span>, defaultValue = <span class="hljs-string">"Tell me a joke"</span>)</span> String message) </span>{
		<span class="hljs-keyword">return</span> Map.of(<span class="hljs-string">"completion"</span>, <span class="hljs-keyword">this</span>.chatClient.prompt().user(message).call().content());
	}
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>When calling the application endpoint via curl, the result is:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-bash hljs" data-lang="bash">❯ curl localhost:8080/ai/simple
{<span class="hljs-string">"completion"</span>:<span class="hljs-string">"Why did the pirate go to the comedy club? To hear some arrr-rated jokes! Arrr, matey!"</span>}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_default_system_text_with_parameters"><a class="anchor" href="#_default_system_text_with_parameters"></a>Default System Text with parameters</h3>
<div class="paragraph">
<p>In the following example, we will use a placeholder in the system text to specify the voice of the completion at runtime instead of design time.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@Configuration</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Config</span> </span>{

    <span class="hljs-meta">@Bean</span>
    <span class="hljs-function">ChatClient <span class="hljs-title">chatClient</span><span class="hljs-params">(ChatClient.Builder builder)</span> </span>{
        <span class="hljs-keyword">return</span> builder.defaultSystem(<span class="hljs-string">"You are a friendly chat bot that answers question in the voice of a {voice}"</span>)
                .build();
    }

}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-meta">@RestController</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">AIController</span> </span>{
	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> ChatClient chatClient;

	AIController(ChatClient chatClient) {
		<span class="hljs-keyword">this</span>.chatClient = chatClient;
	}

	<span class="hljs-meta">@GetMapping</span>(<span class="hljs-string">"/ai"</span>)
	<span class="hljs-function">Map&lt;String, String&gt; <span class="hljs-title">completion</span><span class="hljs-params">(@RequestParam(value = <span class="hljs-string">"message"</span>, defaultValue = <span class="hljs-string">"Tell me a joke"</span>)</span> String message, String voice) </span>{
		<span class="hljs-keyword">return</span> Map.of(<span class="hljs-string">"completion"</span>,
				<span class="hljs-keyword">this</span>.chatClient.prompt()
						.system(sp -&gt; sp.param(<span class="hljs-string">"voice"</span>, voice))
						.user(message)
						.call()
						.content());
	}

}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>When calling the application endpoint via httpie, the result is:</p>
</div>
<div class="listingblock bash">
<div class="content">
<pre class="highlightjs highlight"><code class="language-none hljs">http localhost:8080/ai voice=='Robert DeNiro'
{
    "completion": "You talkin' to me? Okay, here's a joke for ya: Why couldn't the bicycle stand up by itself? Because it was two tired! Classic, right?"
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_other_defaults"><a class="anchor" href="#_other_defaults"></a>Other defaults</h3>
<div class="paragraph">
<p>At the <code>ChatClient.Builder</code> level, you can specify the default prompt configuration.</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>defaultOptions(ChatOptions chatOptions)</code>: Pass in either portable options defined in the <code>ChatOptions</code> class or model-specific options such as those in <code>OpenAiChatOptions</code>. For more information on model-specific <code>ChatOptions</code> implementations, refer to the JavaDocs.</p>
</li>
<li>
<p><code>defaultFunction(String name, String description, java.util.function.Function&lt;I, O&gt; function)</code>: The <code>name</code> is used to refer to the function in user text. The <code>description</code> explains the function’s purpose and helps the AI model choose the correct function for an accurate response. The <code>function</code> argument is a Java function instance that the model will execute when necessary.</p>
</li>
<li>
<p><code>defaultFunctions(String…​ functionNames)</code>: The bean names of `java.util.Function`s defined in the application context.</p>
</li>
<li>
<p><code>defaultUser(String text)</code>, <code>defaultUser(Resource text)</code>, <code>defaultUser(Consumer&lt;UserSpec&gt; userSpecConsumer)</code>: These methods let you define the user text. The <code>Consumer&lt;UserSpec&gt;</code> allows you to use a lambda to specify the user text and any default parameters.</p>
</li>
<li>
<p><code>defaultAdvisors(Advisor…​ advisor)</code>: Advisors allow modification of the data used to create the <code>Prompt</code>. The <code>QuestionAnswerAdvisor</code> implementation enables the pattern of <code>Retrieval Augmented Generation</code> by appending the prompt with context information related to the user text.</p>
</li>
<li>
<p><code>defaultAdvisors(Consumer&lt;AdvisorSpec&gt; advisorSpecConsumer)</code>: This method allows you to define a <code>Consumer</code> to configure multiple advisors using the <code>AdvisorSpec</code>. Advisors can modify the data used to create the final <code>Prompt</code>. The <code>Consumer&lt;AdvisorSpec&gt;</code> lets you specify a lambda to add advisors, such as <code>QuestionAnswerAdvisor</code>, which supports <code>Retrieval Augmented Generation</code> by appending the prompt with relevant context information based on the user text.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>You can override these defaults at runtime using the corresponding methods without the <code>default</code> prefix.</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>options(ChatOptions chatOptions)</code></p>
</li>
<li>
<p><code>function(String name, String description,
java.util.function.Function&lt;I, O&gt; function)</code></p>
</li>
<li>
<p><code>functions(String…​ functionNames)</code></p>
</li>
<li>
<p><code>user(String text)</code>, <code>user(Resource text)</code>, <code>user(Consumer&lt;UserSpec&gt; userSpecConsumer)</code></p>
</li>
<li>
<p><code>advisors(Advisor…​ advisor)</code></p>
</li>
<li>
<p><code>advisors(Consumer&lt;AdvisorSpec&gt; advisorSpecConsumer)</code></p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_advisors"><a class="anchor" href="#_advisors"></a>Advisors</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The <a class="xref page" href="advisors.html">Advisors API</a> provides a flexible and powerful way to intercept, modify, and enhance AI-driven interactions in your Spring applications.</p>
</div>
<div class="paragraph">
<p>A common pattern when calling an AI model with user text is to append or augment the prompt with contextual data.</p>
</div>
<div class="paragraph">
<p>This contextual data can be of different types. Common types include:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><strong>Your own data</strong>: This is data the AI model hasn’t been trained on. Even if the model has seen similar data, the appended contextual data takes precedence in generating the response.</p>
</li>
<li>
<p><strong>Conversational history</strong>: The chat model’s API is stateless. If you tell the AI model your name, it won’t remember it in subsequent interactions. Conversational history must be sent with each request to ensure previous interactions are considered when generating a response.</p>
</li>
</ul>
</div>
<div class="sect2">
<h3 id="_advisor_configuration_in_chatclient"><a class="anchor" href="#_advisor_configuration_in_chatclient"></a>Advisor Configuration in ChatClient</h3>
<div class="paragraph">
<p>The ChatClient fluent API provides an <code>AdvisorSpec</code> interface for configuring advisors. This interface offers methods to add parameters, set multiple parameters at once, and add one or more advisors to the chain.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="hljs-class"><span class="hljs-keyword">interface</span> <span class="hljs-title">AdvisorSpec</span> </span>{
    <span class="hljs-function">AdvisorSpec <span class="hljs-title">param</span><span class="hljs-params">(String k, Object v)</span></span>;
    <span class="hljs-function">AdvisorSpec <span class="hljs-title">params</span><span class="hljs-params">(Map&lt;String, Object&gt; p)</span></span>;
    <span class="hljs-function">AdvisorSpec <span class="hljs-title">advisors</span><span class="hljs-params">(Advisor... advisors)</span></span>;
    <span class="hljs-function">AdvisorSpec <span class="hljs-title">advisors</span><span class="hljs-params">(List&lt;Advisor&gt; advisors)</span></span>;
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock important">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
The order in which advisors are added to the chain is crucial, as it determines the sequence of their execution. Each advisor modifies the prompt or the context in some way, and the changes made by one advisor are passed on to the next in the chain.
</td>
</tr>
</tbody></table>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">ChatClient.builder(chatModel)
    .build()
    .prompt()
    .advisors(
        MessageChatMemoryAdvisor.builder(chatMemory).build(),
        QuestionAnswerAdvisor.builder(vectorStore).build()
    )
    .user(userText)
    .call()
    .content();</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>In this configuration, the <code>MessageChatMemoryAdvisor</code> will be executed first, adding the conversation history to the prompt. Then, the <code>QuestionAnswerAdvisor</code> will perform its search based on the user’s question and the added conversation history, potentially providing more relevant results.</p>
</div>
<div class="paragraph">
<p><a class="xref page" href="retrieval-augmented-generation.html#_questionansweradvisor">Learn about Question Answer Advisor</a></p>
</div>
</div>
<div class="sect2">
<h3 id="_retrieval_augmented_generation"><a class="anchor" href="#_retrieval_augmented_generation"></a>Retrieval Augmented Generation</h3>
<div class="paragraph">
<p>Refer to the <a class="xref page" href="retrieval-augmented-generation.html">Retrieval Augmented Generation</a> guide.</p>
</div>
</div>
<div class="sect2">
<h3 id="_logging"><a class="anchor" href="#_logging"></a>Logging</h3>
<div class="paragraph">
<p>The <code>SimpleLoggerAdvisor</code> is an advisor that logs the <code>request</code> and <code>response</code> data of the <code>ChatClient</code>.
This can be useful for debugging and monitoring your AI interactions.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Spring AI supports observability for LLM and vector store interactions. Refer to the <a class="xref page" href="../observability/index.html">Observability</a> guide for more information.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>To enable logging, add the <code>SimpleLoggerAdvisor</code> to the advisor chain when creating your ChatClient.
It’s recommended to add it toward the end of the chain:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">ChatResponse response = ChatClient.create(chatModel).prompt()
        .advisors(<span class="hljs-keyword">new</span> SimpleLoggerAdvisor())
        .user(<span class="hljs-string">"Tell me a joke?"</span>)
        .call()
        .chatResponse();</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>To see the logs, set the logging level for the advisor package to <code>DEBUG</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>logging.level.org.springframework.ai.chat.client.advisor=DEBUG</pre>
</div>
</div>
<div class="paragraph">
<p>Add this to your <code>application.properties</code> or <code>application.yaml</code> file.</p>
</div>
<div class="paragraph">
<p>You can customize what data from <code>AdvisedRequest</code> and <code>ChatResponse</code> is logged by using the following constructor:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">SimpleLoggerAdvisor(
    Function&lt;AdvisedRequest, String&gt; requestToString,
    Function&lt;ChatResponse, String&gt; responseToString
)</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>Example usage:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java">SimpleLoggerAdvisor customLogger = <span class="hljs-keyword">new</span> SimpleLoggerAdvisor(
    request -&gt; <span class="hljs-string">"Custom request: "</span> + request.userText,
    response -&gt; <span class="hljs-string">"Custom response: "</span> + response.getResult()
);</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This allows you to tailor the logged information to your specific needs.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Be cautious about logging sensitive information in production environments.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_chat_memory"><a class="anchor" href="#_chat_memory"></a>Chat Memory</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The interface <code>ChatMemory</code> represents a storage for chat conversation memory. It provides methods to add messages to a conversation, retrieve messages from a conversation, and clear the conversation history.</p>
</div>
<div class="paragraph">
<p>There is currently one built-in implementation: <code>MessageWindowChatMemory</code>.</p>
</div>
<div class="paragraph">
<p><code>MessageWindowChatMemory</code> is a chat memory implementation that maintains a window of messages up to a specified maximum size (default: 20 messages). When the number of messages exceeds this limit, older messages are evicted, but system messages are preserved. If a new system message is added, all previous system messages are removed from memory. This ensures that the most recent context is always available for the conversation while keeping memory usage bounded.</p>
</div>
<div class="paragraph">
<p>The <code>MessageWindowChatMemory</code> is backed by the <code>ChatMemoryRepository</code> abstraction which provides storage implementations for the chat conversation memory. There are several implementations available, including the <code>InMemoryChatMemoryRepository</code>, <code>JdbcChatMemoryRepository</code>, <code>CassandraChatMemoryRepository</code> and <code>Neo4jChatMemoryRepository</code>.</p>
</div>
<div class="paragraph">
<p>For more details and usage examples, see the <a class="xref page" href="chat-memory.html">Chat Memory</a> documentation.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_implementation_notes"><a class="anchor" href="#_implementation_notes"></a>Implementation Notes</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The combined use of imperative and reactive programming models in <code>ChatClient</code> is a unique aspect of the API.
Often an application will be either reactive or imperative, but not both.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>When customizing the HTTP client interactions of a Model implementation, both the RestClient and the WebClient must be configured.</p>
</li>
</ul>
</div>
<div class="admonitionblock important">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p>Due to a bug in Spring Boot 3.4, the "spring.http.client.factory=jdk" property must be set. Otherwise, it’s set to "reactor" by default, which breaks certain AI workflows like the ImageModel.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="ulist">
<ul>
<li>
<p>Streaming is only supported via the Reactive stack. Imperative applications must include the Reactive stack for this reason (e.g. spring-boot-starter-webflux).</p>
</li>
<li>
<p>Non-streaming is only supportive via the Servlet stack. Reactive applications must include the Servlet stack for this reason (e.g. spring-boot-starter-web) and expect some calls to be blocking.</p>
</li>
<li>
<p>Tool calling is imperative, leading to blocking workflows. This also results in partial/interrupted Micrometer observations (e.g. the ChatClient spans and the tool calling spans are not connected, with the first one remaining incomplete for that reason).</p>
</li>
<li>
<p>The built-in advisors perform blocking operations for standards calls, and non-blocking operations for streaming calls. The Reactor Scheduler used for the advisor streaming calls can be configured via the Builder on each Advisor class.</p>
</li>
</ul>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="../getting-started.html">Getting Started</a></span>
<span class="next"><a href="advisors.html">Advisors</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../index.html">Spring AI</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../index.html">
      1.0.0
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../1.1-SNAPSHOT/index.html">
      1.1.0-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../../../images/spring-ai_reference___img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../../../images/spring-ai_reference___img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../../../images/spring-ai_reference___img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>
</body></html>