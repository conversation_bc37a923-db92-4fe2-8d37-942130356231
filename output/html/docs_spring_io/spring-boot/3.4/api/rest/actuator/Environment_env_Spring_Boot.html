<!DOCTYPE html>

<html><head><title>Environment (env) :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/api/rest/actuator/env.html"/><meta content="2025-06-04T19:23:15.776861" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../../../../../images/spring-boot___img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>
<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../../../reference/packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../../../reference/packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../../../reference/packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../../../reference/packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../../../reference/packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Environment (env)">
<div class="toc-menu"><h3>Environment (env)</h3><ul><li data-level="1"><a href="#env.entire">Retrieving the Entire Environment</a></li><li data-level="2"><a href="#env.entire.response-structure">Response Structure</a></li><li data-level="1"><a href="#env.single-property">Retrieving a Single Property</a></li><li data-level="2"><a href="#env.single-property.response-structure">Response Structure</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/env.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../../index.html">Spring Boot</a></li>
<li>Rest APIs</li>
<li><a href="index.html">Actuator</a></li>
<li><a href="env.html">Environment (<code>env</code>)</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../../api/rest/actuator/env.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Environment (<code>env</code>)</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Environment (env)</h3><ul><li data-level="1"><a href="#env.entire">Retrieving the Entire Environment</a></li><li data-level="2"><a href="#env.entire.response-structure">Response Structure</a></li><li data-level="1"><a href="#env.single-property">Retrieving a Single Property</a></li><li data-level="2"><a href="#env.single-property.response-structure">Response Structure</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>The <code>env</code> endpoint provides information about the application’s <code>Environment</code>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="env.entire"><a class="anchor" href="#env.entire"></a>Retrieving the Entire Environment</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To retrieve the entire environment, make a <code>GET</code> request to <code>/actuator/env</code>, as shown in the following curl-based example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-bash hljs" data-lang="bash">$ curl <span class="hljs-string">'http://localhost:8080/actuator/env'</span> -i -X GET</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The resulting response is similar to the following:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight nowrap"><code class="language-http hljs" data-lang="http">HTTP/1.1 <span class="hljs-number">200</span> OK
<span class="hljs-attribute">Content-Type</span>: application/vnd.spring-boot.actuator.v3+json
<span class="hljs-attribute">Content-Length</span>: 80055

<span class="json">{
  <span class="hljs-attr">"activeProfiles"</span> : [ ],
  <span class="hljs-attr">"defaultProfiles"</span> : [ <span class="hljs-string">"default"</span> ],
  <span class="hljs-attr">"propertySources"</span> : [ {
    <span class="hljs-attr">"name"</span> : <span class="hljs-string">"servletContextInitParams"</span>,
    <span class="hljs-attr">"properties"</span> : { }
  }, {
    <span class="hljs-attr">"name"</span> : <span class="hljs-string">"systemProperties"</span>,
    <span class="hljs-attr">"properties"</span> : {
      <span class="hljs-attr">"java.specification.version"</span> : {
        <span class="hljs-attr">"value"</span> : <span class="hljs-string">"17"</span>
      },
      <span class="hljs-attr">"java.class.path"</span> : {
        <span class="hljs-attr">"value"</span> : <span class="hljs-string">"/home/<USER>/.gradle/caches/8.14/workerMain/gradle-worker.jar:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot-actuator-autoconfigure/build/classes/java/test:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot-actuator-autoconfigure/build/resources/test:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot-actuator-autoconfigure/build/classes/java/main:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot-actuator-autoconfigure/build/resources/main:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot-tools/spring-boot-test-support/build/libs/spring-boot-test-support-3.4.6.jar:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot-actuator/build/libs/spring-boot-actuator-3.4.6.jar:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot-autoconfigure/build/libs/spring-boot-autoconfigure-3.4.6.jar:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot-test/build/libs/spring-boot-test-3.4.6.jar:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot/build/libs/spring-boot-3.4.6.jar:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot/build/libs/spring-boot-3.4.6-test-fixtures.jar:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot/build/classes/kotlin/main:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot/build/classes/java/main:/home/<USER>/work/spring-boot/spring-boot/spring-boot-project/spring-boot/build/resources/main:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.dataformat/jackson-dataformat-xml/2.18.4/ee1e6970ab2d3d3cf29af8716724e5f7aa3e8866/jackson-dataformat-xml-2.18.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.flywaydb/flyway-core/10.20.1/27c35cad285433947ef986c615ce2742f69145de/flyway-core-10.20.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-wavefront/1.14.7/fcbc9b4807365b84a24a1122d6837b98f4f01e46/micrometer-registry-wavefront-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-tracing-reporter-wavefront/1.4.6/ad654b539f0e7e79bf093285cafb091b7fe8b9ce/micrometer-tracing-reporter-wavefront-1.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.wavefront/wavefront-internal-reporter-java/1.7.16/135f9be9f149a6f0614db155ddf51e216d0d2891/wavefront-internal-reporter-java-1.7.16.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.wavefront/wavefront-sdk-java/3.4.3/48a767aa5aa56b56e09c88e8be87de8707cafe77/wavefront-sdk-java-3.4.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.datatype/jackson-datatype-jsr310/2.18.4/3ef73abb0019203a8b626684edaa3e2c2c2def53/jackson-datatype-jsr310-2.18.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-cassandra/4.4.6/e5f2f2c5d8ceb4d5041e2c897cbaceb03f295433/spring-data-cassandra-4.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.cassandra/java-driver-query-builder/4.18.1/a771500f0d15729c740c43711f684a605157d5f7/java-driver-query-builder-4.18.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.cassandra/java-driver-core/4.18.1/1b80caa76a9b4f88b5adf866799aaa4a80f378f7/java-driver-core-4.18.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-elasticsearch/5.4.6/74fdb44f848470e3ae011408652cc86f9101aacb/spring-data-elasticsearch-5.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.dataformat/jackson-dataformat-toml/2.18.4/9ee1f5aefe724fbfc30b60c2c5e36266cab22b17/jackson-dataformat-toml-2.18.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.media/jersey-media-json-jackson/3.1.10/fe379d7a9647ce4c38704457956d1a3630f4188/jersey-media-json-jackson-3.1.10.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.module/jackson-module-jakarta-xmlbind-annotations/2.18.4/13006546911f063512cf47dfa2766e0fce2dc19a/jackson-module-jakarta-xmlbind-annotations-2.18.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.dataformat/jackson-dataformat-yaml/2.18.4/119572c5ecb7d9a08e1cbf3cebeecbe2a496b93f/jackson-dataformat-yaml-2.18.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-rest-webmvc/4.4.6/2993e5ccf62e4ba4762a9076d7ae4c1a83f9adc8/spring-data-rest-webmvc-4.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-rest-core/4.4.6/47c78a69aa926e4091c5bf2aad4f1a029ccc7201/spring-data-rest-core-4.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.datatype/jackson-datatype-jdk8/2.18.4/15b878994d386379068978118718b4991b1b0e4c/jackson-datatype-jdk8-2.18.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-core/2.18.4/cdcc3706edf9b847a55aecad4d1ddb87250882ca/jackson-core-2.18.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.kafka/kafka-streams/3.8.1/9e054a4e7b88bc80f069e22045826013b7b697ec/kafka-streams-3.8.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-annotations/2.18.4/21e5645ac25cd6c281cfc9e51df031055d26ffd6/jackson-annotations-2.18.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/activemq-broker/6.1.6/edece27364088addb26434f20dfad08af0baa5c3/activemq-broker-6.1.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-couchbase/5.4.6/450e307dea653f0fcbaf26436ac4d9a72eabdfe2/spring-data-couchbase-5.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.restdocs/spring-restdocs-mockmvc/3.0.3/ac8256fceb81f1022c80b2de1541e4618874d437/spring-restdocs-mockmvc-3.0.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.restdocs/spring-restdocs-webtestclient/3.0.3/448b2289eebf06828d02d5dfded008c5ec8b33f4/spring-restdocs-webtestclient-3.0.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.restdocs/spring-restdocs-core/3.0.3/8d3f5bcc661cda01b5d70672a1112ed9e83fbbb9/spring-restdocs-core-3.0.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-databind/2.18.4/7533a629c563ef6a5e2424d6ff3654155abea6b0/jackson-databind-2.18.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/ch.qos.logback/logback-classic/1.5.18/fc371f3fc97a639de2d67947cffb7518ec5e3d40/logback-classic-1.5.18.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/artemis-jakarta-server/2.37.0/b2395f7fdd9f692987d81836c59be11f72312311/artemis-jakarta-server-2.37.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/artemis-server/2.37.0/d830695125020e520303a7d8e91990c2dacf7eac/artemis-server-2.37.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.github.ben-manes.caffeine/caffeine/3.1.8/24795585df8afaf70a2cd534786904ea5889c047/caffeine-3.1.8.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.hazelcast/hazelcast-spring/5.5.0/be80826e47712a9f1cf66b9a3f790a6584810fb2/hazelcast-spring-5.5.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.hazelcast/hazelcast/5.5.0/e838d3f54dc1900310b5caff87ff25a5d3ed0d4b/hazelcast-5.5.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.zaxxer/HikariCP/5.1.0/8c96e36c14461fc436bb02b264b96ef3ca5dca8c/HikariCP-5.1.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.lettuce/lettuce-core/6.4.2.RELEASE/8969c20697c74b71288d4d5e69b0fec4047d3d6d/lettuce-core-6.4.2.RELEASE.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.kafka/spring-kafka/3.3.6/7b80a444ab74745e4f5c272675eeecabfe0caea4/spring-kafka-3.3.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-jakarta9/1.14.7/8a9d33eee83f2af28a75bd85b739a235f309e24b/micrometer-jakarta9-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-observation-test/1.14.7/a47acdce33c263222db5d7da3369b4b777fa5209/micrometer-observation-test-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-tracing-bridge-brave/1.4.6/812f762dc6db3da7f1f5bc3ea303826452449d4b/micrometer-tracing-bridge-brave-1.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-tracing-bridge-otel/1.4.6/f36fae0271695a400a5278d77e6a5ede731e51a1/micrometer-tracing-bridge-otel-1.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-tracing/1.4.6/d5b357e1a83a3dfc9650293a604f4e40521e88bc/micrometer-tracing-1.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.amqp/spring-rabbit/3.2.5/706d36de584e349bb33076f26f8f6b258ce5a1d2/spring-rabbit-3.2.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.batch/spring-batch-core/5.2.2/141649d81fae24922d98775cd4b4aecc254528e6/spring-batch-core-5.2.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.graphql/spring-graphql/1.3.5/4b7cc8230111d531a9eec19592d56a2e9e1d71e6/spring-graphql-1.3.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-jpa/3.4.6/93e7dedcd8c9904a5bc40e1bbf7bc4cafd0de5d8/spring-data-jpa-3.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-mongodb/4.4.6/715f32f06f3caa6e3ae696f6aff6be4b273e400a/spring-data-mongodb-4.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-ldap/3.4.6/76aae7ab6d6947c56efa2cff80972c7e29b63c40/spring-data-ldap-3.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-webmvc/6.2.7/a1954b3a51f585c6b8582831661fec981d2571e/spring-webmvc-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.integration/spring-integration-jmx/6.4.5/c944a4a29c45a14ccf6dd9114b4de539edd78377/spring-integration-jmx-6.4.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.integration/spring-integration-core/6.4.5/cce55b74bd36a37113ec401542fab45a2c5a6ab4/spring-integration-core-6.4.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-config/6.4.6/79665e745bfee01a15c90bf0695d5b5b45d73f9e/spring-security-config-6.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-oauth2-resource-server/6.4.6/e85a6c0f45d31d704dac06dad5aeabf0f3334a33/spring-security-oauth2-resource-server-6.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-saml2-service-provider/6.4.6/53dbb4ff15439fa6fc07c593b4c6e5f0fb7fa9b2/spring-security-saml2-service-provider-6.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-test/6.4.6/83edc71406630647c88d2d67dbf5f46529335d91/spring-security-test-6.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-web/6.4.6/1cfc44ea24fd1974ecd7f4c8a3e1d6a2bf9dd1c1/spring-security-web-6.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-redis/3.4.6/8155a83a66b390838866f753b8c82eb1cab7268a/spring-data-redis-3.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-context-support/6.2.7/eddbbcdf8cf29a905ed1605ed8e73cba03d7760a/spring-context-support-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-keyvalue/3.4.6/20db46d47e095bb70918d5b3e3bbf74451f32fcb/spring-data-keyvalue-3.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-oauth2-jose/6.4.6/bed8110d57a302c4e9d8ba212b6f7652aaa62dee/spring-security-oauth2-jose-6.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-oauth2-core/6.4.6/2e32f0638247c6bf818106453080c50bbb7e787d/spring-security-oauth2-core-6.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-core/6.4.6/3b07d89de8182537d649a2e3e95f60c45be99702/spring-security-core-6.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.hateoas/spring-hateoas/2.4.1/86a69fe432361c0697aff89b4c8f293edffca26d/spring-hateoas-2.4.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.plugin/spring-plugin-core/3.0.0/d56aa02dd7272dca30aa598dc8b72e823227046a/spring-plugin-core-3.0.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-context/6.2.7/e0f3850bba7ff52016429e8d2fa39b5107f1fde6/spring-context-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-jms/6.2.7/61833dacab74937295afb8a253498975860dbd6f/spring-jms-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.cache2k/cache2k-micrometer/2.6.1.Final/58731da010cc872915e012325027954bdc3122c8/cache2k-micrometer-2.6.1.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.hibernate.orm/hibernate-micrometer/6.6.15.Final/159b8440c12d32c2ef753892c0a34e7171174a0f/hibernate-micrometer-6.6.15.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-stackdriver/1.14.7/b0eab7b0fed125a2fd9d6557f38f33567427482f/micrometer-registry-stackdriver-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.ext/jersey-micrometer/3.1.10/99c62fbc9a0a70486674f2b81260fc2f172be90a/jersey-micrometer-3.1.10.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-appoptics/1.14.7/a41eaf60edcbb6579e602ab183ce84d10f1ae328/micrometer-registry-appoptics-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-atlas/1.14.7/b143a919c809dad0a252b51db03dfac47db11fd4/micrometer-registry-atlas-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-datadog/1.14.7/3094c78dbe145d15ba4806b39c7d2fded1d0b6ce/micrometer-registry-datadog-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-dynatrace/1.14.7/717147c5e61ce2cc98cfc728db47d14abd216456/micrometer-registry-dynatrace-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-elastic/1.14.7/c0cebf9b3ca8f4484ae4fb2c6f473bd122f6ee9c/micrometer-registry-elastic-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-ganglia/1.14.7/d9aefa9bfc367f8241ffaf16060292ce0a9fa02d/micrometer-registry-ganglia-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-graphite/1.14.7/553915dbf081638c48a8b0032c5f4639906615c4/micrometer-registry-graphite-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-humio/1.14.7/eb9a23f293d7d366ba3d8ef1e58d8dcf75fb3b37/micrometer-registry-humio-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-influx/1.14.7/1f31d8b7e08f56af50fedd1db918358c037c2e89/micrometer-registry-influx-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-jmx/1.14.7/ce94a6624239aba16f0377835408880d3863353e/micrometer-registry-jmx-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-kairos/1.14.7/3a710abf27877b19c1679e553b73ee18f2c54f7b/micrometer-registry-kairos-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-new-relic/1.14.7/df25b8ed93caf41d44d9385d82f01b868c52dde4/micrometer-registry-new-relic-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-otlp/1.14.7/8326530107c79e67bace7b89d0b3dd8a2dc257d0/micrometer-registry-otlp-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-prometheus/1.14.7/511840cf0c5372a28ab5f5046d6c8c6bdcc1ad2d/micrometer-registry-prometheus-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-prometheus-simpleclient/1.14.7/f698688d4bc13c4bd9375f3753429c3de1518ad4/micrometer-registry-prometheus-simpleclient-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-signalfx/1.14.7/64add24c83a02d79e3cd40ff20a3ed4a9942aa70/micrometer-registry-signalfx-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-registry-statsd/1.14.7/4b6fff27870b2aaa87edbeb06efab74430108f64/micrometer-registry-statsd-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-core/1.14.7/8743fcff42005239e0acaf8fd56b509d5b2fd1d2/micrometer-core-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-webflux/6.2.7/35263cd6f459c7d170a5b45f51ff718b4f11de85/spring-webflux-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-web/6.2.7/1bd08eba2f2fd69eef49473e42fe124a20e7f844/spring-web-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-observation/1.14.7/f5a65b5ca7ddb52669efb6c2979181cdbdfbd76d/micrometer-observation-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.prometheus/simpleclient_pushgateway/0.16.0/65935d9855ece6f85c21ad38634703d0917bf88c/simpleclient_pushgateway-0.16.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.zipkin.reporter2/zipkin-reporter-brave/3.4.3/ce81e889a154a86471fdc343d09ec701d78a8db3/zipkin-reporter-brave-3.4.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.zipkin.reporter2/zipkin-sender-urlconnection/3.4.3/c61751c39e7e9d7604d933bdab489e8a684784c1/zipkin-sender-urlconnection-3.4.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-exporter-zipkin/1.43.0/a7b09f8bb96e3a592e2682b4d25f11429667309e/opentelemetry-exporter-zipkin-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-exporter-otlp/1.43.0/6049b76571db09b2694ce96a177b2ee1322a4588/opentelemetry-exporter-otlp-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.projectreactor.netty/reactor-netty-http/1.2.6/a7586caf765cfc77caaa37c33431efe700742218/reactor-netty-http-1.2.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.r2dbc/r2dbc-pool/1.0.2.RELEASE/c6a07e2e312e9bc8a897759a1004023187451c4f/r2dbc-pool-1.0.2.RELEASE.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.r2dbc/r2dbc-proxy/1.1.6.RELEASE/9a41d748a745db2f4b9c3f40a523f03864cf90e7/r2dbc-proxy-1.1.6.RELEASE.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.r2dbc/r2dbc-h2/1.0.0.RELEASE/b99b52c87e7f32136f58131ad7b7a3e2eb168f75/r2dbc-h2-1.0.0.RELEASE.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.r2dbc/r2dbc-spi/1.0.0.RELEASE/e3d15b2d27fdb8fdb76a181b21d5c752adf1d165/r2dbc-spi-1.0.0.RELEASE.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/activemq-openwire-legacy/6.1.6/97650c33b91f5bc9d7793e6b2fa9bf5e6e91576c/activemq-openwire-legacy-6.1.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/activemq-client/6.1.6/64c52537aebd2b88cb3c39e2aa2c5e833fc76772/activemq-client-6.1.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/artemis-jakarta-service-extensions/2.37.0/434b61cb824db57fcb7c7f41f80472fc8a638a1b/artemis-jakarta-service-extensions-2.37.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/artemis-jakarta-client/2.37.0/313a3342089f9aa925a5d71af015b8cff69b64a2/artemis-jakarta-client-2.37.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.jms/jakarta.jms-api/3.1.0/e194cf91a3f908e4846542849ac11a8e0b3c68ad/jakarta.jms-api-3.1.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.hibernate.orm/hibernate-core/6.6.15.Final/9ff5f41fc240a56d7076193094602c62a5294ea5/hibernate-core-6.6.15.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.persistence/jakarta.persistence-api/3.1.0/66901fa1c373c6aff65c13791cc11da72060a8d6/jakarta.persistence-api-3.1.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.undertow/undertow-servlet/2.3.18.Final/d54a5f19d47abc27d11610f8af7dcb9eda000992/undertow-servlet-2.3.18.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty.ee10/jetty-ee10-webapp/12.0.21/ffb814e0b379f65a7819bc5dca9c888196f3ae94/jetty-ee10-webapp-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty.ee10/jetty-ee10-servlet/12.0.21/d36926cd7cd8b681a208c2b8f9dd8ffca70b4076/jetty-ee10-servlet-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.servlet/jakarta.servlet-api/6.0.0/abecc699286e65035ebba9844c03931357a6a963/jakarta.servlet-api-6.0.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/javax.cache/cache-api/1.1.1/c56fb980eb5208bfee29a9a5b9d951aba076bd91/cache-api-1.1.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/artemis-jdbc-store/2.37.0/722d7827e01adf29b3bf60a18134d45cea0b9698/artemis-jdbc-store-2.37.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.commons/commons-dbcp2/2.12.0/45698ece9fda230d0b1c96282c03ce1ebbd6c3ce/commons-dbcp2-2.12.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.kafka/kafka-clients/3.8.1/fd79e3aa252c6d818334e9c0bac8166b426e498c/kafka-clients-3.8.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.logging.log4j/log4j-to-slf4j/2.24.3/da1143e2a2531ee1c2d90baa98eb50a28a39d5a7/log4j-to-slf4j-2.24.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.logging.log4j/log4j-api/2.24.3/b02c125db8b6d295adf72ae6e71af5d83bce2370/log4j-api-2.24.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.tomcat.embed/tomcat-embed-core/10.1.41/bcf9c79a4508fd36c93babc83823c09b3aa2d510/tomcat-embed-core-10.1.41.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.tomcat.embed/tomcat-embed-el/10.1.41/5ca28d87f558ce31b9952dc9592f46633dc5d7a3/tomcat-embed-el-10.1.41.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.tomcat/tomcat-jdbc/10.1.41/9f1d86cbaf0cb7535d2090a4d152aeea70899681/tomcat-jdbc-10.1.41.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-aspects/6.2.7/52461ec85ce1dc6e80fdca92c85bab9faebc0509/spring-aspects-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.aspectj/aspectjweaver/1.9.24/9b5aeb0cea9f958b9c57fb80e62996e95a3e9379/aspectjweaver-1.9.24.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.cache2k/cache2k-spring/2.6.1.Final/cd312efb1645de91bdd8571b98273cb0acf52d15/cache2k-spring-2.6.1.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.angus/angus-mail/2.0.3/56f811522d693700013768db83b741a65da6c61c/angus-mail-2.0.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty.http2/jetty-http2-server/12.0.21/ff6707ce6695780898db2f5afffd3470de7f06f9/jetty-http2-server-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-ee/12.0.21/f2c4fcce13902e273b4d2cd2bbe4be06788a1a5e/jetty-ee-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-session/12.0.21/808fb9860f828156ce97df93607949f36e6c61c4/jetty-session-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-security/12.0.21/7fc2115c1871a970a3ba853b8da6b943e572036c/jetty-security-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-server/12.0.21/ff23efdde64a6b1226b41e27899151fb85957699/jetty-server-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/co.elastic.clients/elasticsearch-java/8.15.5/c66b18da40aba8ce2cf79ce848abc8c2fc8b3eaa/elasticsearch-java-8.15.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.elasticsearch.client/elasticsearch-rest-client/8.15.5/b969e06efea1424c29080c14a8558cf297efe1cf/elasticsearch-rest-client-8.15.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.ext/jersey-spring6/3.1.10/d1c894e01ea70cb41fb10f38974f7e56fc4a1538/jersey-spring6-3.1.10.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.containers/jersey-container-servlet-core/3.1.10/a2106c310bfc6847e5ed5ca85b306645ff6a0c66/jersey-container-servlet-core-3.1.10.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.core/jersey-server/3.1.10/c0c631cfa916560b24aa3f0af1550aec6a87e69f/jersey-server-3.1.10.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.hibernate.validator/hibernate-validator/8.0.2.Final/220e64815dd87535525331de20570017f899eb13/hibernate-validator-8.0.2.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.influxdb/influxdb-java/2.24/638b1ca7c63aa94ce7024fc7bebb1e458a3fb6f6/influxdb-java-2.24.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter/5.11.4/a699f024a4a4706b36bddbeb42d499aff9e09379/junit-jupiter-5.11.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-junit-jupiter/5.14.2/3cfc377d4bb9fe729f3dd9098d9a9b27da58324a/mockito-junit-jupiter-5.14.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-params/5.11.4/e4c86fbe2a39c60c6b87260ef7f7e7c1a1906481/junit-jupiter-params-5.11.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-engine/5.11.4/dc10ec209623986a68ea07f67cdc7d2a65a60355/junit-jupiter-engine-5.11.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-api/5.11.4/308315b28e667db4091b2ba1f7aa220d1ddadb97/junit-jupiter-api-5.11.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.junit.platform/junit-platform-engine/1.11.4/21f61b123ad6ac8f7e73971bff3a096c8d8e1cd0/junit-platform-engine-1.11.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.junit.platform/junit-platform-commons/1.11.4/8898eea3ed0da2641548d602c3e308804f166303/junit-platform-commons-1.11.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.junit.platform/junit-platform-launcher/1.11.4/3d83c201899d8c5e74e1a5d628eab900342a0e48/junit-platform-launcher-1.11.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.liquibase/liquibase-core/4.29.2/bc4d7a9247ee41a1a467aae2fbe69094e354cac5/liquibase-core-4.29.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.mongodb/mongodb-driver-reactivestreams/5.2.1/eab6074c877b0e3a36984bb8efb89f0ce4c7c314/mongodb-driver-reactivestreams-5.2.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.mongodb/mongodb-driver-sync/5.2.1/c837fdf64de3e0211553e1f28ae433ab245fbbc2/mongodb-driver-sync-5.2.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.neo4j.driver/neo4j-java-driver/5.28.5/de9ca1b7f8ef46a3903ff11f7820a9205268fcf3/neo4j-java-driver-5.28.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.quartz-scheduler/quartz/2.3.2/18a6d6b5a40b77bd060b34cb9f2acadc4bae7c8a/quartz-2.3.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-orm/6.2.7/a8966e1cf3591397b2b1381742877d55367f0b21/spring-orm-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-jdbc/6.2.7/9c2a6ac46e3af69b226401678591b6fe0350a617/spring-jdbc-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-messaging/6.2.7/4d969084e3b1915ce04d8768d89fe9c092191500/spring-messaging-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.session/spring-session-core/3.4.3/f1d22526eeb0a4702fd3acebcc1d089d24d3c737/spring-session-core-3.4.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/redis.clients/jedis/5.2.0/a3e3905dcc06a48738783883d13f43873df22e31/jedis-5.2.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-exporter-otlp-common/1.43.0/230fc6ddbd76fedea346ce7dff5d4fa2d13f7f4a/opentelemetry-exporter-otlp-common-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-exporter-sender-okhttp/1.43.0/c583b782dc2e8e631d590f1734bf0767769d8507/opentelemetry-exporter-sender-okhttp-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-exporter-common/1.43.0/7ce251b8a5eae17b744faaaced857f4a3e79aeb6/opentelemetry-exporter-common-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.projectreactor/reactor-test/3.7.6/c76b87256b340452f8b8ec714a2fbdd9b2236f44/reactor-test-3.7.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.prometheus/prometheus-metrics-exposition-formats/1.3.6/13189bdc1931415f03c8993798902cec8e54fb10/prometheus-metrics-exposition-formats-1.3.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/mockwebserver/4.12.0/3bc54cc5c87cd632031d0881f3c313d3799a1476/mockwebserver-4.12.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.jayway.jsonpath/json-path/2.9.0/37fe2217f577b0b68b18e62c4d17a8858ecf9b69/json-path-2.9.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.undertow/undertow-core/2.3.18.Final/981cd13a9f0a626c9365a83d7f8ce1e932d5e4de/undertow-core-2.3.18.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-oxm/6.2.7/2cdb24eff2cd841c8e1a93809e3709708c581a52/spring-oxm-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.jaxb/jaxb-runtime/4.0.5/ca84c2a7169b5293e232b9d00d1e4e36d4c3914a/jaxb-runtime-4.0.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.jaxb/jaxb-core/4.0.5/7b4b11ea5542eea4ad55e1080b23be436795b3/jaxb-core-4.0.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.xml.bind/jakarta.xml.bind-api/4.0.2/6cd5a999b834b63238005b7144136379dc36cad2/jakarta.xml.bind-api-4.0.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.aspectj/aspectjrt/1.9.24/c3e1f7f219500466e49fa963a2b9870cc2480cb/aspectjrt-1.9.24.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.assertj/assertj-core/3.26.3/d26263eb7524252d98e602fc6942996a3195e29/assertj-core-3.26.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.awaitility/awaitility/4.2.2/7336242073ebf83fe034e42b46a403c5501b63c9/awaitility-4.2.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.cache2k/cache2k-core/2.6.1.Final/7e333caaafa2bf4e489b58537b5c4218a4e6505/cache2k-core-2.6.1.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.cache2k/cache2k-api/2.6.1.Final/6afd23d7897fff56515f0cefa6a846bdebe10bce/cache2k-api-2.6.1.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/junit/junit/4.13.2/8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12/junit-4.13.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.hamcrest/hamcrest-library/2.2/cf530c8a0bc993487c64e940ae639bb4a6104dc6/hamcrest-library-2.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.hamcrest/hamcrest-core/2.2/3f2bd07716a31c395e2837254f37f21f0f0ab24b/hamcrest-core-2.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.hamcrest/hamcrest/2.2/1820c0968dba3a11a1b30669bb1f01978a91dedc/hamcrest-2.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.hsqldb/hsqldb/2.7.3/85b49338b36f3051d217295596cf92beb92e4bfb/hsqldb-2.7.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-core/5.14.2/f7bf936008d7664e2002c3faf0c02071c8d10e7c/mockito-core-5.14.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.skyscreamer/jsonassert/1.5.3/aaa43e0823d2a0e106e8754d6a9c4ab24e05e9bc/jsonassert-1.5.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-core-test/6.2.7/eaa108c9e1732bdd15ee27f6ed6ca70d52bb751a/spring-core-test-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-test/6.2.7/77af0b9d6b218d149f77e63ff5ec6349b5d8abdd/spring-test-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.yaml/snakeyaml/2.3/936b36210e27320f920536f695cf1af210c44586/snakeyaml-2.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.management.j2ee/jakarta.management.j2ee-api/1.1.4/dbbe7575f97efd0b04f3a8455cf82c256c853055/jakarta.management.j2ee-api-1.1.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.transaction/jakarta.transaction-api/2.0.1/51a520e3fae406abb84e2e1148e6746ce3f80a1a/jakarta.transaction-api-2.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opensaml/opensaml-saml-impl/4.0.1/995986fd848ede1443469f3aff1f82b740224262/opensaml-saml-impl-4.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opensaml/opensaml-saml-api/4.0.1/2205aba935f4da468382a3dc5f32c3821ec1564c/opensaml-saml-api-4.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opensaml/opensaml-soap-impl/4.0.1/38bfaf5fc189774e94ead218bd1c754da295c226/opensaml-soap-impl-4.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opensaml/opensaml-profile-api/4.0.1/bece5f6d30d4051e6eeaf2b88dd1e5a13f6b28b7/opensaml-profile-api-4.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opensaml/opensaml-soap-api/4.0.1/d8e11e31cb5164788a530478e1831969e94a38b6/opensaml-soap-api-4.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opensaml/opensaml-xmlsec-impl/4.0.1/efa15ba85127ac3b20c75b8d4f04c7e92325a00a/opensaml-xmlsec-impl-4.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opensaml/opensaml-xmlsec-api/4.0.1/edb4365d3d183933cf0d0b31966ea352b8d20c60/opensaml-xmlsec-api-4.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opensaml/opensaml-security-impl/4.0.1/64568e9aa8bd7bcd76983e462f9eb2c3dcacbdce/opensaml-security-impl-4.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opensaml/opensaml-security-api/4.0.1/f3d33ca18cde2a7c7e3643aeca9f03974be9577d/opensaml-security-api-4.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opensaml/opensaml-messaging-api/4.0.1/eb9c9971f6bd2a6681a2a692a1f29a35874de389/opensaml-messaging-api-4.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opensaml/opensaml-core/4.0.1/ec3d1734137d6ccabba7d6d5e149f571beeaa673/opensaml-core-4.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.ldap/spring-ldap-core/3.2.12/eb68e11436648051ef9d884af4e6fadf26d5a17a/spring-ldap-core-3.2.12.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-tx/6.2.7/6cf5d200c297fa8cc0b4cf39c1a20b310d2c3d52/spring-tx-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.amqp/spring-amqp/3.2.5/450307954c2fde84d81d2418dc4c20fd95b6425e/spring-amqp-3.2.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-commons/3.4.6/5c4231d14193a857956344fa7b89f9d61a7376b1/spring-data-commons-3.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-aop/6.2.7/3df3ee9217cb62e65382718351e56f1eeb76e74c/spring-aop-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-beans/6.2.7/64119950b73943f67196705e43acddd7bf714c45/spring-beans-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.batch/spring-batch-infrastructure/5.2.2/19e109ec420e6e6c191b7a5f3262b7fded4c348d/spring-batch-infrastructure-5.2.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-expression/6.2.7/fa525916c85df246fcb70c771d039523dd6f12d9/spring-expression-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-core/6.2.7/525a228b8b4323568b7aa9e49c1e1df27838adf1/spring-core-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.vaadin.external.google/android-json/0.0.20131108.vaadin1/fa26d351fe62a6a17f5cda1287c1c6110dec413f/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.mchange/c3p0/0.9.5.5/37dfc3021e5589d65ff2ae0becf811510b87ab01/c3p0-0.9.5.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven/maven-resolver-provider/3.9.4/d5e38634c1251e000f7718437973a730346bc336/maven-resolver-provider-3.9.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven/maven-model-builder/3.9.4/b12e45736ddfb51f648e1579877a293659e5a96e/maven-model-builder-3.9.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven.resolver/maven-resolver-connector-basic/1.9.14/ffeb368f5587513b29ef9771514ec36d246e65f5/maven-resolver-connector-basic-1.9.14.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven.resolver/maven-resolver-impl/1.9.14/2aa090aca572f7061e5c57f714ef0445bbb4555f/maven-resolver-impl-1.9.14.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven.resolver/maven-resolver-transport-http/1.9.14/1eb4acdce7f21526bd06654b3d7b1edf80370ce8/maven-resolver-transport-http-1.9.14.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven.resolver/maven-resolver-spi/1.9.14/a190c3d49791240c87799f29848505fc9fd8be11/maven-resolver-spi-1.9.14.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven.resolver/maven-resolver-util/1.9.14/ea6fd2997903700191711dee4448746ed30fe4b9/maven-resolver-util-1.9.14.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven.resolver/maven-resolver-api/1.9.14/bd5b7cd9d15c2efcab4d8c8215e769523e8843e4/maven-resolver-api-1.9.14.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/context-propagation/1.1.3/19cea055eed20e80875705fcf9943ca8e978cf92/context-propagation-1.1.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.core/jersey-client/3.1.10/35d87d5a4af28c80e380a00efba94d9af8998849/jersey-client-3.1.10.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.inject/jersey-hk2/3.1.10/85fa449300069937189bf0cfeb6da765e5e3854a/jersey-hk2-3.1.10.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.core/jersey-common/3.1.10/d9d17fdec864f33de58efe240ec53816f95c03f3/jersey-common-3.1.10.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.hk2/hk2/3.0.6/12187beb6321e6a7fdc2ef4d196e16c8c58a3a1/hk2-3.0.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.hk2/hk2-core/3.0.6/78dfb2117c3148e357a0cec8f514e60bbf689efd/hk2-core-3.0.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.hk2/hk2-runlevel/3.0.6/3c347e9bb20f13d98b72ae2b4fc84f14bcbb187e/hk2-runlevel-3.0.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.hk2/hk2-locator/3.0.6/92d5c92c9f23bea4b8681c6f8d6ba3d708619f81/hk2-locator-3.0.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.hk2/hk2-api/3.0.6/5a5152dea2c43384f5c07985eb27140134074ecb/hk2-api-3.0.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.hk2/hk2-utils/3.0.6/b3187d0673c0fd52de197e52c62545c34d4eda29/hk2-utils-3.0.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.inject/jakarta.inject-api/2.0.1/4c28afe1991a941d7702fe1362c365f0a8641d1e/jakarta.inject-api-2.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opensaml/opensaml-storage-api/4.0.1/4e46a7f965ac9f91976b0f298fd4d4e69e9056db/opensaml-storage-api-4.0.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.shibboleth.utilities/java-support/8.0.0/298f946e93922d789b6231599a446cea9dbbe80e/java-support-8.0.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.santuario/xmlsec/2.1.4/cb43326f02e3e77526c24269c8b5d3cc3f7f6653/xmlsec-2.1.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.cloud/google-cloud-monitoring/3.54.0/62d79caf8c62132cb883652449365237a25c9a71/google-cloud-monitoring-3.54.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.auth/google-auth-library-oauth2-http/1.29.0/2a42aead6cdc5d2cd22cdda1b9d7922e6135240f/google-auth-library-oauth2-http-1.29.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.http-client/google-http-client-gson/1.45.0/98af6041ca1d4654a2210cf1c6c06a9401ac6c90/google-http-client-gson-1.45.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.http-client/google-http-client/1.45.0/63eb7643e01797d6aa67c75ae31ffcc6c365db18/google-http-client-1.45.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.httpcomponents/httpclient/4.5.14/1194890e6f56ec29177673f2f12d0b8e627dec98/httpclient-4.5.14.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/commons-codec/commons-codec/1.17.2/cd6bb9d856db5f61871a94d5801efd0b93b7fcb2/commons-codec-1.17.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opencensus/opencensus-contrib-http-util/0.31.1/3c13fc5715231fadb16a9b74a44d9d59c460cfa8/opencensus-contrib-http-util-0.31.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/guava/33.3.1-jre/852f8b363da0111e819460021ca693cacca3e8db/guava-33.3.1-jre.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.api.grpc/proto-google-cloud-monitoring-v3/3.54.0/67b27f202d3f2e06492a4776b7f8eccb56f6fbf0/proto-google-cloud-monitoring-v3-3.54.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.findbugs/jsr305/3.0.2/25ea2e8b0c338a877313bd4672d3fe056ea78f0d/jsr305-3.0.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.dropwizard.metrics/metrics-graphite/4.2.30/a3496cf1147482e8974f322296e92e421f0eff13/metrics-graphite-4.2.30.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.dropwizard.metrics/metrics-jmx/4.2.30/71068f43e2aaca144a04ace266fbf62160ee253b/metrics-jmx-4.2.30.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.dropwizard.metrics/metrics-core/4.2.30/4c0093ffbe0d6a90253e47277ce6dc4f759aff7b/metrics-core-4.2.30.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.velocity/velocity-engine-core/2.2/68d899cb70cd27d495562fa808feb2da4926d38f/velocity-engine-core-2.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven.resolver/maven-resolver-named-locks/1.9.14/71cf74d91b4f6de1bff8fc1fafb401d9ac42b1b8/maven-resolver-named-locks-1.9.14.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.zaxxer/HikariCP-java7/2.4.13/3e441eddedb374d4de8e3abbb0c90997f51cc97b/HikariCP-java7-2.4.13.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/artemis-core-client/2.37.0/fa0e8aac1d0db695995c2616120345c2bcd77326/artemis-core-client-2.37.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/artemis-selector/2.37.0/4ece0fb9fbdd4df8bb643478d7c0d8a6051a8b9b/artemis-selector-2.37.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/artemis-journal/2.37.0/9083858a5fd032a8993a736752ab31a2c497698a/artemis-journal-2.37.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/artemis-lockmanager-api/2.37.0/f236a517f9b9a4550b97325ea854484998ead4a7/artemis-lockmanager-api-2.37.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/artemis-commons/2.37.0/f504f11c93d3f9614f042f72ef6fe4999b4f278c/artemis-commons-2.37.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-xml/12.0.21/3fe9f146e3f2261e8f87d95b0e8db64543540a36/jetty-xml-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty.http2/jetty-http2-common/12.0.21/19b472cdf867a98ac713cfe1cfb78818a1013791/jetty-http2-common-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty.http2/jetty-http2-hpack/12.0.21/148c467ecae438d58b90040b548ec58f09da0a34/jetty-http2-hpack-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-http/12.0.21/9c7ebd2c0f5ee6c800c01f24a38fafa6ad4b82f5/jetty-http-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-io/12.0.21/c894ed7eab6e758b044c8c03eaa8f370c24d7a5d/jetty-io-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.netflix.spectator/spectator-reg-atlas/1.8.12/d563efdd10344234df0402a43b7238627e81ffac/spectator-reg-atlas-1.8.12.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.rabbitmq/amqp-client/5.22.0/d5bf5f55022e14c7bc889fd8750b21cb60db064a/amqp-client-5.22.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.graphql-java/graphql-java/22.3/d61a6f4abd6feb50d5ac9ff3c16eb72df72351bb/graphql-java-22.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.graphql-java/java-dataloader/3.3.0/1786c5efbf871395a32cbce1a2114d6788ac4f53/java-dataloader-3.3.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-util/12.0.21/d3c756aaa18cc9bc22d04263e0f587f0ebc38e93/jetty-util-12.0.21.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.netflix.spectator/spectator-ext-ipc/1.8.12/6c974dd8afcf2268de840e665116326253b6c32b/spectator-ext-ipc-1.8.12.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.netflix.spectator/spectator-api/1.8.12/b199f6b3afb13e5dd700cf742fc62b2c38464f0a/spectator-api-1.8.12.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.couchbase.client/java-client/3.7.9/e773e382b454a1f0681e45085da6135828ff943e/java-client-3.7.9.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.couchbase.client/core-io/3.7.9/547bd235389ec9ddf3a43b64309f28af7a3d96d0/core-io-3.7.9.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.slf4j/slf4j-api/2.0.17/d9e58ac9c7779ba3bf8142aff6c830617a7fe60f/slf4j-api-2.0.17.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.httpcomponents/httpcore/4.4.16/51cf043c87253c9f58b539c9f7e44c8894223850/httpcore-4.4.16.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/logging-interceptor/4.12.0/e922c1f14d365c0f2bed140cc0825e18462c2778/logging-interceptor-4.12.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.zipkin.reporter2/zipkin-sender-okhttp3/3.4.3/cad3fc9ca573ff07f65b4daf9a64b67ef2c226c3/zipkin-sender-okhttp3-3.4.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.retrofit2/converter-moshi/2.9.0/db0979801926e6d39bc2478736145f9761c3e034/converter-moshi-2.9.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.retrofit2/retrofit/2.9.0/d8fdfbd5da952141a665a403348b74538efc05ff/retrofit-2.9.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/okhttp/4.12.0/2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd/okhttp-4.12.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okio/okio-jvm/3.6.0/5600569133b7bdefe1daf9ec7f4abeb6d13e1786/okio-jvm-3.6.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/1.9.25/1c166692314a2639e5edfed0d23ed7eee4a5c7a5/kotlin-stdlib-jdk7-1.9.25.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.25/f700a2f2b8f0d6d0fde48f56d894dc722fb029d7/kotlin-stdlib-1.9.25.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.9.25/20d44e880a284f7b5cd99dd69450b403073f49b2/kotlin-stdlib-jdk8-1.9.25.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy/1.15.11/f61886478e0f9ee4c21d09574736f0ff45e0a46c/byte-buddy-1.15.11.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy-agent/1.15.11/a38b16385e867f59a641330f0362ebe742788ed8/byte-buddy-agent-1.15.11.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml/classmate/1.7.0/e98374da1f2143ac8e6e0a95036994bb19137a3/classmate-1.7.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.opencsv/opencsv/5.9/284ea0b60a24b71a530100783185e7d547ab5339/opencsv-5.9.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.commons/commons-configuration2/2.11.0/af5a2c6abe587074c0be1107fcb27fa2fad91304/commons-configuration2-2.11.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.commons/commons-text/1.12.0/66aa90dc099701c4d3b14bd256c328f592ccf0d6/commons-text-1.12.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven/maven-artifact/3.9.4/9738999a0459221e758ebe728449c43567d7fecc/maven-artifact-3.9.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.commons/commons-lang3/3.17.0/b17d2136f0460dcc0d2016ceefca8723bdf4ee70/commons-lang3-3.17.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.commons/commons-pool2/2.12.1/e5c7b6c9f5a4b59be2bba213fbec70b413376fc1/commons-pool2-2.12.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.gson/gson/2.11.0/527175ca6d81050b53bdd4c457a6d6e017626b0e/gson-2.11.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.h2database/h2/2.3.232/4fcc05d966ccdb2812ae8b9a718f69226c0cf4e2/h2-2.3.232.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.httpcomponents/httpasyncclient/4.1.5/cd18227f1eb8e9a263286c1d7362ceb24f6f9b32/httpasyncclient-4.1.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.httpcomponents/httpcore-nio/4.4.16/cd21c80a9956be48c4c1cfd2f594ba02857d0927/httpcore-nio-4.4.16.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.angus/angus-activation/2.0.2/41f1e0ddd157c856926ed149ab837d110955a9fc/angus-activation-2.0.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.mail/jakarta.mail-api/2.1.3/a327aa5f514ba86e80d54584417d7376ed2bde0e/jakarta.mail-api-2.1.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.activation/jakarta.activation-api/2.1.3/fa165bd70cda600368eee31555222776a46b881f/jakarta.activation-api-2.1.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.annotation/jakarta.annotation-api/2.1.1/48b9bda22b091b1f48b13af03fe36db3be6e1ae3/jakarta.annotation-api-2.1.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.parsson/parsson/1.0.5/f68020271ff70f79412f70ca45973703b6e907c/parsson-1.0.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.json/jakarta.json-api/2.1.3/4febd83e1d9d1561d078af460ecd19532383735c/jakarta.json-api-2.1.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.validation/jakarta.validation-api/3.0.2/92b6631659ba35ca09e44874d3eb936edfeee532/jakarta.validation-api-3.0.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.ext/jersey-entity-filtering/3.1.10/6528601d67f788e0b9ebaee28ea045bc93cd81f6/jersey-entity-filtering-3.1.10.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.ws.rs/jakarta.ws.rs-api/3.1.0/15ce10d249a38865b58fc39521f10f29ab0e3363/jakarta.ws.rs-api-3.1.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jboss.xnio/xnio-nio/3.8.16.Final/9aab17706a371a212fd051b09c7959d29d051379/xnio-nio-3.8.16.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jboss.xnio/xnio-api/3.8.16.Final/7c98c649a4583523c23cc0a7e62ddace1f486b54/xnio-api-3.8.16.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.wildfly.client/wildfly-client-config/1.0.1.Final/2a803b23c40a0de0f03a90d1fd3755747bc05f4b/wildfly-client-config-1.0.1.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jboss.threads/jboss-threads/3.5.0.Final/dd23d4788b3eafe9597ef3fe028e46ceb293ba8d/jboss-threads-3.5.0.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jboss.logging/jboss-logging/3.6.1.Final/886afbb445b4016a37c8960a7aef6ebd769ce7e5/jboss-logging-3.6.1.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.minidev/json-smart/2.5.2/95d166b18f95907be0f46cdb9e1c0695eed03387/json-smart-2.5.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/ch.qos.logback/logback-core/1.5.18/6c0375624f6f36b4e089e2488ba21334a11ef13f/logback-core-1.5.18.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.mongodb/mongodb-driver-core/5.2.1/34aa1e29cdab75c2fd912a6b853319d6936515c6/mongodb-driver-core-5.2.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.mongodb/bson-record-codec/5.2.1/47a9b8ef55b63b6a66e1fd69bcf62a6f547468c8/bson-record-codec-5.2.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.mongodb/bson/5.2.1/a45b5955870d5c9325531a2fef5fbef1329b7e8a/bson-5.2.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.projectreactor.addons/reactor-pool/1.1.2/9b6334fbf10cfa816b1e97183966a64ca6b67334/reactor-pool-1.1.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.projectreactor.netty/reactor-netty-core/1.2.6/a6445eadc1fbe58f6e4fa51b2691e8b0881e19b0/reactor-netty-core-1.2.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.projectreactor/reactor-core/3.7.6/31227819dec364cd2b558a5d0b4581365c3310c8/reactor-core-3.7.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.reactivestreams/reactive-streams/1.0.4/3864a1320d97d7b045f729a326e1e077661f31b7/reactive-streams-1.0.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.retry/spring-retry/2.0.12/62a14736086ffd6f382f0df3bf066a5a34f174fa/spring-retry-2.0.12.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.tomcat/tomcat-annotations-api/10.1.41/9098bb26656a854f395f468805d4b7329a1a75b6/tomcat-annotations-api-10.1.41.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven/maven-model/3.9.4/5b79d873cf6d13b3fc4020eac04e2a62ebbfa0aa/maven-model-3.9.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven/maven-repository-metadata/3.9.4/ef368d45a2caa54634de221540994ca2d810c4c9/maven-repository-metadata-3.9.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.codehaus.plexus/plexus-utils/3.5.1/c6bfb17c97ecc8863e88778ea301be742c62b06d/plexus-utils-3.5.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/failureaccess/1.0.2/c4a06a64e650562f30b7bf9aaec1bfed43aca12b/failureaccess-1.0.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/b421526c5f297295adef1c886e5246c39d4ac629/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.checkerframework/checker-qual/3.47.0/2b3cbe34a6af46bb3692aac208caa4f55cd28584/checker-qual-3.47.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.errorprone/error_prone_annotations/2.35.1/c36f9e12192226278c2b163bf61990b131e59903/error_prone_annotations-2.35.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.j2objc/j2objc-annotations/3.0.0/7399e65dd7e9ff3404f4535b2f017093bdb134c7/j2objc-annotations-3.0.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.cryptacular/cryptacular/1.2.4/4994c015d87886212683245d13e87f6fb903a760/cryptacular-1.2.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.bouncycastle/bcpkix-jdk15on/1.64/3dac163e20110817d850d17e0444852a6d7d0bd7/bcpkix-jdk15on-1.64.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.bouncycastle/bcprov-jdk15on/1.64/1467dac1b787b5ad2a18201c0c281df69882259e/bcprov-jdk15on-1.64.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.woodstox/woodstox-core/7.0.0/beb19c02e7e28a8a4acf4a9cc8c3280ec3b94722/woodstox-core-7.0.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.zipkin.reporter2/zipkin-reporter/3.4.3/18715ff4e3a073ab173a5a50aefeb4cd9bd0080f/zipkin-reporter-3.4.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.zipkin.zipkin2/zipkin/2.27.1/74b5227ae2fe515217107fd228707e4d960ec3ec/zipkin-2.27.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.zipkin.aws/brave-propagation-aws/1.2.5/828490df0b6be58b6de4d3dd3b3a395fb8243f45/brave-propagation-aws-1.2.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.zipkin.brave/brave-context-slf4j/6.0.3/a863730f5af570514b4fa4b920c8455c9556d9cd/brave-context-slf4j-6.0.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.zipkin.brave/brave-instrumentation-http/6.0.3/bc58655bd14d2f8e8975c5951fecab202e1a1e5/brave-instrumentation-http-6.0.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.zipkin.brave/brave/6.0.3/2d626aaf5162463edefcc88291799d5ea9204630/brave-6.0.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.datastax.oss/native-protocol/1.5.1/97e812373a5fe7667384e7ad67819d2c71878bf8/native-protocol-1.5.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.datastax.oss/java-driver-shaded-guava/25.1-jre-graal-sub-1/522771d14d6b7dba67056a39db33f205ffbed6a4/java-driver-shaded-guava-25.1-jre-graal-sub-1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.jaxb/txw2/4.0.5/f36a4ef12120a9bb06d766d6a0e54b144fd7ed98/txw2-4.0.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.sun.istack/istack-commons-runtime/4.1.2/18ec117c85f3ba0ac65409136afa8e42bc74e739/istack-commons-runtime-4.1.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jspecify/jspecify/1.0.0/7425a601c1c7ec76645a78d22b8c6a627edee507/jspecify-1.0.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-commons/1.14.7/8c2c669afe1377ba4343409dc3d360a07fa1b81c/micrometer-commons-1.14.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.neo4j.bolt/neo4j-bolt-connection-netty/2.0.0/b3b0c7301af9ed773f8b9be6ba899caa74b75a59/neo4j-bolt-connection-netty-2.0.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-codec-http2/4.1.121.Final/b9ac1aefe4277d1c648fdd3fab63397695212aeb/netty-codec-http2-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-handler-proxy/4.1.121.Final/2a990d3627c4acffb4db1857eb98be71cf089797/netty-handler-proxy-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-codec-http/4.1.121.Final/53cdc976e967d809d7c84b94a02bda15c8934804/netty-codec-http-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver-dns-native-macos/4.1.121.Final/30deffedd2082339ed095e320fc99e91c1b656cb/netty-resolver-dns-native-macos-4.1.121.Final-osx-x86_64.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver-dns-classes-macos/4.1.121.Final/4b068180f7f43579ee74dec618ebfe700d9f2c44/netty-resolver-dns-classes-macos-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver-dns/4.1.121.Final/537370c12776ec85a45ec79456a866c78924b769/netty-resolver-dns-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-handler/4.1.121.Final/8ee11055fae8d4dc60ae81fad924cf5bba73f1b6/netty-handler-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-native-epoll/4.1.121.Final/7f8ae85ea3d9785451962a3ccc761bebb31f394d/netty-transport-native-epoll-4.1.121.Final-linux-x86_64.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-classes-epoll/4.1.121.Final/4e157b803175057034c42d434bae6ae46d22f34b/netty-transport-classes-epoll-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-native-kqueue/4.1.121.Final/563a00afd745b6440fd59f8b2e3d507092f21650/netty-transport-native-kqueue-4.1.121.Final-osx-x86_64.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-classes-kqueue/4.1.121.Final/f179eb963663e94c5ad3cd0af31ce3cba237d3fd/netty-transport-classes-kqueue-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-native-unix-common/4.1.121.Final/8b73e6fd9a5abca863f4d91a8623b9bf381bce81/netty-transport-native-unix-common-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-codec-socks/4.1.121.Final/23ddd663a5bce3162c9124f51117b7bf3a84299d/netty-codec-socks-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-codec-dns/4.1.121.Final/96cb258cf8745c41909cd57b5462565e8bca6c86/netty-codec-dns-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-codec/4.1.121.Final/69dd3a2a5b77f8d951fb05690f65448d96210888/netty-codec-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport/4.1.121.Final/726358c7a8d0bf25d8ba6be5e2318f1b14bb508d/netty-transport-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-buffer/4.1.121.Final/7f4edd9e82d3b62d8218e766a01dfc9769c6b290/netty-buffer-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver/4.1.121.Final/e5af1b8cd5ec29a597c6e5d455bcab53991cb581/netty-resolver-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-common/4.1.121.Final/7a5252fc3543286abbd1642eac74e4df87f7235f/netty-common-4.1.121.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-tcnative-classes/2.0.70.Final/5f75accc769e69f2e6c1e56ba3a08b6c4bcc25f8/netty-tcnative-classes-2.0.70.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk-extension-autoconfigure-spi/1.43.0/ef96761d2234ab0973aea432d74ddb6cf4589ca/opentelemetry-sdk-extension-autoconfigure-spi-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk/1.43.0/5e4e695beb5400c1866dcf800f08ca8307405b3b/opentelemetry-sdk-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk-trace/1.43.0/69619c6433ebf0fe5196f7c3fe18bd543279b451/opentelemetry-sdk-trace-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk-metrics/1.43.0/ca508a02ef7bb96678d054de264fcc0bf41e729c/opentelemetry-sdk-metrics-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk-logs/1.43.0/e613c8ecf1efc1a275c7fe744773035df954c291/opentelemetry-sdk-logs-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk-common/1.43.0/8f2bb94d9d9fe070de698b973c1f1f247a166dcf/opentelemetry-sdk-common-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-extension-trace-propagators/1.43.0/70b813daf4b298a950fd0652305912cf1275c0dc/opentelemetry-extension-trace-propagators-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry.instrumentation/opentelemetry-instrumentation-api-incubator/2.9.0-alpha/e9cd5f29fec417794432a80afa8f9e7ef42ca813/opentelemetry-instrumentation-api-incubator-2.9.0-alpha.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry.instrumentation/opentelemetry-instrumentation-api/2.9.0/d5d9208c35e53bca234a7badee709976464614c8/opentelemetry-instrumentation-api-2.9.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-api-incubator/1.43.0-alpha/2dcee00ad7552d9190857e271f6066e655c6c719/opentelemetry-api-incubator-1.43.0-alpha.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-api/1.43.0/ce6889e709baac891833dc1c718702661650b3b3/opentelemetry-api-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-context/1.43.0/473c7a67d2d587f7faefe7b6d9c00743e82819ee/opentelemetry-context-1.43.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.prometheus/prometheus-metrics-core/1.3.6/44ed241094654bc74b92190e2b20053cfe025c72/prometheus-metrics-core-1.3.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.prometheus/prometheus-metrics-exposition-textformats/1.3.6/dd3bc5613557d700d7f9f8d6fc466fd8f9ae15c4/prometheus-metrics-exposition-textformats-1.3.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.prometheus/prometheus-metrics-config/1.3.6/752835b9c6864548517543bee2541dc49b2328e4/prometheus-metrics-config-1.3.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.prometheus/prometheus-metrics-model/1.3.6/c596fb819e2210ed0b342b9bd89b1fbb7b80b6f9/prometheus-metrics-model-1.3.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.prometheus/prometheus-metrics-tracer-common/1.3.6/a690d4105fbb74b4d426ad361473e42dfaf400f9/prometheus-metrics-tracer-common-1.3.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.prometheus/simpleclient_common/0.16.0/a09a8c790a20309b942a9fdbfe77da22407096e6/simpleclient_common-0.16.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.prometheus/simpleclient/0.16.0/28b0eaf7c500c506976da8d0fc9cad6c278e8d87/simpleclient-0.16.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.prometheus/simpleclient_tracer_otel/0.16.0/cc3d2b7b7cb6f077e3b1ee1d3e99eb54fddfa151/simpleclient_tracer_otel-0.16.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.prometheus/simpleclient_tracer_otel_agent/0.16.0/9d724771e339ff7ec6cd7c0cc170d3470904c5/simpleclient_tracer_otel_agent-0.16.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.prometheus/simpleclient_tracer_common/0.16.0/dec00ef7c6155c4ca1109ec8248f7ff58d8f6cd3/simpleclient_tracer_common-0.16.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-jcl/6.2.7/16b5f3446a6fcf097f4910c935086d08db657653/spring-jcl-6.2.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-crypto/6.4.6/b6712de987926b4cd455265446ea5fd9b35169ad/spring-security-crypto-6.4.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.typesafe/config/1.4.1/19058a07624a87f90d129af7cd9c68bee94535a9/config-1.4.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-posix/3.1.15/f7d6737adcbd5925d625b8f99166de2cbf13caac/jnr-posix-3.1.15.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.hdrhistogram/HdrHistogram/2.2.2/7959933ebcc0f05b2eaa5af0a0c8689fa257b15c/HdrHistogram-2.2.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.hibernate.common/hibernate-commons-annotations/7.0.3.Final/e183c4be8bb41d12e9f19b374e00c34a0a85f439/hibernate-commons-annotations-7.0.3.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.smallrye/jandex/3.2.0/f17ad860f62a08487b9edabde608f8ac55c62fa7/jandex-3.2.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.antlr/antlr4-runtime/4.13.0/5a02e48521624faaf5ff4d99afc88b01686af655/antlr4-runtime-4.13.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.msgpack/msgpack-core/0.9.6/d829f746a66769787e5d7ecab47ba0e4477050e2/msgpack-core-0.9.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.json/json/20240303/ebb88e8fb5122b7506d5cf1d69f1ccdb790d22a/json-20240303.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.github.luben/zstd-jni/1.5.6-4/ba9e303e0b5e94cdd0017390d7d8c06f47fd61f7/zstd-jni-1.5.6-4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.lz4/lz4-java/1.8.0/4b986a99445e49ea5fbf5d149c4b63f6ed6c6780/lz4-java-1.8.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.xerial.snappy/snappy-java/1.1.10.5/ac605269f3598506196e469f1fb0d7ed5c55059e/snappy-java-1.1.10.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.rocksdb/rocksdbjni/7.9.2/6409b667493149191b09fe1fce94bada6096a3e9/rocksdbjni-7.9.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/commons-io/commons-io/2.16.1/377d592e740dc77124e0901291dbfaa6810a200e/commons-io-2.16.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.commons/commons-collections4/4.4/62ebe7544cb7164d87e0637a2a6a2bdc981395e8/commons-collections4-4.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.neo4j.bolt/neo4j-bolt-connection-pooled/2.0.0/d0b92a07a328a437dfd8a6409c7be6a3bb04fc9b/neo4j-bolt-connection-pooled-2.0.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.neo4j.bolt/neo4j-bolt-connection-routed/2.0.0/964ff5c3730175a2c02849ba1145ee1325b117a6/neo4j-bolt-connection-routed-2.0.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.neo4j.bolt/neo4j-bolt-connection/2.0.0/8900ac202dda9cb9e3974bd31e62e753c57a12d9/neo4j-bolt-connection-2.0.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.mchange/mchange-commons-java/0.2.19/7a4bee38ea02bd7dee776869b19fb3f6861d6acf/mchange-commons-java-0.2.19.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.tomcat/tomcat-juli/10.1.41/c623b61bddcface8b3a3d4c7b1ff122210f53862/tomcat-juli-10.1.41.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.codehaus.plexus/plexus-interpolation/1.26/25b919c664b79795ccde0ede5cee0fd68b544197/plexus-interpolation-1.26.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.maven/maven-builder-support/3.9.4/671110e56110d5c89eda904f198719a2b5fa4a21/maven-builder-support-3.9.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.sisu/org.eclipse.sisu.inject/0.3.5/d4265dd4f0f1d7a06d80df5a5f475d5ff9c17140/org.eclipse.sisu.inject-0.3.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.codehaus.woodstox/stax2-api/4.2.2/b0d746cadea928e5264f2ea294ea9a1bf815bbde/stax2-api-4.2.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.fusesource.hawtbuf/hawtbuf/1.11/8f0e50ad8bea37b84b698ec40cce09e47714a63e/hawtbuf-1.11.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.hk2/spring-bridge/3.0.6/e7047f31108550310f1ed9c2814caea58d875ccc/spring-bridge-3.0.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.dynatrace.metric.util/dynatrace-metric-utils-java/2.2.1/a25d8db688874b00aef7539d3bcefda603024c60/dynatrace-metric-utils-java-2.2.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/info.ganglia.gmetric4j/gmetric4j/1.0.10/3d62003123b586adb86cb028cc0f8a8c3a701d81/gmetric4j-1.0.10.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry.proto/opentelemetry-proto/1.3.2-alpha/6b1ebd548f788bdc3c3acd5e5df6902ee7694944/opentelemetry-proto-1.3.2-alpha.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.signalfx.public/signalfx-java/1.0.49/171fc4b2e0c5933437e81ed456be932f9f62291c/signalfx-java-1.0.49.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/aopalliance/aopalliance/1.0/235ba8b489512805ac13a8f9ea77a1ca5ebe3e8/aopalliance-1.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.zipkin.contrib.brave-propagation-w3c/brave-propagation-tracecontext/0.2.0/bdc26787eb3bf99e7172b53df4e170e9a067b44/brave-propagation-tracecontext-0.2.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry.semconv/opentelemetry-semconv/1.25.0-alpha/76b3d4ca0a8f20b27c1590ceece54f0c7fb5857e/opentelemetry-semconv-1.25.0-alpha.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.objenesis/objenesis/3.3/1049c09f1de4331e8193e579448d0916d75b7631/objenesis-3.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.thoughtworks.qdox/qdox/2.2.0/39651eb3ce73d6e506490ea352e1e13eab6b55e8/qdox-2.2.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.nimbusds/nimbus-jose-jwt/9.37.3/700f71ffefd60c16bd8ce711a956967ea9071cec/nimbus-jose-jwt-9.37.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.latencyutils/LatencyUtils/2.0.3/769c0b82cb2421c8256300e907298a9410a2a3d3/LatencyUtils-2.0.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-ffi/2.2.11/bcf004ce358c87fc4cd2853b658d336348d0370f/jnr-ffi-2.2.11.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-constants/0.10.3/af19ec7359dd72cbc7869c00bf7cdfa4c308e985/jnr-constants-0.10.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.moshi/moshi/1.8.0/752e7b187599d3ccb174d00ba7235e29add736be/moshi-1.8.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.opencensus/opencensus-api/0.31.1/66a60c7201c2b8b20ce495f0295b32bb0ccbbc57/opencensus-api-0.31.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-context/1.67.1/7dde554dda7efaff5ee7bc2f38783c72be3b996f/grpc-context-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-api/1.67.1/d897b56d469d366660affffb69c9d58149b93505/grpc-api-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-stub/1.67.1/d035e33a23045b8cffc3e9c96f5f9a0a6ad9d1c5/grpc-stub-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-protobuf/1.67.1/3b8ef38636f9f8ca07d762cd2d2dba247771311e/grpc-protobuf-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-protobuf-lite/1.67.1/55ae1c465f7389f4c91eb238152c4e1482eb21e6/grpc-protobuf-lite-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.api/api-common/2.40.0/5e4274c3e865795de36ad117d1b461abbe565e03/api-common-2.40.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.auto.value/auto-value-annotations/1.11.0/f0d047931d07cfbc6fa4079854f181ff62891d6f/auto-value-annotations-1.11.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.protobuf/protobuf-java/3.25.5/5ae5c9ec39930ae9b5a61b32b93288818ec05ec1/protobuf-java-3.25.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.api.grpc/proto-google-common-protos/2.48.0/86b550469172d830d174d7434c318d7dff179910/proto-google-common-protos-2.48.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.api/gax/2.57.0/e8dbb1f2bf140068246e7a4cc9e2d21904f93eb5/gax-2.57.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.auth/google-auth-library-credentials/1.29.0/19af4907301816d9328c1eb1fcc6dd05c8a0b544/google-auth-library-credentials-1.29.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.api/gax-grpc/2.57.0/89d01e2b1908d291c6671e7edb8791cab473b743/gax-grpc-2.57.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-inprocess/1.67.1/311dfbe3254cb4ec13fe68259f29f0aea2eed4ae/grpc-inprocess-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-core/1.67.1/bc20b275303dfdad7fd2daaf5cca6e4fd91439ae/grpc-core-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.android/annotations/4.1.1.4/a1678ba907bf92691d879fef34e1a187038f9259/annotations-4.1.1.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.codehaus.mojo/animal-sniffer-annotations/1.24/aa9ba58d30e0aad7f1808fce9c541ea3760678d8/animal-sniffer-annotations-1.24.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-alts/1.67.1/2f0801d5e1ac4d3627c3ffe4a20828b600399cfc/grpc-alts-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-grpclb/1.67.1/cf8871db2ba0a1f75292579a395c7181818594c8/grpc-grpclb-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.protobuf/protobuf-java-util/3.25.5/38cc5ce479603e36466feda2a9f1dfdb2210ef00/protobuf-java-util-3.25.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.conscrypt/conscrypt-openjdk-uber/2.5.2/d858f142ea189c62771c505a6548d8606ac098fe/conscrypt-openjdk-uber-2.5.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-auth/1.67.1/4a84fbffe3d9bac50751d3efd4a91c00ba1f40ca/grpc-auth-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-netty-shaded/1.67.1/8cc4f36b2a9799126651db9eb8d89183b720ec36/grpc-netty-shaded-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-util/1.67.1/6d0608ab58365deb080980c3c32230be644c3110/grpc-util-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.perfmark/perfmark-api/0.27.0/f86f575a41b091786a4b027cd9c0c1d2e3fc1c01/perfmark-api-0.27.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-googleapis/1.67.1/89b75588084abf669d2c05213381aa0153a1b30e/grpc-googleapis-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-xds/1.67.1/2bb94b8f1c4560b52abe4830b75f692a8da7da09/grpc-xds-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-services/1.67.1/c5abeec1cc2893e45164ea79ae3b72c85b27f4d6/grpc-services-1.67.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.re2j/re2j/1.7/2949632c1b4acce0d7784f28e3152e9cf3c2ec7a/re2j-1.7.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.threeten/threetenbp/1.7.0/8703e893440e550295aa358281db468625bc9a05/threetenbp-1.7.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.wildfly.common/wildfly-common/1.5.4.Final/735ceee5616d5143bac1bc740e444697073c002f/wildfly-common-1.5.4.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.minidev/accessors-smart/2.5.2/ce16fd235cfee48e67eda33e684423bba09f7d07/accessors-smart-2.5.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jgroups/jgroups/5.3.10.Final/80ea94ab9ffa73f091b10705965a1e894c556f97/jgroups-5.3.10.Final.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/de.dentrassi.crypto/pem-keystore/2.4.0/a4ba3c7b740b9dada642e1b219d537bafd8c237c/pem-keystore-2.4.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/commons-beanutils/commons-beanutils/1.9.4/d52b9abcd97f38c81342bb7e7ae1eee9b73cba51/commons-beanutils-1.9.4.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jctools/jctools-core/4.0.5/9ab38ca19877236986db4894ef1400a7ca23db80/jctools-core-4.0.5.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/commons-collections/commons-collections/3.2.2/8ad72fe39fa8c91eaaf12aadb21e0c3661fe26d5/commons-collections-3.2.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.activemq/activemq-artemis-native/2.0.0/a1bdd3cea7822f3b90063456b0c9928702076bd1/activemq-artemis-native-2.0.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.hk2/osgi-resource-locator/1.0.3/de3b21279df7e755e38275137539be5e2c80dd58/osgi-resource-locator-1.0.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.hk2/class-model/3.0.6/cdb59ed7b763f2fbee0a5595b2d1bad91d22f27f/class-model-3.0.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.javassist/javassist/3.30.2-GA/284580b5e42dfa1b8267058566435d9e93fae7f7/javassist-3.30.2-GA.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.opentest4j/opentest4j/1.3.0/152ea56b3a72f655d4fd677fc0ef2596c3dd5e6e/opentest4j-1.3.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/13.0/919f0dfe192fb4e063e7dacadee7f8bb9a2672a9/annotations-13.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.acplt.remotetea/remotetea-oncrpc/1.1.2/705c490ad22ff4627389853439f9decf5ee69be/remotetea-oncrpc-1.1.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.tdunning/t-digest/3.2/2ab94758b0276a8a26102adf8d528cf6d0567b9a/t-digest-3.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.atteo/evo-inflector/1.3/4cf8b5f363c60e63f8b7688ac053590460f2768e/evo-inflector-1.3.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.github.stephenc.jcip/jcip-annotations/1.0-1/ef31541dd28ae2cefdd17c7ebf352d93e9058c63/jcip-annotations-1.0-1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.github.jnr/jffi/1.3.9/b776ea131fa693af2c943368b52acf94131cbd7/jffi-1.3.9.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.github.jnr/jffi/1.3.9/163d683f80c06911f3e770c723d6e399e4c59448/jffi-1.3.9-native.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-util/9.2/fbc178fc5ba3dab50fd7e8a5317b8b647c8e8946/asm-util-9.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-commons/9.6/f1a9e5508eff490744144565c47326c8648be309/asm-commons-9.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-analysis/9.2/7487dd756daf96cab9986e44b9d7bcb796a61c10/asm-analysis-9.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-tree/9.6/c0cdda9d211e965d2a4448aa3fd86110f2f8c2de/asm-tree-9.6.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm/9.7.1/f0ed132a49244b042cd0e15702ab9f2ce3cc8436/asm-9.7.1.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-a64asm/1.0.0/a1cb8dbe71b5a6a0288043c3ba3ca64545be165/jnr-a64asm-1.0.0.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-x86asm/1.0.2/6936bbd6c5b235665d87bd450f5e13b52d4b48/jnr-x86asm-1.0.2.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.bouncycastle/bcpkix-jdk18on/1.78/dd61bcdb87678451dd42d42e267979bd4b4451a1/bcpkix-jdk18on-1.78.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.bouncycastle/bcutil-jdk18on/1.78/81c1f5e06f206be5dad137d563609dbe66c81d31/bcutil-jdk18on-1.78.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.bouncycastle/bcprov-jdk18on/1.78/619aafb92dc0b4c6cc4cf86c487ca48ee2d67a8e/bcprov-jdk18on-1.78.jar:/home/<USER>/.gradle/caches/modules-2/files-2.1/org.glassfish.hk2.external/aopalliance-repackaged/3.0.6/e3c3f17b649c97155640616026bd32b1043b3c1d/aopalliance-repackaged-3.0.6.jar"</span>
      },
      <span class="hljs-attr">"java.vm.vendor"</span> : {
        <span class="hljs-attr">"value"</span> : <span class="hljs-string">"BellSoft"</span>
      }
    }
  }, {
    <span class="hljs-attr">"name"</span> : <span class="hljs-string">"systemEnvironment"</span>,
    <span class="hljs-attr">"properties"</span> : {
      <span class="hljs-attr">"JAVA_HOME"</span> : {
        <span class="hljs-attr">"value"</span> : <span class="hljs-string">"/opt/hostedtoolcache/Java_Liberica_jdk/17.0.15-10/x64"</span>,
        <span class="hljs-attr">"origin"</span> : <span class="hljs-string">"System Environment Property \"JAVA_HOME\""</span>
      }
    }
  }, {
    <span class="hljs-attr">"name"</span> : <span class="hljs-string">"Config resource 'class path resource [org/springframework/boot/actuate/autoconfigure/env/application.properties]' via location 'classpath:/org/springframework/boot/actuate/autoconfigure/env/'"</span>,
    <span class="hljs-attr">"properties"</span> : {
      <span class="hljs-attr">"com.example.cache.max-size"</span> : {
        <span class="hljs-attr">"value"</span> : <span class="hljs-string">"1000"</span>,
        <span class="hljs-attr">"origin"</span> : <span class="hljs-string">"class path resource [org/springframework/boot/actuate/autoconfigure/env/application.properties] - 1:29"</span>
      }
    }
  }, {
    <span class="hljs-attr">"name"</span> : <span class="hljs-string">"logCorrelation"</span>,
    <span class="hljs-attr">"properties"</span> : { }
  }, {
    <span class="hljs-attr">"name"</span> : <span class="hljs-string">"applicationInfo"</span>,
    <span class="hljs-attr">"properties"</span> : { }
  } ]
}</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Sanitization of sensitive values has been switched off for this example.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="env.entire.response-structure"><a class="anchor" href="#env.entire.response-structure"></a>Response Structure</h3>
<div class="paragraph">
<p>The response contains details of the application’s <code>Environment</code>.
The following table describes the structure of the response:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 42.8571%;"/>
<col style="width: 14.2857%;"/>
<col style="width: 42.8572%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Path</th>
<th class="tableblock halign-left valign-top">Type</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>activeProfiles</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Array</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Names of the active profiles, if any.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>defaultProfiles</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Array</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Names of the default profiles, if any.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>propertySources</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Array</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Property sources in order of precedence.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>propertySources.[].name</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>String</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Name of the property source.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>propertySources.[].properties</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Object</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Properties in the property source keyed by property name.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>propertySources.[].properties.*.value</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>String</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Value of the property.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>propertySources.[].properties.*.origin</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>String</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Origin of the property, if any.</p></td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="env.single-property"><a class="anchor" href="#env.single-property"></a>Retrieving a Single Property</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To retrieve a single property, make a <code>GET</code> request to <code>/actuator/env/{property.name}</code>, as shown in the following curl-based example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-bash hljs" data-lang="bash">$ curl <span class="hljs-string">'http://localhost:8080/actuator/env/com.example.cache.max-size'</span> -i -X GET</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The preceding example retrieves information about the property named <code>com.example.cache.max-size</code>.
The resulting response is similar to the following:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight nowrap"><code class="language-http hljs" data-lang="http">HTTP/1.1 <span class="hljs-number">200</span> OK
<span class="hljs-attribute">Content-Disposition</span>: inline;filename=f.txt
<span class="hljs-attribute">Content-Type</span>: application/vnd.spring-boot.actuator.v3+json
<span class="hljs-attribute">Content-Length</span>: 793

<span class="json">{<span class="hljs-attr">"property"</span>:{<span class="hljs-attr">"source"</span>:<span class="hljs-string">"Config resource 'class path resource [org/springframework/boot/actuate/autoconfigure/env/application.properties]' via location 'classpath:/org/springframework/boot/actuate/autoconfigure/env/'"</span>,<span class="hljs-attr">"value"</span>:<span class="hljs-string">"1000"</span>},<span class="hljs-attr">"activeProfiles"</span>:[],<span class="hljs-attr">"defaultProfiles"</span>:[<span class="hljs-string">"default"</span>],<span class="hljs-attr">"propertySources"</span>:[{<span class="hljs-attr">"name"</span>:<span class="hljs-string">"servletContextInitParams"</span>},{<span class="hljs-attr">"name"</span>:<span class="hljs-string">"systemProperties"</span>},{<span class="hljs-attr">"name"</span>:<span class="hljs-string">"systemEnvironment"</span>},{<span class="hljs-attr">"name"</span>:<span class="hljs-string">"Config resource 'class path resource [org/springframework/boot/actuate/autoconfigure/env/application.properties]' via location 'classpath:/org/springframework/boot/actuate/autoconfigure/env/'"</span>,<span class="hljs-attr">"property"</span>:{<span class="hljs-attr">"value"</span>:<span class="hljs-string">"1000"</span>,<span class="hljs-attr">"origin"</span>:<span class="hljs-string">"class path resource [org/springframework/boot/actuate/autoconfigure/env/application.properties] - 1:29"</span>}},{<span class="hljs-attr">"name"</span>:<span class="hljs-string">"logCorrelation"</span>},{<span class="hljs-attr">"name"</span>:<span class="hljs-string">"applicationInfo"</span>}]}</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Sanitization of sensitive values has been switched off for this example.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="env.single-property.response-structure"><a class="anchor" href="#env.single-property.response-structure"></a>Response Structure</h3>
<div class="paragraph">
<p>The response contains details of the requested property.
The following table describes the structure of the response:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 42.8571%;"/>
<col style="width: 14.2857%;"/>
<col style="width: 42.8572%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Path</th>
<th class="tableblock halign-left valign-top">Type</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>property</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Object</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Property from the environment, if found.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>property.source</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>String</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Name of the source of the property.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>property.value</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>String</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Value of the property.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>activeProfiles</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Array</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Names of the active profiles, if any.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>defaultProfiles</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Array</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Names of the default profiles, if any.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>propertySources</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Array</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Property sources in order of precedence.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>propertySources.[].name</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>String</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Name of the property source.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>propertySources.[].property</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Object</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Property in the property source, if any.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>propertySources.[].property.value</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Varies</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Value of the property.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>propertySources.[].property.origin</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>String</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Origin of the property, if any.</p></td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="configprops.html">Configuration Properties (<code>configprops</code>)</a></span>
<span class="next"><a href="flyway.html">Flyway (<code>flyway</code>)</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../../api/rest/actuator/env.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="env.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../../3.3/api/rest/actuator/env.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../../4.0-SNAPSHOT/api/rest/actuator/env.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../../3.5-SNAPSHOT/api/rest/actuator/env.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../../3.4-SNAPSHOT/api/rest/actuator/env.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../../3.3-SNAPSHOT/api/rest/actuator/env.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../../../../../images/spring-boot___img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../../../../../images/spring-boot___img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../../../../../images/spring-boot___img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>
</body></html>