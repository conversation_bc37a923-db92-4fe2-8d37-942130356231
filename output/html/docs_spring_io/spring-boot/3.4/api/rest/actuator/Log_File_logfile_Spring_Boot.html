<!DOCTYPE html>
<html><head><title>Log File (logfile) :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/api/rest/actuator/logfile.html"/><meta content="2025-06-04T16:03:33.717522" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../../../reference/packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../../../reference/packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../../../reference/packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../../../reference/packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../../../reference/packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../reference/actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../reference/actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Log File (logfile)">
<div class="toc-menu"><h3>Log File (logfile)</h3><ul><li data-level="1"><a href="#logfile.retrieving">Retrieving the Log File</a></li><li data-level="1"><a href="#logfile.retrieving-part">Retrieving Part of the Log File</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/logfile.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../../index.html">Spring Boot</a></li>
<li>Rest APIs</li>
<li><a href="index.html">Actuator</a></li>
<li><a href="logfile.html">Log File (<code>logfile</code>)</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../../api/rest/actuator/logfile.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Log File (<code>logfile</code>)</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Log File (logfile)</h3><ul><li data-level="1"><a href="#logfile.retrieving">Retrieving the Log File</a></li><li data-level="1"><a href="#logfile.retrieving-part">Retrieving Part of the Log File</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>The <code>logfile</code> endpoint provides access to the contents of the application’s log file.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="logfile.retrieving"><a class="anchor" href="#logfile.retrieving"></a>Retrieving the Log File</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To retrieve the log file, make a <code>GET</code> request to <code>/actuator/logfile</code>, as shown in the following curl-based example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-bash hljs" data-lang="bash">$ curl <span class="hljs-string">'http://localhost:8080/actuator/logfile'</span> -i -X GET</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The resulting response is similar to the following:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight nowrap"><code class="language-http hljs" data-lang="http">HTTP/1.1 <span class="hljs-number">200</span> OK
<span class="hljs-attribute">Accept-Ranges</span>: bytes
<span class="hljs-attribute">Content-Type</span>: text/plain;charset=UTF-8
<span class="hljs-attribute">Content-Length</span>: 4707

<span class="yaml">  <span class="hljs-string">.</span>   <span class="hljs-string">____</span>          <span class="hljs-string">_</span>            <span class="hljs-string">__</span> <span class="hljs-string">_</span> <span class="hljs-string">_</span>
 <span class="hljs-string">/\\</span> <span class="hljs-string">/</span> <span class="hljs-string">___'_</span> <span class="hljs-string">__</span> <span class="hljs-string">_</span> <span class="hljs-string">_(_)_</span> <span class="hljs-string">__</span>  <span class="hljs-string">__</span> <span class="hljs-string">_</span> <span class="hljs-string">\</span> <span class="hljs-string">\</span> <span class="hljs-string">\</span> <span class="hljs-string">\</span>
<span class="hljs-string">(</span> <span class="hljs-string">(</span> <span class="hljs-string">)\___</span> <span class="hljs-string">|</span> <span class="hljs-string">'_ | '</span><span class="hljs-string">_|</span> <span class="hljs-string">|</span> <span class="hljs-string">'_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '</span>  <span class="hljs-string">|____|</span> <span class="hljs-string">.__|_|</span> <span class="hljs-string">|_|_|</span> <span class="hljs-string">|_\__,</span> <span class="hljs-string">|</span> <span class="hljs-string">/</span> <span class="hljs-string">/</span> <span class="hljs-string">/</span> <span class="hljs-string">/</span>
 <span class="hljs-string">=========|_|==============|___/=/_/_/_/</span>
 <span class="hljs-string">::</span> <span class="hljs-attr">Spring Boot ::</span>

<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:30.910</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">s.f.SampleWebFreeMarkerApplication       :</span> <span class="hljs-string">Starting</span> <span class="hljs-string">SampleWebFreeMarkerApplication</span> <span class="hljs-string">with</span> <span class="hljs-string">PID</span> <span class="hljs-number">19866</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:30.913</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">s.f.SampleWebFreeMarkerApplication       :</span> <span class="hljs-literal">No</span> <span class="hljs-string">active</span> <span class="hljs-string">profile</span> <span class="hljs-string">set,</span> <span class="hljs-attr">falling back to default profiles:</span> <span class="hljs-string">default</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:30.952</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">ConfigServletWebServerApplicationContext :</span> <span class="hljs-string">Refreshing</span> <span class="hljs-string">org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@76b10754:</span> <span class="hljs-string">startup</span> <span class="hljs-string">date</span> <span class="hljs-string">[Tue</span> <span class="hljs-string">Aug</span> <span class="hljs-number">08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:30</span> <span class="hljs-string">BST</span> <span class="hljs-number">2017</span><span class="hljs-string">];</span> <span class="hljs-string">root</span> <span class="hljs-string">of</span> <span class="hljs-string">context</span> <span class="hljs-string">hierarchy</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:31.878</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">o.s.b.w.embedded.tomcat.TomcatWebServer  :</span> <span class="hljs-string">Tomcat</span> <span class="hljs-string">initialized</span> <span class="hljs-string">with</span> <span class="hljs-string">port</span> <span class="hljs-number">8080</span> <span class="hljs-string">(http)</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:31.889</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">o.apache.catalina.core.StandardService   :</span> <span class="hljs-string">Starting</span> <span class="hljs-string">service</span> <span class="hljs-string">[Tomcat]</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:31.890</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">org.apache.catalina.core.StandardEngine  : Starting Servlet Engine:</span> <span class="hljs-string">Apache</span> <span class="hljs-string">Tomcat/8.5.16</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:31.978</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[ost-startStop-1]</span> <span class="hljs-string">o.a.c.c.C.[Tomcat].[localhost].[/]</span>       <span class="hljs-string">:</span> <span class="hljs-string">Initializing</span> <span class="hljs-string">Spring</span> <span class="hljs-string">embedded</span> <span class="hljs-string">WebApplicationContext</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:31.978</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[ost-startStop-1]</span> <span class="hljs-attr">o.s.web.context.ContextLoader            : Root WebApplicationContext:</span> <span class="hljs-string">initialization</span> <span class="hljs-string">completed</span> <span class="hljs-string">in</span> <span class="hljs-number">1028</span> <span class="hljs-string">ms</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.080</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[ost-startStop-1]</span> <span class="hljs-attr">o.s.b.w.servlet.ServletRegistrationBean  : Mapping servlet:</span> <span class="hljs-string">'dispatcherServlet'</span> <span class="hljs-string">to</span> <span class="hljs-string">[/]</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.084</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[ost-startStop-1]</span> <span class="hljs-attr">o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter:</span> <span class="hljs-string">'characterEncodingFilter'</span> <span class="hljs-attr">to:</span> <span class="hljs-string">[/*]</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.084</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[ost-startStop-1]</span> <span class="hljs-attr">o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter:</span> <span class="hljs-string">'hiddenHttpMethodFilter'</span> <span class="hljs-attr">to:</span> <span class="hljs-string">[/*]</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.084</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[ost-startStop-1]</span> <span class="hljs-attr">o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter:</span> <span class="hljs-string">'httpPutFormContentFilter'</span> <span class="hljs-attr">to:</span> <span class="hljs-string">[/*]</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.084</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[ost-startStop-1]</span> <span class="hljs-attr">o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter:</span> <span class="hljs-string">'requestContextFilter'</span> <span class="hljs-attr">to:</span> <span class="hljs-string">[/*]</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.349</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">s.w.s.m.m.a.RequestMappingHandlerAdapter :</span> <span class="hljs-string">Looking</span> <span class="hljs-string">for</span> <span class="hljs-string">@ControllerAdvice:</span> <span class="hljs-string">org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@76b10754:</span> <span class="hljs-string">startup</span> <span class="hljs-string">date</span> <span class="hljs-string">[Tue</span> <span class="hljs-string">Aug</span> <span class="hljs-number">08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:30</span> <span class="hljs-string">BST</span> <span class="hljs-number">2017</span><span class="hljs-string">];</span> <span class="hljs-string">root</span> <span class="hljs-string">of</span> <span class="hljs-string">context</span> <span class="hljs-string">hierarchy</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.420</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">s.w.s.m.m.a.RequestMappingHandlerMapping :</span> <span class="hljs-string">Mapped</span> <span class="hljs-string">"{[/error]}"</span> <span class="hljs-string">onto</span> <span class="hljs-string">public</span> <span class="hljs-string">org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,</span> <span class="hljs-string">java.lang.Object&gt;&gt;</span> <span class="hljs-string">org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(jakarta.servlet.http.HttpServletRequest)</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.421</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">s.w.s.m.m.a.RequestMappingHandlerMapping :</span> <span class="hljs-string">Mapped</span> <span class="hljs-string">"{[/error],produces=[text/html]}"</span> <span class="hljs-string">onto</span> <span class="hljs-string">public</span> <span class="hljs-string">org.springframework.web.servlet.ModelAndView</span> <span class="hljs-string">org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.444</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">o.s.w.s.handler.SimpleUrlHandlerMapping  :</span> <span class="hljs-string">Mapped</span> <span class="hljs-string">URL</span> <span class="hljs-string">path</span> <span class="hljs-string">[/webjars/**]</span> <span class="hljs-string">onto</span> <span class="hljs-string">handler</span> <span class="hljs-string">of</span> <span class="hljs-string">type</span> <span class="hljs-string">[class</span> <span class="hljs-string">org.springframework.web.servlet.resource.ResourceHttpRequestHandler]</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.444</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">o.s.w.s.handler.SimpleUrlHandlerMapping  :</span> <span class="hljs-string">Mapped</span> <span class="hljs-string">URL</span> <span class="hljs-string">path</span> <span class="hljs-string">[/**]</span> <span class="hljs-string">onto</span> <span class="hljs-string">handler</span> <span class="hljs-string">of</span> <span class="hljs-string">type</span> <span class="hljs-string">[class</span> <span class="hljs-string">org.springframework.web.servlet.resource.ResourceHttpRequestHandler]</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.471</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">o.s.w.s.handler.SimpleUrlHandlerMapping  :</span> <span class="hljs-string">Mapped</span> <span class="hljs-string">URL</span> <span class="hljs-string">path</span> <span class="hljs-string">[/**/favicon.ico]</span> <span class="hljs-string">onto</span> <span class="hljs-string">handler</span> <span class="hljs-string">of</span> <span class="hljs-string">type</span> <span class="hljs-string">[class</span> <span class="hljs-string">org.springframework.web.servlet.resource.ResourceHttpRequestHandler]</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.600</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">o.s.w.s.v.f.FreeMarkerConfigurer         :</span> <span class="hljs-string">ClassTemplateLoader</span> <span class="hljs-string">for</span> <span class="hljs-string">Spring</span> <span class="hljs-string">macros</span> <span class="hljs-string">added</span> <span class="hljs-string">to</span> <span class="hljs-string">FreeMarker</span> <span class="hljs-string">configuration</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.681</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">o.s.j.e.a.AnnotationMBeanExporter        :</span> <span class="hljs-string">Registering</span> <span class="hljs-string">beans</span> <span class="hljs-string">for</span> <span class="hljs-string">JMX</span> <span class="hljs-string">exposure</span> <span class="hljs-string">on</span> <span class="hljs-string">startup</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.744</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">o.s.b.w.embedded.tomcat.TomcatWebServer  :</span> <span class="hljs-string">Tomcat</span> <span class="hljs-string">started</span> <span class="hljs-string">on</span> <span class="hljs-string">port</span> <span class="hljs-number">8080</span> <span class="hljs-string">(http)</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:32.750</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">s.f.SampleWebFreeMarkerApplication       :</span> <span class="hljs-string">Started</span> <span class="hljs-string">SampleWebFreeMarkerApplication</span> <span class="hljs-string">in</span> <span class="hljs-number">2.172</span> <span class="hljs-string">seconds</span> <span class="hljs-string">(JVM</span> <span class="hljs-string">running</span> <span class="hljs-string">for</span> <span class="hljs-number">2.479</span><span class="hljs-string">)</span></span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="logfile.retrieving-part"><a class="anchor" href="#logfile.retrieving-part"></a>Retrieving Part of the Log File</h2>
<div class="sectionbody">
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Retrieving part of the log file is not supported when using Jersey.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>To retrieve part of the log file, make a <code>GET</code> request to <code>/actuator/logfile</code> by using the <code>Range</code> header, as shown in the following curl-based example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-bash hljs" data-lang="bash">$ curl <span class="hljs-string">'http://localhost:8080/actuator/logfile'</span> -i -X GET \
    -H <span class="hljs-string">'Range: bytes=0-1023'</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The preceding example retrieves the first 1024 bytes of the log file.
The resulting response is similar to the following:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight nowrap"><code class="language-http hljs" data-lang="http">HTTP/1.1 <span class="hljs-number">206</span> Partial Content
<span class="hljs-attribute">Accept-Ranges</span>: bytes
<span class="hljs-attribute">Content-Type</span>: text/plain;charset=UTF-8
<span class="hljs-attribute">Content-Range</span>: bytes 0-1023/4707
<span class="hljs-attribute">Content-Length</span>: 1024

<span class="yaml">  <span class="hljs-string">.</span>   <span class="hljs-string">____</span>          <span class="hljs-string">_</span>            <span class="hljs-string">__</span> <span class="hljs-string">_</span> <span class="hljs-string">_</span>
 <span class="hljs-string">/\\</span> <span class="hljs-string">/</span> <span class="hljs-string">___'_</span> <span class="hljs-string">__</span> <span class="hljs-string">_</span> <span class="hljs-string">_(_)_</span> <span class="hljs-string">__</span>  <span class="hljs-string">__</span> <span class="hljs-string">_</span> <span class="hljs-string">\</span> <span class="hljs-string">\</span> <span class="hljs-string">\</span> <span class="hljs-string">\</span>
<span class="hljs-string">(</span> <span class="hljs-string">(</span> <span class="hljs-string">)\___</span> <span class="hljs-string">|</span> <span class="hljs-string">'_ | '</span><span class="hljs-string">_|</span> <span class="hljs-string">|</span> <span class="hljs-string">'_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '</span>  <span class="hljs-string">|____|</span> <span class="hljs-string">.__|_|</span> <span class="hljs-string">|_|_|</span> <span class="hljs-string">|_\__,</span> <span class="hljs-string">|</span> <span class="hljs-string">/</span> <span class="hljs-string">/</span> <span class="hljs-string">/</span> <span class="hljs-string">/</span>
 <span class="hljs-string">=========|_|==============|___/=/_/_/_/</span>
 <span class="hljs-string">::</span> <span class="hljs-attr">Spring Boot ::</span>

<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:30.910</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">s.f.SampleWebFreeMarkerApplication       :</span> <span class="hljs-string">Starting</span> <span class="hljs-string">SampleWebFreeMarkerApplication</span> <span class="hljs-string">with</span> <span class="hljs-string">PID</span> <span class="hljs-number">19866</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:30.913</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">s.f.SampleWebFreeMarkerApplication       :</span> <span class="hljs-literal">No</span> <span class="hljs-string">active</span> <span class="hljs-string">profile</span> <span class="hljs-string">set,</span> <span class="hljs-attr">falling back to default profiles:</span> <span class="hljs-string">default</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:30.952</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">ConfigServletWebServerApplicationContext :</span> <span class="hljs-string">Refreshing</span> <span class="hljs-string">org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@76b10754:</span> <span class="hljs-string">startup</span> <span class="hljs-string">date</span> <span class="hljs-string">[Tue</span> <span class="hljs-string">Aug</span> <span class="hljs-number">08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:30</span> <span class="hljs-string">BST</span> <span class="hljs-number">2017</span><span class="hljs-string">];</span> <span class="hljs-string">root</span> <span class="hljs-string">of</span> <span class="hljs-string">context</span> <span class="hljs-string">hierarchy</span>
<span class="hljs-number">2017</span><span class="hljs-number">-08</span><span class="hljs-number">-08</span> <span class="hljs-number">17</span><span class="hljs-string">:12:31.878</span>  <span class="hljs-string">INFO</span> <span class="hljs-number">19866</span> <span class="hljs-string">---</span> <span class="hljs-string">[</span>           <span class="hljs-string">main]</span> <span class="hljs-attr">o.s.b.w.embedded.tomcat.TomcatWebServer  :</span> <span class="hljs-string">Tomcat</span> <span class="hljs-string">initialized</span> <span class="hljs-string">with</span> <span class="hljs-string">port</span> <span class="hljs-number">8080</span> <span class="hljs-string">(http)</span>
<span class="hljs-number">20</span></span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="liquibase.html">Liquibase (<code>liquibase</code>)</a></span>
<span class="next"><a href="loggers.html">Loggers (<code>loggers</code>)</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../../api/rest/actuator/logfile.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="logfile.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../../3.3/api/rest/actuator/logfile.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../../4.0-SNAPSHOT/api/rest/actuator/logfile.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../../3.5-SNAPSHOT/api/rest/actuator/logfile.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../../3.4-SNAPSHOT/api/rest/actuator/logfile.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../../3.3-SNAPSHOT/api/rest/actuator/logfile.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>