<!DOCTYPE html>
<html><head><title>Packaging Executable Archives :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/maven-plugin/packaging.html"/><meta content="2025-06-04T16:55:49.066411" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="goals.html">Goals</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Packaging Executable Archives">
<div class="toc-menu"><h3>Packaging Executable Archives</h3><ul><li data-level="1"><a href="#packaging.layers">Layered Jar or War</a></li><li data-level="2"><a href="#packaging.layers.configuration">Custom Layers Configuration</a></li><li data-level="1"><a href="#packaging.repackage-goal">spring-boot:repackage</a></li><li data-level="2"><a href="#packaging.repackage-goal.required-parameters">Required parameters</a></li><li data-level="2"><a href="#packaging.repackage-goal.optional-parameters">Optional parameters</a></li><li data-level="2"><a href="#packaging.repackage-goal.parameter-details">Parameter details</a></li><li data-level="1"><a href="#packaging.examples">Examples</a></li><li data-level="2"><a href="#packaging.examples.custom-classifier">Custom Classifier</a></li><li data-level="2"><a href="#packaging.examples.custom-name">Custom Name</a></li><li data-level="2"><a href="#packaging.examples.local-artifact">Local Repackaged Artifact</a></li><li data-level="2"><a href="#packaging.examples.custom-layout">Custom Layout</a></li><li data-level="2"><a href="#packaging.examples.exclude-dependency">Dependency Exclusion</a></li><li data-level="2"><a href="#packaging.examples.layered-archive-tools">JAR Tools</a></li><li data-level="2"><a href="#packaging.examples.custom-layers-configuration">Custom Layers Configuration</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-maven-plugin/src/docs/antora/modules/maven-plugin/pages/packaging.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../index.html">Spring Boot</a></li>
<li><a href="../build-tool-plugin/index.html">Build Tool Plugins</a></li>
<li><a href="index.html">Maven Plugin</a></li>
<li><a href="packaging.html">Packaging Executable Archives</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../maven-plugin/packaging.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Packaging Executable Archives</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Packaging Executable Archives</h3><ul><li data-level="1"><a href="#packaging.layers">Layered Jar or War</a></li><li data-level="2"><a href="#packaging.layers.configuration">Custom Layers Configuration</a></li><li data-level="1"><a href="#packaging.repackage-goal">spring-boot:repackage</a></li><li data-level="2"><a href="#packaging.repackage-goal.required-parameters">Required parameters</a></li><li data-level="2"><a href="#packaging.repackage-goal.optional-parameters">Optional parameters</a></li><li data-level="2"><a href="#packaging.repackage-goal.parameter-details">Parameter details</a></li><li data-level="1"><a href="#packaging.examples">Examples</a></li><li data-level="2"><a href="#packaging.examples.custom-classifier">Custom Classifier</a></li><li data-level="2"><a href="#packaging.examples.custom-name">Custom Name</a></li><li data-level="2"><a href="#packaging.examples.local-artifact">Local Repackaged Artifact</a></li><li data-level="2"><a href="#packaging.examples.custom-layout">Custom Layout</a></li><li data-level="2"><a href="#packaging.examples.exclude-dependency">Dependency Exclusion</a></li><li data-level="2"><a href="#packaging.examples.layered-archive-tools">JAR Tools</a></li><li data-level="2"><a href="#packaging.examples.custom-layers-configuration">Custom Layers Configuration</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>The plugin can create executable archives (jar files and war files) that contain all of an application’s dependencies and can then be run with <code>java -jar</code>.</p>
</div>
<div class="paragraph">
<p>Packaging an executable archive is performed by the <code>repackage</code> goal, as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">executions</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">execution</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">goals</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">goal</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">goal</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">goals</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">execution</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">executions</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock warning">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-warning" title="Warning"></i>
</td>
<td class="content">
The <code>repackage</code> goal is not meant to be used alone on the command-line as it operates on the source
<code>jar</code> (or <code>war</code>) produced by the <code>package</code> phase.
To use this goal on the command-line, you must include the <code>package</code> phase: <code>mvn package spring-boot:repackage</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you are using <code>spring-boot-starter-parent</code>, such execution is already pre-configured with a <code>repackage</code> execution ID so that only the plugin definition should be added.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The example above repackages a <code>jar</code> or <code>war</code> archive that is built during the package phase of the Maven lifecycle, including any <code>provided</code> dependencies that are defined in the project.
If some of these dependencies need to be excluded, you can use one of the <code>exclude</code> options; see the <a href="#packaging.examples.exclude-dependency">dependency exclusion</a> for more details.</p>
</div>
<div class="paragraph">
<p>The original (that is non-executable) artifact is renamed to <code>.original</code> by default but it is also possible to keep the original artifact using a custom classifier.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <code>outputFileNameMapping</code> feature of the <code>maven-war-plugin</code> is currently not supported.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The <code>spring-boot-devtools</code> and <code>spring-boot-docker-compose</code> modules are automatically excluded by default (you can control this using the <code>excludeDevtools</code> and <code>excludeDockerCompose</code> properties).
In order to make that work with <code>war</code> packaging, the <code>spring-boot-devtools</code> and <code>spring-boot-docker-compose</code> dependencies must be set as <code>optional</code> or with the <code>provided</code> scope.</p>
</div>
<div class="paragraph">
<p>The plugin rewrites your manifest, and in particular it manages the <code>Main-Class</code> and <code>Start-Class</code> entries.
If the defaults don’t work you have to configure the values in the Spring Boot plugin, not in the jar plugin.
The <code>Main-Class</code> in the manifest is controlled by the <code>layout</code> property of the Spring Boot plugin, as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">mainClass</span>&gt;</span>${start.class}<span class="hljs-tag">&lt;/<span class="hljs-name">mainClass</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">layout</span>&gt;</span>ZIP<span class="hljs-tag">&lt;/<span class="hljs-name">layout</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">executions</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">execution</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">goals</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">goal</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">goal</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">goals</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">execution</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">executions</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The <code>layout</code> property defaults to a value determined by the archive type (<code>jar</code> or <code>war</code>). The following layouts are available:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>JAR</code>: regular executable JAR layout.</p>
</li>
<li>
<p><code>WAR</code>: executable WAR layout. <code>provided</code> dependencies are placed in <code>WEB-INF/lib-provided</code> to avoid any clash when the <code>war</code> is deployed in a servlet container.</p>
</li>
<li>
<p><code>ZIP</code> (alias to <code>DIR</code>): similar to the <code>JAR</code> layout using <code>PropertiesLauncher</code>.</p>
</li>
<li>
<p><code>NONE</code>: Bundle all dependencies and project resources. Does not bundle a bootstrap loader.</p>
</li>
</ul>
</div>
</div>
</div>
<div class="sect1">
<h2 id="packaging.layers"><a class="anchor" href="#packaging.layers"></a>Layered Jar or War</h2>
<div class="sectionbody">
<div class="paragraph">
<p>A repackaged jar contains the application’s classes and dependencies in <code>BOOT-INF/classes</code> and <code>BOOT-INF/lib</code> respectively.
Similarly, an executable war contains the application’s classes in <code>WEB-INF/classes</code> and dependencies in <code>WEB-INF/lib</code> and <code>WEB-INF/lib-provided</code>.
For cases where a docker image needs to be built from the contents of a jar or war, it’s useful to be able to separate these directories further so that they can be written into distinct layers.</p>
</div>
<div class="paragraph">
<p>Layered archives use the same layout as a regular repackaged jar or war, but include an additional meta-data file that describes each layer.</p>
</div>
<div class="paragraph">
<p>By default, the following layers are defined:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>dependencies</code> for any dependency whose version does not contain <code>SNAPSHOT</code>.</p>
</li>
<li>
<p><code>spring-boot-loader</code> for the loader classes.</p>
</li>
<li>
<p><code>snapshot-dependencies</code> for any dependency whose version contains <code>SNAPSHOT</code>.</p>
</li>
<li>
<p><code>application</code> for local module dependencies, application classes, and resources.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Module dependencies are identified by looking at all of the modules that are part of the current build.
If a module dependency can only be resolved because it has been installed into Maven’s local cache and it is not part of the current build, it will be identified as regular dependency.</p>
</div>
<div class="paragraph">
<p>The layers order is important as it determines how likely previous layers can be cached when part of the application changes.
The default order is <code>dependencies</code>, <code>spring-boot-loader</code>, <code>snapshot-dependencies</code>, <code>application</code>.
Content that is least likely to change should be added first, followed by layers that are more likely to change.</p>
</div>
<div class="paragraph">
<p>The repackaged archive includes the <code>layers.idx</code> file by default.
To disable this feature, you can do so in the following manner:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">project</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">layers</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">enabled</span>&gt;</span>false<span class="hljs-tag">&lt;/<span class="hljs-name">enabled</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">layers</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">project</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="sect2">
<h3 id="packaging.layers.configuration"><a class="anchor" href="#packaging.layers.configuration"></a>Custom Layers Configuration</h3>
<div class="paragraph">
<p>Depending on your application, you may want to tune how layers are created and add new ones.
This can be done using a separate configuration file that should be registered as shown below:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">project</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">layers</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">enabled</span>&gt;</span>true<span class="hljs-tag">&lt;/<span class="hljs-name">enabled</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>${project.basedir}/src/layers.xml<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">layers</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">project</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The configuration file describes how an archive can be separated into layers, and the order of those layers.
The following example shows how the default ordering described above can be defined explicitly:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">layers</span> <span class="hljs-attr">xmlns</span>=<span class="hljs-string">"http://www.springframework.org/schema/boot/layers"</span>
		<span class="hljs-attr">xmlns:xsi</span>=<span class="hljs-string">"http://www.w3.org/2001/XMLSchema-instance"</span>
		<span class="hljs-attr">xsi:schemaLocation</span>=<span class="hljs-string">"http://www.springframework.org/schema/boot/layers
	                      https://www.springframework.org/schema/boot/layers/layers-3.4.xsd"</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">application</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">into</span> <span class="hljs-attr">layer</span>=<span class="hljs-string">"spring-boot-loader"</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">include</span>&gt;</span>org/springframework/boot/loader/**<span class="hljs-tag">&lt;/<span class="hljs-name">include</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">into</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">into</span> <span class="hljs-attr">layer</span>=<span class="hljs-string">"application"</span> /&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">application</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">dependencies</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">into</span> <span class="hljs-attr">layer</span>=<span class="hljs-string">"application"</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">includeModuleDependencies</span> /&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">into</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">into</span> <span class="hljs-attr">layer</span>=<span class="hljs-string">"snapshot-dependencies"</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">include</span>&gt;</span>*:*:*SNAPSHOT<span class="hljs-tag">&lt;/<span class="hljs-name">include</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">into</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">into</span> <span class="hljs-attr">layer</span>=<span class="hljs-string">"dependencies"</span> /&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">dependencies</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">layerOrder</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">layer</span>&gt;</span>dependencies<span class="hljs-tag">&lt;/<span class="hljs-name">layer</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">layer</span>&gt;</span>spring-boot-loader<span class="hljs-tag">&lt;/<span class="hljs-name">layer</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">layer</span>&gt;</span>snapshot-dependencies<span class="hljs-tag">&lt;/<span class="hljs-name">layer</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">layer</span>&gt;</span>application<span class="hljs-tag">&lt;/<span class="hljs-name">layer</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">layerOrder</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">layers</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The <code>layers</code> XML format is defined in three sections:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>The <code>&lt;application&gt;</code> block defines how the application classes and resources should be layered.</p>
</li>
<li>
<p>The <code>&lt;dependencies&gt;</code> block defines how dependencies should be layered.</p>
</li>
<li>
<p>The <code>&lt;layerOrder&gt;</code> block defines the order that the layers should be written.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Nested <code>&lt;into&gt;</code> blocks are used within <code>&lt;application&gt;</code> and <code>&lt;dependencies&gt;</code> sections to claim content for a layer.
The blocks are evaluated in the order that they are defined, from top to bottom.
Any content not claimed by an earlier block remains available for subsequent blocks to consider.</p>
</div>
<div class="paragraph">
<p>The <code>&lt;into&gt;</code> block claims content using nested <code>&lt;include&gt;</code> and <code>&lt;exclude&gt;</code> elements.
The <code>&lt;application&gt;</code> section uses Ant-style path matching for include/exclude expressions.
The <code>&lt;dependencies&gt;</code> section uses <code>group:artifact[:version]</code> patterns.
It also provides <code>&lt;includeModuleDependencies /&gt;</code> and <code>&lt;excludeModuleDependencies /&gt;</code> elements that can be used to include or exclude local module dependencies.</p>
</div>
<div class="paragraph">
<p>If no <code>&lt;include&gt;</code> is defined, then all content (not claimed by an earlier block) is considered.</p>
</div>
<div class="paragraph">
<p>If no <code>&lt;exclude&gt;</code> is defined, then no exclusions are applied.</p>
</div>
<div class="paragraph">
<p>Looking at the <code>&lt;dependencies&gt;</code> example above, we can see that the first <code>&lt;into&gt;</code> will claim all module dependencies for the <code>application.layer</code>.
The next <code>&lt;into&gt;</code> will claim all SNAPSHOT dependencies for the <code>snapshot-dependencies</code> layer.
The final <code>&lt;into&gt;</code> will claim anything left (in this case, any dependency that is not a SNAPSHOT) for the <code>dependencies</code> layer.</p>
</div>
<div class="paragraph">
<p>The <code>&lt;application&gt;</code> block has similar rules.
First claiming <code>org/springframework/boot/loader/**</code> content for the <code>spring-boot-loader</code> layer.
Then claiming any remaining classes and resources for the <code>application</code> layer.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The order that <code>&lt;into&gt;</code> blocks are defined is often different from the order that the layers are written.
For this reason the <code>&lt;layerOrder&gt;</code> element must always be included and <em>must</em> cover all layers referenced by the <code>&lt;into&gt;</code> blocks.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="packaging.repackage-goal"><a class="anchor" href="#packaging.repackage-goal"></a><code>spring-boot:repackage</code></h2>
<div class="sectionbody">
<div class="paragraph">
<p><code>org.springframework.boot:spring-boot-maven-plugin:3.4.6</code></p>
</div>
<div class="paragraph">
<p>Repackage existing JAR and WAR archives so that they can be executed from the command line using <code>java -jar</code>. With <code>layout=NONE</code> can also be used simply to package a JAR with nested dependencies (and no main class, so not executable).</p>
</div>
<div class="sect2">
<h3 id="packaging.repackage-goal.required-parameters"><a class="anchor" href="#packaging.repackage-goal.required-parameters"></a>Required parameters</h3>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 37.5%;"/>
<col style="width: 25%;"/>
<col style="width: 37.5%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Name</th>
<th class="tableblock halign-left valign-top">Type</th>
<th class="tableblock halign-left valign-top">Default</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.output-directory">outputDirectory</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>File</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>${project.build.directory}</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="packaging.repackage-goal.optional-parameters"><a class="anchor" href="#packaging.repackage-goal.optional-parameters"></a>Optional parameters</h3>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 37.5%;"/>
<col style="width: 25%;"/>
<col style="width: 37.5%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Name</th>
<th class="tableblock halign-left valign-top">Type</th>
<th class="tableblock halign-left valign-top">Default</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.attach">attach</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>true</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.classifier">classifier</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>String</code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.embedded-launch-script">embeddedLaunchScript</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>File</code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.embedded-launch-script-properties">embeddedLaunchScriptProperties</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Properties</code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.exclude-devtools">excludeDevtools</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>true</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.exclude-docker-compose">excludeDockerCompose</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>true</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.exclude-group-ids">excludeGroupIds</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>String</code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.excludes">excludes</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>List</code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.executable">executable</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>false</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.include-system-scope">includeSystemScope</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>false</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.include-tools">includeTools</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>true</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.includes">includes</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>List</code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.layers">layers</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code><a class="xref page" href="api/java/org/springframework/boot/maven/Layers.html">Layers</a></code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.layout">layout</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code><a class="xref page" href="api/java/org/springframework/boot/maven/AbstractPackagerMojo.LayoutType.html">LayoutType</a></code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.layout-factory">layoutFactory</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code><a class="xref page" href="../api/java/org/springframework/boot/loader/tools/LayoutFactory.html">LayoutFactory</a></code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.loader-implementation">loaderImplementation</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code><a class="xref page" href="../api/java/org/springframework/boot/loader/tools/LoaderImplementation.html">LoaderImplementation</a></code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.main-class">mainClass</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>String</code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.output-timestamp">outputTimestamp</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>String</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>${project.build.outputTimestamp}</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.requires-unpack">requiresUnpack</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>List</code></p></td>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#packaging.repackage-goal.parameter-details.skip">skip</a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>false</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="packaging.repackage-goal.parameter-details"><a class="anchor" href="#packaging.repackage-goal.parameter-details"></a>Parameter details</h3>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.attach"><a class="anchor" href="#packaging.repackage-goal.parameter-details.attach"></a><code>attach</code></h4>
<div class="paragraph">
<p>Attach the repackaged archive to be installed into your local Maven repository or deployed to a remote repository. If no classifier has been configured, it will replace the normal jar. If a <code>classifier</code> has been configured such that the normal jar and the repackaged jar are different, it will be attached alongside the normal jar. When the property is set to <code>false</code>, the repackaged archive will not be installed or deployed.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>attach</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>true</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.4.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.classifier"><a class="anchor" href="#packaging.repackage-goal.parameter-details.classifier"></a><code>classifier</code></h4>
<div class="paragraph">
<p>Classifier to add to the repackaged archive. If not given, the main artifact will be replaced by the repackaged archive. If given, the classifier will also be used to determine the source archive to repackage: if an artifact with that classifier already exists, it will be used as source and replaced. If no such artifact exists, the main artifact will be used as source and the repackaged archive will be attached as a supplemental artifact with that classifier. Attaching the artifact allows to deploy it alongside to the original one, see <a class="external" href="https://maven.apache.org/plugins/maven-deploy-plugin/examples/deploying-with-classifiers.html" target="_blank">the Maven documentation for more details</a>.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>classifier</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java.lang.String</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.0.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.embedded-launch-script"><a class="anchor" href="#packaging.repackage-goal.parameter-details.embedded-launch-script"></a><code>embeddedLaunchScript</code></h4>
<div class="paragraph">
<p>The embedded launch script to prepend to the front of the jar if it is fully executable. If not specified the 'Spring Boot' default script will be used.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>embeddedLaunchScript</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java.io.File</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.3.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.embedded-launch-script-properties"><a class="anchor" href="#packaging.repackage-goal.parameter-details.embedded-launch-script-properties"></a><code>embeddedLaunchScriptProperties</code></h4>
<div class="paragraph">
<p>Properties that should be expanded in the embedded launch script.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>embeddedLaunchScriptProperties</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java.util.Properties</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.3.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.exclude-devtools"><a class="anchor" href="#packaging.repackage-goal.parameter-details.exclude-devtools"></a><code>excludeDevtools</code></h4>
<div class="paragraph">
<p>Exclude Spring Boot devtools from the repackaged archive.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>excludeDevtools</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>true</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring-boot.repackage.excludeDevtools</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.3.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.exclude-docker-compose"><a class="anchor" href="#packaging.repackage-goal.parameter-details.exclude-docker-compose"></a><code>excludeDockerCompose</code></h4>
<div class="paragraph">
<p>Exclude Spring Boot dev services from the repackaged archive.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>excludeDockerCompose</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>true</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring-boot.repackage.excludeDockerCompose</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>3.1.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.exclude-group-ids"><a class="anchor" href="#packaging.repackage-goal.parameter-details.exclude-group-ids"></a><code>excludeGroupIds</code></h4>
<div class="paragraph">
<p>Comma separated list of groupId names to exclude (exact match).</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>excludeGroupIds</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java.lang.String</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring-boot.excludeGroupIds</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.1.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.excludes"><a class="anchor" href="#packaging.repackage-goal.parameter-details.excludes"></a><code>excludes</code></h4>
<div class="paragraph">
<p>Collection of artifact definitions to exclude. The <code>Exclude</code> element defines mandatory <code>groupId</code> and <code>artifactId</code> components and an optional <code>classifier</code> component. When configured as a property, values should be comma-separated with colon-separated components: <code>groupId:artifactId,groupId:artifactId:classifier</code></p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>excludes</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java.util.List</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring-boot.excludes</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.1.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.executable"><a class="anchor" href="#packaging.repackage-goal.parameter-details.executable"></a><code>executable</code></h4>
<div class="paragraph">
<p>Make a fully executable jar for *nix machines by prepending a launch script to the jar.   Currently, some tools do not accept this format so you may not always be able to use this technique. For example, <code>jar -xf</code> may silently fail to extract a jar or war that has been made fully-executable. It is recommended that you only enable this option if you intend to execute it directly, rather than running it with <code>java -jar</code> or deploying it to a servlet container.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>executable</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>false</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.3.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.include-system-scope"><a class="anchor" href="#packaging.repackage-goal.parameter-details.include-system-scope"></a><code>includeSystemScope</code></h4>
<div class="paragraph">
<p>Include system scoped dependencies.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>includeSystemScope</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>false</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.4.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.include-tools"><a class="anchor" href="#packaging.repackage-goal.parameter-details.include-tools"></a><code>includeTools</code></h4>
<div class="paragraph">
<p>Include JAR tools.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>includeTools</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>true</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>3.3.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.includes"><a class="anchor" href="#packaging.repackage-goal.parameter-details.includes"></a><code>includes</code></h4>
<div class="paragraph">
<p>Collection of artifact definitions to include. The <code>Include</code> element defines mandatory <code>groupId</code> and <code>artifactId</code> components and an optional <code>classifier</code> component. When configured as a property, values should be comma-separated with colon-separated components: <code>groupId:artifactId,groupId:artifactId:classifier</code></p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>includes</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java.util.List</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring-boot.includes</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.2.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.layers"><a class="anchor" href="#packaging.repackage-goal.parameter-details.layers"></a><code>layers</code></h4>
<div class="paragraph">
<p>Layer configuration with options to disable layer creation, exclude layer tools jar, and provide a custom layers configuration file.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>layers</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code><a class="xref page" href="api/java/org/springframework/boot/maven/Layers.html">org.springframework.boot.maven.Layers</a></code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>2.3.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.layout"><a class="anchor" href="#packaging.repackage-goal.parameter-details.layout"></a><code>layout</code></h4>
<div class="paragraph">
<p>The type of archive (which corresponds to how the dependencies are laid out inside it). Possible values are <code>JAR</code>, <code>WAR</code>, <code>ZIP</code>, <code>DIR</code>, <code>NONE</code>. Defaults to a guess based on the archive type.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>layout</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code><a class="xref page" href="api/java/org/springframework/boot/maven/AbstractPackagerMojo.LayoutType.html">org.springframework.boot.maven.AbstractPackagerMojo$LayoutType</a></code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring-boot.repackage.layout</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.0.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.layout-factory"><a class="anchor" href="#packaging.repackage-goal.parameter-details.layout-factory"></a><code>layoutFactory</code></h4>
<div class="paragraph">
<p>The layout factory that will be used to create the executable archive if no explicit layout is set. Alternative layouts implementations can be provided by 3rd parties.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>layoutFactory</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code><a class="xref page" href="../api/java/org/springframework/boot/loader/tools/LayoutFactory.html">org.springframework.boot.loader.tools.LayoutFactory</a></code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.5.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.loader-implementation"><a class="anchor" href="#packaging.repackage-goal.parameter-details.loader-implementation"></a><code>loaderImplementation</code></h4>
<div class="paragraph">
<p>The loader implementation that should be used.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>loaderImplementation</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code><a class="xref page" href="../api/java/org/springframework/boot/loader/tools/LoaderImplementation.html">org.springframework.boot.loader.tools.LoaderImplementation</a></code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>3.2.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.main-class"><a class="anchor" href="#packaging.repackage-goal.parameter-details.main-class"></a><code>mainClass</code></h4>
<div class="paragraph">
<p>The name of the main class. If not specified the first compiled class found that contains a <code>main</code> method will be used.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>mainClass</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java.lang.String</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.0.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.output-directory"><a class="anchor" href="#packaging.repackage-goal.parameter-details.output-directory"></a><code>outputDirectory</code></h4>
<div class="paragraph">
<p>Directory containing the generated archive.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>outputDirectory</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java.io.File</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>${project.build.directory}</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.0.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.output-timestamp"><a class="anchor" href="#packaging.repackage-goal.parameter-details.output-timestamp"></a><code>outputTimestamp</code></h4>
<div class="paragraph">
<p>Timestamp for reproducible output archive entries, either formatted as ISO 8601 (<code>yyyy-MM-dd’T’HH:mm:ssXXX</code>) or an <code>int</code> representing seconds since the epoch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>outputTimestamp</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java.lang.String</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>${project.build.outputTimestamp}</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>2.3.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.requires-unpack"><a class="anchor" href="#packaging.repackage-goal.parameter-details.requires-unpack"></a><code>requiresUnpack</code></h4>
<div class="paragraph">
<p>A list of the libraries that must be unpacked from uber jars in order to run. Specify each library as a <code>&lt;dependency&gt;</code> with a <code>&lt;groupId&gt;</code> and a <code>&lt;artifactId&gt;</code> and they will be unpacked at runtime.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>requiresUnpack</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>java.util.List</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.1.0</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="packaging.repackage-goal.parameter-details.skip"><a class="anchor" href="#packaging.repackage-goal.parameter-details.skip"></a><code>skip</code></h4>
<div class="paragraph">
<p>Skip the execution.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 10%;"/>
<col style="width: 90%;"/>
</colgroup>
<tbody>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Name</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>skip</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Type</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>boolean</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Default value</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>false</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">User property</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>spring-boot.repackage.skip</code></p></td>
</tr>
<tr>
<th class="tableblock halign-left valign-top"><p class="tableblock">Since</p></th>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>1.2.0</code></p></td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="packaging.examples"><a class="anchor" href="#packaging.examples"></a>Examples</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="packaging.examples.custom-classifier"><a class="anchor" href="#packaging.examples.custom-classifier"></a>Custom Classifier</h3>
<div class="paragraph">
<p>By default, the <code>repackage</code> goal replaces the original artifact with the repackaged one.
That is a sane behavior for modules that represent an application but if your module is used as a dependency of another module, you need to provide a classifier for the repackaged one.
The reason for that is that application classes are packaged in <code>BOOT-INF/classes</code> so that the dependent module cannot load a repackaged jar’s classes.</p>
</div>
<div class="paragraph">
<p>If that is the case or if you prefer to keep the original artifact and attach the repackaged one with a different classifier, configure the plugin as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">project</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">executions</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">execution</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">id</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">id</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">goals</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">goal</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">goal</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">goals</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">classifier</span>&gt;</span>exec<span class="hljs-tag">&lt;/<span class="hljs-name">classifier</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">execution</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">executions</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">project</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>If you are using <code>spring-boot-starter-parent</code>, the <code>repackage</code> goal is executed automatically in an execution with id <code>repackage</code>.
In that setup, only the configuration should be specified, as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">project</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">executions</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">execution</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">id</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">id</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">classifier</span>&gt;</span>exec<span class="hljs-tag">&lt;/<span class="hljs-name">classifier</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">execution</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">executions</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">project</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This configuration will generate two artifacts: the original one and the repackaged counter part produced by the repackage goal.
Both will be installed/deployed transparently.</p>
</div>
<div class="paragraph">
<p>You can also use the same configuration if you want to repackage a secondary artifact the same way the main artifact is replaced.
The following configuration installs/deploys a single <code>task</code> classified artifact with the repackaged application:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">project</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.apache.maven.plugins<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>maven-jar-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">executions</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">execution</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">goals</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">goal</span>&gt;</span>jar<span class="hljs-tag">&lt;/<span class="hljs-name">goal</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">goals</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">phase</span>&gt;</span>package<span class="hljs-tag">&lt;/<span class="hljs-name">phase</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">classifier</span>&gt;</span>task<span class="hljs-tag">&lt;/<span class="hljs-name">classifier</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">execution</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">executions</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">executions</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">execution</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">id</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">id</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">goals</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">goal</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">goal</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">goals</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">classifier</span>&gt;</span>task<span class="hljs-tag">&lt;/<span class="hljs-name">classifier</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">execution</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">executions</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">project</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>As both the <code>maven-jar-plugin</code> and the <code>spring-boot-maven-plugin</code> run at the same phase, it is important that the jar plugin is defined first (so that it runs before the repackage goal).
Again, if you are using <code>spring-boot-starter-parent</code>, this can be simplified as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">project</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.apache.maven.plugins<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>maven-jar-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">executions</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">execution</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">id</span>&gt;</span>default-jar<span class="hljs-tag">&lt;/<span class="hljs-name">id</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">classifier</span>&gt;</span>task<span class="hljs-tag">&lt;/<span class="hljs-name">classifier</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">execution</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">executions</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">executions</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">execution</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">id</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">id</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">classifier</span>&gt;</span>task<span class="hljs-tag">&lt;/<span class="hljs-name">classifier</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">execution</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">executions</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">project</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="packaging.examples.custom-name"><a class="anchor" href="#packaging.examples.custom-name"></a>Custom Name</h3>
<div class="paragraph">
<p>If you need the repackaged jar to have a different local name than the one defined by the <code>artifactId</code> attribute of the project, use the standard <code>finalName</code>, as shown in the following example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">project</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">finalName</span>&gt;</span>my-app<span class="hljs-tag">&lt;/<span class="hljs-name">finalName</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">executions</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">execution</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">id</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">id</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">goals</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">goal</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">goal</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">goals</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">execution</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">executions</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">project</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This configuration will generate the repackaged artifact in <code>target/my-app.jar</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="packaging.examples.local-artifact"><a class="anchor" href="#packaging.examples.local-artifact"></a>Local Repackaged Artifact</h3>
<div class="paragraph">
<p>By default, the <code>repackage</code> goal replaces the original artifact with the executable one.
If you need to only deploy the original jar and yet be able to run your app with the regular file name, configure the plugin as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">project</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">executions</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">execution</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">id</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">id</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">goals</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">goal</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">goal</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">goals</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">attach</span>&gt;</span>false<span class="hljs-tag">&lt;/<span class="hljs-name">attach</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">execution</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">executions</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">project</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This configuration generates two artifacts: the original one and the executable counter part produced by the <code>repackage</code> goal.
Only the original one will be installed/deployed.</p>
</div>
</div>
<div class="sect2">
<h3 id="packaging.examples.custom-layout"><a class="anchor" href="#packaging.examples.custom-layout"></a>Custom Layout</h3>
<div class="paragraph">
<p>Spring Boot repackages the jar file for this project using a custom layout factory defined in the additional jar file, provided as a dependency to the build plugin:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">project</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">executions</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">execution</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">id</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">id</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">goals</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">goal</span>&gt;</span>repackage<span class="hljs-tag">&lt;/<span class="hljs-name">goal</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">goals</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">layoutFactory</span> <span class="hljs-attr">implementation</span>=<span class="hljs-string">"com.example.CustomLayoutFactory"</span>&gt;</span>
								<span class="hljs-tag">&lt;<span class="hljs-name">customProperty</span>&gt;</span>value<span class="hljs-tag">&lt;/<span class="hljs-name">customProperty</span>&gt;</span>
							<span class="hljs-tag">&lt;/<span class="hljs-name">layoutFactory</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">execution</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">executions</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">dependencies</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>com.example<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>custom-layout<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">version</span>&gt;</span>0.0.1.BUILD-SNAPSHOT<span class="hljs-tag">&lt;/<span class="hljs-name">version</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">dependencies</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">project</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The layout factory is provided as an implementation of <code>LayoutFactory</code> (from <code>spring-boot-loader-tools</code>) explicitly specified in the pom.
If there is only one custom <code>LayoutFactory</code> on the plugin classpath and it is listed in <code>META-INF/spring.factories</code> then it is unnecessary to explicitly set it in the plugin configuration.</p>
</div>
<div class="paragraph">
<p>Layout factories are always ignored if an explicit <a href="#packaging.repackage-goal.parameter-details.layout-factory">layout</a> is set.</p>
</div>
</div>
<div class="sect2">
<h3 id="packaging.examples.exclude-dependency"><a class="anchor" href="#packaging.examples.exclude-dependency"></a>Dependency Exclusion</h3>
<div class="paragraph">
<p>By default, both the <code>repackage</code> and the <code>run</code> goals will include any <code>provided</code> dependencies that are defined in the project.
A Spring Boot project should consider <code>provided</code> dependencies as "container" dependencies that are required to run the application.
Generally speaking, Spring Boot projects are not used as dependencies and are therefore unlikely to have any <code>optional</code> dependencies.
When a project does have optional dependencies they too will be included by the <code>repackage</code> and <code>run</code> goals.</p>
</div>
<div class="paragraph">
<p>Some of these dependencies may not be required at all and should be excluded from the executable jar.
For consistency, they should not be present either when running the application.</p>
</div>
<div class="paragraph">
<p>There are two ways one can exclude a dependency from being packaged/used at runtime:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Exclude a specific artifact identified by <code>groupId</code> and <code>artifactId</code>, optionally with a <code>classifier</code> if needed.</p>
</li>
<li>
<p>Exclude any artifact belonging to a given <code>groupId</code>.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The following example excludes <code>com.example:module1</code>, and only that artifact:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">project</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">excludes</span>&gt;</span>
						<span class="hljs-tag">&lt;<span class="hljs-name">exclude</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>com.example<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
							<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>module1<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
						<span class="hljs-tag">&lt;/<span class="hljs-name">exclude</span>&gt;</span>
					<span class="hljs-tag">&lt;/<span class="hljs-name">excludes</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">project</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>This example excludes any artifact belonging to the <code>com.example</code> group:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">project</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">excludeGroupIds</span>&gt;</span>com.example<span class="hljs-tag">&lt;/<span class="hljs-name">excludeGroupIds</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">project</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="packaging.examples.layered-archive-tools"><a class="anchor" href="#packaging.examples.layered-archive-tools"></a>JAR Tools</h3>
<div class="paragraph">
<p>When a layered jar or war is created, the <code>spring-boot-jarmode-tools</code> jar will be added as a dependency to your archive.
With this jar on the classpath, you can launch your application in a special mode which allows the bootstrap code to run something entirely different from your application, for example, something that extracts the layers.
If you wish to exclude this dependency, you can do so in the following manner:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">project</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">build</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">plugins</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">plugin</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-maven-plugin<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
				<span class="hljs-tag">&lt;<span class="hljs-name">configuration</span>&gt;</span>
					<span class="hljs-tag">&lt;<span class="hljs-name">includeTools</span>&gt;</span>false<span class="hljs-tag">&lt;/<span class="hljs-name">includeTools</span>&gt;</span>
				<span class="hljs-tag">&lt;/<span class="hljs-name">configuration</span>&gt;</span>
			<span class="hljs-tag">&lt;/<span class="hljs-name">plugin</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">plugins</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">build</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">project</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="packaging.examples.custom-layers-configuration"><a class="anchor" href="#packaging.examples.custom-layers-configuration"></a>Custom Layers Configuration</h3>
<div class="paragraph">
<p>The default setup splits dependencies into snapshot and non-snapshot, however, you may have more complex rules.
For example, you may want to isolate company-specific dependencies of your project in a dedicated layer.
The following <code>layers.xml</code> configuration shown one such setup:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">layers</span> <span class="hljs-attr">xmlns</span>=<span class="hljs-string">"http://www.springframework.org/schema/boot/layers"</span>
		<span class="hljs-attr">xmlns:xsi</span>=<span class="hljs-string">"http://www.w3.org/2001/XMLSchema-instance"</span>
		<span class="hljs-attr">xsi:schemaLocation</span>=<span class="hljs-string">"http://www.springframework.org/schema/boot/layers
						  https://www.springframework.org/schema/boot/layers/layers-3.4.xsd"</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">application</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">into</span> <span class="hljs-attr">layer</span>=<span class="hljs-string">"spring-boot-loader"</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">include</span>&gt;</span>org/springframework/boot/loader/**<span class="hljs-tag">&lt;/<span class="hljs-name">include</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">into</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">into</span> <span class="hljs-attr">layer</span>=<span class="hljs-string">"application"</span> /&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">application</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">dependencies</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">into</span> <span class="hljs-attr">layer</span>=<span class="hljs-string">"snapshot-dependencies"</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">include</span>&gt;</span>*:*:*SNAPSHOT<span class="hljs-tag">&lt;/<span class="hljs-name">include</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">into</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">into</span> <span class="hljs-attr">layer</span>=<span class="hljs-string">"company-dependencies"</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">include</span>&gt;</span>com.acme:*<span class="hljs-tag">&lt;/<span class="hljs-name">include</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">into</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">into</span> <span class="hljs-attr">layer</span>=<span class="hljs-string">"dependencies"</span>/&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">dependencies</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">layerOrder</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">layer</span>&gt;</span>dependencies<span class="hljs-tag">&lt;/<span class="hljs-name">layer</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">layer</span>&gt;</span>spring-boot-loader<span class="hljs-tag">&lt;/<span class="hljs-name">layer</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">layer</span>&gt;</span>snapshot-dependencies<span class="hljs-tag">&lt;/<span class="hljs-name">layer</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">layer</span>&gt;</span>company-dependencies<span class="hljs-tag">&lt;/<span class="hljs-name">layer</span>&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">layer</span>&gt;</span>application<span class="hljs-tag">&lt;/<span class="hljs-name">layer</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">layerOrder</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">layers</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The configuration above creates an additional <code>company-dependencies</code> layer with all libraries with the <code>com.acme</code> groupId.</p>
</div>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="goals.html">Goals</a></span>
<span class="next"><a href="build-image.html">Packaging OCI Images</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../maven-plugin/packaging.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="packaging.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../3.3/maven-plugin/packaging.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../4.0-SNAPSHOT/maven-plugin/packaging.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../3.5-SNAPSHOT/maven-plugin/packaging.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../3.4-SNAPSHOT/maven-plugin/packaging.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../3.3-SNAPSHOT/maven-plugin/packaging.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>