<!DOCTYPE html>

<html><head><title>Data Access :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/how-to/data-access.html"/><meta content="2025-06-04T19:23:11.838299" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../../../images/spring-boot___img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>
<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="logging.html">Logging</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="2">
<a class="nav-link" href="data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Data Access">
<div class="toc-menu"><h3>Data Access</h3><ul><li data-level="1"><a href="#howto.data-access.configure-custom-datasource">Configure a Custom DataSource</a></li><li data-level="1"><a href="#howto.data-access.configure-two-datasources">Configure Two DataSources</a></li><li data-level="1"><a href="#howto.data-access.spring-data-repositories">Use Spring Data Repositories</a></li><li data-level="1"><a href="#howto.data-access.separate-entity-definitions-from-spring-configuration">Separate @Entity Definitions from Spring Configuration</a></li><li data-level="1"><a href="#howto.data-access.filter-scanned-entity-definitions">Filter Scanned @Entity Definitions</a></li><li data-level="1"><a href="#howto.data-access.jpa-properties">Configure JPA Properties</a></li><li data-level="1"><a href="#howto.data-access.configure-hibernate-naming-strategy">Configure Hibernate Naming Strategy</a></li><li data-level="1"><a href="#howto.data-access.configure-hibernate-second-level-caching">Configure Hibernate Second-Level Caching</a></li><li data-level="1"><a href="#howto.data-access.dependency-injection-in-hibernate-components">Use Dependency Injection in Hibernate Components</a></li><li data-level="1"><a href="#howto.data-access.use-custom-entity-manager">Use a Custom EntityManagerFactory</a></li><li data-level="1"><a href="#howto.data-access.use-multiple-entity-managers">Using Multiple EntityManagerFactories</a></li><li data-level="1"><a href="#howto.data-access.use-traditional-persistence-xml">Use a Traditional persistence.xml File</a></li><li data-level="1"><a href="#howto.data-access.use-spring-data-jpa-and-mongo-repositories">Use Spring Data JPA and Mongo Repositories</a></li><li data-level="1"><a href="#howto.data-access.customize-spring-data-web-support">Customize Spring Data’s Web Support</a></li><li data-level="1"><a href="#howto.data-access.exposing-spring-data-repositories-as-rest">Expose Spring Data Repositories as REST Endpoint</a></li><li data-level="1"><a href="#howto.data-access.configure-a-component-that-is-used-by-jpa">Configure a Component that is Used by JPA</a></li><li data-level="1"><a href="#howto.data-access.configure-jooq-with-multiple-datasources">Configure jOOQ with Two DataSources</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/data-access.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../index.html">Spring Boot</a></li>
<li><a href="index.html">How-to Guides</a></li>
<li><a href="data-access.html">Data Access</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../how-to/data-access.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Data Access</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Data Access</h3><ul><li data-level="1"><a href="#howto.data-access.configure-custom-datasource">Configure a Custom DataSource</a></li><li data-level="1"><a href="#howto.data-access.configure-two-datasources">Configure Two DataSources</a></li><li data-level="1"><a href="#howto.data-access.spring-data-repositories">Use Spring Data Repositories</a></li><li data-level="1"><a href="#howto.data-access.separate-entity-definitions-from-spring-configuration">Separate @Entity Definitions from Spring Configuration</a></li><li data-level="1"><a href="#howto.data-access.filter-scanned-entity-definitions">Filter Scanned @Entity Definitions</a></li><li data-level="1"><a href="#howto.data-access.jpa-properties">Configure JPA Properties</a></li><li data-level="1"><a href="#howto.data-access.configure-hibernate-naming-strategy">Configure Hibernate Naming Strategy</a></li><li data-level="1"><a href="#howto.data-access.configure-hibernate-second-level-caching">Configure Hibernate Second-Level Caching</a></li><li data-level="1"><a href="#howto.data-access.dependency-injection-in-hibernate-components">Use Dependency Injection in Hibernate Components</a></li><li data-level="1"><a href="#howto.data-access.use-custom-entity-manager">Use a Custom EntityManagerFactory</a></li><li data-level="1"><a href="#howto.data-access.use-multiple-entity-managers">Using Multiple EntityManagerFactories</a></li><li data-level="1"><a href="#howto.data-access.use-traditional-persistence-xml">Use a Traditional persistence.xml File</a></li><li data-level="1"><a href="#howto.data-access.use-spring-data-jpa-and-mongo-repositories">Use Spring Data JPA and Mongo Repositories</a></li><li data-level="1"><a href="#howto.data-access.customize-spring-data-web-support">Customize Spring Data’s Web Support</a></li><li data-level="1"><a href="#howto.data-access.exposing-spring-data-repositories-as-rest">Expose Spring Data Repositories as REST Endpoint</a></li><li data-level="1"><a href="#howto.data-access.configure-a-component-that-is-used-by-jpa">Configure a Component that is Used by JPA</a></li><li data-level="1"><a href="#howto.data-access.configure-jooq-with-multiple-datasources">Configure jOOQ with Two DataSources</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot includes a number of starters for working with data sources.
This section answers questions related to doing so.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.configure-custom-datasource"><a class="anchor" href="#howto.data-access.configure-custom-datasource"></a>Configure a Custom DataSource</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To configure your own <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a>, define a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> of that type in your configuration.
Spring Boot reuses your <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> anywhere one is required, including database initialization.
If you need to externalize some settings, you can bind your <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> to the environment (see <a class="xref page" href="../reference/features/external-config.html#features.external-config.typesafe-configuration-properties.third-party-configuration">Third-party Configuration</a>).</p>
</div>
<div class="paragraph">
<p>The following example shows how to define a data source in a bean:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_1_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_1_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_1_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_java" class="tabpanel" id="_tabs_1_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataSourceConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@ConfigurationProperties</span>(prefix = <span class="hljs-string">"app.datasource"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> SomeDataSource <span class="hljs-title">dataSource</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> SomeDataSource();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_1_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataSourceConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@ConfigurationProperties(prefix = <span class="hljs-meta-string">"app.datasource"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">dataSource</span><span class="hljs-params">()</span></span>: SomeDataSource {
		<span class="hljs-keyword">return</span> SomeDataSource()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The following example shows how to define a data source by setting its properties:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_2_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_2_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_2_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_properties" class="tabpanel" id="_tabs_2_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">app.datasource.url</span>=<span class="hljs-string">jdbc:h2:mem:mydb</span>
<span class="hljs-meta">app.datasource.username</span>=<span class="hljs-string">sa</span>
<span class="hljs-meta">app.datasource.pool-size</span>=<span class="hljs-string">30</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_2_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">app:</span>
  <span class="hljs-attr">datasource:</span>
    <span class="hljs-attr">url:</span> <span class="hljs-string">"jdbc:h2:mem:mydb"</span>
    <span class="hljs-attr">username:</span> <span class="hljs-string">"sa"</span>
    <span class="hljs-attr">pool-size:</span> <span class="hljs-number">30</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Assuming that <code>SomeDataSource</code> has regular JavaBean properties for the URL, the username, and the pool size, these settings are bound automatically before the <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> is made available to other components.</p>
</div>
<div class="paragraph">
<p>Spring Boot also provides a utility builder class, called <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jdbc/DataSourceBuilder.html"><code>DataSourceBuilder</code></a>, that can be used to create one of the standard data sources (if it is on the classpath).
The builder can detect which one to use based on what is available on the classpath.
It also auto-detects the driver based on the JDBC URL.</p>
</div>
<div class="paragraph">
<p>The following example shows how to create a data source by using a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jdbc/DataSourceBuilder.html"><code>DataSourceBuilder</code></a>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_3_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_3_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_3_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_java" class="tabpanel" id="_tabs_3_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> javax.sql.DataSource;

<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.jdbc.DataSourceBuilder;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataSourceConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"app.datasource"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> DataSource <span class="hljs-title">dataSource</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> DataSourceBuilder.create().build();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_3_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> javax.sql.DataSource

<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.boot.jdbc.DataSourceBuilder
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataSourceConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"app.datasource"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">dataSource</span><span class="hljs-params">()</span></span>: DataSource {
		<span class="hljs-keyword">return</span> DataSourceBuilder.create().build()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>To run an app with that <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a>, all you need is the connection information.
Pool-specific settings can also be provided.
Check the implementation that is going to be used at runtime for more details.</p>
</div>
<div class="paragraph">
<p>The following example shows how to define a JDBC data source by setting properties:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_4_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_4_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_4_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_properties" class="tabpanel" id="_tabs_4_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">app.datasource.url</span>=<span class="hljs-string">***************************</span>
<span class="hljs-meta">app.datasource.username</span>=<span class="hljs-string">dbuser</span>
<span class="hljs-meta">app.datasource.password</span>=<span class="hljs-string">dbpass</span>
<span class="hljs-meta">app.datasource.pool-size</span>=<span class="hljs-string">30</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_4_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">app:</span>
  <span class="hljs-attr">datasource:</span>
    <span class="hljs-attr">url:</span> <span class="hljs-string">"***************************"</span>
    <span class="hljs-attr">username:</span> <span class="hljs-string">"dbuser"</span>
    <span class="hljs-attr">password:</span> <span class="hljs-string">"dbpass"</span>
    <span class="hljs-attr">pool-size:</span> <span class="hljs-number">30</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>However, there is a catch due to the method’s <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> return type.
This hides the actual type of the connection pool so no configuration property metadata is generated for your custom <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> and no auto-completion is available in your IDE.
To address this problem, use the builder’s <code>type(Class)</code> method to specify the type of <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> to be built and update the method’s return type.
For example, the following shows how to create a <a class="apiref external" href="https://javadoc.io/doc/com.zaxxer/HikariCP/5.1.0/com.zaxxer.hikari/com/zaxxer/hikari/HikariDataSource.html" target="_blank"><code>HikariDataSource</code></a> with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jdbc/DataSourceBuilder.html"><code>DataSourceBuilder</code></a>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_5_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_5_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_5_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_java" class="tabpanel" id="_tabs_5_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> com.zaxxer.hikari.HikariDataSource;

<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.jdbc.DataSourceBuilder;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataSourceConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"app.datasource"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> HikariDataSource <span class="hljs-title">dataSource</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> DataSourceBuilder.create().type(HikariDataSource<span class="hljs-class">.<span class="hljs-keyword">class</span>).<span class="hljs-title">build</span>()</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_5_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> com.zaxxer.hikari.HikariDataSource
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.boot.jdbc.DataSourceBuilder
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataSourceConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"app.datasource"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">dataSource</span><span class="hljs-params">()</span></span>: HikariDataSource {
		<span class="hljs-keyword">return</span> DataSourceBuilder.create().type(HikariDataSource::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>).<span class="hljs-title">build</span></span>()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Unfortunately, this basic setup does not work because Hikari has no <code>url</code> property.
Instead, it has a <code>jdbc-url</code> property which means that you must rewrite your configuration as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_6_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_6_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_6_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_properties" class="tabpanel" id="_tabs_6_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">app.datasource.jdbc-url</span>=<span class="hljs-string">***************************</span>
<span class="hljs-meta">app.datasource.username</span>=<span class="hljs-string">dbuser</span>
<span class="hljs-meta">app.datasource.password</span>=<span class="hljs-string">dbpass</span>
<span class="hljs-meta">app.datasource.pool-size</span>=<span class="hljs-string">30</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_6_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">app:</span>
  <span class="hljs-attr">datasource:</span>
    <span class="hljs-attr">jdbc-url:</span> <span class="hljs-string">"***************************"</span>
    <span class="hljs-attr">username:</span> <span class="hljs-string">"dbuser"</span>
    <span class="hljs-attr">password:</span> <span class="hljs-string">"dbpass"</span>
    <span class="hljs-attr">pool-size:</span> <span class="hljs-number">30</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>To address this problem, make use of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jdbc/DataSourceProperties.html"><code>DataSourceProperties</code></a> which will handle the <code>url</code> to <code>jdbc-url</code> translation for you.
You can initialize a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jdbc/DataSourceBuilder.html"><code>DataSourceBuilder</code></a> from the state of any <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jdbc/DataSourceProperties.html"><code>DataSourceProperties</code></a> object using its <code>initializeDataSourceBuilder()</code> method.
You could inject the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jdbc/DataSourceProperties.html"><code>DataSourceProperties</code></a> that Spring Boot creates automatically, however, that would split your configuration across <code>spring.datasource.*</code> and <code>app.datasource.*</code>.
To avoid this, define a custom <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jdbc/DataSourceProperties.html"><code>DataSourceProperties</code></a> with a custom configuration properties prefix, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_7_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_7_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_7_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_java" class="tabpanel" id="_tabs_7_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> com.zaxxer.hikari.HikariDataSource;

<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Primary;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataSourceConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@Primary</span>
	<span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"app.datasource"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> DataSourceProperties <span class="hljs-title">dataSourceProperties</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> DataSourceProperties();
	}

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"app.datasource.configuration"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> HikariDataSource <span class="hljs-title">dataSource</span><span class="hljs-params">(DataSourceProperties properties)</span> </span>{
		<span class="hljs-keyword">return</span> properties.initializeDataSourceBuilder().type(HikariDataSource<span class="hljs-class">.<span class="hljs-keyword">class</span>).<span class="hljs-title">build</span>()</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_7_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> com.zaxxer.hikari.HikariDataSource
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.jdbc.DataSourceProperties
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Primary

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyDataSourceConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@Primary</span>
	<span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"app.datasource"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">dataSourceProperties</span><span class="hljs-params">()</span></span>: DataSourceProperties {
		<span class="hljs-keyword">return</span> DataSourceProperties()
	}

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"app.datasource.configuration"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">dataSource</span><span class="hljs-params">(properties: <span class="hljs-type">DataSourceProperties</span>)</span></span>: HikariDataSource {
		<span class="hljs-keyword">return</span> properties.initializeDataSourceBuilder().type(HikariDataSource::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>).<span class="hljs-title">build</span></span>()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>This setup is equivalent to what Spring Boot does for you by default, except that the pool’s type is specified in code and its settings are exposed as <code>app.datasource.configuration.*</code> properties.
<a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jdbc/DataSourceProperties.html"><code>DataSourceProperties</code></a> takes care of the <code>url</code> to <code>jdbc-url</code> translation, so you can configure it as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_8_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_8_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_8_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_properties" class="tabpanel" id="_tabs_8_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">app.datasource.url</span>=<span class="hljs-string">***************************</span>
<span class="hljs-meta">app.datasource.username</span>=<span class="hljs-string">dbuser</span>
<span class="hljs-meta">app.datasource.password</span>=<span class="hljs-string">dbpass</span>
<span class="hljs-meta">app.datasource.configuration.maximum-pool-size</span>=<span class="hljs-string">30</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_8_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">app:</span>
  <span class="hljs-attr">datasource:</span>
    <span class="hljs-attr">url:</span> <span class="hljs-string">"***************************"</span>
    <span class="hljs-attr">username:</span> <span class="hljs-string">"dbuser"</span>
    <span class="hljs-attr">password:</span> <span class="hljs-string">"dbpass"</span>
    <span class="hljs-attr">configuration:</span>
      <span class="hljs-attr">maximum-pool-size:</span> <span class="hljs-number">30</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Note that, as the custom configuration specifies in code that Hikari should be used, <code>app.datasource.type</code> will have no effect.</p>
</div>
<div class="paragraph">
<p>As described in <a class="xref page" href="../reference/data/sql.html#data.sql.datasource.connection-pool">Supported Connection Pools</a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jdbc/DataSourceBuilder.html"><code>DataSourceBuilder</code></a> supports several different connection pools.
To use a pool other than Hikari, add it to the classpath, use the <code>type(Class)</code> method to specify the pool class to use, and update the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> method’s return type to match.
This will also provide you with configuration property metadata for the specific connection pool that you’ve chosen.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Spring Boot will expose Hikari-specific settings to <code>spring.datasource.hikari</code>.
This example uses a more generic <code>configuration</code> sub namespace as the example does not support multiple datasource implementations.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>See <a class="xref page" href="../reference/data/sql.html#data.sql.datasource">Configure a DataSource</a> and the <a class="external" href="https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jdbc/DataSourceAutoConfiguration.java" target="_blank"><code>DataSourceAutoConfiguration</code></a> class for more details.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.configure-two-datasources"><a class="anchor" href="#howto.data-access.configure-two-datasources"></a>Configure Two DataSources</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To define an additional <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a>, an approach that’s similar to the previous section can be used.
A key difference is that the <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> must be declared with <code>defaultCandidate=false</code>.
This prevents the auto-configured <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> from backing off.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="https://docs.spring.io/spring-framework/reference/6.2/core/beans/dependencies/factory-autowire.html#beans-factory-autowire-candidate">Spring Framework reference documentation</a> describes this feature in more details.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>To allow the additional <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> to be injected where it’s needed, also annotate it with <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Qualifier.html"><code>@Qualifier</code></a> as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_9">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_9_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_9_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_9_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_9_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_9_java" class="tabpanel" id="_tabs_9_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> com.zaxxer.hikari.HikariDataSource;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Qualifier;
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.jdbc.DataSourceBuilder;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyAdditionalDataSourceConfiguration</span> </span>{

	<span class="hljs-meta">@Qualifier</span>(<span class="hljs-string">"second"</span>)
	<span class="hljs-meta">@Bean</span>(defaultCandidate = <span class="hljs-keyword">false</span>)
	<span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"app.datasource"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> HikariDataSource <span class="hljs-title">secondDataSource</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> DataSourceBuilder.create().type(HikariDataSource<span class="hljs-class">.<span class="hljs-keyword">class</span>).<span class="hljs-title">build</span>()</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_9_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_9_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> com.zaxxer.hikari.HikariDataSource

<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Qualifier
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.boot.jdbc.DataSourceBuilder
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyAdditionalDataSourceConfiguration</span> </span>{

	<span class="hljs-meta">@Qualifier(<span class="hljs-meta-string">"second"</span>)</span>
	<span class="hljs-meta">@Bean(defaultCandidate = false)</span>
	<span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"app.datasource"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">secondDataSource</span><span class="hljs-params">()</span></span>: HikariDataSource {
		<span class="hljs-keyword">return</span> DataSourceBuilder.create().type(HikariDataSource::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>).<span class="hljs-title">build</span></span>()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>To consume the additional <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a>, annotate the injection point with the same <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Qualifier.html"><code>@Qualifier</code></a>.</p>
</div>
<div class="paragraph">
<p>The auto-configured and additional data sources can be configured as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_10">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_10_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_10_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_10_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_10_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_10_properties" class="tabpanel" id="_tabs_10_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.datasource.url</span>=<span class="hljs-string">****************************</span>
<span class="hljs-meta">spring.datasource.username</span>=<span class="hljs-string">dbuser</span>
<span class="hljs-meta">spring.datasource.password</span>=<span class="hljs-string">dbpass</span>
<span class="hljs-meta">spring.datasource.configuration.maximum-pool-size</span>=<span class="hljs-string">30</span>
<span class="hljs-meta">app.datasource.url</span>=<span class="hljs-string">*****************************</span>
<span class="hljs-meta">app.datasource.username</span>=<span class="hljs-string">dbuser</span>
<span class="hljs-meta">app.datasource.password</span>=<span class="hljs-string">dbpass</span>
<span class="hljs-meta">app.datasource.max-total</span>=<span class="hljs-string">30</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_10_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_10_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">datasource:</span>
    <span class="hljs-attr">url:</span> <span class="hljs-string">"****************************"</span>
    <span class="hljs-attr">username:</span> <span class="hljs-string">"dbuser"</span>
    <span class="hljs-attr">password:</span> <span class="hljs-string">"dbpass"</span>
    <span class="hljs-attr">configuration:</span>
      <span class="hljs-attr">maximum-pool-size:</span> <span class="hljs-number">30</span>
<span class="hljs-attr">app:</span>
  <span class="hljs-attr">datasource:</span>
    <span class="hljs-attr">url:</span> <span class="hljs-string">"*****************************"</span>
    <span class="hljs-attr">username:</span> <span class="hljs-string">"dbuser"</span>
    <span class="hljs-attr">password:</span> <span class="hljs-string">"dbpass"</span>
    <span class="hljs-attr">max-total:</span> <span class="hljs-number">30</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>More advanced, implementation-specific, configuration of the auto-configured <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> is available through the <code>spring.datasource.configuration.*</code> properties.
You can apply the same concept to the additional <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> as well, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_11">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_11_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_11_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_11_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_11_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_11_java" class="tabpanel" id="_tabs_11_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> com.zaxxer.hikari.HikariDataSource;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Qualifier;
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCompleteAdditionalDataSourceConfiguration</span> </span>{

	<span class="hljs-meta">@Qualifier</span>(<span class="hljs-string">"second"</span>)
	<span class="hljs-meta">@Bean</span>(defaultCandidate = <span class="hljs-keyword">false</span>)
	<span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"app.datasource"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> DataSourceProperties <span class="hljs-title">secondDataSourceProperties</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> DataSourceProperties();
	}

	<span class="hljs-meta">@Qualifier</span>(<span class="hljs-string">"second"</span>)
	<span class="hljs-meta">@Bean</span>(defaultCandidate = <span class="hljs-keyword">false</span>)
	<span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"app.datasource.configuration"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> HikariDataSource <span class="hljs-title">secondDataSource</span><span class="hljs-params">(
			@Qualifier(<span class="hljs-string">"secondDataSourceProperties"</span>)</span> DataSourceProperties secondDataSourceProperties) </span>{
		<span class="hljs-keyword">return</span> secondDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource<span class="hljs-class">.<span class="hljs-keyword">class</span>).<span class="hljs-title">build</span>()</span>;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_11_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_11_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> com.zaxxer.hikari.HikariDataSource

<span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Qualifier
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.jdbc.DataSourceProperties
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCompleteAdditionalDataSourceConfiguration</span> </span>{

	<span class="hljs-meta">@Qualifier(<span class="hljs-meta-string">"second"</span>)</span>
	<span class="hljs-meta">@Bean(defaultCandidate = false)</span>
	<span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"app.datasource"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">secondDataSourceProperties</span><span class="hljs-params">()</span></span>: DataSourceProperties {
		<span class="hljs-keyword">return</span> DataSourceProperties()
	}

	<span class="hljs-meta">@Qualifier(<span class="hljs-meta-string">"second"</span>)</span>
	<span class="hljs-meta">@Bean(defaultCandidate = false)</span>
	<span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"app.datasource.configuration"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">secondDataSource</span><span class="hljs-params">(secondDataSourceProperties: <span class="hljs-type">DataSourceProperties</span>)</span></span>: HikariDataSource {
		<span class="hljs-keyword">return</span> secondDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>).<span class="hljs-title">build</span></span>()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The preceding example configures the additional data source with the same logic as Spring Boot would use in auto-configuration.
Note that the <code>app.datasource.configuration.*</code> properties provide advanced settings based on the chosen implementation.</p>
</div>
<div class="paragraph">
<p>As with <a href="#howto.data-access.configure-custom-datasource">configuring a single custom </a><a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a>, the type of one or both of the <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> beans can be customized using the <code>type(Class)</code> method on <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jdbc/DataSourceBuilder.html"><code>DataSourceBuilder</code></a>.
See <a class="xref page" href="../reference/data/sql.html#data.sql.datasource.connection-pool">Supported Connection Pools</a> for details of the supported types.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.spring-data-repositories"><a class="anchor" href="#howto.data-access.spring-data-repositories"></a>Use Spring Data Repositories</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Data can create implementations of <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> interfaces of various flavors.
Spring Boot handles all of that for you, as long as those <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> implementations are included in one of the <a class="xref page" href="../reference/using/auto-configuration.html#using.auto-configuration.packages">auto-configuration packages</a>, typically the package (or a sub-package) of your main application class that is annotated with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html"><code>@SpringBootApplication</code></a> or <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/EnableAutoConfiguration.html"><code>@EnableAutoConfiguration</code></a>.</p>
</div>
<div class="paragraph">
<p>For many applications, all you need is to put the right Spring Data dependencies on your classpath.
There is a <code>spring-boot-starter-data-jpa</code> for JPA, <code>spring-boot-starter-data-mongodb</code> for Mongodb, and various other starters for supported technologies.
To get started, create some repository interfaces to handle your <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/Entity.html" target="_blank"><code>@Entity</code></a> objects.</p>
</div>
<div class="paragraph">
<p>Spring Boot determines the location of your <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> implementations by scanning the <a class="xref page" href="../reference/using/auto-configuration.html#using.auto-configuration.packages">auto-configuration packages</a>.
For more control, use the <code>@Enable…Repositories</code> annotations from Spring Data.</p>
</div>
<div class="paragraph">
<p>For more about Spring Data, see the <a class="external" href="https://spring.io/projects/spring-data" target="_blank">Spring Data project page</a>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.separate-entity-definitions-from-spring-configuration"><a class="anchor" href="#howto.data-access.separate-entity-definitions-from-spring-configuration"></a>Separate @Entity Definitions from Spring Configuration</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot determines the location of your <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/Entity.html" target="_blank"><code>@Entity</code></a> definitions by scanning the <a class="xref page" href="../reference/using/auto-configuration.html#using.auto-configuration.packages">auto-configuration packages</a>.
For more control, use the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/domain/EntityScan.html"><code>@EntityScan</code></a> annotation, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_12">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_12_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_12_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_12_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_12_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_12_java" class="tabpanel" id="_tabs_12_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.EnableAutoConfiguration;
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.domain.EntityScan;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-meta">@EnableAutoConfiguration</span>
<span class="hljs-meta">@EntityScan</span>(basePackageClasses = City<span class="hljs-class">.<span class="hljs-keyword">class</span>)
<span class="hljs-title">public</span> <span class="hljs-title">class</span> <span class="hljs-title">MyApplication</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_12_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_12_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.EnableAutoConfiguration
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.domain.EntityScan
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-meta">@EnableAutoConfiguration</span>
<span class="hljs-meta">@EntityScan(basePackageClasses = [City::class])</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyApplication</span> </span>{

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.filter-scanned-entity-definitions"><a class="anchor" href="#howto.data-access.filter-scanned-entity-definitions"></a>Filter Scanned @Entity Definitions</h2>
<div class="sectionbody">
<div class="paragraph">
<p>It is possible to filter the <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/Entity.html" target="_blank"><code>@Entity</code></a> definitions using a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/orm/jpa/persistenceunit/ManagedClassNameFilter.html"><code>ManagedClassNameFilter</code></a> bean.
This can be useful in tests when only a sub-set of the available entities should be considered.
In the following example, only entities from the <code>com.example.app.customer</code> package are included:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_13">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_13_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_13_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_13_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_13_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_13_java" class="tabpanel" id="_tabs_13_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.orm.jpa.persistenceunit.ManagedClassNameFilter;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyEntityScanConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> ManagedClassNameFilter <span class="hljs-title">entityScanFilter</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> (className) -&gt; className.startsWith(<span class="hljs-string">"com.example.app.customer."</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_13_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_13_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.orm.jpa.persistenceunit.ManagedClassNameFilter

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyEntityScanConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">entityScanFilter</span><span class="hljs-params">()</span></span> : ManagedClassNameFilter {
		<span class="hljs-keyword">return</span> ManagedClassNameFilter { className -&gt;
			className.startsWith(<span class="hljs-string">"com.example.app.customer."</span>)
		}
	}
}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.jpa-properties"><a class="anchor" href="#howto.data-access.jpa-properties"></a>Configure JPA Properties</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Data JPA already provides some vendor-independent configuration options (such as those for SQL logging), and Spring Boot exposes those options and a few more for Hibernate as external configuration properties.
Some of them are automatically detected according to the context so you should not have to set them.</p>
</div>
<div class="paragraph">
<p>The <code>spring.jpa.hibernate.ddl-auto</code> is a special case, because, depending on runtime conditions, it has different defaults.
If an embedded database is used and no schema manager (such as Liquibase or Flyway) is handling the <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a>, it defaults to <code>create-drop</code>.
In all other cases, it defaults to <code>none</code>.</p>
</div>
<div class="paragraph">
<p>The dialect to use is detected by the JPA provider.
If you prefer to set the dialect yourself, set the <code>spring.jpa.database-platform</code> property.</p>
</div>
<div class="paragraph">
<p>The most common options to set are shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_14">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_14_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_14_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_14_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_14_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_14_properties" class="tabpanel" id="_tabs_14_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.jpa.hibernate.naming.physical-strategy</span>=<span class="hljs-string">com.example.MyPhysicalNamingStrategy</span>
<span class="hljs-meta">spring.jpa.show-sql</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_14_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_14_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">jpa:</span>
    <span class="hljs-attr">hibernate:</span>
      <span class="hljs-attr">naming:</span>
        <span class="hljs-attr">physical-strategy:</span> <span class="hljs-string">"com.example.MyPhysicalNamingStrategy"</span>
    <span class="hljs-attr">show-sql:</span> <span class="hljs-literal">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>In addition, all properties in <code>spring.jpa.properties.*</code> are passed through as normal JPA properties (with the prefix stripped) when the local <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a> is created.</p>
</div>
<div class="admonitionblock warning">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-warning" title="Warning"></i>
</td>
<td class="content">
<div class="paragraph">
<p>You need to ensure that names defined under <code>spring.jpa.properties.*</code> exactly match those expected by your JPA provider.
Spring Boot will not attempt any kind of relaxed binding for these entries.</p>
</div>
<div class="paragraph">
<p>For example, if you want to configure Hibernate’s batch size you must use <code>spring.jpa.properties.hibernate.jdbc.batch_size</code>.
If you use other forms, such as <code>batchSize</code> or <code>batch-size</code>, Hibernate will not apply the setting.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you need to apply advanced customization to Hibernate properties, consider registering a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/orm/jpa/HibernatePropertiesCustomizer.html"><code>HibernatePropertiesCustomizer</code></a> bean that will be invoked prior to creating the <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a>.
This takes precedence over anything that is applied by the auto-configuration.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.configure-hibernate-naming-strategy"><a class="anchor" href="#howto.data-access.configure-hibernate-naming-strategy"></a>Configure Hibernate Naming Strategy</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Hibernate uses <a class="external" href="https://docs.jboss.org/hibernate/orm/6.6/userguide/html_single/Hibernate_User_Guide.html#naming" target="_blank">two different naming strategies</a> to map names from the object model to the corresponding database names.
The fully qualified class name of the physical and the implicit strategy implementations can be configured by setting the <code>spring.jpa.hibernate.naming.physical-strategy</code> and <code>spring.jpa.hibernate.naming.implicit-strategy</code> properties, respectively.
Alternatively, if <a class="apiref external" href="https://docs.jboss.org/hibernate/orm/6.6/javadocs/org/hibernate/boot/model/naming/ImplicitNamingStrategy.html" target="_blank"><code>ImplicitNamingStrategy</code></a> or <a class="apiref external" href="https://docs.jboss.org/hibernate/orm/6.6/javadocs/org/hibernate/boot/model/naming/PhysicalNamingStrategy.html" target="_blank"><code>PhysicalNamingStrategy</code></a> beans are available in the application context, Hibernate will be automatically configured to use them.</p>
</div>
<div class="paragraph">
<p>By default, Spring Boot configures the physical naming strategy with <a class="apiref external" href="https://docs.jboss.org/hibernate/orm/6.6/javadocs/org/hibernate/boot/model/naming/CamelCaseToUnderscoresNamingStrategy.html" target="_blank"><code>CamelCaseToUnderscoresNamingStrategy</code></a>.
Using this strategy, all dots are replaced by underscores and camel casing is replaced by underscores as well.
Additionally, by default, all table names are generated in lower case.
For example, a <code>TelephoneNumber</code> entity is mapped to the <code>telephone_number</code> table.
If your schema requires mixed-case identifiers, define a custom <a class="apiref external" href="https://docs.jboss.org/hibernate/orm/6.6/javadocs/org/hibernate/boot/model/naming/CamelCaseToUnderscoresNamingStrategy.html" target="_blank"><code>CamelCaseToUnderscoresNamingStrategy</code></a> bean, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_15">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_15_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_15_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_15_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_15_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_15_java" class="tabpanel" id="_tabs_15_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy;
<span class="hljs-keyword">import</span> org.hibernate.engine.jdbc.env.spi.JdbcEnvironment;

<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyHibernateConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> CamelCaseToUnderscoresNamingStrategy <span class="hljs-title">caseSensitivePhysicalNamingStrategy</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> CamelCaseToUnderscoresNamingStrategy() {

			<span class="hljs-meta">@Override</span>
			<span class="hljs-function"><span class="hljs-keyword">protected</span> <span class="hljs-keyword">boolean</span> <span class="hljs-title">isCaseInsensitive</span><span class="hljs-params">(JdbcEnvironment jdbcEnvironment)</span> </span>{
				<span class="hljs-keyword">return</span> <span class="hljs-keyword">false</span>;
			}

		};
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_15_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_15_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
<span class="hljs-keyword">import</span> org.hibernate.engine.jdbc.env.spi.JdbcEnvironment
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyHibernateConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">caseSensitivePhysicalNamingStrategy</span><span class="hljs-params">()</span></span>: CamelCaseToUnderscoresNamingStrategy {
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">object</span> : CamelCaseToUnderscoresNamingStrategy() {
			<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">isCaseInsensitive</span><span class="hljs-params">(jdbcEnvironment: <span class="hljs-type">JdbcEnvironment</span>)</span></span>: <span class="hljs-built_in">Boolean</span> {
				<span class="hljs-keyword">return</span> <span class="hljs-literal">false</span>
			}
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you prefer to use Hibernate’s default instead, set the following property:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_16">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_16_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_16_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_16_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_16_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_16_properties" class="tabpanel" id="_tabs_16_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.jpa.hibernate.naming.physical-strategy</span>=<span class="hljs-string">org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_16_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_16_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">jpa:</span>
    <span class="hljs-attr">hibernate:</span>
      <span class="hljs-attr">naming:</span>
        <span class="hljs-attr">physical-strategy:</span> <span class="hljs-string">org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Alternatively, you can configure the following bean:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_17">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_17_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_17_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_17_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_17_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_17_java" class="tabpanel" id="_tabs_17_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl;

<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyHibernateConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function">PhysicalNamingStrategyStandardImpl <span class="hljs-title">caseSensitivePhysicalNamingStrategy</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> PhysicalNamingStrategyStandardImpl();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_17_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_17_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-keyword">internal</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyHibernateConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">caseSensitivePhysicalNamingStrategy</span><span class="hljs-params">()</span></span>: PhysicalNamingStrategyStandardImpl {
		<span class="hljs-keyword">return</span> PhysicalNamingStrategyStandardImpl()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>See <a class="external" href="https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaAutoConfiguration.java" target="_blank"><code>HibernateJpaAutoConfiguration</code></a> and <a class="external" href="https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/orm/jpa/JpaBaseConfiguration.java" target="_blank"><code>JpaBaseConfiguration</code></a> for more details.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.configure-hibernate-second-level-caching"><a class="anchor" href="#howto.data-access.configure-hibernate-second-level-caching"></a>Configure Hibernate Second-Level Caching</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Hibernate <a class="external" href="https://docs.jboss.org/hibernate/orm/6.6/userguide/html_single/Hibernate_User_Guide.html#caching" target="_blank">second-level cache</a> can be configured for a range of cache providers.
Rather than configuring Hibernate to lookup the cache provider again, it is better to provide the one that is available in the context whenever possible.</p>
</div>
<div class="paragraph">
<p>To do this with JCache, first make sure that <code>org.hibernate.orm:hibernate-jcache</code> is available on the classpath.
Then, add a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/orm/jpa/HibernatePropertiesCustomizer.html"><code>HibernatePropertiesCustomizer</code></a> bean as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_18">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_18_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_18_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_18_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_18_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_18_java" class="tabpanel" id="_tabs_18_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.hibernate.cache.jcache.ConfigSettings;

<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
<span class="hljs-keyword">import</span> org.springframework.cache.jcache.JCacheCacheManager;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyHibernateSecondLevelCacheConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> HibernatePropertiesCustomizer <span class="hljs-title">hibernateSecondLevelCacheCustomizer</span><span class="hljs-params">(JCacheCacheManager cacheManager)</span> </span>{
		<span class="hljs-keyword">return</span> (properties) -&gt; properties.put(ConfigSettings.CACHE_MANAGER, cacheManager.getCacheManager());
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_18_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_18_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.hibernate.cache.jcache.ConfigSettings
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer
<span class="hljs-keyword">import</span> org.springframework.cache.jcache.JCacheCacheManager
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyHibernateSecondLevelCacheConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">hibernateSecondLevelCacheCustomizer</span><span class="hljs-params">(cacheManager: <span class="hljs-type">JCacheCacheManager</span>)</span></span>: HibernatePropertiesCustomizer {
		<span class="hljs-keyword">return</span> HibernatePropertiesCustomizer { properties -&gt;
			properties[ConfigSettings.CACHE_MANAGER] = cacheManager.cacheManager
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>This customizer will configure Hibernate to use the same <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/CacheManager.html"><code>CacheManager</code></a> as the one that the application uses.
It is also possible to use separate <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/CacheManager.html"><code>CacheManager</code></a> instances.
For details, see <a class="external" href="https://docs.jboss.org/hibernate/orm/6.6/userguide/html_single/Hibernate_User_Guide.html#caching-provider-jcache" target="_blank">the Hibernate user guide</a>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.dependency-injection-in-hibernate-components"><a class="anchor" href="#howto.data-access.dependency-injection-in-hibernate-components"></a>Use Dependency Injection in Hibernate Components</h2>
<div class="sectionbody">
<div class="paragraph">
<p>By default, Spring Boot registers a <a class="apiref external" href="https://docs.jboss.org/hibernate/orm/6.6/javadocs/org/hibernate/resource/beans/container/spi/BeanContainer.html" target="_blank"><code>BeanContainer</code></a> implementation that uses the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/BeanFactory.html"><code>BeanFactory</code></a> so that converters and entity listeners can use regular dependency injection.</p>
</div>
<div class="paragraph">
<p>You can disable or tune this behavior by registering a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/orm/jpa/HibernatePropertiesCustomizer.html"><code>HibernatePropertiesCustomizer</code></a> that removes or changes the <code>hibernate.resource.beans.container</code> property.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.use-custom-entity-manager"><a class="anchor" href="#howto.data-access.use-custom-entity-manager"></a>Use a Custom EntityManagerFactory</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To take full control of the configuration of the <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a>, you need to add a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> named ‘entityManagerFactory’.
Spring Boot auto-configuration switches off its entity manager in the presence of a bean of that type.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
When you create a bean for <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/orm/jpa/LocalContainerEntityManagerFactoryBean.html"><code>LocalContainerEntityManagerFactoryBean</code></a> yourself, any customization that was applied during the creation of the auto-configured <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/orm/jpa/LocalContainerEntityManagerFactoryBean.html"><code>LocalContainerEntityManagerFactoryBean</code></a> is lost.
Make sure to use the auto-configured <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/orm/jpa/EntityManagerFactoryBuilder.html"><code>EntityManagerFactoryBuilder</code></a> to retain JPA and vendor properties.
This is particularly important if you were relying on <code>spring.jpa.*</code> properties for configuring things like the naming strategy or the DDL mode.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.use-multiple-entity-managers"><a class="anchor" href="#howto.data-access.use-multiple-entity-managers"></a>Using Multiple EntityManagerFactories</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you need to use JPA against multiple datasources, you likely need one <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a> per datasource.
The <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/orm/jpa/LocalContainerEntityManagerFactoryBean.html"><code>LocalContainerEntityManagerFactoryBean</code></a> from Spring ORM allows you to configure an <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a> for your needs.
You can also reuse <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/orm/jpa/JpaProperties.html"><code>JpaProperties</code></a> to bind settings for a second <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a>.
Building upon <a href="#howto.data-access.configure-two-datasources">the example for configuring a second </a><a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a>, a second <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a> can be defined as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_19">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_19_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_19_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_19_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_19_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_19_java" class="tabpanel" id="_tabs_19_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> java.util.LinkedHashMap;
<span class="hljs-keyword">import</span> java.util.Map;
<span class="hljs-keyword">import</span> java.util.function.Function;

<span class="hljs-keyword">import</span> javax.sql.DataSource;

<span class="hljs-keyword">import</span> org.springframework.beans.factory.annotation.Qualifier;
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties;
<span class="hljs-keyword">import</span> org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.orm.jpa.JpaVendorAdapter;
<span class="hljs-keyword">import</span> org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
<span class="hljs-keyword">import</span> org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyAdditionalEntityManagerFactoryConfiguration</span> </span>{

	<span class="hljs-meta">@Qualifier</span>(<span class="hljs-string">"second"</span>)
	<span class="hljs-meta">@Bean</span>(defaultCandidate = <span class="hljs-keyword">false</span>)
	<span class="hljs-meta">@ConfigurationProperties</span>(<span class="hljs-string">"app.jpa"</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> JpaProperties <span class="hljs-title">secondJpaProperties</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> JpaProperties();
	}

	<span class="hljs-meta">@Qualifier</span>(<span class="hljs-string">"second"</span>)
	<span class="hljs-meta">@Bean</span>(defaultCandidate = <span class="hljs-keyword">false</span>)
	<span class="hljs-function"><span class="hljs-keyword">public</span> LocalContainerEntityManagerFactoryBean <span class="hljs-title">secondEntityManagerFactory</span><span class="hljs-params">(@Qualifier(<span class="hljs-string">"second"</span>)</span> DataSource dataSource,
			@<span class="hljs-title">Qualifier</span><span class="hljs-params">(<span class="hljs-string">"second"</span>)</span> JpaProperties jpaProperties) </span>{
		EntityManagerFactoryBuilder builder = createEntityManagerFactoryBuilder(jpaProperties);
		<span class="hljs-keyword">return</span> builder.dataSource(dataSource).packages(Order<span class="hljs-class">.<span class="hljs-keyword">class</span>).<span class="hljs-title">persistenceUnit</span>("<span class="hljs-title">second</span>").<span class="hljs-title">build</span>()</span>;
	}

	<span class="hljs-function"><span class="hljs-keyword">private</span> EntityManagerFactoryBuilder <span class="hljs-title">createEntityManagerFactoryBuilder</span><span class="hljs-params">(JpaProperties jpaProperties)</span> </span>{
		JpaVendorAdapter jpaVendorAdapter = createJpaVendorAdapter(jpaProperties);
		Function&lt;DataSource, Map&lt;String, ?&gt;&gt; jpaPropertiesFactory = (dataSource) -&gt; createJpaProperties(dataSource,
				jpaProperties.getProperties());
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> EntityManagerFactoryBuilder(jpaVendorAdapter, jpaPropertiesFactory, <span class="hljs-keyword">null</span>);
	}

	<span class="hljs-function"><span class="hljs-keyword">private</span> JpaVendorAdapter <span class="hljs-title">createJpaVendorAdapter</span><span class="hljs-params">(JpaProperties jpaProperties)</span> </span>{
		<span class="hljs-comment">// ... map JPA properties as needed</span>
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> HibernateJpaVendorAdapter();
	}

	<span class="hljs-keyword">private</span> Map&lt;String, ?&gt; createJpaProperties(DataSource dataSource, Map&lt;String, ?&gt; existingProperties) {
		Map&lt;String, ?&gt; jpaProperties = <span class="hljs-keyword">new</span> LinkedHashMap&lt;&gt;(existingProperties);
		<span class="hljs-comment">// ... map JPA properties that require the DataSource (e.g. DDL flags)</span>
		<span class="hljs-keyword">return</span> jpaProperties;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_19_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_19_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.beans.factory.<span class="hljs-keyword">annotation</span>.Qualifier
<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.orm.jpa.JpaProperties
<span class="hljs-keyword">import</span> org.springframework.boot.context.properties.ConfigurationProperties
<span class="hljs-keyword">import</span> org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.orm.jpa.JpaVendorAdapter
<span class="hljs-keyword">import</span> org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean
<span class="hljs-keyword">import</span> org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter
<span class="hljs-keyword">import</span> javax.sql.DataSource

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyAdditionalEntityManagerFactoryConfiguration</span> </span>{

	<span class="hljs-meta">@Qualifier(<span class="hljs-meta-string">"second"</span>)</span>
	<span class="hljs-meta">@Bean(defaultCandidate = false)</span>
	<span class="hljs-meta">@ConfigurationProperties(<span class="hljs-meta-string">"app.jpa"</span>)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">secondJpaProperties</span><span class="hljs-params">()</span></span>: JpaProperties {
		<span class="hljs-keyword">return</span> JpaProperties()
	}

	<span class="hljs-meta">@Qualifier(<span class="hljs-meta-string">"second"</span>)</span>
	<span class="hljs-meta">@Bean(defaultCandidate = false)</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">firstEntityManagerFactory</span><span class="hljs-params">(
		<span class="hljs-meta">@Qualifier(<span class="hljs-meta-string">"second"</span>)</span> dataSource: <span class="hljs-type">DataSource</span>,
		<span class="hljs-meta">@Qualifier(<span class="hljs-meta-string">"second"</span>)</span> jpaProperties: <span class="hljs-type">JpaProperties</span>
	)</span></span>: LocalContainerEntityManagerFactoryBean {
		<span class="hljs-keyword">val</span> builder = createEntityManagerFactoryBuilder(jpaProperties)
		<span class="hljs-keyword">return</span> builder.dataSource(dataSource).packages(Order::<span class="hljs-class"><span class="hljs-keyword">class</span>.<span class="hljs-title">java</span>).<span class="hljs-title">persistenceUnit</span></span>(<span class="hljs-string">"second"</span>).build()
	}

	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">createEntityManagerFactoryBuilder</span><span class="hljs-params">(jpaProperties: <span class="hljs-type">JpaProperties</span>)</span></span>: EntityManagerFactoryBuilder {
		<span class="hljs-keyword">val</span> jpaVendorAdapter = createJpaVendorAdapter(jpaProperties)
		<span class="hljs-keyword">val</span> jpaPropertiesFactory = { dataSource: DataSource -&gt;
				createJpaProperties(dataSource, jpaProperties.properties) }
		<span class="hljs-keyword">return</span> EntityManagerFactoryBuilder(jpaVendorAdapter, jpaPropertiesFactory, <span class="hljs-literal">null</span>)
	}

	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">createJpaVendorAdapter</span><span class="hljs-params">(jpaProperties: <span class="hljs-type">JpaProperties</span>)</span></span>: JpaVendorAdapter {
		<span class="hljs-comment">// ... map JPA properties as needed</span>
		<span class="hljs-keyword">return</span> HibernateJpaVendorAdapter()
	}

	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">createJpaProperties</span><span class="hljs-params">(dataSource: <span class="hljs-type">DataSource</span>, existingProperties: <span class="hljs-type">Map</span>&lt;<span class="hljs-type">String</span>, *&gt;)</span></span>: Map&lt;String, *&gt; {
		<span class="hljs-keyword">val</span> jpaProperties: Map&lt;String, *&gt; = LinkedHashMap(existingProperties)
		<span class="hljs-comment">// ... map JPA properties that require the DataSource (e.g. DDL flags)</span>
		<span class="hljs-keyword">return</span> jpaProperties
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The example above creates an <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a> using the <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> bean qualified with <code>@Qualifier("second")</code>.
It scans entities located in the same package as <code>Order</code>.
It is possible to map additional JPA properties using the <code>app.jpa</code> namespace.
The use of <code>@Bean(defaultCandidate=false)</code> allows the <code>secondJpaProperties</code> and <code>secondEntityManagerFactory</code> beans to be defined without interfering with auto-configured beans of the same type.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="https://docs.spring.io/spring-framework/reference/6.2/core/beans/dependencies/factory-autowire.html#beans-factory-autowire-candidate">Spring Framework reference documentation</a> describes this feature in more details.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>You should provide a similar configuration for any more additional data sources for which you need JPA access.
To complete the picture, you need to configure a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/orm/jpa/JpaTransactionManager.html"><code>JpaTransactionManager</code></a> for each <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a> as well.
Alternatively, you might be able to use a JTA transaction manager that spans both.</p>
</div>
<div class="paragraph">
<p>If you use Spring Data, you need to configure <a class="apiref" href="https://docs.spring.io/spring-data/jpa/docs/3.4.x/api/org/springframework/data/jpa/repository/config/EnableJpaRepositories.html"><code>@EnableJpaRepositories</code></a> accordingly, as shown in the following examples:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_20">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_20_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_20_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_20_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_20_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_20_java" class="tabpanel" id="_tabs_20_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.data.jpa.repository.config.EnableJpaRepositories;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-meta">@EnableJpaRepositories</span>(basePackageClasses = Order<span class="hljs-class">.<span class="hljs-keyword">class</span>, <span class="hljs-title">entityManagerFactoryRef</span> </span>= <span class="hljs-string">"entityManagerFactory"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">OrderConfiguration</span> </span>{

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_20_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_20_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.jpa.repository.config.EnableJpaRepositories

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-meta">@EnableJpaRepositories(basePackageClasses = [Order::class], entityManagerFactoryRef = <span class="hljs-meta-string">"firstEntityManagerFactory"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">OrderConfiguration</span></span></span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_21">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_21_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_21_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_21_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_21_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_21_java" class="tabpanel" id="_tabs_21_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.data.jpa.repository.config.EnableJpaRepositories;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-meta">@EnableJpaRepositories</span>(basePackageClasses = Customer<span class="hljs-class">.<span class="hljs-keyword">class</span>, <span class="hljs-title">entityManagerFactoryRef</span> </span>= <span class="hljs-string">"secondEntityManagerFactory"</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">CustomerConfiguration</span> </span>{

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_21_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_21_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.<span class="hljs-keyword">data</span>.jpa.repository.config.EnableJpaRepositories

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-meta">@EnableJpaRepositories(basePackageClasses = [Customer::class], entityManagerFactoryRef = <span class="hljs-meta-string">"secondEntityManagerFactory"</span>)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">CustomerConfiguration</span></span></span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.use-traditional-persistence-xml"><a class="anchor" href="#howto.data-access.use-traditional-persistence-xml"></a>Use a Traditional persistence.xml File</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot will not search for or use a <code>META-INF/persistence.xml</code> by default.
If you prefer to use a traditional <code>persistence.xml</code>, you need to define your own <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> of type <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/orm/jpa/LocalEntityManagerFactoryBean.html"><code>LocalEntityManagerFactoryBean</code></a> (with an ID of ‘entityManagerFactory’) and set the persistence unit name there.</p>
</div>
<div class="paragraph">
<p>See <a class="external" href="https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/orm/jpa/JpaBaseConfiguration.java" target="_blank"><code>JpaBaseConfiguration</code></a> for the default settings.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.use-spring-data-jpa-and-mongo-repositories"><a class="anchor" href="#howto.data-access.use-spring-data-jpa-and-mongo-repositories"></a>Use Spring Data JPA and Mongo Repositories</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Data JPA and Spring Data Mongo can both automatically create <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> implementations for you.
If they are both present on the classpath, you might have to do some extra configuration to tell Spring Boot which repositories to create.
The most explicit way to do that is to use the standard Spring Data <a class="apiref" href="https://docs.spring.io/spring-data/jpa/docs/3.4.x/api/org/springframework/data/jpa/repository/config/EnableJpaRepositories.html"><code>@EnableJpaRepositories</code></a> and <a class="apiref" href="https://docs.spring.io/spring-data/mongodb/docs/4.4.x/api/org/springframework/data/mongodb/repository/config/EnableMongoRepositories.html"><code>@EnableMongoRepositories</code></a> annotations and provide the location of your <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> interfaces.</p>
</div>
<div class="paragraph">
<p>There are also flags (<code>spring.data.*.repositories.enabled</code> and <code>spring.data.*.repositories.type</code>) that you can use to switch the auto-configured repositories on and off in external configuration.
Doing so is useful, for instance, in case you want to switch off the Mongo repositories and still use the auto-configured <a class="apiref" href="https://docs.spring.io/spring-data/mongodb/docs/4.4.x/api/org/springframework/data/mongodb/core/MongoTemplate.html"><code>MongoTemplate</code></a>.</p>
</div>
<div class="paragraph">
<p>The same obstacle and the same features exist for other auto-configured Spring Data repository types (Elasticsearch, Redis, and others).
To work with them, change the names of the annotations and flags accordingly.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.customize-spring-data-web-support"><a class="anchor" href="#howto.data-access.customize-spring-data-web-support"></a>Customize Spring Data’s Web Support</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Data provides web support that simplifies the use of Spring Data repositories in a web application.
Spring Boot provides properties in the <code>spring.data.web</code> namespace for customizing its configuration.
Note that if you are using Spring Data REST, you must use the properties in the <code>spring.data.rest</code> namespace instead.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.exposing-spring-data-repositories-as-rest"><a class="anchor" href="#howto.data-access.exposing-spring-data-repositories-as-rest"></a>Expose Spring Data Repositories as REST Endpoint</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Data REST can expose the <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> implementations as REST endpoints for you,
provided Spring MVC has been enabled for the application.</p>
</div>
<div class="paragraph">
<p>Spring Boot exposes a set of useful properties (from the <code>spring.data.rest</code> namespace) that customize the <a class="apiref" href="https://docs.spring.io/spring-data/rest/docs/4.4.x/api/org/springframework/data/rest/core/config/RepositoryRestConfiguration.html"><code>RepositoryRestConfiguration</code></a>.
If you need to provide additional customization, you should use a <a class="apiref" href="https://docs.spring.io/spring-data/rest/docs/4.4.x/api/org/springframework/data/rest/webmvc/config/RepositoryRestConfigurer.html"><code>RepositoryRestConfigurer</code></a> bean.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If you do not specify any order on your custom <a class="apiref" href="https://docs.spring.io/spring-data/rest/docs/4.4.x/api/org/springframework/data/rest/webmvc/config/RepositoryRestConfigurer.html"><code>RepositoryRestConfigurer</code></a>, it runs after the one Spring Boot uses internally.
If you need to specify an order, make sure it is higher than 0.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.configure-a-component-that-is-used-by-jpa"><a class="anchor" href="#howto.data-access.configure-a-component-that-is-used-by-jpa"></a>Configure a Component that is Used by JPA</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you want to configure a component that JPA uses, then you need to ensure that the component is initialized before JPA.
When the component is auto-configured, Spring Boot takes care of this for you.
For example, when Flyway is auto-configured, Hibernate is configured to depend on Flyway so that Flyway has a chance to initialize the database before Hibernate tries to use it.</p>
</div>
<div class="paragraph">
<p>If you are configuring a component yourself, you can use an <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/orm/jpa/EntityManagerFactoryDependsOnPostProcessor.html"><code>EntityManagerFactoryDependsOnPostProcessor</code></a> subclass as a convenient way of setting up the necessary dependencies.
For example, if you use Hibernate Search with Elasticsearch as its index manager, any <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a> beans must be configured to depend on the <code>elasticsearchClient</code> bean, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_22">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_22_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_22_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_22_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_22_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_22_java" class="tabpanel" id="_tabs_22_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> jakarta.persistence.EntityManagerFactory;

<span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.orm.jpa.EntityManagerFactoryDependsOnPostProcessor;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-comment">/**
 * {<span class="hljs-doctag">@link</span> EntityManagerFactoryDependsOnPostProcessor} that ensures that
 * {<span class="hljs-doctag">@link</span> EntityManagerFactory} beans depend on the {<span class="hljs-doctag">@code</span> elasticsearchClient} bean.
 */</span>
<span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">ElasticsearchEntityManagerFactoryDependsOnPostProcessor</span>
		<span class="hljs-keyword">extends</span> <span class="hljs-title">EntityManagerFactoryDependsOnPostProcessor</span> </span>{

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">ElasticsearchEntityManagerFactoryDependsOnPostProcessor</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">super</span>(<span class="hljs-string">"elasticsearchClient"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_22_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_22_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.autoconfigure.orm.jpa.EntityManagerFactoryDependsOnPostProcessor
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">ElasticsearchEntityManagerFactoryDependsOnPostProcessor</span> :
	<span class="hljs-type">EntityManagerFactoryDependsOnPostProcessor</span></span>(<span class="hljs-string">"elasticsearchClient"</span>)</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.data-access.configure-jooq-with-multiple-datasources"><a class="anchor" href="#howto.data-access.configure-jooq-with-multiple-datasources"></a>Configure jOOQ with Two DataSources</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you need to use jOOQ with multiple data sources, you should create your own <a class="apiref external" href="https://www.jooq.org/javadoc/3.19.23/org/jooq/DSLContext.html" target="_blank"><code>DSLContext</code></a> for each one.
See <a class="external" href="https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jooq/JooqAutoConfiguration.java" target="_blank"><code>JooqAutoConfiguration</code></a> for more details.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
In particular, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jooq/ExceptionTranslatorExecuteListener.html"><code>ExceptionTranslatorExecuteListener</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jooq/SpringTransactionProvider.html"><code>SpringTransactionProvider</code></a> can be reused to provide similar features to what the auto-configuration does with a single <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a>.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="logging.html">Logging</a></span>
<span class="next"><a href="data-initialization.html">Database Initialization</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../how-to/data-access.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="data-access.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../3.3/how-to/data-access.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../4.0-SNAPSHOT/how-to/data-access.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../3.5-SNAPSHOT/how-to/data-access.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../3.4-SNAPSHOT/how-to/data-access.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../3.3-SNAPSHOT/how-to/data-access.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../../../images/spring-boot___img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../../../images/spring-boot___img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../../../images/spring-boot___img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>
</body></html>