<!DOCTYPE html>
<html><head><title>Embedded Web Servers :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/how-to/webserver.html"/><meta content="2025-06-04T16:06:28.515630" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../reference/packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../reference/actuator/index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/loggers.html">Loggers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../reference/actuator/cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="2">
<a class="nav-link" href="webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Embedded Web Servers">
<div class="toc-menu"><h3>Embedded Web Servers</h3><ul><li data-level="1"><a href="#howto.webserver.use-another">Use Another Web Server</a></li><li data-level="1"><a href="#howto.webserver.disable">Disabling the Web Server</a></li><li data-level="1"><a href="#howto.webserver.change-port">Change the HTTP Port</a></li><li data-level="1"><a href="#howto.webserver.use-random-port">Use a Random Unassigned HTTP Port</a></li><li data-level="1"><a href="#howto.webserver.discover-port">Discover the HTTP Port at Runtime</a></li><li data-level="1"><a href="#howto.webserver.enable-response-compression">Enable HTTP Response Compression</a></li><li data-level="1"><a href="#howto.webserver.configure-ssl">Configure SSL</a></li><li data-level="2"><a href="#howto.webserver.configure-ssl.pem-files">Using PEM-encoded files</a></li><li data-level="2"><a href="#howto.webserver.configure-ssl.bundles">Using SSL Bundles</a></li><li data-level="2"><a href="#howto.webserver.configure-ssl.sni">Configure Server Name Indication</a></li><li data-level="1"><a href="#howto.webserver.configure-http2">Configure HTTP/2</a></li><li data-level="2"><a href="#howto.webserver.configure-http2.tomcat">HTTP/2 With Tomcat</a></li><li data-level="2"><a href="#howto.webserver.configure-http2.jetty">HTTP/2 With Jetty</a></li><li data-level="2"><a href="#howto.webserver.configure-http2.netty">HTTP/2 With Reactor Netty</a></li><li data-level="2"><a href="#howto.webserver.configure-http2.undertow">HTTP/2 With Undertow</a></li><li data-level="1"><a href="#howto.webserver.configure">Configure the Web Server</a></li><li data-level="1"><a href="#howto.webserver.add-servlet-filter-listener">Add a Servlet, Filter, or Listener to an Application</a></li><li data-level="2"><a href="#howto.webserver.add-servlet-filter-listener.spring-bean">Add a Servlet, Filter, or Listener by Using a Spring Bean</a></li><li data-level="2"><a href="#howto.webserver.add-servlet-filter-listener.using-scanning">Add Servlets, Filters, and Listeners by Using Classpath Scanning</a></li><li data-level="1"><a href="#howto.webserver.configure-access-logs">Configure Access Logging</a></li><li data-level="1"><a href="#howto.webserver.use-behind-a-proxy-server">Running Behind a Front-end Proxy Server</a></li><li data-level="2"><a href="#howto.webserver.use-behind-a-proxy-server.tomcat">Customize Tomcat’s Proxy Configuration</a></li><li data-level="1"><a href="#howto.webserver.enable-multiple-connectors-in-tomcat">Enable Multiple Connectors with Tomcat</a></li><li data-level="1"><a href="#howto.webserver.enable-tomcat-mbean-registry">Enable Tomcat’s MBean Registry</a></li><li data-level="1"><a href="#howto.webserver.enable-multiple-listeners-in-undertow">Enable Multiple Listeners with Undertow</a></li><li data-level="1"><a href="#howto.webserver.create-websocket-endpoints-using-serverendpoint">Create WebSocket Endpoints Using @ServerEndpoint</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/webserver.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../index.html">Spring Boot</a></li>
<li><a href="index.html">How-to Guides</a></li>
<li><a href="webserver.html">Embedded Web Servers</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../how-to/webserver.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Embedded Web Servers</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Embedded Web Servers</h3><ul><li data-level="1"><a href="#howto.webserver.use-another">Use Another Web Server</a></li><li data-level="1"><a href="#howto.webserver.disable">Disabling the Web Server</a></li><li data-level="1"><a href="#howto.webserver.change-port">Change the HTTP Port</a></li><li data-level="1"><a href="#howto.webserver.use-random-port">Use a Random Unassigned HTTP Port</a></li><li data-level="1"><a href="#howto.webserver.discover-port">Discover the HTTP Port at Runtime</a></li><li data-level="1"><a href="#howto.webserver.enable-response-compression">Enable HTTP Response Compression</a></li><li data-level="1"><a href="#howto.webserver.configure-ssl">Configure SSL</a></li><li data-level="2"><a href="#howto.webserver.configure-ssl.pem-files">Using PEM-encoded files</a></li><li data-level="2"><a href="#howto.webserver.configure-ssl.bundles">Using SSL Bundles</a></li><li data-level="2"><a href="#howto.webserver.configure-ssl.sni">Configure Server Name Indication</a></li><li data-level="1"><a href="#howto.webserver.configure-http2">Configure HTTP/2</a></li><li data-level="2"><a href="#howto.webserver.configure-http2.tomcat">HTTP/2 With Tomcat</a></li><li data-level="2"><a href="#howto.webserver.configure-http2.jetty">HTTP/2 With Jetty</a></li><li data-level="2"><a href="#howto.webserver.configure-http2.netty">HTTP/2 With Reactor Netty</a></li><li data-level="2"><a href="#howto.webserver.configure-http2.undertow">HTTP/2 With Undertow</a></li><li data-level="1"><a href="#howto.webserver.configure">Configure the Web Server</a></li><li data-level="1"><a href="#howto.webserver.add-servlet-filter-listener">Add a Servlet, Filter, or Listener to an Application</a></li><li data-level="2"><a href="#howto.webserver.add-servlet-filter-listener.spring-bean">Add a Servlet, Filter, or Listener by Using a Spring Bean</a></li><li data-level="2"><a href="#howto.webserver.add-servlet-filter-listener.using-scanning">Add Servlets, Filters, and Listeners by Using Classpath Scanning</a></li><li data-level="1"><a href="#howto.webserver.configure-access-logs">Configure Access Logging</a></li><li data-level="1"><a href="#howto.webserver.use-behind-a-proxy-server">Running Behind a Front-end Proxy Server</a></li><li data-level="2"><a href="#howto.webserver.use-behind-a-proxy-server.tomcat">Customize Tomcat’s Proxy Configuration</a></li><li data-level="1"><a href="#howto.webserver.enable-multiple-connectors-in-tomcat">Enable Multiple Connectors with Tomcat</a></li><li data-level="1"><a href="#howto.webserver.enable-tomcat-mbean-registry">Enable Tomcat’s MBean Registry</a></li><li data-level="1"><a href="#howto.webserver.enable-multiple-listeners-in-undertow">Enable Multiple Listeners with Undertow</a></li><li data-level="1"><a href="#howto.webserver.create-websocket-endpoints-using-serverendpoint">Create WebSocket Endpoints Using @ServerEndpoint</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>Each Spring Boot web application includes an embedded web server.
This feature leads to a number of how-to questions, including how to change the embedded server and how to configure the embedded server.
This section answers those questions.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.use-another"><a class="anchor" href="#howto.webserver.use-another"></a>Use Another Web Server</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Many Spring Boot starters include default embedded containers.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>For servlet stack applications, the <code>spring-boot-starter-web</code> includes Tomcat by including <code>spring-boot-starter-tomcat</code>, but you can use <code>spring-boot-starter-jetty</code> or <code>spring-boot-starter-undertow</code> instead.</p>
</li>
<li>
<p>For reactive stack applications, the <code>spring-boot-starter-webflux</code> includes  Reactor Netty by including <code>spring-boot-starter-reactor-netty</code>, but you can use <code>spring-boot-starter-tomcat</code>, <code>spring-boot-starter-jetty</code>, or <code>spring-boot-starter-undertow</code> instead.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>When switching to a different HTTP server, you need to swap the default dependencies for those that you need instead.
To help with this process, Spring Boot provides a separate starter for each of the supported HTTP servers.</p>
</div>
<div class="paragraph">
<p>The following Maven example shows how to exclude Tomcat and include Jetty for Spring MVC:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-starter-web<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">exclusions</span>&gt;</span>
		<span class="hljs-comment">&lt;!-- Exclude the Tomcat dependency --&gt;</span>
		<span class="hljs-tag">&lt;<span class="hljs-name">exclusion</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
			<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-starter-tomcat<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
		<span class="hljs-tag">&lt;/<span class="hljs-name">exclusion</span>&gt;</span>
	<span class="hljs-tag">&lt;/<span class="hljs-name">exclusions</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span>
<span class="hljs-comment">&lt;!-- Use Jetty instead --&gt;</span>
<span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>org.springframework.boot<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>spring-boot-starter-jetty<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The following Gradle example configures the necessary dependencies and a <a class="external" href="https://docs.gradle.org/current/userguide/resolution_rules.html#sec:module_replacement" target="_blank">module replacement</a> to use Undertow in place of Reactor Netty for Spring WebFlux:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-gradle hljs" data-lang="gradle">dependencies {
	implementation "org.springframework.boot:spring-boot-starter-undertow"
	implementation "org.springframework.boot:spring-boot-starter-webflux"
	modules {
		module("org.springframework.boot:spring-boot-starter-reactor-netty") {
			replacedBy("org.springframework.boot:spring-boot-starter-undertow", "Use Undertow instead of Reactor Netty")
		}
	}
}</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<code>spring-boot-starter-reactor-netty</code> is required to use the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a> class, so you may need to keep a dependency on Netty even when you need to include a different HTTP server.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.disable"><a class="anchor" href="#howto.webserver.disable"></a>Disabling the Web Server</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If your classpath contains the necessary bits to start a web server, Spring Boot will automatically start it.
To disable this behavior configure the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/WebApplicationType.html"><code>WebApplicationType</code></a> in your <code>application.properties</code>, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_1_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_1_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_1_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_properties" class="tabpanel" id="_tabs_1_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.main.web-application-type</span>=<span class="hljs-string">none</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_1_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">main:</span>
    <span class="hljs-attr">web-application-type:</span> <span class="hljs-string">"none"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.change-port"><a class="anchor" href="#howto.webserver.change-port"></a>Change the HTTP Port</h2>
<div class="sectionbody">
<div class="paragraph">
<p>In a standalone application, the main HTTP port defaults to <code>8080</code> but can be set with <code>server.port</code> (for example, in <code>application.properties</code> or as a System property).
Thanks to relaxed binding of <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html"><code>Environment</code></a> values, you can also use <code>SERVER_PORT</code> (for example, as an OS environment variable).</p>
</div>
<div class="paragraph">
<p>To switch off the HTTP endpoints completely but still create a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/context/WebApplicationContext.html"><code>WebApplicationContext</code></a>, use <code>server.port=-1</code> (doing so is sometimes useful for testing).</p>
</div>
<div class="paragraph">
<p>For more details, see <a class="xref page" href="../reference/web/servlet.html#web.servlet.embedded-container.customizing">Customizing Embedded Servlet Containers</a> in the ‘Spring Boot Features’ section, or the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/web/ServerProperties.html"><code>ServerProperties</code></a> class.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.use-random-port"><a class="anchor" href="#howto.webserver.use-random-port"></a>Use a Random Unassigned HTTP Port</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To scan for a free port (using OS natives to prevent clashes) use <code>server.port=0</code>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.discover-port"><a class="anchor" href="#howto.webserver.discover-port"></a>Discover the HTTP Port at Runtime</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can access the port the server is running on from log output or from the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/context/WebServerApplicationContext.html"><code>WebServerApplicationContext</code></a> through its <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/WebServer.html"><code>WebServer</code></a>.
The best way to get that and be sure it has been initialized is to add a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> of type <code>ApplicationListener&lt;WebServerInitializedEvent&gt;</code> and pull the container out of the event when it is published.</p>
</div>
<div class="paragraph">
<p>Tests that use <code>@SpringBootTest(webEnvironment=WebEnvironment.RANDOM_PORT)</code> can also inject the actual port into a field by using the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/web/server/LocalServerPort.html"><code>@LocalServerPort</code></a> annotation, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_2_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_2_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_2_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_java" class="tabpanel" id="_tabs_2_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest;
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
<span class="hljs-keyword">import</span> org.springframework.boot.test.web.server.LocalServerPort;

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest</span>(webEnvironment = WebEnvironment.RANDOM_PORT)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebIntegrationTests</span> </span>{

	<span class="hljs-meta">@LocalServerPort</span>
	<span class="hljs-keyword">int</span> port;

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_2_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest
<span class="hljs-keyword">import</span> org.springframework.boot.test.context.SpringBootTest.WebEnvironment
<span class="hljs-keyword">import</span> org.springframework.boot.test.web.server.LocalServerPort

</span><span class="fold-block"><span class="hljs-meta">@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebIntegrationTests</span> </span>{

	<span class="hljs-meta">@LocalServerPort</span>
	<span class="hljs-keyword">var</span> port = <span class="hljs-number">0</span>

	<span class="hljs-comment">// ...</span>

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/test/web/server/LocalServerPort.html"><code>@LocalServerPort</code></a> is a meta-annotation for <code>@Value("${local.server.port}")</code>.
Do not try to inject the port in a regular application.
As we just saw, the value is set only after the container has been initialized.
Contrary to a test, application code callbacks are processed early (before the value is actually available).</p>
</div>
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.enable-response-compression"><a class="anchor" href="#howto.webserver.enable-response-compression"></a>Enable HTTP Response Compression</h2>
<div class="sectionbody">
<div class="paragraph">
<p>HTTP response compression is supported by Jetty, Tomcat, Reactor Netty, and Undertow.
It can be enabled in <code>application.properties</code>, as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_3_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_3_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_3_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_properties" class="tabpanel" id="_tabs_3_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.compression.enabled</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_3_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">compression:</span>
    <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>By default, responses must be at least 2048 bytes in length for compression to be performed.
You can configure this behavior by setting the <code>server.compression.min-response-size</code> property.</p>
</div>
<div class="paragraph">
<p>By default, responses are compressed only if their content type is one of the following:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>text/html</code></p>
</li>
<li>
<p><code>text/xml</code></p>
</li>
<li>
<p><code>text/plain</code></p>
</li>
<li>
<p><code>text/css</code></p>
</li>
<li>
<p><code>text/javascript</code></p>
</li>
<li>
<p><code>application/javascript</code></p>
</li>
<li>
<p><code>application/json</code></p>
</li>
<li>
<p><code>application/xml</code></p>
</li>
</ul>
</div>
<div class="paragraph">
<p>You can configure this behavior by setting the <code>server.compression.mime-types</code> property.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.configure-ssl"><a class="anchor" href="#howto.webserver.configure-ssl"></a>Configure SSL</h2>
<div class="sectionbody">
<div class="paragraph">
<p>SSL can be configured declaratively by setting the various <code>server.ssl.*</code> properties, typically in <code>application.properties</code> or <code>application.yaml</code>.
See <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/Ssl.html"><code>Ssl</code></a> for details of all of the supported properties.</p>
</div>
<div class="paragraph">
<p>The following example shows setting SSL properties using a Java KeyStore file:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_4_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_4_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_4_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_properties" class="tabpanel" id="_tabs_4_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.port</span>=<span class="hljs-string">8443</span>
<span class="hljs-meta">server.ssl.key-store</span>=<span class="hljs-string">classpath:keystore.jks</span>
<span class="hljs-meta">server.ssl.key-store-password</span>=<span class="hljs-string">secret</span>
<span class="hljs-meta">server.ssl.key-password</span>=<span class="hljs-string">another-secret</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_4_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">port:</span> <span class="hljs-number">8443</span>
  <span class="hljs-attr">ssl:</span>
    <span class="hljs-attr">key-store:</span> <span class="hljs-string">"classpath:keystore.jks"</span>
    <span class="hljs-attr">key-store-password:</span> <span class="hljs-string">"secret"</span>
    <span class="hljs-attr">key-password:</span> <span class="hljs-string">"another-secret"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Using configuration such as the preceding example means the application no longer supports a plain HTTP connector at port 8080.
Spring Boot does not support the configuration of both an HTTP connector and an HTTPS connector through <code>application.properties</code>.
If you want to have both, you need to configure one of them programmatically.
We recommend using <code>application.properties</code> to configure HTTPS, as the HTTP connector is the easier of the two to configure programmatically.</p>
</div>
<div class="sect2">
<h3 id="howto.webserver.configure-ssl.pem-files"><a class="anchor" href="#howto.webserver.configure-ssl.pem-files"></a>Using PEM-encoded files</h3>
<div class="paragraph">
<p>You can use PEM-encoded files instead of Java KeyStore files.
You should use PKCS#8 key files wherever possible.
PEM-encoded PKCS#8 key files start with a <code>-----BEGIN PRIVATE KEY-----</code> or <code>-----BEGIN ENCRYPTED PRIVATE KEY-----</code> header.</p>
</div>
<div class="paragraph">
<p>If you have files in other formats, e.g., PKCS#1 (<code>-----BEGIN RSA PRIVATE KEY-----</code>) or SEC 1 (<code>-----BEGIN EC PRIVATE KEY-----</code>), you can convert them to PKCS#8 using OpenSSL:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-shell hljs" data-lang="shell">openssl pkcs8 -topk8 -nocrypt -in &lt;input file&gt; -out &lt;output file&gt;</code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>The following example shows setting SSL properties using PEM-encoded certificate and private key files:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_5_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_5_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_5_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_properties" class="tabpanel" id="_tabs_5_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.port</span>=<span class="hljs-string">8443</span>
<span class="hljs-meta">server.ssl.certificate</span>=<span class="hljs-string">classpath:my-cert.crt</span>
<span class="hljs-meta">server.ssl.certificate-private-key</span>=<span class="hljs-string">classpath:my-cert.key</span>
<span class="hljs-meta">server.ssl.trust-certificate</span>=<span class="hljs-string">classpath:ca-cert.crt</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_5_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">port:</span> <span class="hljs-number">8443</span>
  <span class="hljs-attr">ssl:</span>
    <span class="hljs-attr">certificate:</span> <span class="hljs-string">"classpath:my-cert.crt"</span>
    <span class="hljs-attr">certificate-private-key:</span> <span class="hljs-string">"classpath:my-cert.key"</span>
    <span class="hljs-attr">trust-certificate:</span> <span class="hljs-string">"classpath:ca-cert.crt"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="howto.webserver.configure-ssl.bundles"><a class="anchor" href="#howto.webserver.configure-ssl.bundles"></a>Using SSL Bundles</h3>
<div class="paragraph">
<p>Alternatively, the SSL trust material can be configured in an <a class="xref page" href="../reference/features/ssl.html">SSL bundle</a> and applied to the web server as shown in this example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_6_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_6_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_6_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_properties" class="tabpanel" id="_tabs_6_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.port</span>=<span class="hljs-string">8443</span>
<span class="hljs-meta">server.ssl.bundle</span>=<span class="hljs-string">example</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_6_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">port:</span> <span class="hljs-number">8443</span>
  <span class="hljs-attr">ssl:</span>
    <span class="hljs-attr">bundle:</span> <span class="hljs-string">"example"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>The <code>server.ssl.bundle</code> property can not be combined with the discrete Java KeyStore or PEM property options under <code>server.ssl</code>.</p>
</div>
<div class="paragraph">
<p>The <code>server.ssl.ciphers</code>, <code>server.ssl.enabled-protocols</code>, <code>server.ssl.protocol</code> properties are also ignored when using a bundle.
These properties should instead be defined using <code>spring.ssl.bundle.&lt;type&gt;.&lt;name&gt;.options</code> properties.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="howto.webserver.configure-ssl.sni"><a class="anchor" href="#howto.webserver.configure-ssl.sni"></a>Configure Server Name Indication</h3>
<div class="paragraph">
<p>Tomcat, Netty, and Undertow can be configured to use unique SSL trust material for individual host names to support Server Name Indication (SNI).
SNI configuration is not supported with Jetty, but Jetty can <a class="external" href="https://eclipse.dev/jetty/documentation/jetty-12/operations-guide/index.html#og-protocols-ssl-sni" target="_blank">automatically set up SNI</a> if multiple certificates are provided to it.</p>
</div>
<div class="paragraph">
<p>Assuming <a class="xref page" href="../reference/features/ssl.html">SSL bundles</a> named <code>web</code>, <code>web-alt1</code>, and <code>web-alt2</code> have been configured, the following configuration can be used to assign each bundle to a host name served by the embedded web server:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_7_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_7_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_7_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_properties" class="tabpanel" id="_tabs_7_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.port</span>=<span class="hljs-string">8443</span>
<span class="hljs-meta">server.ssl.bundle</span>=<span class="hljs-string">web</span>
<span class="hljs-meta">server.ssl.server-name-bundles[0].server-name</span>=<span class="hljs-string">alt1.example.com</span>
<span class="hljs-meta">server.ssl.server-name-bundles[0].bundle</span>=<span class="hljs-string">web-alt1</span>
<span class="hljs-meta">server.ssl.server-name-bundles[1].server-name</span>=<span class="hljs-string">alt2.example.com</span>
<span class="hljs-meta">server.ssl.server-name-bundles[1].bundle</span>=<span class="hljs-string">web-alt2</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_7_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">port:</span> <span class="hljs-number">8443</span>
  <span class="hljs-attr">ssl:</span>
    <span class="hljs-attr">bundle:</span> <span class="hljs-string">"web"</span>
    <span class="hljs-attr">server-name-bundles:</span>
      <span class="hljs-bullet">-</span> <span class="hljs-attr">server-name:</span> <span class="hljs-string">"alt1.example.com"</span>
        <span class="hljs-attr">bundle:</span> <span class="hljs-string">"web-alt1"</span>
      <span class="hljs-bullet">-</span> <span class="hljs-attr">server-name:</span> <span class="hljs-string">"alt2.example.com"</span>
        <span class="hljs-attr">bundle:</span> <span class="hljs-string">"web-alt2"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The bundle specified with <code>server.ssl.bundle</code> will be used for the default host, and for any client that does support SNI.
This default bundle must be configured if any <code>server.ssl.server-name-bundles</code> are configured.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.configure-http2"><a class="anchor" href="#howto.webserver.configure-http2"></a>Configure HTTP/2</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can enable HTTP/2 support in your Spring Boot application with the <code>server.http2.enabled</code> configuration property.
Both <code>h2</code> (HTTP/2 over TLS) and <code>h2c</code> (HTTP/2 over TCP) are supported.
To use <code>h2</code>, SSL must also be enabled.
When SSL is not enabled, <code>h2c</code> will be used.
You may, for example, want to use <code>h2c</code> when your application is <a href="#howto.webserver.use-behind-a-proxy-server">running behind a proxy server</a> that is performing TLS termination.</p>
</div>
<div class="sect2">
<h3 id="howto.webserver.configure-http2.tomcat"><a class="anchor" href="#howto.webserver.configure-http2.tomcat"></a>HTTP/2 With Tomcat</h3>
<div class="paragraph">
<p>Spring Boot ships by default with Tomcat 10.1.x which supports <code>h2c</code> and <code>h2</code> out of the box.
Alternatively, you can use <code>libtcnative</code> for <code>h2</code> support if the library and its dependencies are installed on the host operating system.</p>
</div>
<div class="paragraph">
<p>The library directory must be made available, if not already, to the JVM library path.
You can do so with a JVM argument such as <code>-Djava.library.path=/usr/local/opt/tomcat-native/lib</code>.
More on this in the <a class="external" href="https://tomcat.apache.org/tomcat-10.1-doc/apr.html" target="_blank">official Tomcat documentation</a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="howto.webserver.configure-http2.jetty"><a class="anchor" href="#howto.webserver.configure-http2.jetty"></a>HTTP/2 With Jetty</h3>
<div class="paragraph">
<p>For HTTP/2 support, Jetty requires the additional <code>org.eclipse.jetty.http2:jetty-http2-server</code> dependency.
To use <code>h2c</code> no other dependencies are required.
To use <code>h2</code>, you also need to choose one of the following dependencies, depending on your deployment:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>org.eclipse.jetty:jetty-alpn-java-server</code> to use the JDK built-in support</p>
</li>
<li>
<p><code>org.eclipse.jetty:jetty-alpn-conscrypt-server</code> and the <a class="external" href="https://www.conscrypt.org/" target="_blank">Conscrypt library</a></p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="howto.webserver.configure-http2.netty"><a class="anchor" href="#howto.webserver.configure-http2.netty"></a>HTTP/2 With Reactor Netty</h3>
<div class="paragraph">
<p>The <code>spring-boot-webflux-starter</code> is using by default Reactor Netty as a server.
Reactor Netty supports <code>h2c</code> and <code>h2</code> out of the box.
For optimal runtime performance, this server also supports <code>h2</code> with native libraries.
To enable that, your application needs to have an additional dependency.</p>
</div>
<div class="paragraph">
<p>Spring Boot manages the version for the <code>io.netty:netty-tcnative-boringssl-static</code> "uber jar", containing native libraries for all platforms.
Developers can choose to import only the required dependencies using a classifier (see <a class="external" href="https://netty.io/wiki/forked-tomcat-native.html" target="_blank">the Netty official documentation</a>).</p>
</div>
</div>
<div class="sect2">
<h3 id="howto.webserver.configure-http2.undertow"><a class="anchor" href="#howto.webserver.configure-http2.undertow"></a>HTTP/2 With Undertow</h3>
<div class="paragraph">
<p>Undertow supports <code>h2c</code> and <code>h2</code> out of the box.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.configure"><a class="anchor" href="#howto.webserver.configure"></a>Configure the Web Server</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Generally, you should first consider using one of the many available configuration keys and customize your web server by adding new entries in your <code>application.properties</code> or <code>application.yaml</code> file.
See <a class="xref page" href="properties-and-configuration.html#howto.properties-and-configuration.discover-build-in-options-for-external-properties">Discover Built-in Options for External Properties</a>).
The <code>server.*</code> namespace is quite useful here, and it includes namespaces like <code>server.tomcat.*</code>, <code>server.jetty.*</code> and others, for server-specific features.
See the list of <a class="xref page" href="../appendix/application-properties/index.html">Common Application Properties</a>.</p>
</div>
<div class="paragraph">
<p>The previous sections covered already many common use cases, such as compression, SSL or HTTP/2.
However, if a configuration key does not exist for your use case, you should then look at <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/WebServerFactoryCustomizer.html"><code>WebServerFactoryCustomizer</code></a>.
You can declare such a component and get access to the server factory relevant to your choice: you should select the variant for the chosen Server (Tomcat, Jetty, Reactor Netty, Undertow) and the chosen web stack (servlet or reactive).</p>
</div>
<div class="paragraph">
<p>The example below is for Tomcat with the <code>spring-boot-starter-web</code> (servlet stack):</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_8_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_8_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_8_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_java" class="tabpanel" id="_tabs_8_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer;
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyTomcatWebServerCustomizer</span> <span class="hljs-keyword">implements</span> <span class="hljs-title">WebServerFactoryCustomizer</span>&lt;<span class="hljs-title">TomcatServletWebServerFactory</span>&gt; </span>{

	<span class="hljs-meta">@Override</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-keyword">void</span> <span class="hljs-title">customize</span><span class="hljs-params">(TomcatServletWebServerFactory factory)</span> </span>{
		<span class="hljs-comment">// customize the factory here</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_8_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyTomcatWebServerCustomizer</span> : <span class="hljs-type">WebServerFactoryCustomizer</span>&lt;<span class="hljs-type">TomcatServletWebServerFactory?</span>&gt; </span>{

	<span class="hljs-keyword">override</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">customize</span><span class="hljs-params">(factory: <span class="hljs-type">TomcatServletWebServerFactory</span>?)</span></span> {
		<span class="hljs-comment">// customize the factory here</span>
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Spring Boot uses that infrastructure internally to auto-configure the server.
Auto-configured <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/WebServerFactoryCustomizer.html"><code>WebServerFactoryCustomizer</code></a> beans have an order of <code>0</code> and will be processed before any user-defined customizers, unless it has an explicit order that states otherwise.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Once you have got access to a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/WebServerFactory.html"><code>WebServerFactory</code></a> using the customizer, you can use it to configure specific parts, like connectors, server resources, or the server itself - all using server-specific APIs.</p>
</div>
<div class="paragraph">
<p>In addition Spring Boot provides:</p>
</div>
<table class="tableblock frame-all grid-all stretch" id="howto-configure-webserver-customizers">
<colgroup>
<col style="width: 20%;"/>
<col style="width: 40%;"/>
<col style="width: 40%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Server</th>
<th class="tableblock halign-left valign-top">Servlet stack</th>
<th class="tableblock halign-left valign-top">Reactive stack</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Tomcat</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/tomcat/TomcatServletWebServerFactory.html"><code>TomcatServletWebServerFactory</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/tomcat/TomcatReactiveWebServerFactory.html"><code>TomcatReactiveWebServerFactory</code></a></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Jetty</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/jetty/JettyServletWebServerFactory.html"><code>JettyServletWebServerFactory</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/jetty/JettyReactiveWebServerFactory.html"><code>JettyReactiveWebServerFactory</code></a></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Undertow</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/undertow/UndertowServletWebServerFactory.html"><code>UndertowServletWebServerFactory</code></a></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/undertow/UndertowReactiveWebServerFactory.html"><code>UndertowReactiveWebServerFactory</code></a></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Reactor</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">N/A</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/netty/NettyReactiveWebServerFactory.html"><code>NettyReactiveWebServerFactory</code></a></p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>As a last resort, you can also declare your own <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/WebServerFactory.html"><code>WebServerFactory</code></a> bean, which will override the one provided by Spring Boot.
When you do so, auto-configured customizers are still applied on your custom factory, so use that option carefully.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.add-servlet-filter-listener"><a class="anchor" href="#howto.webserver.add-servlet-filter-listener"></a>Add a Servlet, Filter, or Listener to an Application</h2>
<div class="sectionbody">
<div class="paragraph">
<p>In a servlet stack application, that is with the <code>spring-boot-starter-web</code>, there are two ways to add <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Servlet.html" target="_blank"><code>Servlet</code></a>, <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a>, <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/ServletContextListener.html" target="_blank"><code>ServletContextListener</code></a>, and the other listeners supported by the Servlet API to your application:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a href="#howto.webserver.add-servlet-filter-listener.spring-bean">Add a Servlet, Filter, or Listener by Using a Spring Bean</a></p>
</li>
<li>
<p><a href="#howto.webserver.add-servlet-filter-listener.using-scanning">Add Servlets, Filters, and Listeners by Using Classpath Scanning</a></p>
</li>
</ul>
</div>
<div class="sect2">
<h3 id="howto.webserver.add-servlet-filter-listener.spring-bean"><a class="anchor" href="#howto.webserver.add-servlet-filter-listener.spring-bean"></a>Add a Servlet, Filter, or Listener by Using a Spring Bean</h3>
<div class="paragraph">
<p>To add a <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Servlet.html" target="_blank"><code>Servlet</code></a>, <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a>, or servlet <code>*Listener</code> by using a Spring bean, you must provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> definition for it.
Doing so can be very useful when you want to inject configuration or dependencies.
However, you must be very careful that they do not cause eager initialization of too many other beans, because they have to be installed in the container very early in the application lifecycle.
(For example, it is not a good idea to have them depend on your <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> or JPA configuration.)
You can work around such restrictions by initializing the beans lazily when first used instead of on initialization.</p>
</div>
<div class="paragraph">
<p>In the case of filters and servlets, you can also add mappings and init parameters by adding a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/FilterRegistrationBean.html"><code>FilterRegistrationBean</code></a> or a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/ServletRegistrationBean.html"><code>ServletRegistrationBean</code></a> instead of or in addition to the underlying component.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
<div class="paragraph">
<p>If no <code>dispatcherType</code> is specified on a filter registration, <code>REQUEST</code> is used.
This aligns with the servlet specification’s default dispatcher type.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Like any other Spring bean, you can define the order of servlet filter beans; please make sure to check the <a class="xref page" href="../reference/web/servlet.html#web.servlet.embedded-container.servlets-filters-listeners.beans">Registering Servlets, Filters, and Listeners as Spring Beans</a> section.</p>
</div>
<div class="sect3">
<h4 id="howto.webserver.add-servlet-filter-listener.spring-bean.disable"><a class="anchor" href="#howto.webserver.add-servlet-filter-listener.spring-bean.disable"></a>Disable Registration of a Servlet or Filter</h4>
<div class="paragraph">
<p>As <a href="#howto.webserver.add-servlet-filter-listener.spring-bean">described earlier</a>, any <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Servlet.html" target="_blank"><code>Servlet</code></a> or <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a> beans are registered with the servlet container automatically.
To disable registration of a particular <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Filter.html" target="_blank"><code>Filter</code></a> or <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/Servlet.html" target="_blank"><code>Servlet</code></a> bean, create a registration bean for it and mark it as disabled, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_9">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_9_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_9_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_9_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_9_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_9_java" class="tabpanel" id="_tabs_9_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.servlet.FilterRegistrationBean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyFilterConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> FilterRegistrationBean&lt;MyFilter&gt; <span class="hljs-title">registration</span><span class="hljs-params">(MyFilter filter)</span> </span>{
		FilterRegistrationBean&lt;MyFilter&gt; registration = <span class="hljs-keyword">new</span> FilterRegistrationBean&lt;&gt;(filter);
		registration.setEnabled(<span class="hljs-keyword">false</span>);
		<span class="hljs-keyword">return</span> registration;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_9_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_9_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.boot.web.servlet.FilterRegistrationBean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyFilterConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">registration</span><span class="hljs-params">(filter: <span class="hljs-type">MyFilter</span>)</span></span>: FilterRegistrationBean&lt;MyFilter&gt; {
		<span class="hljs-keyword">val</span> registration = FilterRegistrationBean(filter)
		registration.isEnabled = <span class="hljs-literal">false</span>
		<span class="hljs-keyword">return</span> registration
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="howto.webserver.add-servlet-filter-listener.using-scanning"><a class="anchor" href="#howto.webserver.add-servlet-filter-listener.using-scanning"></a>Add Servlets, Filters, and Listeners by Using Classpath Scanning</h3>
<div class="paragraph">
<p><a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/annotation/WebServlet.html" target="_blank"><code>@WebServlet</code></a>, <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/annotation/WebFilter.html" target="_blank"><code>@WebFilter</code></a>, and <a class="apiref external" href="https://jakarta.ee/specifications/servlet/6.0/apidocs/jakarta.servlet/jakarta/servlet/annotation/WebListener.html" target="_blank"><code>@WebListener</code></a> annotated classes can be automatically registered with an embedded servlet container by annotating a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html"><code>@Configuration</code></a> class with <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/ServletComponentScan.html"><code>@ServletComponentScan</code></a> and specifying the package(s) containing the components that you want to register.
By default, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/servlet/ServletComponentScan.html"><code>@ServletComponentScan</code></a> scans from the package of the annotated class.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.configure-access-logs"><a class="anchor" href="#howto.webserver.configure-access-logs"></a>Configure Access Logging</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Access logs can be configured for Tomcat, Undertow, and Jetty through their respective namespaces.</p>
</div>
<div class="paragraph">
<p>For instance, the following settings log access on Tomcat with a <a class="external" href="https://tomcat.apache.org/tomcat-10.1-doc/config/valve.html#Access_Logging" target="_blank">custom pattern</a>.</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_10">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_10_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_10_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_10_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_10_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_10_properties" class="tabpanel" id="_tabs_10_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.tomcat.basedir</span>=<span class="hljs-string">my-tomcat</span>
<span class="hljs-meta">server.tomcat.accesslog.enabled</span>=<span class="hljs-string">true</span>
<span class="hljs-meta">server.tomcat.accesslog.pattern</span>=<span class="hljs-string">%t %a %r %s (%D microseconds)</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_10_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_10_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">tomcat:</span>
    <span class="hljs-attr">basedir:</span> <span class="hljs-string">"my-tomcat"</span>
    <span class="hljs-attr">accesslog:</span>
      <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span>
      <span class="hljs-attr">pattern:</span> <span class="hljs-string">"%t %a %r %s (%D microseconds)"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The default location for logs is a <code>logs</code> directory relative to the Tomcat base directory.
By default, the <code>logs</code> directory is a temporary directory, so you may want to fix Tomcat’s base directory or use an absolute path for the logs.
In the preceding example, the logs are available in <code>my-tomcat/logs</code> relative to the working directory of the application.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Access logging for Undertow can be configured in a similar fashion, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_11">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_11_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_11_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_11_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_11_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_11_properties" class="tabpanel" id="_tabs_11_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.undertow.accesslog.enabled</span>=<span class="hljs-string">true</span>
<span class="hljs-meta">server.undertow.accesslog.pattern</span>=<span class="hljs-string">%t %a %r %s (%D milliseconds)</span>
<span class="hljs-meta">server.undertow.options.server.record-request-start-time</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_11_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_11_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">undertow:</span>
    <span class="hljs-attr">accesslog:</span>
      <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span>
      <span class="hljs-attr">pattern:</span> <span class="hljs-string">"%t %a %r %s (%D milliseconds)"</span>
    <span class="hljs-attr">options:</span>
      <span class="hljs-attr">server:</span>
        <span class="hljs-attr">record-request-start-time:</span> <span class="hljs-literal">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Note that, in addition to enabling access logging and configuring its pattern, recording request start times has also been enabled.
This is required when including the response time (<code>%D</code>) in the access log pattern.
Logs are stored in a <code>logs</code> directory relative to the working directory of the application.
You can customize this location by setting the <code>server.undertow.accesslog.dir</code> property.</p>
</div>
<div class="paragraph">
<p>Finally, access logging for Jetty can also be configured as follows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_12">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_12_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_12_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_12_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_12_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_12_properties" class="tabpanel" id="_tabs_12_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.jetty.accesslog.enabled</span>=<span class="hljs-string">true</span>
<span class="hljs-meta">server.jetty.accesslog.filename</span>=<span class="hljs-string">/var/log/jetty-access.log</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_12_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_12_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">jetty:</span>
    <span class="hljs-attr">accesslog:</span>
      <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span>
      <span class="hljs-attr">filename:</span> <span class="hljs-string">"/var/log/jetty-access.log"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>By default, logs are redirected to <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/System.html#err" target="_blank"><code>System.err</code></a>.
For more details, see the Jetty documentation.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.use-behind-a-proxy-server"><a class="anchor" href="#howto.webserver.use-behind-a-proxy-server"></a>Running Behind a Front-end Proxy Server</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If your application is running behind a proxy, a load-balancer or in the cloud, the request information (like the host, port, scheme…​) might change along the way.
Your application may be running on <code>***********:8080</code>, but HTTP clients should only see <code>example.org</code>.</p>
</div>
<div class="paragraph">
<p><a class="external" href="https://tools.ietf.org/html/rfc7239" target="_blank">RFC7239 "Forwarded Headers"</a> defines the <code>Forwarded</code> HTTP header; proxies can use this header to provide information about the original request.
You can configure your application to read those headers and automatically use that information when creating links and sending them to clients in HTTP 302 responses, JSON documents or HTML pages.
There are also non-standard headers, like <code>X-Forwarded-Host</code>, <code>X-Forwarded-Port</code>, <code>X-Forwarded-Proto</code>, <code>X-Forwarded-Ssl</code>, and <code>X-Forwarded-Prefix</code>.</p>
</div>
<div class="paragraph">
<p>If the proxy adds the commonly used <code>X-Forwarded-For</code> and <code>X-Forwarded-Proto</code> headers, setting <code>server.forward-headers-strategy</code> to <code>NATIVE</code> is enough to support those.
With this option, the Web servers themselves natively support this feature; you can check their specific documentation to learn about specific behavior.</p>
</div>
<div class="paragraph">
<p>If this is not enough, Spring Framework provides a <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webmvc/filters.html#filters-forwarded-headers">ForwardedHeaderFilter</a> for the servlet stack and a <a href="https://docs.spring.io/spring-framework/reference/6.2/web/webflux/reactive-spring.html#webflux-forwarded-headers">ForwardedHeaderTransformer</a> for the reactive stack.
You can use them in your application by setting <code>server.forward-headers-strategy</code> to <code>FRAMEWORK</code>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you are using Tomcat and terminating SSL at the proxy, <code>server.tomcat.redirect-context-root</code> should be set to <code>false</code>.
This allows the <code>X-Forwarded-Proto</code> header to be honored before any redirects are performed.
</td>
</tr>
</tbody></table>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If your application runs <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/cloud/CloudPlatform.html#enum-constant-summary">in a supported Cloud Platform</a>, the <code>server.forward-headers-strategy</code> property defaults to <code>NATIVE</code>.
In all other instances, it defaults to <code>NONE</code>.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="howto.webserver.use-behind-a-proxy-server.tomcat"><a class="anchor" href="#howto.webserver.use-behind-a-proxy-server.tomcat"></a>Customize Tomcat’s Proxy Configuration</h3>
<div class="paragraph">
<p>If you use Tomcat, you can additionally configure the names of the headers used to carry “forwarded” information, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_13">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_13_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_13_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_13_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_13_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_13_properties" class="tabpanel" id="_tabs_13_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.tomcat.remoteip.remote-ip-header</span>=<span class="hljs-string">x-your-remote-ip-header</span>
<span class="hljs-meta">server.tomcat.remoteip.protocol-header</span>=<span class="hljs-string">x-your-protocol-header</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_13_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_13_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">tomcat:</span>
    <span class="hljs-attr">remoteip:</span>
      <span class="hljs-attr">remote-ip-header:</span> <span class="hljs-string">"x-your-remote-ip-header"</span>
      <span class="hljs-attr">protocol-header:</span> <span class="hljs-string">"x-your-protocol-header"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Tomcat is also configured with a regular expression that matches internal proxies that are to be trusted.
See the <a class="xref page" href="../appendix/application-properties/index.html#application-properties.server.server.tomcat.remoteip.internal-proxies"><code>server.tomcat.remoteip.internal-proxies</code> entry in the appendix</a> for its default value.
You can customize the valve’s configuration by adding an entry to <code>application.properties</code>, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_14">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_14_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_14_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_14_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_14_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_14_properties" class="tabpanel" id="_tabs_14_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.tomcat.remoteip.internal-proxies</span>=<span class="hljs-string">192\.168\.\d{1,3}\.\d{1,3}</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_14_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_14_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">tomcat:</span>
    <span class="hljs-attr">remoteip:</span>
      <span class="hljs-attr">internal-proxies:</span> <span class="hljs-string">"192\\.168\\.\\d{1,3}\\.\\d{1,3}"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
You can trust all proxies by setting the <code>internal-proxies</code> to empty (but do not do so in production).
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>You can take complete control of the configuration of Tomcat’s <a class="apiref external" href="https://tomcat.apache.org/tomcat-10.1-doc/api/org/apache/catalina/valves/RemoteIpValve.html" target="_blank"><code>RemoteIpValve</code></a> by switching the automatic one off (to do so, set <code>server.forward-headers-strategy=NONE</code>) and adding a new valve instance using a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/server/WebServerFactoryCustomizer.html"><code>WebServerFactoryCustomizer</code></a> bean.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.enable-multiple-connectors-in-tomcat"><a class="anchor" href="#howto.webserver.enable-multiple-connectors-in-tomcat"></a>Enable Multiple Connectors with Tomcat</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can add an <a class="apiref external" href="https://tomcat.apache.org/tomcat-10.1-doc/api/org/apache/catalina/connector/Connector.html" target="_blank"><code>Connector</code></a> to the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/tomcat/TomcatServletWebServerFactory.html"><code>TomcatServletWebServerFactory</code></a>, which can allow multiple connectors, including HTTP and HTTPS connectors, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_15">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_15_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_15_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_15_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_15_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_15_java" class="tabpanel" id="_tabs_15_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.apache.catalina.connector.Connector;

<span class="hljs-keyword">import</span> org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyTomcatConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> WebServerFactoryCustomizer&lt;TomcatServletWebServerFactory&gt; <span class="hljs-title">connectorCustomizer</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> (tomcat) -&gt; tomcat.addAdditionalTomcatConnectors(createConnector());
	}

	<span class="hljs-function"><span class="hljs-keyword">private</span> Connector <span class="hljs-title">createConnector</span><span class="hljs-params">()</span> </span>{
		Connector connector = <span class="hljs-keyword">new</span> Connector(<span class="hljs-string">"org.apache.coyote.http11.Http11NioProtocol"</span>);
		connector.setPort(<span class="hljs-number">8081</span>);
		<span class="hljs-keyword">return</span> connector;
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_15_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_15_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.apache.catalina.connector.Connector
<span class="hljs-keyword">import</span> org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyTomcatConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">connectorCustomizer</span><span class="hljs-params">()</span></span>: WebServerFactoryCustomizer&lt;TomcatServletWebServerFactory&gt; {
		<span class="hljs-keyword">return</span> WebServerFactoryCustomizer { tomcat: TomcatServletWebServerFactory -&gt;
			tomcat.addAdditionalTomcatConnectors(
				createConnector()
			)
		}
	}

	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">createConnector</span><span class="hljs-params">()</span></span>: Connector {
		<span class="hljs-keyword">val</span> connector = Connector(<span class="hljs-string">"org.apache.coyote.http11.Http11NioProtocol"</span>)
		connector.port = <span class="hljs-number">8081</span>
		<span class="hljs-keyword">return</span> connector
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.enable-tomcat-mbean-registry"><a class="anchor" href="#howto.webserver.enable-tomcat-mbean-registry"></a>Enable Tomcat’s MBean Registry</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Embedded Tomcat’s MBean registry is disabled by default.
This minimizes Tomcat’s memory footprint.
If you want to use Tomcat’s MBeans, for example so that they can be used by Micrometer to expose metrics, you must use the <code>server.tomcat.mbeanregistry.enabled</code> property to do so, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_16">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_16_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_16_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_16_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_16_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_16_properties" class="tabpanel" id="_tabs_16_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">server.tomcat.mbeanregistry.enabled</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_16_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_16_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">server:</span>
  <span class="hljs-attr">tomcat:</span>
    <span class="hljs-attr">mbeanregistry:</span>
      <span class="hljs-attr">enabled:</span> <span class="hljs-literal">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.enable-multiple-listeners-in-undertow"><a class="anchor" href="#howto.webserver.enable-multiple-listeners-in-undertow"></a>Enable Multiple Listeners with Undertow</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Add an <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/undertow/UndertowBuilderCustomizer.html"><code>UndertowBuilderCustomizer</code></a> to the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/embedded/undertow/UndertowServletWebServerFactory.html"><code>UndertowServletWebServerFactory</code></a> and add a listener to the <code>io.undertow.Undertow.Builder</code>, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_17">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_17_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_17_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_17_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_17_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_17_java" class="tabpanel" id="_tabs_17_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.undertow.Undertow.Builder;

<span class="hljs-keyword">import</span> org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyUndertowConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> WebServerFactoryCustomizer&lt;UndertowServletWebServerFactory&gt; <span class="hljs-title">undertowListenerCustomizer</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> (factory) -&gt; factory.addBuilderCustomizers(<span class="hljs-keyword">this</span>::addHttpListener);
	}

	<span class="hljs-function"><span class="hljs-keyword">private</span> Builder <span class="hljs-title">addHttpListener</span><span class="hljs-params">(Builder builder)</span> </span>{
		<span class="hljs-keyword">return</span> builder.addHttpListener(<span class="hljs-number">8080</span>, <span class="hljs-string">"0.0.0.0"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_17_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_17_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.undertow.Undertow
<span class="hljs-keyword">import</span> org.springframework.boot.web.embedded.undertow.UndertowBuilderCustomizer
<span class="hljs-keyword">import</span> org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory
<span class="hljs-keyword">import</span> org.springframework.boot.web.server.WebServerFactoryCustomizer
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyUndertowConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">undertowListenerCustomizer</span><span class="hljs-params">()</span></span>: WebServerFactoryCustomizer&lt;UndertowServletWebServerFactory&gt; {
		<span class="hljs-keyword">return</span> WebServerFactoryCustomizer { factory: UndertowServletWebServerFactory -&gt;
			factory.addBuilderCustomizers(
				UndertowBuilderCustomizer { builder: Undertow.Builder -&gt; addHttpListener(builder) })
		}
	}

	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">addHttpListener</span><span class="hljs-params">(builder: <span class="hljs-type">Undertow</span>.<span class="hljs-type">Builder</span>)</span></span>: Undertow.Builder {
		<span class="hljs-keyword">return</span> builder.addHttpListener(<span class="hljs-number">8080</span>, <span class="hljs-string">"0.0.0.0"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto.webserver.create-websocket-endpoints-using-serverendpoint"><a class="anchor" href="#howto.webserver.create-websocket-endpoints-using-serverendpoint"></a>Create WebSocket Endpoints Using @ServerEndpoint</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you want to use <a class="apiref external" href="https://jakarta.ee/specifications/websocket/2.1/apidocs/server/jakarta/websocket/server/ServerEndpoint.html" target="_blank"><code>@ServerEndpoint</code></a> in a Spring Boot application that used an embedded container, you must declare a single <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/socket/server/standard/ServerEndpointExporter.html"><code>ServerEndpointExporter</code></a> <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a>, as shown in the following example:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_18">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_18_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_18_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_18_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_18_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_18_java" class="tabpanel" id="_tabs_18_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;
<span class="hljs-keyword">import</span> org.springframework.web.socket.server.standard.ServerEndpointExporter;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebSocketConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> ServerEndpointExporter <span class="hljs-title">serverEndpointExporter</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> ServerEndpointExporter();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_18_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_18_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration
<span class="hljs-keyword">import</span> org.springframework.web.socket.server.standard.ServerEndpointExporter

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyWebSocketConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">serverEndpointExporter</span><span class="hljs-params">()</span></span>: ServerEndpointExporter {
		<span class="hljs-keyword">return</span> ServerEndpointExporter()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The bean shown in the preceding example registers any <a class="apiref external" href="https://jakarta.ee/specifications/websocket/2.1/apidocs/server/jakarta/websocket/server/ServerEndpoint.html" target="_blank"><code>@ServerEndpoint</code></a> annotated beans with the underlying WebSocket container.
When deployed to a standalone servlet container, this role is performed by a servlet container initializer, and the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/socket/server/standard/ServerEndpointExporter.html"><code>ServerEndpointExporter</code></a> bean is not required.</p>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="properties-and-configuration.html">Properties and Configuration</a></span>
<span class="next"><a href="spring-mvc.html">Spring MVC</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../how-to/webserver.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="webserver.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../3.3/how-to/webserver.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../4.0-SNAPSHOT/how-to/webserver.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../3.5-SNAPSHOT/how-to/webserver.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../3.4-SNAPSHOT/how-to/webserver.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../3.3-SNAPSHOT/how-to/webserver.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>