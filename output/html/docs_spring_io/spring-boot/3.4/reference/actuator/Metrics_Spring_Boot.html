<!DOCTYPE html>
<html><head><title>Metrics :: Spring Boot</title><link href="https://docs.spring.io/spring-boot/3.4/reference/actuator/metrics.html"/><meta content="2025-06-04T16:06:21.992892" name="crawled_at"/></head><body class="article">
<header class="header">
<nav class="navbar">
<div class="navbar-brand">
<a class="navbar-item" href="https://spring.io">
<img alt="Spring" class="block" id="springlogo" src="../../../_/img/spring-logo.svg"/>
</a>
<button class="navbar-burger" data-target="topbar-nav">
<span></span>
<span></span>
<span></span>
</button>
</div>
<div class="navbar-menu" id="topbar-nav">
<div class="navbar-end">
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Why Spring</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/why-spring">Overview</a>
<a class="navbar-item" href="https://spring.io/microservices">Microservices</a>
<a class="navbar-item" href="https://spring.io/reactive">Reactive</a>
<a class="navbar-item" href="https://spring.io/event-driven">Event
              Driven</a>
<a class="navbar-item" href="https://spring.io/cloud">Cloud</a>
<a class="navbar-item" href="https://spring.io/web-applications">Web
              Applications</a>
<a class="navbar-item" href="https://spring.io/serverless">Serverless</a>
<a class="navbar-item" href="https://spring.io/batch">Batch</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Learn</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/learn">Overview</a>
<a class="navbar-item" href="https://spring.io/quickstart">Quickstart</a>
<a class="navbar-item" href="https://spring.io/guides">Guides</a>
<a class="navbar-item" href="https://spring.io/blog">Blog</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Projects</a>
<div class="navbar-dropdown" style="min-width: 280px">
<a class="navbar-item" href="https://spring.io/projects">Overview</a>
<a class="navbar-item" href="https://spring.io/projects/spring-boot">Spring Boot</a>
<a class="navbar-item" href="https://spring.io/projects/spring-framework">Spring Framework</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud">Spring Cloud</a>
<a class="navbar-item" href="https://spring.io/projects/spring-cloud-dataflow">Spring Cloud Data Flow</a>
<a class="navbar-item" href="https://spring.io/projects/spring-data">Spring Data</a>
<a class="navbar-item" href="https://spring.io/projects/spring-integration">Spring Integration</a>
<a class="navbar-item" href="https://spring.io/projects/spring-batch">Spring Batch</a>
<a class="navbar-item" href="https://spring.io/projects/spring-security">Spring Security</a>
<a class="navbar-item navbar-item-special" href="https://spring.io/projects">View all projects</a>
<li class="navbar-item navbar-item-special-3">DEVELOPMENT TOOLS</li>
<a class="navbar-item" href="https://spring.io/tools">Spring Tools 4</a>
<a class="navbar-item navbar-item-special-2" href="https://start.spring.io">Spring Initializr
              <svg class="external-link-icon" viewbox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" points="15 10.94 15 15 1 15 1 1 5.06 1" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><polyline fill="none" points="8.93 1 15 1 15 7.07" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></polyline><line fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="2" x1="15" x2="8" y1="1" y2="8"></line></svg></a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Academy</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.academy/courses">Courses</a>
<a class="navbar-item" href="https://spring.academy/learning-path">Get Certified</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable">
<a class="navbar-link" href="#">Solutions</a>
<div class="navbar-dropdown lg">
<a class="navbar-item" href="https://spring.io/solutions">Overview</a>
<a class="navbar-item" href="https://spring.io/support">Spring Runtime</a>
<a class="navbar-item" href="https://spring.io/consulting">Spring Consulting</a>
<a class="navbar-item" href="https://spring.academy/teams">Spring Academy For Teams</a>
<a class="navbar-item" href="https://spring.io/security">Security Advisories</a>
</div>
</div>
<div class="navbar-item has-dropdown is-hoverable is-community">
<a class="navbar-link" href="#">Community</a>
<div class="navbar-dropdown">
<a class="navbar-item" href="https://spring.io/community">Overview</a>
<a class="navbar-item" href="https://spring.io/events">Events</a>
<a class="navbar-item" href="https://spring.io/team">Team</a>
</div>
</div>
</div>
</div>
<label class="theme-toggler">
<input id="switch-theme-checkbox" name="switch-theme-checkbox" style="" type="checkbox"/>
<span class="icon"><svg aria-hidden="true" class="svg-inline--fa fa-moon moon" data-icon="moon" data-prefix="fas" focusable="false" role="img" viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z" fill="currentColor"></path>
</svg>
<svg aria-hidden="true" class="svg-inline--fa fa-sun sun" data-icon="sun" data-prefix="fas" focusable="false" role="img" viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z" fill="currentColor"></path>
</svg></span>
<span class="text">light</span>
</label>
</nav>
</header>

<div class="body">
<div class="nav-container" data-component="boot" data-version="3.4.6">
<aside class="nav">
<div class="panels">
<div class="nav-panel-menu is-active" data-panel="menu">
<nav class="nav-menu">
<div class="context">
<span class="title">Spring Boot</span>
<span class="version">3.4.6</span>
<button class="browse-version" id="browse-version">
<svg height="24px" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewbox="0 0 512 512" width="24px" xml:space="preserve"><g><path d="M256,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S273.7,224,256,224L256,224z"></path><path d="M128.4,224c-17.7,0-32,14.3-32,32s14.3,32,32,32c17.7,0,32-14.3,32-32S146,224,128.4,224L128.4,224z"></path><path d="M384,224c-17.7,0-32,14.3-32,32s14.3,32,32,32s32-14.3,32-32S401.7,224,384,224L384,224z"></path></g></svg>
</button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div><ul class="nav-list">
<li class="nav-item is-active is-current-path" data-depth="0">
<ul class="nav-list">
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-home" href="../../index.html">Overview</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-book" href="../../documentation.html">Documentation</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-question" href="../../community.html">Community</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-server" href="../../system-requirements.html">System Requirements</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-gift" href="../../installing.html">Installing Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<a class="nav-link navtree-icon-rocket" href="../../upgrading.html">Upgrading Spring Boot</a>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../tutorial/index.html">Tutorials</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../tutorial/first-application/index.html">Developing Your First Spring Boot Application</a>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../index.html">Reference</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../using/index.html">Developing with Spring Boot</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/build-systems.html">Build Systems</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/structuring-your-code.html">Structuring Your Code</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/configuration-classes.html">Configuration Classes</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/auto-configuration.html">Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/spring-beans-and-dependency-injection.html">Spring Beans and Dependency Injection</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/using-the-springbootapplication-annotation.html">Using the @SpringBootApplication Annotation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/running-your-application.html">Running Your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/devtools.html">Developer Tools</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../using/packaging-for-production.html">Packaging Your Application for Production</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../features/index.html">Core Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/spring-application.html">SpringApplication</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/external-config.html">Externalized Configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/profiles.html">Profiles</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/internationalization.html">Internationalization</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/aop.html">Aspect-Oriented Programming</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/json.html">JSON</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/task-execution-and-scheduling.html">Task Execution and Scheduling</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/dev-services.html">Development-time Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/developing-auto-configuration.html">Creating Your Own Auto-configuration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/kotlin.html">Kotlin Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../features/ssl.html">SSL</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../web/index.html">Web</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/servlet.html">Servlet Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/reactive.html">Reactive Web Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/graceful-shutdown.html">Graceful Shutdown</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-security.html">Spring Security</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-session.html">Spring Session</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-graphql.html">Spring for GraphQL</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../web/spring-hateoas.html">Spring HATEOAS</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../data/index.html">Data</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/sql.html">SQL Databases</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../data/nosql.html">Working with NoSQL Technologies</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../io/index.html">IO</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/caching.html">Caching</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/hazelcast.html">Hazelcast</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/quartz.html">Quartz Scheduler</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/email.html">Sending Email</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/validation.html">Validation</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/rest-client.html">Calling REST Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/webservices.html">Web Services</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../io/jta.html">Distributed Transactions With JTA</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../messaging/index.html">Messaging</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/jms.html">JMS</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/amqp.html">AMQP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/kafka.html">Apache Kafka Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/pulsar.html">Apache Pulsar Support</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/rsocket.html">RSocket</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/spring-integration.html">Spring Integration</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../messaging/websockets.html">WebSockets</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../testing/index.html">Testing</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-scope-dependencies.html">Test Scope Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-applications.html">Testing Spring Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/spring-boot-applications.html">Testing Spring Boot Applications</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/testcontainers.html">Testcontainers</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../testing/test-utilities.html">Test Utilities</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/index.html">Packaging Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/efficient.html">Efficient Deployments</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/aot.html">Ahead-of-Time Processing With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/native-image/index.html">GraalVM Native Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/introducing-graalvm-native-images.html">Introducing GraalVM Native Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/native-image/advanced-topics.html">Advanced Native Images Topics</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../packaging/checkpoint-restore.html">Checkpoint and Restore With the JVM</a>
</li>
<li class="nav-item" data-depth="3">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../packaging/container-images/index.html">Container Images</a>
<ul class="nav-list">
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/efficient-images.html">Efficient Container Images</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/dockerfiles.html">Dockerfiles</a>
</li>
<li class="nav-item" data-depth="4">
<a class="nav-link" href="../packaging/container-images/cloud-native-buildpacks.html">Cloud Native Buildpacks</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item is-active is-current-path" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="index.html">Production-ready Features</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="enabling.html">Enabling Production-ready Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="endpoints.html">Endpoints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="monitoring.html">Monitoring and Management Over HTTP</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="jmx.html">Monitoring and Management over JMX</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="observability.html">Observability</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="loggers.html">Loggers</a>
</li>
<li class="nav-item is-current-page is-active" data-depth="3">
<a class="nav-link" href="metrics.html">Metrics</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="tracing.html">Tracing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="auditing.html">Auditing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="http-exchanges.html">Recording HTTP Exchanges</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="process-monitoring.html">Process Monitoring</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="cloud-foundry.html">Cloud Foundry Support</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/index.html">How-to Guides</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/application.html">Spring Boot Application</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/properties-and-configuration.html">Properties and Configuration</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/webserver.html">Embedded Web Servers</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/spring-mvc.html">Spring MVC</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/jersey.html">Jersey</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/http-clients.html">HTTP Clients</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/logging.html">Logging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-access.html">Data Access</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/data-initialization.html">Database Initialization</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/nosql.html">NoSQL</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/messaging.html">Messaging</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/batch.html">Batch Applications</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/actuator.html">Actuator</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/security.html">Security</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/hotswapping.html">Hot Swapping</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/testing.html">Testing</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/build.html">Build</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/native-image/index.html">GraalVM Native Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/developing-your-first-application.html">Developing Your First GraalVM Native Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/native-image/testing-native-applications.html">Testing GraalVM Native Images</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/class-data-sharing.html">Class Data Sharing</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../how-to/deployment/index.html">Deploying Spring Boot Applications</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/traditional-deployment.html">Traditional Deployment</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/cloud.html">Deploying to the Cloud</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../how-to/deployment/installing.html">Installing Spring Boot Applications</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../how-to/docker-compose.html">Docker Compose</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../build-tool-plugin/index.html">Build Tool Plugins</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../maven-plugin/index.html">Maven Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/using.html">Using the Plugin</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/goals.html">Goals</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/run.html">Running your Application with Maven</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/integration-tests.html">Running Integration Tests</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/build-info.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../maven-plugin/help.html">Help Information</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../gradle-plugin/index.html">Gradle Plugin</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/getting-started.html">Getting Started</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/managing-dependencies.html">Managing Dependencies</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging.html">Packaging Executable Archives</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/packaging-oci-image.html">Packaging OCI Images</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/publishing.html">Publishing your Application</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/running.html">Running your Application with Gradle</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/aot.html">Ahead-of-Time Processing</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/integrating-with-actuator.html">Integrating with Actuator</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../gradle-plugin/reacting.html">Reacting to Other Plugins</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/antlib.html">Spring Boot AntLib Module</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../build-tool-plugin/other-build-systems.html">Supporting Other Build Systems</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../cli/index.html">Spring Boot CLI</a>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/installation.html">Installing the CLI</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../cli/using-the-cli.html">Using the CLI</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Rest APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../api/rest/actuator/index.html">Actuator</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/auditevents.html">Audit Events (<code>auditevents</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/beans.html">Beans (<code>beans</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/caches.html">Caches (<code>caches</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/conditions.html">Conditions Evaluation Report (<code>conditions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/configprops.html">Configuration Properties (<code>configprops</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/env.html">Environment (<code>env</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/flyway.html">Flyway (<code>flyway</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/health.html">Health (<code>health</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/heapdump.html">Heap Dump (<code>heapdump</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/httpexchanges.html">HTTP Exchanges (<code>httpexchanges</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/info.html">Info (<code>info</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/integrationgraph.html">Spring Integration Graph (<code>integrationgraph</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/liquibase.html">Liquibase (<code>liquibase</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/logfile.html">Log File (<code>logfile</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/loggers.html">Loggers (<code>loggers</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/mappings.html">Mappings (<code>mappings</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/metrics.html">Metrics (<code>metrics</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/prometheus.html">Prometheus (<code>prometheus</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/quartz.html">Quartz (<code>quartz</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sbom.html">Software Bill of Materials (<code>sbom</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/scheduledtasks.html">Scheduled Tasks (<code>scheduledtasks</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/sessions.html">Sessions (<code>sessions</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/shutdown.html">Shutdown (<code>shutdown</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/startup.html">Application Startup (<code>startup</code>)</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../api/rest/actuator/threaddump.html">Thread Dump (<code>threaddump</code>)</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Java APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/java/index.html" target="_blank">Spring Boot</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../gradle-plugin/api/java/index.html" target="_blank">Gradle Plugin</a>
</li>
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../maven-plugin/api/java/index.html" target="_blank">Maven Plugin</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Kotlin APIs</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link link-external" href="../../api/kotlin/index.html" target="_blank">Spring Boot</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Specifications</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/configuration-metadata/index.html">Configuration Metadata</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/format.html">Metadata Format</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/manual-hints.html">Providing Manual Hints</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/configuration-metadata/annotation-processor.html">Generating Your Own Metadata by Using the Annotation Processor</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../specification/executable-jar/index.html">The Executable Jar Format</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/nested-jars.html">Nested JARs</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/jarfile-class.html">Spring Boot’s “NestedJarFile” Class</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/launching.html">Launching Executable Jars</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/property-launcher.html">PropertiesLauncher Features</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/restrictions.html">Executable Jar Restrictions</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../specification/executable-jar/alternatives.html">Alternative Single Jar Solutions</a>
</li>
</ul>
</li>
</ul>
</li>
<li class="nav-item" data-depth="1">
<button class="nav-item-toggle"></button>
<span class="nav-text" style="cursor: pointer;">Appendix</span>
<ul class="nav-list">
<li class="nav-item" data-depth="2">
<a class="nav-link" href="../../appendix/application-properties/index.html">Common Application Properties</a>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/auto-configuration-classes/index.html">Auto-configuration Classes</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/core.html">spring-boot-autoconfigure</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/auto-configuration-classes/actuator.html">spring-boot-actuator-autoconfigure</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/test-auto-configuration/index.html">Test Auto-configuration Annotations</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/test-auto-configuration/slices.html">Test Slices</a>
</li>
</ul>
</li>
<li class="nav-item" data-depth="2">
<button class="nav-item-toggle"></button>
<a class="nav-link" href="../../appendix/dependency-versions/index.html">Dependency Versions</a>
<ul class="nav-list">
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/coordinates.html">Managed Dependency Coordinates</a>
</li>
<li class="nav-item" data-depth="3">
<a class="nav-link" href="../../appendix/dependency-versions/properties.html">Version Properties</a>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="toggle-sm">
<button class="nav-toggle" id="nav-toggle-2"></button>
</div>
</nav>
</div>
<div class="nav-collapse">
<button id="nav-collapse-toggle"><span></span></button>
</div>
</div>
<div class="nav-resize"></div>
</aside>
</div>
<main class="article">
<div class="toolbar" role="navigation">
<button class="nav-toggle" id="nav-toggle-1"></button>
<div class="search">
<button class="DocSearch-Button search-button">
<svg enable-background="new 0 0 32 32" id="Glyph" version="1.1" viewbox="0 0 32 32" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M27.414,24.586l-5.077-5.077C23.386,17.928,24,16.035,24,14c0-5.514-4.486-10-10-10S4,8.486,4,14  s4.486,10,10,10c2.035,0,3.928-0.614,5.509-1.663l5.077,5.077c0.78,0.781,2.048,0.781,2.828,0  C28.195,26.633,28.195,25.367,27.414,24.586z M7,14c0-3.86,3.14-7,7-7s7,3.14,7,7s-3.14,7-7,7S7,17.86,7,14z" id="XMLID_223_"></path>
</svg>
<span>Search</span>
<span class="search-key">CTRL + k</span>
</button>
</div>
</div>
<div class="content">
<aside class="sidebar">
<div class="content">
<div class="toc" data-levels="2" data-title="Metrics">
<div class="toc-menu"><h3>Metrics</h3><ul><li data-level="1"><a href="#actuator.metrics.getting-started">Getting Started</a></li><li data-level="1"><a href="#actuator.metrics.export">Supported Monitoring Systems</a></li><li data-level="2"><a href="#actuator.metrics.export.appoptics">AppOptics</a></li><li data-level="2"><a href="#actuator.metrics.export.atlas">Atlas</a></li><li data-level="2"><a href="#actuator.metrics.export.datadog">Datadog</a></li><li data-level="2"><a href="#actuator.metrics.export.dynatrace">Dynatrace</a></li><li data-level="2"><a href="#actuator.metrics.export.elastic">Elastic</a></li><li data-level="2"><a href="#actuator.metrics.export.ganglia">Ganglia</a></li><li data-level="2"><a href="#actuator.metrics.export.graphite">Graphite</a></li><li data-level="2"><a href="#actuator.metrics.export.humio">Humio</a></li><li data-level="2"><a href="#actuator.metrics.export.influx">Influx</a></li><li data-level="2"><a href="#actuator.metrics.export.jmx">JMX</a></li><li data-level="2"><a href="#actuator.metrics.export.kairos">KairosDB</a></li><li data-level="2"><a href="#actuator.metrics.export.newrelic">New Relic</a></li><li data-level="2"><a href="#actuator.metrics.export.otlp">OTLP</a></li><li data-level="2"><a href="#actuator.metrics.export.prometheus">Prometheus</a></li><li data-level="2"><a href="#actuator.metrics.export.signalfx">SignalFx</a></li><li data-level="2"><a href="#actuator.metrics.export.simple">Simple</a></li><li data-level="2"><a href="#actuator.metrics.export.stackdriver">Stackdriver</a></li><li data-level="2"><a href="#actuator.metrics.export.statsd">StatsD</a></li><li data-level="2"><a href="#actuator.metrics.export.wavefront">Wavefront</a></li><li data-level="1"><a href="#actuator.metrics.supported">Supported Metrics and Meters</a></li><li data-level="2"><a href="#actuator.metrics.supported.jvm">JVM Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.system">System Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.application-startup">Application Startup Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.logger">Logger Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.tasks">Task Execution and Scheduling Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.jms">JMS Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.spring-mvc">Spring MVC Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.spring-webflux">Spring WebFlux Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.jersey">Jersey Server Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.http-clients">HTTP Client Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.tomcat">Tomcat Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.cache">Cache Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.spring-batch">Spring Batch Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.spring-graphql">Spring GraphQL Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.jdbc">DataSource Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.hibernate">Hibernate Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.spring-data-repository">Spring Data Repository Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.rabbitmq">RabbitMQ Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.spring-integration">Spring Integration Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.kafka">Kafka Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.mongodb">MongoDB Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.jetty">Jetty Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.redis">Redis Metrics</a></li><li data-level="1"><a href="#actuator.metrics.registering-custom">Registering Custom Metrics</a></li><li data-level="1"><a href="#actuator.metrics.customizing">Customizing Individual Metrics</a></li><li data-level="2"><a href="#actuator.metrics.customizing.common-tags">Common Tags</a></li><li data-level="2"><a href="#actuator.metrics.customizing.per-meter-properties">Per-meter Properties</a></li><li data-level="1"><a href="#actuator.metrics.endpoint">Metrics Endpoint</a></li><li data-level="1"><a href="#actuator.metrics.micrometer-observation">Integration with Micrometer Observation</a></li></ul></div>
</div>
<div class="sidebar-links">
<a href="https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/actuator/metrics.adoc">
<svg height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m16 2.012 3 3L16.713 7.3l-3-3zM4 14v3h3l8.299-8.287-3-3zm0 6h16v2H4z"></path></svg>
          Edit this Page
        </a>
<a href="https://github.com/spring-projects/spring-boot" title="GitHub">
<svg height="512px" id="Layer_1" version="1.1" viewbox="0 0 512 512" width="512px" xmlns="http://www.w3.org/2000/svg"><style type="text/css">
              .st0{fill-rule:evenodd;clip-rule:evenodd;} </style><g><path class="st0" d="M256,32C132.3,32,32,134.8,32,261.7c0,101.5,64.2,187.5,153.2,217.9c11.2,2.1,15.3-5,15.3-11.1   c0-5.5-0.2-19.9-0.3-39.1c-62.3,13.9-75.5-30.8-75.5-30.8c-10.2-26.5-24.9-33.6-24.9-33.6c-20.3-14.3,1.5-14,1.5-14   c22.5,1.6,34.3,23.7,34.3,23.7c20,35.1,52.4,25,65.2,19.1c2-14.8,7.8-25,14.2-30.7c-49.7-5.8-102-25.5-102-113.5   c0-25.1,8.7-45.6,23-61.6c-2.3-5.8-10-29.2,2.2-60.8c0,0,18.8-6.2,61.6,23.5c17.9-5.1,37-7.6,56.1-7.7c19,0.1,38.2,2.6,56.1,7.7   c42.8-29.7,61.5-23.5,61.5-23.5c12.2,31.6,4.5,55,2.2,60.8c14.3,16.1,23,36.6,23,61.6c0,88.2-52.4,107.6-102.3,113.3   c8,7.1,15.2,21.1,15.2,42.5c0,30.7-0.3,55.5-0.3,63c0,6.1,4,13.3,15.4,11C415.9,449.1,480,363.1,480,261.7   C480,134.8,379.7,32,256,32z"></path></g></svg>
          GitHub Project
        </a>
<a href="https://stackoverflow.com/tags/spring-boot">
<svg viewbox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path d="M290.7 311L95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"></path></svg>
          Stack Overflow
        </a>
</div>
</div>
</aside>
<article class="doc">
<div class="breadcrumbs-container">
<nav aria-label="breadcrumbs" class="breadcrumbs">
<ul>
<li><a href="../../index.html">Spring Boot</a></li>
<li><a href="../index.html">Reference</a></li>
<li><a href="index.html">Production-ready Features</a></li>
<li><a href="metrics.html">Metrics</a></li>
</ul>
</nav>
</div><div class="admonitionblock important latest">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-important" title="Important"></i>
</td>
<td class="content">
<div class="paragraph">
<p> For the latest stable version, please use <a href="../../../reference/actuator/metrics.html">Spring Boot 3.5.0</a>!</p>
</div>
</td>
</tr></tbody>
</table>
</div>
<h1 class="page" id="page-title">Metrics</h1>
<aside class="toc embedded"><div class="toc-menu"><h3>Metrics</h3><ul><li data-level="1"><a href="#actuator.metrics.getting-started">Getting Started</a></li><li data-level="1"><a href="#actuator.metrics.export">Supported Monitoring Systems</a></li><li data-level="2"><a href="#actuator.metrics.export.appoptics">AppOptics</a></li><li data-level="2"><a href="#actuator.metrics.export.atlas">Atlas</a></li><li data-level="2"><a href="#actuator.metrics.export.datadog">Datadog</a></li><li data-level="2"><a href="#actuator.metrics.export.dynatrace">Dynatrace</a></li><li data-level="2"><a href="#actuator.metrics.export.elastic">Elastic</a></li><li data-level="2"><a href="#actuator.metrics.export.ganglia">Ganglia</a></li><li data-level="2"><a href="#actuator.metrics.export.graphite">Graphite</a></li><li data-level="2"><a href="#actuator.metrics.export.humio">Humio</a></li><li data-level="2"><a href="#actuator.metrics.export.influx">Influx</a></li><li data-level="2"><a href="#actuator.metrics.export.jmx">JMX</a></li><li data-level="2"><a href="#actuator.metrics.export.kairos">KairosDB</a></li><li data-level="2"><a href="#actuator.metrics.export.newrelic">New Relic</a></li><li data-level="2"><a href="#actuator.metrics.export.otlp">OTLP</a></li><li data-level="2"><a href="#actuator.metrics.export.prometheus">Prometheus</a></li><li data-level="2"><a href="#actuator.metrics.export.signalfx">SignalFx</a></li><li data-level="2"><a href="#actuator.metrics.export.simple">Simple</a></li><li data-level="2"><a href="#actuator.metrics.export.stackdriver">Stackdriver</a></li><li data-level="2"><a href="#actuator.metrics.export.statsd">StatsD</a></li><li data-level="2"><a href="#actuator.metrics.export.wavefront">Wavefront</a></li><li data-level="1"><a href="#actuator.metrics.supported">Supported Metrics and Meters</a></li><li data-level="2"><a href="#actuator.metrics.supported.jvm">JVM Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.system">System Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.application-startup">Application Startup Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.logger">Logger Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.tasks">Task Execution and Scheduling Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.jms">JMS Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.spring-mvc">Spring MVC Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.spring-webflux">Spring WebFlux Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.jersey">Jersey Server Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.http-clients">HTTP Client Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.tomcat">Tomcat Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.cache">Cache Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.spring-batch">Spring Batch Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.spring-graphql">Spring GraphQL Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.jdbc">DataSource Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.hibernate">Hibernate Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.spring-data-repository">Spring Data Repository Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.rabbitmq">RabbitMQ Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.spring-integration">Spring Integration Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.kafka">Kafka Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.mongodb">MongoDB Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.jetty">Jetty Metrics</a></li><li data-level="2"><a href="#actuator.metrics.supported.redis">Redis Metrics</a></li><li data-level="1"><a href="#actuator.metrics.registering-custom">Registering Custom Metrics</a></li><li data-level="1"><a href="#actuator.metrics.customizing">Customizing Individual Metrics</a></li><li data-level="2"><a href="#actuator.metrics.customizing.common-tags">Common Tags</a></li><li data-level="2"><a href="#actuator.metrics.customizing.per-meter-properties">Per-meter Properties</a></li><li data-level="1"><a href="#actuator.metrics.endpoint">Metrics Endpoint</a></li><li data-level="1"><a href="#actuator.metrics.micrometer-observation">Integration with Micrometer Observation</a></li></ul></div></aside><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot Actuator provides dependency management and auto-configuration for <a class="external" href="https://micrometer.io" target="_blank">Micrometer</a>, an application metrics facade that supports <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14" target="_blank">numerous monitoring systems</a>, including:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a href="#actuator.metrics.export.appoptics">AppOptics</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.atlas">Atlas</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.datadog">Datadog</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.dynatrace">Dynatrace</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.elastic">Elastic</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.ganglia">Ganglia</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.graphite">Graphite</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.humio">Humio</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.influx">Influx</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.jmx">JMX</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.kairos">KairosDB</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.newrelic">New Relic</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.otlp">OTLP</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.prometheus">Prometheus</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.signalfx">SignalFx</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.simple">Simple</a> (in-memory)</p>
</li>
<li>
<p><a href="#actuator.metrics.export.stackdriver">Stackdriver</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.statsd">StatsD</a></p>
</li>
<li>
<p><a href="#actuator.metrics.export.wavefront">Wavefront</a></p>
</li>
</ul>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
To learn more about Micrometer’s capabilities, see its <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14" target="_blank">reference documentation</a>, in particular the <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/concepts" target="_blank">concepts section</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.metrics.getting-started"><a class="anchor" href="#actuator.metrics.getting-started"></a>Getting Started</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot auto-configures a composite <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/MeterRegistry.html" target="_blank"><code>MeterRegistry</code></a> and adds a registry to the composite for each of the supported implementations that it finds on the classpath.
Having a dependency on <code>micrometer-registry-{system}</code> in your runtime classpath is enough for Spring Boot to configure the registry.</p>
</div>
<div class="paragraph">
<p>Most registries share common features.
For instance, you can disable a particular registry even if the Micrometer registry implementation is on the classpath.
The following example disables Datadog:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_1">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_1_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_1_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_1_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_1_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_1_properties" class="tabpanel" id="_tabs_1_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.datadog.metrics.export.enabled</span>=<span class="hljs-string">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_1_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_1_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">datadog:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">enabled:</span> <span class="hljs-literal">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also disable all registries unless stated otherwise by the registry-specific property, as the following example shows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_2">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_2_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_2_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_2_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_2_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_2_properties" class="tabpanel" id="_tabs_2_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.defaults.metrics.export.enabled</span>=<span class="hljs-string">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_2_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_2_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">defaults:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">enabled:</span> <span class="hljs-literal">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Spring Boot also adds any auto-configured registries to the global static composite registry on the <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/Metrics.html" target="_blank"><code>Metrics</code></a> class, unless you explicitly tell it not to:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_3">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_3_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_3_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_3_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_3_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_3_properties" class="tabpanel" id="_tabs_3_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.metrics.use-global-registry</span>=<span class="hljs-string">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_3_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_3_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">metrics:</span>
    <span class="hljs-attr">use-global-registry:</span> <span class="hljs-literal">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can register any number of <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/autoconfigure/metrics/MeterRegistryCustomizer.html"><code>MeterRegistryCustomizer</code></a> beans to further configure the registry, such as applying common tags, before any meters are registered with the registry:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_4">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_4_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_4_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_4_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_4_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_4_java" class="tabpanel" id="_tabs_4_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.MeterRegistry;

<span class="hljs-keyword">import</span> org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMeterRegistryConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> MeterRegistryCustomizer&lt;MeterRegistry&gt; <span class="hljs-title">metricsCommonTags</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> (registry) -&gt; registry.config().commonTags(<span class="hljs-string">"region"</span>, <span class="hljs-string">"us-east-1"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_4_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_4_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.MeterRegistry
<span class="hljs-keyword">import</span> org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMeterRegistryConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">metricsCommonTags</span><span class="hljs-params">()</span></span>: MeterRegistryCustomizer&lt;MeterRegistry&gt; {
		<span class="hljs-keyword">return</span> MeterRegistryCustomizer { registry -&gt;
			registry.config().commonTags(<span class="hljs-string">"region"</span>, <span class="hljs-string">"us-east-1"</span>)
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can apply customizations to particular registry implementations by being more specific about the generic type:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_5">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_5_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_5_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_5_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_5_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_5_java" class="tabpanel" id="_tabs_5_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.Meter;
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.config.NamingConvention;
<span class="hljs-keyword">import</span> io.micrometer.graphite.GraphiteMeterRegistry;

<span class="hljs-keyword">import</span> org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMeterRegistryConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> MeterRegistryCustomizer&lt;GraphiteMeterRegistry&gt; <span class="hljs-title">graphiteMetricsNamingConvention</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> (registry) -&gt; registry.config().namingConvention(<span class="hljs-keyword">this</span>::name);
	}

	<span class="hljs-function"><span class="hljs-keyword">private</span> String <span class="hljs-title">name</span><span class="hljs-params">(String name, Meter.Type type, String baseUnit)</span> </span>{
		<span class="hljs-keyword">return</span> ...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_5_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_5_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.Meter
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.config.NamingConvention
<span class="hljs-keyword">import</span> io.micrometer.graphite.GraphiteMeterRegistry
<span class="hljs-keyword">import</span> org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMeterRegistryConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">graphiteMetricsNamingConvention</span><span class="hljs-params">()</span></span>: MeterRegistryCustomizer&lt;GraphiteMeterRegistry&gt; {
		<span class="hljs-keyword">return</span> MeterRegistryCustomizer { registry: GraphiteMeterRegistry -&gt;
			registry.config().namingConvention(<span class="hljs-keyword">this</span>::name)
		}
	}

	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">name</span><span class="hljs-params">(name: <span class="hljs-type">String</span>, type: <span class="hljs-type">Meter</span>.<span class="hljs-type">Type</span>, baseUnit: <span class="hljs-type">String</span>?)</span></span>: String {
		<span class="hljs-keyword">return</span>  ...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Spring Boot also <a href="#actuator.metrics.supported">configures built-in instrumentation</a> that you can control through configuration or dedicated annotation markers.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.metrics.export"><a class="anchor" href="#actuator.metrics.export"></a>Supported Monitoring Systems</h2>
<div class="sectionbody">
<div class="paragraph">
<p>This section briefly describes each of the supported monitoring systems.</p>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.appoptics"><a class="anchor" href="#actuator.metrics.export.appoptics"></a>AppOptics</h3>
<div class="paragraph">
<p>By default, the AppOptics registry periodically pushes metrics to <code><a class="bare external" href="https://api.appoptics.com/v1/measurements" target="_blank">api.appoptics.com/v1/measurements</a></code>.
To export metrics to SaaS <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/appOptics" target="_blank">AppOptics</a>, your API token must be provided:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_6">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_6_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_6_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_6_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_6_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_6_properties" class="tabpanel" id="_tabs_6_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.appoptics.metrics.export.api-token</span>=<span class="hljs-string">YOUR_TOKEN</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_6_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_6_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">appoptics:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">api-token:</span> <span class="hljs-string">"YOUR_TOKEN"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.atlas"><a class="anchor" href="#actuator.metrics.export.atlas"></a>Atlas</h3>
<div class="paragraph">
<p>By default, metrics are exported to <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/atlas" target="_blank">Atlas</a> running on your local machine.
You can provide the location of the <a class="external" href="https://github.com/Netflix/atlas" target="_blank">Atlas server</a>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_7">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_7_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_7_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_7_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_7_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_7_properties" class="tabpanel" id="_tabs_7_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.atlas.metrics.export.uri</span>=<span class="hljs-string">https://atlas.example.com:7101/api/v1/publish</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_7_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_7_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">atlas:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">uri:</span> <span class="hljs-string">"https://atlas.example.com:7101/api/v1/publish"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.datadog"><a class="anchor" href="#actuator.metrics.export.datadog"></a>Datadog</h3>
<div class="paragraph">
<p>A Datadog registry periodically pushes metrics to <a class="external" href="https://www.datadoghq.com" target="_blank">datadoghq</a>.
To export metrics to <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/datadog" target="_blank">Datadog</a>, you must provide your API key:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_8">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_8_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_8_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_8_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_8_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_8_properties" class="tabpanel" id="_tabs_8_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.datadog.metrics.export.api-key</span>=<span class="hljs-string">YOUR_KEY</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_8_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_8_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">datadog:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">api-key:</span> <span class="hljs-string">"YOUR_KEY"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If you additionally provide an application key (optional), then metadata such as meter descriptions, types, and base units will also be exported:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_9">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_9_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_9_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_9_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_9_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_9_properties" class="tabpanel" id="_tabs_9_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.datadog.metrics.export.api-key</span>=<span class="hljs-string">YOUR_API_KEY</span>
<span class="hljs-meta">management.datadog.metrics.export.application-key</span>=<span class="hljs-string">YOUR_APPLICATION_KEY</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_9_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_9_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">datadog:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">api-key:</span> <span class="hljs-string">"YOUR_API_KEY"</span>
        <span class="hljs-attr">application-key:</span> <span class="hljs-string">"YOUR_APPLICATION_KEY"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>By default, metrics are sent to the Datadog US <a class="external" href="https://docs.datadoghq.com/getting_started/site" target="_blank">site</a> (<code><a class="bare external" href="https://api.datadoghq.com" target="_blank">api.datadoghq.com</a></code>).
If your Datadog project is hosted on one of the other sites, or you need to send metrics through a proxy, configure the URI accordingly:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_10">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_10_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_10_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_10_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_10_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_10_properties" class="tabpanel" id="_tabs_10_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.datadog.metrics.export.uri</span>=<span class="hljs-string">https://api.datadoghq.eu</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_10_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_10_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">datadog:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">uri:</span> <span class="hljs-string">"https://api.datadoghq.eu"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also change the interval at which metrics are sent to Datadog:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_11">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_11_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_11_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_11_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_11_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_11_properties" class="tabpanel" id="_tabs_11_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.datadog.metrics.export.step</span>=<span class="hljs-string">30s</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_11_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_11_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">datadog:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">step:</span> <span class="hljs-string">"30s"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.dynatrace"><a class="anchor" href="#actuator.metrics.export.dynatrace"></a>Dynatrace</h3>
<div class="paragraph">
<p>Dynatrace offers two metrics ingest APIs, both of which are implemented for <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/dynatrace" target="_blank">Micrometer</a>.
You can find the Dynatrace documentation on Micrometer metrics ingest <a class="external" href="https://docs.dynatrace.com/docs/shortlink/micrometer-metrics-ingest" target="_blank">here</a>.
Configuration properties in the <code>v1</code> namespace apply only when exporting to the <a class="external" href="https://docs.dynatrace.com/docs/shortlink/api-metrics" target="_blank">Timeseries v1 API</a>.
Configuration properties in the <code>v2</code> namespace apply only when exporting to the <a class="external" href="https://docs.dynatrace.com/docs/shortlink/api-metrics-v2-post-datapoints" target="_blank">Metrics v2 API</a>.
Note that this integration can export only to either the <code>v1</code> or <code>v2</code> version of the API at a time, with <code>v2</code> being preferred.
If the <code>device-id</code> (required for v1 but not used in v2) is set in the <code>v1</code> namespace, metrics are exported to the <code>v1</code> endpoint.
Otherwise, <code>v2</code> is assumed.</p>
</div>
<div class="sect3">
<h4 id="actuator.metrics.export.dynatrace.v2-api"><a class="anchor" href="#actuator.metrics.export.dynatrace.v2-api"></a>v2 API</h4>
<div class="paragraph">
<p>You can use the v2 API in two ways.</p>
</div>
<div class="sect4">
<h5 id="actuator.metrics.export.dynatrace.v2-api.auto-config"><a class="anchor" href="#actuator.metrics.export.dynatrace.v2-api.auto-config"></a>Auto-configuration</h5>
<div class="paragraph">
<p>Dynatrace auto-configuration is available for hosts that are monitored by the OneAgent or by the Dynatrace Operator for Kubernetes.</p>
</div>
<div class="paragraph">
<p><strong>Local OneAgent:</strong> If a OneAgent is running on the host, metrics are automatically exported to the <a class="external" href="https://docs.dynatrace.com/docs/shortlink/local-api" target="_blank">local OneAgent ingest endpoint</a>.
The ingest endpoint forwards the metrics to the Dynatrace backend.</p>
</div>
<div class="paragraph">
<p><strong>Dynatrace Kubernetes Operator:</strong> When running in Kubernetes with the Dynatrace Operator installed, the registry will automatically pick up your endpoint URI and API token from the operator instead.</p>
</div>
<div class="paragraph">
<p>This is the default behavior and requires no special setup beyond a dependency on <code>io.micrometer:micrometer-registry-dynatrace</code>.</p>
</div>
</div>
<div class="sect4">
<h5 id="actuator.metrics.export.dynatrace.v2-api.manual-config"><a class="anchor" href="#actuator.metrics.export.dynatrace.v2-api.manual-config"></a>Manual Configuration</h5>
<div class="paragraph">
<p>If no auto-configuration is available, the endpoint of the <a class="external" href="https://docs.dynatrace.com/docs/shortlink/api-metrics-v2-post-datapoints" target="_blank">Metrics v2 API</a> and an API token are required.
The <a class="external" href="https://docs.dynatrace.com/docs/shortlink/api-authentication" target="_blank">API token</a> must have the “Ingest metrics” (<code>metrics.ingest</code>) permission set.
We recommend limiting the scope of the token to this one permission.
You must ensure that the endpoint URI contains the path (for example, <code>/api/v2/metrics/ingest</code>):</p>
</div>
<div class="paragraph">
<p>The URL of the Metrics API v2 ingest endpoint is different according to your deployment option:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>SaaS: <code>https://{your-environment-id}.live.dynatrace.com/api/v2/metrics/ingest</code></p>
</li>
<li>
<p>Managed deployments: <code>https://{your-domain}/e/{your-environment-id}/api/v2/metrics/ingest</code></p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The example below configures metrics export using the <code>example</code> environment id:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_12">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_12_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_12_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_12_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_12_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_12_properties" class="tabpanel" id="_tabs_12_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.dynatrace.metrics.export.uri</span>=<span class="hljs-string">https://example.live.dynatrace.com/api/v2/metrics/ingest</span>
<span class="hljs-meta">management.dynatrace.metrics.export.api-token</span>=<span class="hljs-string">YOUR_TOKEN</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_12_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_12_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">dynatrace:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">uri:</span> <span class="hljs-string">"https://example.live.dynatrace.com/api/v2/metrics/ingest"</span>
        <span class="hljs-attr">api-token:</span> <span class="hljs-string">"YOUR_TOKEN"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>When using the Dynatrace v2 API, the following optional features are available (more details can be found in the <a class="external" href="https://docs.dynatrace.com/docs/shortlink/micrometer-metrics-ingest#dt-configuration-properties" target="_blank">Dynatrace documentation</a>):</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Metric key prefix: Sets a prefix that is prepended to all exported metric keys.</p>
</li>
<li>
<p>Enrich with Dynatrace metadata: If a OneAgent or Dynatrace operator is running, enrich metrics with additional metadata (for example, about the host, process, or pod).</p>
</li>
<li>
<p>Default dimensions: Specify key-value pairs that are added to all exported metrics.
If tags with the same key are specified with Micrometer, they overwrite the default dimensions.</p>
</li>
<li>
<p>Use Dynatrace Summary instruments: In some cases the Micrometer Dynatrace registry created metrics that were rejected.
In Micrometer 1.9.x, this was fixed by introducing Dynatrace-specific summary instruments.
Setting this toggle to <code>false</code> forces Micrometer to fall back to the behavior that was the default before 1.9.x.
It should only be used when encountering problems while migrating from Micrometer 1.8.x to 1.9.x.</p>
</li>
<li>
<p>Export meter metadata: Starting from Micrometer 1.12.0, the Dynatrace exporter will also export meter metadata, such as unit and description by default.
Use the <code>export-meter-metadata</code> toggle to turn this feature off.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>It is possible to not specify a URI and API token, as shown in the following example.
In this scenario, the automatically configured endpoint is used:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_13">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_13_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_13_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_13_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_13_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_13_properties" class="tabpanel" id="_tabs_13_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.dynatrace.metrics.export.v2.metric-key-prefix</span>=<span class="hljs-string">your.key.prefix</span>
<span class="hljs-meta">management.dynatrace.metrics.export.v2.enrich-with-dynatrace-metadata</span>=<span class="hljs-string">true</span>
<span class="hljs-meta">management.dynatrace.metrics.export.v2.default-dimensions.key1</span>=<span class="hljs-string">value1</span>
<span class="hljs-meta">management.dynatrace.metrics.export.v2.default-dimensions.key2</span>=<span class="hljs-string">value2</span>
<span class="hljs-meta">management.dynatrace.metrics.export.v2.use-dynatrace-summary-instruments</span>=<span class="hljs-string">true</span>
<span class="hljs-meta">management.dynatrace.metrics.export.v2.export-meter-metadata</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_13_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_13_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">dynatrace:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-comment"># Specify uri and api-token here if not using the local OneAgent endpoint.</span>
        <span class="hljs-attr">v2:</span>
          <span class="hljs-attr">metric-key-prefix:</span> <span class="hljs-string">"your.key.prefix"</span>
          <span class="hljs-attr">enrich-with-dynatrace-metadata:</span> <span class="hljs-literal">true</span>
          <span class="hljs-attr">default-dimensions:</span>
            <span class="hljs-attr">key1:</span> <span class="hljs-string">"value1"</span>
            <span class="hljs-attr">key2:</span> <span class="hljs-string">"value2"</span>
          <span class="hljs-attr">use-dynatrace-summary-instruments:</span> <span class="hljs-literal">true</span> <span class="hljs-comment"># (default: true)</span>
          <span class="hljs-attr">export-meter-metadata:</span> <span class="hljs-literal">true</span>             <span class="hljs-comment"># (default: true)</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect3">
<h4 id="actuator.metrics.export.dynatrace.v1-api"><a class="anchor" href="#actuator.metrics.export.dynatrace.v1-api"></a>v1 API (Legacy)</h4>
<div class="paragraph">
<p>The Dynatrace v1 API metrics registry pushes metrics to the configured URI periodically by using the <a class="external" href="https://docs.dynatrace.com/docs/shortlink/api-metrics" target="_blank">Timeseries v1 API</a>.
For backwards-compatibility with existing setups, when <code>device-id</code> is set (required for v1, but not used in v2), metrics are exported to the Timeseries v1 endpoint.
To export metrics to <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/dynatrace" target="_blank">Dynatrace</a>, your API token, device ID, and URI must be provided:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_14">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_14_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_14_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_14_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_14_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_14_properties" class="tabpanel" id="_tabs_14_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.dynatrace.metrics.export.uri</span>=<span class="hljs-string">https://{your-environment-id}.live.dynatrace.com</span>
<span class="hljs-meta">management.dynatrace.metrics.export.api-token</span>=<span class="hljs-string">YOUR_TOKEN</span>
<span class="hljs-meta">management.dynatrace.metrics.export.v1.device-id</span>=<span class="hljs-string">YOUR_DEVICE_ID</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_14_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_14_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">dynatrace:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">uri:</span> <span class="hljs-string">"https://{your-environment-id}.live.dynatrace.com"</span>
        <span class="hljs-attr">api-token:</span> <span class="hljs-string">"YOUR_TOKEN"</span>
        <span class="hljs-attr">v1:</span>
          <span class="hljs-attr">device-id:</span> <span class="hljs-string">"YOUR_DEVICE_ID"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>For the v1 API, you must specify the base environment URI without a path, as the v1 endpoint path is added automatically.</p>
</div>
</div>
<div class="sect3">
<h4 id="actuator.metrics.export.dynatrace.version-independent-settings"><a class="anchor" href="#actuator.metrics.export.dynatrace.version-independent-settings"></a>Version-independent Settings</h4>
<div class="paragraph">
<p>In addition to the API endpoint and token, you can also change the interval at which metrics are sent to Dynatrace.
The default export interval is <code>60s</code>.
The following example sets the export interval to 30 seconds:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_15">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_15_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_15_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_15_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_15_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_15_properties" class="tabpanel" id="_tabs_15_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.dynatrace.metrics.export.step</span>=<span class="hljs-string">30s</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_15_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_15_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">dynatrace:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">step:</span> <span class="hljs-string">"30s"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can find more information on how to set up the Dynatrace exporter for Micrometer in the <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/dynatrace" target="_blank">Micrometer documentation</a> and the <a class="external" href="https://docs.dynatrace.com/docs/shortlink/micrometer-metrics-ingest" target="_blank">Dynatrace documentation</a>.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.elastic"><a class="anchor" href="#actuator.metrics.export.elastic"></a>Elastic</h3>
<div class="paragraph">
<p>By default, metrics are exported to <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/elastic" target="_blank">Elastic</a> running on your local machine.
You can provide the location of the Elastic server to use by using the following property:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_16">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_16_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_16_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_16_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_16_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_16_properties" class="tabpanel" id="_tabs_16_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.elastic.metrics.export.host</span>=<span class="hljs-string">https://elastic.example.com:8086</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_16_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_16_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">elastic:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">host:</span> <span class="hljs-string">"https://elastic.example.com:8086"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.ganglia"><a class="anchor" href="#actuator.metrics.export.ganglia"></a>Ganglia</h3>
<div class="paragraph">
<p>By default, metrics are exported to <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/ganglia" target="_blank">Ganglia</a> running on your local machine.
You can provide the <a class="external" href="http://ganglia.sourceforge.net" target="_blank">Ganglia server</a> host and port, as the following example shows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_17">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_17_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_17_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_17_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_17_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_17_properties" class="tabpanel" id="_tabs_17_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.ganglia.metrics.export.host</span>=<span class="hljs-string">ganglia.example.com</span>
<span class="hljs-meta">management.ganglia.metrics.export.port</span>=<span class="hljs-string">9649</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_17_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_17_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">ganglia:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">host:</span> <span class="hljs-string">"ganglia.example.com"</span>
        <span class="hljs-attr">port:</span> <span class="hljs-number">9649</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.graphite"><a class="anchor" href="#actuator.metrics.export.graphite"></a>Graphite</h3>
<div class="paragraph">
<p>By default, metrics are exported to <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/graphite" target="_blank">Graphite</a> running on your local machine.
You can provide the <a class="external" href="https://graphiteapp.org" target="_blank">Graphite server</a> host and port, as the following example shows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_18">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_18_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_18_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_18_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_18_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_18_properties" class="tabpanel" id="_tabs_18_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.graphite.metrics.export.host</span>=<span class="hljs-string">graphite.example.com</span>
<span class="hljs-meta">management.graphite.metrics.export.port</span>=<span class="hljs-string">9004</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_18_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_18_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">graphite:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
         <span class="hljs-attr">host:</span> <span class="hljs-string">"graphite.example.com"</span>
         <span class="hljs-attr">port:</span> <span class="hljs-number">9004</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Micrometer provides a default <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/util/HierarchicalNameMapper.html" target="_blank"><code>HierarchicalNameMapper</code></a> that governs how a dimensional meter ID is <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/graphite#_hierarchical_name_mapping" target="_blank">mapped to flat hierarchical names</a>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<div class="paragraph">
<p>To take control over this behavior, define your <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-registry-graphite/1.14.7/io/micrometer/graphite/GraphiteMeterRegistry.html" target="_blank"><code>GraphiteMeterRegistry</code></a> and supply your own <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/util/HierarchicalNameMapper.html" target="_blank"><code>HierarchicalNameMapper</code></a>.
Auto-configured <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-registry-graphite/1.14.7/io/micrometer/graphite/GraphiteConfig.html" target="_blank"><code>GraphiteConfig</code></a> and <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/Clock.html" target="_blank"><code>Clock</code></a> beans are provided unless you define your own:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_19">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_19_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_19_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_19_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_19_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_19_java" class="tabpanel" id="_tabs_19_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.Clock;
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.Meter;
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.config.NamingConvention;
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.util.HierarchicalNameMapper;
<span class="hljs-keyword">import</span> io.micrometer.graphite.GraphiteConfig;
<span class="hljs-keyword">import</span> io.micrometer.graphite.GraphiteMeterRegistry;

<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyGraphiteConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> GraphiteMeterRegistry <span class="hljs-title">graphiteMeterRegistry</span><span class="hljs-params">(GraphiteConfig config, Clock clock)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> GraphiteMeterRegistry(config, clock, <span class="hljs-keyword">this</span>::toHierarchicalName);
	}

	<span class="hljs-function"><span class="hljs-keyword">private</span> String <span class="hljs-title">toHierarchicalName</span><span class="hljs-params">(Meter.Id id, NamingConvention convention)</span> </span>{
		<span class="hljs-keyword">return</span> ...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_19_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_19_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.Clock
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.Meter
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.config.NamingConvention
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.util.HierarchicalNameMapper
<span class="hljs-keyword">import</span> io.micrometer.graphite.GraphiteConfig
<span class="hljs-keyword">import</span> io.micrometer.graphite.GraphiteMeterRegistry
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyGraphiteConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">graphiteMeterRegistry</span><span class="hljs-params">(config: <span class="hljs-type">GraphiteConfig</span>, clock: <span class="hljs-type">Clock</span>)</span></span>: GraphiteMeterRegistry {
		<span class="hljs-keyword">return</span> GraphiteMeterRegistry(config, clock, <span class="hljs-keyword">this</span>::toHierarchicalName)
	}
	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">toHierarchicalName</span><span class="hljs-params">(id: <span class="hljs-type">Meter</span>.<span class="hljs-type">Id</span>, convention: <span class="hljs-type">NamingConvention</span>)</span></span>: String {
		<span class="hljs-keyword">return</span>  ...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.humio"><a class="anchor" href="#actuator.metrics.export.humio"></a>Humio</h3>
<div class="paragraph">
<p>By default, the Humio registry periodically pushes metrics to <a class="bare external" href="https://cloud.humio.com" target="_blank">cloud.humio.com</a>.
To export metrics to SaaS <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/humio" target="_blank">Humio</a>, you must provide your API token:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_20">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_20_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_20_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_20_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_20_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_20_properties" class="tabpanel" id="_tabs_20_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.humio.metrics.export.api-token</span>=<span class="hljs-string">YOUR_TOKEN</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_20_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_20_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">humio:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">api-token:</span> <span class="hljs-string">"YOUR_TOKEN"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You should also configure one or more tags to identify the data source to which metrics are pushed:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_21">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_21_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_21_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_21_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_21_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_21_properties" class="tabpanel" id="_tabs_21_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.humio.metrics.export.tags.alpha</span>=<span class="hljs-string">a</span>
<span class="hljs-meta">management.humio.metrics.export.tags.bravo</span>=<span class="hljs-string">b</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_21_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_21_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">humio:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">tags:</span>
          <span class="hljs-attr">alpha:</span> <span class="hljs-string">"a"</span>
          <span class="hljs-attr">bravo:</span> <span class="hljs-string">"b"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.influx"><a class="anchor" href="#actuator.metrics.export.influx"></a>Influx</h3>
<div class="paragraph">
<p>By default, metrics are exported to an <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/influx" target="_blank">Influx</a> v1 instance running on your local machine with the default configuration.
To export metrics to InfluxDB v2, configure the <code>org</code>, <code>bucket</code>, and authentication <code>token</code> for writing metrics.
You can provide the location of the <a class="external" href="https://www.influxdata.com" target="_blank">Influx server</a> to use by using:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_22">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_22_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_22_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_22_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_22_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_22_properties" class="tabpanel" id="_tabs_22_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.influx.metrics.export.uri</span>=<span class="hljs-string">https://influx.example.com:8086</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_22_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_22_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">influx:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">uri:</span> <span class="hljs-string">"https://influx.example.com:8086"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.jmx"><a class="anchor" href="#actuator.metrics.export.jmx"></a>JMX</h3>
<div class="paragraph">
<p>Micrometer provides a hierarchical mapping to <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/jmx" target="_blank">JMX</a>, primarily as a cheap and portable way to view metrics locally.
By default, metrics are exported to the <code>metrics</code> JMX domain.
You can provide the domain to use by using:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_23">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_23_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_23_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_23_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_23_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_23_properties" class="tabpanel" id="_tabs_23_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.jmx.metrics.export.domain</span>=<span class="hljs-string">com.example.app.metrics</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_23_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_23_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">jmx:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">domain:</span> <span class="hljs-string">"com.example.app.metrics"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Micrometer provides a default <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/util/HierarchicalNameMapper.html" target="_blank"><code>HierarchicalNameMapper</code></a> that governs how a dimensional meter ID is <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/jmx#_hierarchical_name_mapping" target="_blank">mapped to flat hierarchical names</a>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<div class="paragraph">
<p>To take control over this behavior, define your <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-registry-jmx/1.14.7/io/micrometer/jmx/JmxMeterRegistry.html" target="_blank"><code>JmxMeterRegistry</code></a> and supply your own <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/util/HierarchicalNameMapper.html" target="_blank"><code>HierarchicalNameMapper</code></a>.
Auto-configured <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-registry-jmx/1.14.7/io/micrometer/jmx/JmxConfig.html" target="_blank"><code>JmxConfig</code></a> and <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/Clock.html" target="_blank"><code>Clock</code></a> beans are provided unless you define your own:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_24">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_24_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_24_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_24_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_24_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_24_java" class="tabpanel" id="_tabs_24_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.Clock;
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.Meter;
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.config.NamingConvention;
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.util.HierarchicalNameMapper;
<span class="hljs-keyword">import</span> io.micrometer.jmx.JmxConfig;
<span class="hljs-keyword">import</span> io.micrometer.jmx.JmxMeterRegistry;

<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyJmxConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> JmxMeterRegistry <span class="hljs-title">jmxMeterRegistry</span><span class="hljs-params">(JmxConfig config, Clock clock)</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> JmxMeterRegistry(config, clock, <span class="hljs-keyword">this</span>::toHierarchicalName);
	}

	<span class="hljs-function"><span class="hljs-keyword">private</span> String <span class="hljs-title">toHierarchicalName</span><span class="hljs-params">(Meter.Id id, NamingConvention convention)</span> </span>{
		<span class="hljs-keyword">return</span> ...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_24_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_24_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.Clock
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.Meter
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.config.NamingConvention
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.util.HierarchicalNameMapper
<span class="hljs-keyword">import</span> io.micrometer.jmx.JmxConfig
<span class="hljs-keyword">import</span> io.micrometer.jmx.JmxMeterRegistry
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyJmxConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">jmxMeterRegistry</span><span class="hljs-params">(config: <span class="hljs-type">JmxConfig</span>, clock: <span class="hljs-type">Clock</span>)</span></span>: JmxMeterRegistry {
		<span class="hljs-keyword">return</span> JmxMeterRegistry(config, clock, <span class="hljs-keyword">this</span>::toHierarchicalName)
	}

	<span class="hljs-keyword">private</span> <span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">toHierarchicalName</span><span class="hljs-params">(id: <span class="hljs-type">Meter</span>.<span class="hljs-type">Id</span>, convention: <span class="hljs-type">NamingConvention</span>)</span></span>: String {
		<span class="hljs-keyword">return</span>  ...
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.kairos"><a class="anchor" href="#actuator.metrics.export.kairos"></a>KairosDB</h3>
<div class="paragraph">
<p>By default, metrics are exported to <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/kairos" target="_blank">KairosDB</a> running on your local machine.
You can provide the location of the <a class="external" href="https://kairosdb.github.io/" target="_blank">KairosDB server</a> to use by using:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_25">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_25_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_25_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_25_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_25_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_25_properties" class="tabpanel" id="_tabs_25_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.kairos.metrics.export.uri</span>=<span class="hljs-string">https://kairosdb.example.com:8080/api/v1/datapoints</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_25_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_25_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">kairos:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">uri:</span> <span class="hljs-string">"https://kairosdb.example.com:8080/api/v1/datapoints"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.newrelic"><a class="anchor" href="#actuator.metrics.export.newrelic"></a>New Relic</h3>
<div class="paragraph">
<p>A New Relic registry periodically pushes metrics to <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/new-relic" target="_blank">New Relic</a>.
To export metrics to <a class="external" href="https://newrelic.com" target="_blank">New Relic</a>, you must provide your API key and account ID:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_26">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_26_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_26_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_26_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_26_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_26_properties" class="tabpanel" id="_tabs_26_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.newrelic.metrics.export.api-key</span>=<span class="hljs-string">YOUR_KEY</span>
<span class="hljs-meta">management.newrelic.metrics.export.account-id</span>=<span class="hljs-string">YOUR_ACCOUNT_ID</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_26_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_26_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">newrelic:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">api-key:</span> <span class="hljs-string">"YOUR_KEY"</span>
        <span class="hljs-attr">account-id:</span> <span class="hljs-string">"YOUR_ACCOUNT_ID"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also change the interval at which metrics are sent to New Relic:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_27">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_27_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_27_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_27_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_27_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_27_properties" class="tabpanel" id="_tabs_27_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.newrelic.metrics.export.step</span>=<span class="hljs-string">30s</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_27_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_27_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">newrelic:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">step:</span> <span class="hljs-string">"30s"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>By default, metrics are published through REST calls, but you can also use the Java Agent API if you have it on the classpath:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_28">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_28_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_28_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_28_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_28_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_28_properties" class="tabpanel" id="_tabs_28_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.newrelic.metrics.export.client-provider-type</span>=<span class="hljs-string">insights-agent</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_28_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_28_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">newrelic:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">client-provider-type:</span> <span class="hljs-string">"insights-agent"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Finally, you can take full control by defining your own <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-registry-new-relic/1.14.7/io/micrometer/newrelic/NewRelicClientProvider.html" target="_blank"><code>NewRelicClientProvider</code></a> bean.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.otlp"><a class="anchor" href="#actuator.metrics.export.otlp"></a>OTLP</h3>
<div class="paragraph">
<p>By default, metrics are exported over the <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/otlp" target="_blank">OpenTelemetry protocol (OTLP)</a> to a consumer running on your local machine.
To export to another location, provide the location of the <a class="external" href="https://opentelemetry.io/" target="_blank">OTLP metrics endpoint</a> using <code>management.otlp.metrics.export.url</code>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_29">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_29_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_29_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_29_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_29_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_29_properties" class="tabpanel" id="_tabs_29_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.otlp.metrics.export.url</span>=<span class="hljs-string">https://otlp.example.com:4318/v1/metrics</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_29_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_29_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">otlp:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">url:</span> <span class="hljs-string">"https://otlp.example.com:4318/v1/metrics"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Custom headers, for example for authentication, can also be provided using <code>management.otlp.metrics.export.headers.*</code> properties.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.prometheus"><a class="anchor" href="#actuator.metrics.export.prometheus"></a>Prometheus</h3>
<div class="paragraph">
<p><a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/prometheus" target="_blank">Prometheus</a> expects to scrape or poll individual application instances for metrics.
Spring Boot provides an actuator endpoint at <code>/actuator/prometheus</code> to present a <a class="external" href="https://prometheus.io" target="_blank">Prometheus scrape</a> with the appropriate format.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
By default, the endpoint is not available and must be exposed. See <a class="xref page" href="endpoints.html#actuator.endpoints.exposing">exposing endpoints</a> for more details.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>The following example <code>scrape_config</code> adds to <code>prometheus.yml</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">scrape_configs:</span>
<span class="hljs-bullet">-</span> <span class="hljs-attr">job_name:</span> <span class="hljs-string">"spring"</span>
  <span class="hljs-attr">metrics_path:</span> <span class="hljs-string">"/actuator/prometheus"</span>
  <span class="hljs-attr">static_configs:</span>
  <span class="hljs-bullet">-</span> <span class="hljs-attr">targets:</span> <span class="hljs-string">["HOST:PORT"]</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p><a class="external" href="https://prometheus.io/docs/prometheus/latest/feature_flags/#exemplars-storage" target="_blank">Prometheus Exemplars</a> are also supported.
To enable this feature, a <a class="apiref external" href="https://javadoc.io/doc/io.prometheus/prometheus-metrics-tracer-common/1.3.6/io/prometheus/metrics/tracer/common/SpanContext.html" target="_blank"><code>SpanContext</code></a> bean should be present.
If you’re using the deprecated Prometheus simpleclient support and want to enable that feature, a <a class="apiref external" href="https://javadoc.io/doc/io.prometheus/simpleclient_tracer_common/0.16.0/io/prometheus/client/exemplars/tracer/common/SpanContextSupplier.html" target="_blank"><code>SpanContextSupplier</code></a> bean should be present.
If you use <a class="external" href="https://docs.micrometer.io/tracing/reference/1.4" target="_blank">Micrometer Tracing</a>, this will be auto-configured for you, but you can always create your own if you want.
Please check the <a class="external" href="https://prometheus.io/docs/prometheus/latest/feature_flags/#exemplars-storage" target="_blank">Prometheus Docs</a>, since this feature needs to be explicitly enabled on Prometheus' side, and it is only supported using the <a class="external" href="https://github.com/OpenObservability/OpenMetrics/blob/v1.0.0/specification/OpenMetrics.md#exemplars" target="_blank">OpenMetrics</a> format.</p>
</div>
<div class="paragraph">
<p>For ephemeral or batch jobs that may not exist long enough to be scraped, you can use <a class="external" href="https://github.com/prometheus/pushgateway" target="_blank">Prometheus Pushgateway</a> support to expose the metrics to Prometheus.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The Prometheus Pushgateway only works with the deprecated Prometheus simpleclient for now, until the Prometheus 1.x client adds support for it.
To switch to the simpleclient, remove <code>io.micrometer:micrometer-registry-prometheus</code> from your project and add <code>io.micrometer:micrometer-registry-prometheus-simpleclient</code> instead.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>To enable Prometheus Pushgateway support, add the following dependency to your project:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-xml hljs" data-lang="xml"><span class="hljs-tag">&lt;<span class="hljs-name">dependency</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">groupId</span>&gt;</span>io.prometheus<span class="hljs-tag">&lt;/<span class="hljs-name">groupId</span>&gt;</span>
	<span class="hljs-tag">&lt;<span class="hljs-name">artifactId</span>&gt;</span>simpleclient_pushgateway<span class="hljs-tag">&lt;/<span class="hljs-name">artifactId</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">dependency</span>&gt;</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
<div class="paragraph">
<p>When the Prometheus Pushgateway dependency is present on the classpath and the <code>management.prometheus.metrics.export.pushgateway.enabled</code> property is set to <code>true</code>, a <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/metrics/export/prometheus/PrometheusPushGatewayManager.html"><code>PrometheusPushGatewayManager</code></a> bean is auto-configured.
This manages the pushing of metrics to a Prometheus Pushgateway.</p>
</div>
<div class="paragraph">
<p>You can tune the <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/metrics/export/prometheus/PrometheusPushGatewayManager.html"><code>PrometheusPushGatewayManager</code></a> by using properties under <code>management.prometheus.metrics.export.pushgateway</code>.
For advanced configuration, you can also provide your own <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/metrics/export/prometheus/PrometheusPushGatewayManager.html"><code>PrometheusPushGatewayManager</code></a> bean.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.signalfx"><a class="anchor" href="#actuator.metrics.export.signalfx"></a>SignalFx</h3>
<div class="paragraph">
<p>SignalFx registry periodically pushes metrics to <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/signalFx" target="_blank">SignalFx</a>.
To export metrics to <a class="external" href="https://www.signalfx.com" target="_blank">SignalFx</a>, you must provide your access token:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_30">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_30_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_30_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_30_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_30_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_30_properties" class="tabpanel" id="_tabs_30_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.signalfx.metrics.export.access-token</span>=<span class="hljs-string">YOUR_ACCESS_TOKEN</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_30_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_30_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">signalfx:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">access-token:</span> <span class="hljs-string">"YOUR_ACCESS_TOKEN"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also change the interval at which metrics are sent to SignalFx:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_31">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_31_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_31_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_31_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_31_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_31_properties" class="tabpanel" id="_tabs_31_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.signalfx.metrics.export.step</span>=<span class="hljs-string">30s</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_31_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_31_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">signalfx:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">step:</span> <span class="hljs-string">"30s"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.simple"><a class="anchor" href="#actuator.metrics.export.simple"></a>Simple</h3>
<div class="paragraph">
<p>Micrometer ships with a simple, in-memory backend that is automatically used as a fallback if no other registry is configured.
This lets you see what metrics are collected in the <a href="#actuator.metrics.endpoint">metrics endpoint</a>.</p>
</div>
<div class="paragraph">
<p>The in-memory backend disables itself as soon as you use any other available backend.
You can also disable it explicitly:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_32">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_32_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_32_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_32_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_32_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_32_properties" class="tabpanel" id="_tabs_32_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.simple.metrics.export.enabled</span>=<span class="hljs-string">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_32_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_32_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">simple:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">enabled:</span> <span class="hljs-literal">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.stackdriver"><a class="anchor" href="#actuator.metrics.export.stackdriver"></a>Stackdriver</h3>
<div class="paragraph">
<p>The Stackdriver registry periodically pushes metrics to <a class="external" href="https://cloud.google.com/stackdriver/" target="_blank">Stackdriver</a>.
To export metrics to SaaS <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/stackdriver" target="_blank">Stackdriver</a>, you must provide your Google Cloud project ID:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_33">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_33_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_33_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_33_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_33_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_33_properties" class="tabpanel" id="_tabs_33_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.stackdriver.metrics.export.project-id</span>=<span class="hljs-string">my-project</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_33_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_33_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">stackdriver:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">project-id:</span> <span class="hljs-string">"my-project"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also change the interval at which metrics are sent to Stackdriver:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_34">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_34_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_34_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_34_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_34_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_34_properties" class="tabpanel" id="_tabs_34_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.stackdriver.metrics.export.step</span>=<span class="hljs-string">30s</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_34_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_34_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">stackdriver:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">step:</span> <span class="hljs-string">"30s"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.statsd"><a class="anchor" href="#actuator.metrics.export.statsd"></a>StatsD</h3>
<div class="paragraph">
<p>The StatsD registry eagerly pushes metrics over UDP to a StatsD agent.
By default, metrics are exported to a <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/statsD" target="_blank">StatsD</a> agent running on your local machine.
You can provide the StatsD agent host, port, and protocol to use by using:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_35">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_35_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_35_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_35_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_35_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_35_properties" class="tabpanel" id="_tabs_35_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.statsd.metrics.export.host</span>=<span class="hljs-string">statsd.example.com</span>
<span class="hljs-meta">management.statsd.metrics.export.port</span>=<span class="hljs-string">9125</span>
<span class="hljs-meta">management.statsd.metrics.export.protocol</span>=<span class="hljs-string">udp</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_35_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_35_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">statsd:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">host:</span> <span class="hljs-string">"statsd.example.com"</span>
        <span class="hljs-attr">port:</span> <span class="hljs-number">9125</span>
        <span class="hljs-attr">protocol:</span> <span class="hljs-string">"udp"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>You can also change the StatsD line protocol to use (it defaults to Datadog):</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_36">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_36_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_36_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_36_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_36_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_36_properties" class="tabpanel" id="_tabs_36_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.statsd.metrics.export.flavor</span>=<span class="hljs-string">etsy</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_36_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_36_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">statsd:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">flavor:</span> <span class="hljs-string">"etsy"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.export.wavefront"><a class="anchor" href="#actuator.metrics.export.wavefront"></a>Wavefront</h3>
<div class="paragraph">
<p>The Wavefront registry periodically pushes metrics to <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/implementations/wavefront" target="_blank">Wavefront</a>.
If you are exporting metrics to <a class="external" href="https://www.wavefront.com/" target="_blank">Wavefront</a> directly, you must provide your API token:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_37">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_37_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_37_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_37_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_37_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_37_properties" class="tabpanel" id="_tabs_37_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.wavefront.api-token</span>=<span class="hljs-string">YOUR_API_TOKEN</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_37_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_37_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">wavefront:</span>
    <span class="hljs-attr">api-token:</span> <span class="hljs-string">"YOUR_API_TOKEN"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Alternatively, you can use a Wavefront sidecar or an internal proxy in your environment to forward metrics data to the Wavefront API host:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_38">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_38_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_38_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_38_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_38_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_38_properties" class="tabpanel" id="_tabs_38_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.wavefront.uri</span>=<span class="hljs-string">proxy://localhost:2878</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_38_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_38_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">wavefront:</span>
    <span class="hljs-attr">uri:</span> <span class="hljs-string">"proxy://localhost:2878"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
If you publish metrics to a Wavefront proxy (as described in <a class="external" href="https://docs.wavefront.com/proxies_installing.html" target="_blank">the Wavefront documentation</a>), the host must be in the <code>proxy://HOST:PORT</code> format.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>You can also change the interval at which metrics are sent to Wavefront:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_39">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_39_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_39_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_39_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_39_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_39_properties" class="tabpanel" id="_tabs_39_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.wavefront.metrics.export.step</span>=<span class="hljs-string">30s</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_39_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_39_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">wavefront:</span>
    <span class="hljs-attr">metrics:</span>
      <span class="hljs-attr">export:</span>
        <span class="hljs-attr">step:</span> <span class="hljs-string">"30s"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.metrics.supported"><a class="anchor" href="#actuator.metrics.supported"></a>Supported Metrics and Meters</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot provides automatic meter registration for a wide variety of technologies.
In most situations, the defaults provide sensible metrics that can be published to any of the supported monitoring systems.</p>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.jvm"><a class="anchor" href="#actuator.metrics.supported.jvm"></a>JVM Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables JVM Metrics by using core Micrometer classes.
JVM metrics are published under the <code>jvm.</code> meter name.</p>
</div>
<div class="paragraph">
<p>The following JVM metrics are provided:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Various memory and buffer pool details</p>
</li>
<li>
<p>Statistics related to garbage collection</p>
</li>
<li>
<p>Thread utilization</p>
</li>
<li>
<p>The number of classes loaded and unloaded</p>
</li>
<li>
<p>JVM version information</p>
</li>
<li>
<p>JIT compilation time</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.system"><a class="anchor" href="#actuator.metrics.supported.system"></a>System Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables system metrics by using core Micrometer classes.
System metrics are published under the <code>system.</code>, <code>process.</code>, and <code>disk.</code> meter names.</p>
</div>
<div class="paragraph">
<p>The following system metrics are provided:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>CPU metrics</p>
</li>
<li>
<p>File descriptor metrics</p>
</li>
<li>
<p>Uptime metrics (both the amount of time the application has been running and a fixed gauge of the absolute start time)</p>
</li>
<li>
<p>Disk space available</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.application-startup"><a class="anchor" href="#actuator.metrics.supported.application-startup"></a>Application Startup Metrics</h3>
<div class="paragraph">
<p>Auto-configuration exposes application startup time metrics:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>application.started.time</code>: time taken to start the application.</p>
</li>
<li>
<p><code>application.ready.time</code>: time taken for the application to be ready to service requests.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Metrics are tagged by the fully qualified name of the application class.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.logger"><a class="anchor" href="#actuator.metrics.supported.logger"></a>Logger Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables the event metrics for both Logback and Log4J2.
The details are published under the <code>log4j2.events.</code> or <code>logback.events.</code> meter names.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.tasks"><a class="anchor" href="#actuator.metrics.supported.tasks"></a>Task Execution and Scheduling Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables the instrumentation of all available <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/scheduling/concurrent/ThreadPoolTaskExecutor.html"><code>ThreadPoolTaskExecutor</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/scheduling/concurrent/ThreadPoolTaskScheduler.html"><code>ThreadPoolTaskScheduler</code></a> beans, as long as the underling <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/ThreadPoolExecutor.html" target="_blank"><code>ThreadPoolExecutor</code></a> is available.
Metrics are tagged by the name of the executor, which is derived from the bean name.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.jms"><a class="anchor" href="#actuator.metrics.supported.jms"></a>JMS Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables the instrumentation of all available <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jms/core/JmsTemplate.html"><code>JmsTemplate</code></a> beans and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/jms/annotation/JmsListener.html"><code>@JmsListener</code></a> annotated methods.
This will produce <code>"jms.message.publish"</code> and <code>"jms.message.process"</code> metrics respectively.
See the <a href="https://docs.spring.io/spring-framework/reference/6.2/integration/observability.html#observability.jms">Spring Framework reference documentation for more information on produced observations</a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.spring-mvc"><a class="anchor" href="#actuator.metrics.supported.spring-mvc"></a>Spring MVC Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables the instrumentation of all requests handled by Spring MVC controllers and functional handlers.
By default, metrics are generated with the name, <code>http.server.requests</code>.
You can customize the name by setting the <code>management.observations.http.server.requests.name</code> property.</p>
</div>
<div class="paragraph">
<p>See the <a href="https://docs.spring.io/spring-framework/reference/6.2/integration/observability.html#observability.http-server.servlet">Spring Framework reference documentation for more information on produced observations</a>.</p>
</div>
<div class="paragraph">
<p>To add to the default tags, provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> that extends <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/server/observation/DefaultServerRequestObservationConvention.html"><code>DefaultServerRequestObservationConvention</code></a> from the <code>org.springframework.http.server.observation</code> package.
To replace the default tags, provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> that implements <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/server/observation/ServerRequestObservationConvention.html"><code>ServerRequestObservationConvention</code></a>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
In some cases, exceptions handled in web controllers are not recorded as request metrics tags.
Applications can opt in and record exceptions by <a class="xref page" href="../web/servlet.html#web.servlet.spring-mvc.error-handling">setting handled exceptions as request attributes</a>.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>By default, all requests are handled.
To customize the filter, provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> that implements <code>FilterRegistrationBean&lt;ServerHttpObservationFilter&gt;</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.spring-webflux"><a class="anchor" href="#actuator.metrics.supported.spring-webflux"></a>Spring WebFlux Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables the instrumentation of all requests handled by Spring WebFlux controllers and functional handlers.
By default, metrics are generated with the name, <code>http.server.requests</code>.
You can customize the name by setting the <code>management.observations.http.server.requests.name</code> property.</p>
</div>
<div class="paragraph">
<p>See the <a href="https://docs.spring.io/spring-framework/reference/6.2/integration/observability.html#observability.http-server.reactive">Spring Framework reference documentation for more information on produced observations</a>.</p>
</div>
<div class="paragraph">
<p>To add to the default tags, provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> that extends <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/server/reactive/observation/DefaultServerRequestObservationConvention.html"><code>DefaultServerRequestObservationConvention</code></a> from the <code>org.springframework.http.server.reactive.observation</code> package.
To replace the default tags, provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> that implements <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/server/reactive/observation/ServerRequestObservationConvention.html"><code>ServerRequestObservationConvention</code></a>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
In some cases, exceptions handled in controllers and handler functions are not recorded as request metrics tags.
Applications can opt in and record exceptions by <a class="xref page" href="../web/reactive.html#web.reactive.webflux.error-handling">setting handled exceptions as request attributes</a>.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.jersey"><a class="anchor" href="#actuator.metrics.supported.jersey"></a>Jersey Server Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables the instrumentation of all requests handled by the Jersey JAX-RS implementation.
By default, metrics are generated with the name, <code>http.server.requests</code>.
You can customize the name by setting the <code>management.observations.http.server.requests.name</code> property.</p>
</div>
<div class="paragraph">
<p>By default, Jersey server metrics are tagged with the following information:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;"/>
<col style="width: 50%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Tag</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>exception</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The simple class name of any exception that was thrown while handling the request.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>method</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The request’s method (for example, <code>GET</code> or <code>POST</code>)</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>outcome</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The request’s outcome, based on the status code of the response.
  1xx is <code>INFORMATIONAL</code>, 2xx is <code>SUCCESS</code>, 3xx is <code>REDIRECTION</code>, 4xx is <code>CLIENT_ERROR</code>, and 5xx is <code>SERVER_ERROR</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>status</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The response’s HTTP status code (for example, <code>200</code> or <code>500</code>)</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>uri</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The request’s URI template prior to variable substitution, if possible (for example, <code>/api/person/{id}</code>)</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>To customize the tags, provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> that implements <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/binder/jersey/server/JerseyObservationConvention.html" target="_blank"><code>JerseyObservationConvention</code></a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.http-clients"><a class="anchor" href="#actuator.metrics.supported.http-clients"></a>HTTP Client Metrics</h3>
<div class="paragraph">
<p>Spring Boot Actuator manages the instrumentation of <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a>, <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a> and <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a>.
For that, you have to inject the auto-configured builder and use it to create instances:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/client/RestTemplateBuilder.html"><code>RestTemplateBuilder</code></a> for <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a></p>
</li>
<li>
<p><a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.Builder.html"><code>WebClient.Builder</code></a> for <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a></p>
</li>
<li>
<p><a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.Builder.html"><code>RestClient.Builder</code></a> for <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a></p>
</li>
</ul>
</div>
<div class="paragraph">
<p>You can also manually apply the customizers responsible for this instrumentation, namely <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/metrics/web/client/ObservationRestTemplateCustomizer.html"><code>ObservationRestTemplateCustomizer</code></a>, <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/metrics/web/reactive/client/ObservationWebClientCustomizer.html"><code>ObservationWebClientCustomizer</code></a> and <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/metrics/web/client/ObservationRestClientCustomizer.html"><code>ObservationRestClientCustomizer</code></a>.</p>
</div>
<div class="paragraph">
<p>By default, metrics are generated with the name, <code>http.client.requests</code>.
You can customize the name by setting the <code>management.observations.http.client.requests.name</code> property.</p>
</div>
<div class="paragraph">
<p>See the <a href="https://docs.spring.io/spring-framework/reference/6.2/integration/observability.html#observability.http-client">Spring Framework reference documentation for more information on produced observations</a>.</p>
</div>
<div class="paragraph">
<p>To customize the tags when using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html"><code>RestTemplate</code></a> or <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html"><code>RestClient</code></a>, provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> that implements <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/client/observation/ClientRequestObservationConvention.html"><code>ClientRequestObservationConvention</code></a> from the <code>org.springframework.http.client.observation</code> package.
To customize the tags when using <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html"><code>WebClient</code></a>, provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> that implements <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/ClientRequestObservationConvention.html"><code>ClientRequestObservationConvention</code></a> from the <code>org.springframework.web.reactive.function.client</code> package.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.tomcat"><a class="anchor" href="#actuator.metrics.supported.tomcat"></a>Tomcat Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables the instrumentation of Tomcat only when an MBean <a class="apiref external" href="https://tomcat.apache.org/tomcat-10.1-doc/api/org/apache/tomcat/util/modeler/Registry.html" target="_blank"><code>Registry</code></a> is enabled.
By default, the MBean registry is disabled, but you can enable it by setting <code>server.tomcat.mbeanregistry.enabled</code> to <code>true</code>.</p>
</div>
<div class="paragraph">
<p>Tomcat metrics are published under the <code>tomcat.</code> meter name.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.cache"><a class="anchor" href="#actuator.metrics.supported.cache"></a>Cache Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables the instrumentation of all available <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/Cache.html"><code>Cache</code></a> instances on startup, with metrics prefixed with <code>cache</code>.
Cache instrumentation is standardized for a basic set of metrics.
Additional, cache-specific metrics are also available.</p>
</div>
<div class="paragraph">
<p>The following cache libraries are supported:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Cache2k</p>
</li>
<li>
<p>Caffeine</p>
</li>
<li>
<p>Hazelcast</p>
</li>
<li>
<p>Any compliant JCache (JSR-107) implementation</p>
</li>
<li>
<p>Redis</p>
</li>
</ul>
</div>
<div class="admonitionblock warning">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-warning" title="Warning"></i>
</td>
<td class="content">
Metrics should be enabled for the auto-configuration to pick them up.
Refer to the documentation of the cache library you are using for more details.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Metrics are tagged by the name of the cache and by the name of the <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/cache/CacheManager.html"><code>CacheManager</code></a>, which is derived from the bean name.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Only caches that are configured on startup are bound to the registry.
For caches not defined in the cache’s configuration, such as caches created on the fly or programmatically after the startup phase, an explicit registration is required.
A <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/metrics/cache/CacheMetricsRegistrar.html"><code>CacheMetricsRegistrar</code></a> bean is made available to make that process easier.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.spring-batch"><a class="anchor" href="#actuator.metrics.supported.spring-batch"></a>Spring Batch Metrics</h3>
<div class="paragraph">
<p>See the <a href="https://docs.spring.io/spring-batch/reference/5.2/monitoring-and-metrics.html">Spring Batch reference documentation</a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.spring-graphql"><a class="anchor" href="#actuator.metrics.supported.spring-graphql"></a>Spring GraphQL Metrics</h3>
<div class="paragraph">
<p>See the <a href="https://docs.spring.io/spring-graphql/reference/1.3/observability.html">Spring GraphQL reference documentation</a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.jdbc"><a class="anchor" href="#actuator.metrics.supported.jdbc"></a>DataSource Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables the instrumentation of all available <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> objects with metrics prefixed with <code>jdbc.connections</code>.
Data source instrumentation results in gauges that represent the currently active, idle, maximum allowed, and minimum allowed connections in the pool.</p>
</div>
<div class="paragraph">
<p>Metrics are also tagged by the name of the <a class="apiref external" href="https://docs.oracle.com/en/java/javase/17/docs/api/java.sql/javax/sql/DataSource.html" target="_blank"><code>DataSource</code></a> computed based on the bean name.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
By default, Spring Boot provides metadata for all supported data sources.
You can add additional <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/jdbc/metadata/DataSourcePoolMetadataProvider.html"><code>DataSourcePoolMetadataProvider</code></a> beans if your favorite data source is not supported.
See <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jdbc/metadata/DataSourcePoolMetadataProvidersConfiguration.html"><code>DataSourcePoolMetadataProvidersConfiguration</code></a> for examples.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>Also, Hikari-specific metrics are exposed with a <code>hikaricp</code> prefix.
Each metric is tagged by the name of the pool (you can control it with <code>spring.datasource.name</code>).</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.hibernate"><a class="anchor" href="#actuator.metrics.supported.hibernate"></a>Hibernate Metrics</h3>
<div class="paragraph">
<p>If <code>org.hibernate.orm:hibernate-micrometer</code> is on the classpath, all available Hibernate <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a> instances that have statistics enabled are instrumented with a metric named <code>hibernate</code>.</p>
</div>
<div class="paragraph">
<p>Metrics are also tagged by the name of the <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a>, which is derived from the bean name.</p>
</div>
<div class="paragraph">
<p>To enable statistics, the standard JPA property <code>hibernate.generate_statistics</code> must be set to <code>true</code>.
You can enable that on the auto-configured <a class="apiref external" href="https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/EntityManagerFactory.html" target="_blank"><code>EntityManagerFactory</code></a>:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_40">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_40_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_40_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_40_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_40_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_40_properties" class="tabpanel" id="_tabs_40_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">spring.jpa.properties[hibernate.generate_statistics]</span>=<span class="hljs-string">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_40_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_40_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">spring:</span>
  <span class="hljs-attr">jpa:</span>
    <span class="hljs-attr">properties:</span>
      <span class="hljs-string">"[hibernate.generate_statistics]"</span><span class="hljs-string">:</span> <span class="hljs-literal">true</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.spring-data-repository"><a class="anchor" href="#actuator.metrics.supported.spring-data-repository"></a>Spring Data Repository Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables the instrumentation of all Spring Data <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> method invocations.
By default, metrics are generated with the name, <code>spring.data.repository.invocations</code>.
You can customize the name by setting the <code>management.metrics.data.repository.metric-name</code> property.</p>
</div>
<div class="paragraph">
<p>The <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/annotation/Timed.html" target="_blank"><code>@Timed</code></a> annotation from the <code>io.micrometer.core.annotation</code> package is supported on <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> interfaces and methods.
If you do not want to record metrics for all <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> invocations, you can set <code>management.metrics.data.repository.autotime.enabled</code> to <code>false</code> and exclusively use <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/annotation/Timed.html" target="_blank"><code>@Timed</code></a> annotations instead.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
A <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/annotation/Timed.html" target="_blank"><code>@Timed</code></a> annotation with <code>longTask = true</code> enables a long task timer for the method.
Long task timers require a separate metric name and can be stacked with a short task timer.
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>By default, repository invocation related metrics are tagged with the following information:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;"/>
<col style="width: 50%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Tag</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>repository</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The simple class name of the source <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a>.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>method</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The name of the <a class="apiref" href="https://docs.spring.io/spring-data/commons/docs/3.4.x/api/org/springframework/data/repository/Repository.html"><code>Repository</code></a> method that was invoked.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>state</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The result state (<code>SUCCESS</code>, <code>ERROR</code>, <code>CANCELED</code>, or <code>RUNNING</code>).</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>exception</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The simple class name of any exception that was thrown from the invocation.</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>To replace the default tags, provide a <a class="apiref" href="https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Bean.html"><code>@Bean</code></a> that implements <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/metrics/data/RepositoryTagsProvider.html"><code>RepositoryTagsProvider</code></a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.rabbitmq"><a class="anchor" href="#actuator.metrics.supported.rabbitmq"></a>RabbitMQ Metrics</h3>
<div class="paragraph">
<p>Auto-configuration enables the instrumentation of all available RabbitMQ connection factories with a metric named <code>rabbitmq</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.spring-integration"><a class="anchor" href="#actuator.metrics.supported.spring-integration"></a>Spring Integration Metrics</h3>
<div class="paragraph">
<p>Spring Integration automatically provides <a href="https://docs.spring.io/spring-integration/reference/6.4/metrics.html#micrometer-integration">Micrometer support</a> whenever a <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/MeterRegistry.html" target="_blank"><code>MeterRegistry</code></a> bean is available.
Metrics are published under the <code>spring.integration.</code> meter name.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.kafka"><a class="anchor" href="#actuator.metrics.supported.kafka"></a>Kafka Metrics</h3>
<div class="paragraph">
<p>Auto-configuration registers a <a class="apiref" href="https://docs.spring.io/spring-kafka/docs/3.3.x/api/org/springframework/kafka/core/MicrometerConsumerListener.html"><code>MicrometerConsumerListener</code></a> and <a class="apiref" href="https://docs.spring.io/spring-kafka/docs/3.3.x/api/org/springframework/kafka/core/MicrometerProducerListener.html"><code>MicrometerProducerListener</code></a> for the auto-configured consumer factory and producer factory, respectively.
It also registers a <a class="apiref" href="https://docs.spring.io/spring-kafka/docs/3.3.x/api/org/springframework/kafka/streams/KafkaStreamsMicrometerListener.html"><code>KafkaStreamsMicrometerListener</code></a> for <a class="apiref" href="https://docs.spring.io/spring-kafka/docs/3.3.x/api/org/springframework/kafka/config/StreamsBuilderFactoryBean.html"><code>StreamsBuilderFactoryBean</code></a>.
For more detail, see the <a href="https://docs.spring.io/spring-kafka/reference/3.3/kafka/micrometer.html#micrometer-native">Micrometer Native Metrics</a> section of the Spring Kafka documentation.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.mongodb"><a class="anchor" href="#actuator.metrics.supported.mongodb"></a>MongoDB Metrics</h3>
<div class="paragraph">
<p>This section briefly describes the available metrics for MongoDB.</p>
</div>
<div class="sect3">
<h4 id="actuator.metrics.supported.mongodb.command"><a class="anchor" href="#actuator.metrics.supported.mongodb.command"></a>MongoDB Command Metrics</h4>
<div class="paragraph">
<p>Auto-configuration registers a <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/binder/mongodb/MongoMetricsCommandListener.html" target="_blank"><code>MongoMetricsCommandListener</code></a> with the auto-configured <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-sync/com/mongodb/client/MongoClient.html" target="_blank"><code>MongoClient</code></a>.</p>
</div>
<div class="paragraph">
<p>A timer metric named <code>mongodb.driver.commands</code> is created for each command issued to the underlying MongoDB driver.
Each metric is tagged with the following information by default:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;"/>
<col style="width: 50%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Tag</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>command</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The name of the command issued.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>cluster.id</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The identifier of the cluster to which the command was sent.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>server.address</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The address of the server to which the command was sent.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>status</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The outcome of the command (<code>SUCCESS</code> or <code>FAILED</code>).</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>To replace the default metric tags, define a <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/binder/mongodb/MongoCommandTagsProvider.html" target="_blank"><code>MongoCommandTagsProvider</code></a> bean, as the following example shows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_41">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_41_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_41_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_41_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_41_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_41_java" class="tabpanel" id="_tabs_41_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.binder.mongodb.MongoCommandTagsProvider;

<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCommandTagsProviderConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> MongoCommandTagsProvider <span class="hljs-title">customCommandTagsProvider</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> CustomCommandTagsProvider();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_41_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_41_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.binder.mongodb.MongoCommandTagsProvider
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyCommandTagsProviderConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">customCommandTagsProvider</span><span class="hljs-params">()</span></span>: MongoCommandTagsProvider? {
		<span class="hljs-keyword">return</span> CustomCommandTagsProvider()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>To disable the auto-configured command metrics, set the following property:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_42">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_42_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_42_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_42_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_42_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_42_properties" class="tabpanel" id="_tabs_42_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.metrics.mongo.command.enabled</span>=<span class="hljs-string">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_42_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_42_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">metrics:</span>
    <span class="hljs-attr">mongo:</span>
      <span class="hljs-attr">command:</span>
        <span class="hljs-attr">enabled:</span> <span class="hljs-literal">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect3">
<h4 id="actuator.metrics.supported.mongodb.connection-pool"><a class="anchor" href="#actuator.metrics.supported.mongodb.connection-pool"></a>MongoDB Connection Pool Metrics</h4>
<div class="paragraph">
<p>Auto-configuration registers a <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/binder/mongodb/MongoMetricsConnectionPoolListener.html" target="_blank"><code>MongoMetricsConnectionPoolListener</code></a> with the auto-configured <a class="apiref external" href="https://mongodb.github.io/mongo-java-driver/5.2/apidocs/mongodb-driver-sync/com/mongodb/client/MongoClient.html" target="_blank"><code>MongoClient</code></a>.</p>
</div>
<div class="paragraph">
<p>The following gauge metrics are created for the connection pool:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>mongodb.driver.pool.size</code> reports the current size of the connection pool, including idle and in-use members.</p>
</li>
<li>
<p><code>mongodb.driver.pool.checkedout</code> reports the count of connections that are currently in use.</p>
</li>
<li>
<p><code>mongodb.driver.pool.waitqueuesize</code> reports the current size of the wait queue for a connection from the pool.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Each metric is tagged with the following information by default:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;"/>
<col style="width: 50%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Tag</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>cluster.id</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The identifier of the cluster to which the connection pool corresponds.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>server.address</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">The address of the server to which the connection pool corresponds.</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>To replace the default metric tags, define a <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/binder/mongodb/MongoConnectionPoolTagsProvider.html" target="_blank"><code>MongoConnectionPoolTagsProvider</code></a> bean:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_43">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_43_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_43_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_43_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_43_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_43_java" class="tabpanel" id="_tabs_43_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.binder.mongodb.MongoConnectionPoolTagsProvider;

<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyConnectionPoolTagsProviderConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> MongoConnectionPoolTagsProvider <span class="hljs-title">customConnectionPoolTagsProvider</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> CustomConnectionPoolTagsProvider();
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_43_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_43_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.binder.mongodb.MongoConnectionPoolTagsProvider
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyConnectionPoolTagsProviderConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">customConnectionPoolTagsProvider</span><span class="hljs-params">()</span></span>: MongoConnectionPoolTagsProvider {
		<span class="hljs-keyword">return</span> CustomConnectionPoolTagsProvider()
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>To disable the auto-configured connection pool metrics, set the following property:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_44">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_44_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_44_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_44_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_44_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_44_properties" class="tabpanel" id="_tabs_44_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.metrics.mongo.connectionpool.enabled</span>=<span class="hljs-string">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_44_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_44_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">metrics:</span>
    <span class="hljs-attr">mongo:</span>
      <span class="hljs-attr">connectionpool:</span>
        <span class="hljs-attr">enabled:</span> <span class="hljs-literal">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.jetty"><a class="anchor" href="#actuator.metrics.supported.jetty"></a>Jetty Metrics</h3>
<div class="paragraph">
<p>Auto-configuration binds metrics for Jetty’s <a class="apiref external" href="https://javadoc.jetty.org/jetty-12/org/eclipse/jetty/util/thread/ThreadPool.html" target="_blank"><code>ThreadPool</code></a> by using Micrometer’s <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/binder/jetty/JettyServerThreadPoolMetrics.html" target="_blank"><code>JettyServerThreadPoolMetrics</code></a>.
Metrics for Jetty’s <a class="apiref external" href="https://javadoc.jetty.org/jetty-12/org/eclipse/jetty/server/Connector.html" target="_blank"><code>Connector</code></a> instances are bound by using Micrometer’s <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/binder/jetty/JettyConnectionMetrics.html" target="_blank"><code>JettyConnectionMetrics</code></a> and, when <code>server.ssl.enabled</code> is set to <code>true</code>, Micrometer’s <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/binder/jetty/JettySslHandshakeMetrics.html" target="_blank"><code>JettySslHandshakeMetrics</code></a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.supported.redis"><a class="anchor" href="#actuator.metrics.supported.redis"></a>Redis Metrics</h3>
<div class="paragraph">
<p>Auto-configuration registers a <a class="apiref external" href="https://javadoc.io/doc/io.lettuce/lettuce-core/6.4.2.RELEASE/io/lettuce/core/metrics/MicrometerCommandLatencyRecorder.html" target="_blank"><code>MicrometerCommandLatencyRecorder</code></a> for the auto-configured <a class="apiref" href="https://docs.spring.io/spring-data/redis/docs/3.4.x/api/org/springframework/data/redis/connection/lettuce/LettuceConnectionFactory.html"><code>LettuceConnectionFactory</code></a>.
For more detail, see the <a class="external" href="https://lettuce.io/core/6.4.2.RELEASE/reference/index.html#command.latency.metrics.micrometer" target="_blank">Micrometer Metrics section</a> of the Lettuce documentation.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.metrics.registering-custom"><a class="anchor" href="#actuator.metrics.registering-custom"></a>Registering Custom Metrics</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To register custom metrics, inject <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/MeterRegistry.html" target="_blank"><code>MeterRegistry</code></a> into your component:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_45">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_45_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_45_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_45_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_45_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_45_java" class="tabpanel" id="_tabs_45_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.MeterRegistry;
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.Tags;

<span class="hljs-keyword">import</span> org.springframework.stereotype.Component;

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span> </span>{

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">final</span> Dictionary dictionary;

	<span class="hljs-function"><span class="hljs-keyword">public</span> <span class="hljs-title">MyBean</span><span class="hljs-params">(MeterRegistry registry)</span> </span>{
		<span class="hljs-keyword">this</span>.dictionary = Dictionary.load();
		registry.gauge(<span class="hljs-string">"dictionary.size"</span>, Tags.empty(), <span class="hljs-keyword">this</span>.dictionary.getWords().size());
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_45_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_45_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.MeterRegistry
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.Tags
<span class="hljs-keyword">import</span> org.springframework.stereotype.Component

</span><span class="fold-block"><span class="hljs-meta">@Component</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyBean</span></span>(registry: MeterRegistry) {

	<span class="hljs-keyword">private</span> <span class="hljs-keyword">val</span> dictionary: Dictionary

	<span class="hljs-keyword">init</span> {
		dictionary = Dictionary.load()
		registry.gauge(<span class="hljs-string">"dictionary.size"</span>, Tags.empty(), dictionary.words.size)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If your metrics depend on other beans, we recommend that you use a <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/binder/MeterBinder.html" target="_blank"><code>MeterBinder</code></a> to register them:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_46">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_46_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_46_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_46_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_46_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_46_java" class="tabpanel" id="_tabs_46_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.Gauge;
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.binder.MeterBinder;

<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;

</span><span class="fold-block"><span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMeterBinderConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> MeterBinder <span class="hljs-title">queueSize</span><span class="hljs-params">(Queue queue)</span> </span>{
		<span class="hljs-keyword">return</span> (registry) -&gt; Gauge.builder(<span class="hljs-string">"queueSize"</span>, queue::size).register(registry);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_46_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_46_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.Gauge
<span class="hljs-keyword">import</span> io.micrometer.core.instrument.binder.MeterBinder
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean

</span><span class="fold-block"><span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMeterBinderConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">queueSize</span><span class="hljs-params">(queue: <span class="hljs-type">Queue</span>)</span></span>: MeterBinder {
		<span class="hljs-keyword">return</span> MeterBinder { registry -&gt;
			Gauge.builder(<span class="hljs-string">"queueSize"</span>, queue::size).register(registry)
		}
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Using a <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/binder/MeterBinder.html" target="_blank"><code>MeterBinder</code></a> ensures that the correct dependency relationships are set up and that the bean is available when the metric’s value is retrieved.
A <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/binder/MeterBinder.html" target="_blank"><code>MeterBinder</code></a> implementation can also be useful if you find that you repeatedly instrument a suite of metrics across components or applications.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
By default, metrics from all <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/binder/MeterBinder.html" target="_blank"><code>MeterBinder</code></a> beans are automatically bound to the Spring-managed <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/MeterRegistry.html" target="_blank"><code>MeterRegistry</code></a>.
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.metrics.customizing"><a class="anchor" href="#actuator.metrics.customizing"></a>Customizing Individual Metrics</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you need to apply customizations to specific <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/Meter.html" target="_blank"><code>Meter</code></a> instances, you can use the <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/config/MeterFilter.html" target="_blank"><code>MeterFilter</code></a> interface.</p>
</div>
<div class="paragraph">
<p>For example, if you want to rename the <code>mytag.region</code> tag to <code>mytag.area</code> for all meter IDs beginning with <code>com.example</code>, you can do the following:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Java|Kotlin" id="_tabs_47">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_47_java--panel" aria-selected="true" class="tab is-selected" data-sync-id="Java" id="_tabs_47_java" role="tab" tabindex="0">
<p>Java</p>
</li>
<li aria-controls="_tabs_47_kotlin--panel" class="tab" data-sync-id="Kotlin" id="_tabs_47_kotlin" role="tab" tabindex="-1">
<p>Kotlin</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_47_java" class="tabpanel" id="_tabs_47_java--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-java hljs" data-lang="java"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.config.MeterFilter;

<span class="hljs-keyword">import</span> org.springframework.context.annotation.Bean;
<span class="hljs-keyword">import</span> org.springframework.context.annotation.Configuration;

</span><span class="fold-block"><span class="hljs-meta">@Configuration</span>(proxyBeanMethods = <span class="hljs-keyword">false</span>)
<span class="hljs-keyword">public</span> <span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMetricsFilterConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">public</span> MeterFilter <span class="hljs-title">renameRegionTagMeterFilter</span><span class="hljs-params">()</span> </span>{
		<span class="hljs-keyword">return</span> MeterFilter.renameTag(<span class="hljs-string">"com.example"</span>, <span class="hljs-string">"mytag.region"</span>, <span class="hljs-string">"mytag.area"</span>);
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_47_kotlin" class="tabpanel is-hidden" hidden="" id="_tabs_47_kotlin--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-kotlin hljs" data-lang="kotlin"><span class="fold-block is-hidden-folded"><span class="hljs-keyword">import</span> io.micrometer.core.instrument.config.MeterFilter
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Bean
<span class="hljs-keyword">import</span> org.springframework.context.<span class="hljs-keyword">annotation</span>.Configuration

</span><span class="fold-block"><span class="hljs-meta">@Configuration(proxyBeanMethods = false)</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">MyMetricsFilterConfiguration</span> </span>{

	<span class="hljs-meta">@Bean</span>
	<span class="hljs-function"><span class="hljs-keyword">fun</span> <span class="hljs-title">renameRegionTagMeterFilter</span><span class="hljs-params">()</span></span>: MeterFilter {
		<span class="hljs-keyword">return</span> MeterFilter.renameTag(<span class="hljs-string">"com.example"</span>, <span class="hljs-string">"mytag.region"</span>, <span class="hljs-string">"mytag.area"</span>)
	}

}</span></code><div class="source-toolbox"><button class="fold-button" data-folded-title="Expand foldable text" data-unfolded-title="Collapse foldable text" title="Expand foldable text"></button><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
By default, all <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/config/MeterFilter.html" target="_blank"><code>MeterFilter</code></a> beans are automatically bound to the Spring-managed <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/MeterRegistry.html" target="_blank"><code>MeterRegistry</code></a>.
Make sure to register your metrics by using the Spring-managed <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/MeterRegistry.html" target="_blank"><code>MeterRegistry</code></a> and not any of the static methods on <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/Metrics.html" target="_blank"><code>Metrics</code></a>.
These use the global registry that is not Spring-managed.
</td>
</tr>
</tbody></table>
</div>
<div class="sect2">
<h3 id="actuator.metrics.customizing.common-tags"><a class="anchor" href="#actuator.metrics.customizing.common-tags"></a>Common Tags</h3>
<div class="paragraph">
<p>Common tags are generally used for dimensional drill-down on the operating environment, such as host, instance, region, stack, and others.
Commons tags are applied to all meters and can be configured, as the following example shows:</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_48">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_48_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_48_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_48_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_48_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_48_properties" class="tabpanel" id="_tabs_48_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.metrics.tags.region</span>=<span class="hljs-string">us-east-1</span>
<span class="hljs-meta">management.metrics.tags.stack</span>=<span class="hljs-string">prod</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_48_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_48_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">metrics:</span>
    <span class="hljs-attr">tags:</span>
      <span class="hljs-attr">region:</span> <span class="hljs-string">"us-east-1"</span>
      <span class="hljs-attr">stack:</span> <span class="hljs-string">"prod"</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The preceding example adds <code>region</code> and <code>stack</code> tags to all meters with a value of <code>us-east-1</code> and <code>prod</code>, respectively.</p>
</div>
<div class="admonitionblock note">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The order of common tags is important if you use Graphite.
As the order of common tags cannot be guaranteed by using this approach, Graphite users are advised to define a custom <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/config/MeterFilter.html" target="_blank"><code>MeterFilter</code></a> instead.
</td>
</tr>
</tbody></table>
</div>
</div>
<div class="sect2">
<h3 id="actuator.metrics.customizing.per-meter-properties"><a class="anchor" href="#actuator.metrics.customizing.per-meter-properties"></a>Per-meter Properties</h3>
<div class="paragraph">
<p>In addition to <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/config/MeterFilter.html" target="_blank"><code>MeterFilter</code></a> beans, you can apply a limited set of customization on a per-meter basis using properties.
Per-meter customizations are applied, using Spring Boot’s <a class="apiref" href="https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/autoconfigure/metrics/PropertiesMeterFilter.html"><code>PropertiesMeterFilter</code></a>, to any meter IDs that start with the given name.
The following example filters out any meters that have an ID starting with <code>example.remote</code>.</p>
</div>
<div class="openblock tabs is-sync is-loaded" data-sync-group-id="Properties|YAML" id="_tabs_49">
<div class="content">
<div class="ulist tablist">
<ul role="tablist">
<li aria-controls="_tabs_49_properties--panel" aria-selected="true" class="tab is-selected" data-sync-id="Properties" id="_tabs_49_properties" role="tab" tabindex="0">
<p>Properties</p>
</li>
<li aria-controls="_tabs_49_yaml--panel" class="tab" data-sync-id="YAML" id="_tabs_49_yaml" role="tab" tabindex="-1">
<p>YAML</p>
</li>
</ul>
</div>
<div aria-labelledby="_tabs_49_properties" class="tabpanel" id="_tabs_49_properties--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-properties hljs" data-lang="properties"><span class="hljs-meta">management.metrics.enable.example.remote</span>=<span class="hljs-string">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
<div aria-labelledby="_tabs_49_yaml" class="tabpanel is-hidden" hidden="" id="_tabs_49_yaml--panel" role="tabpanel">
<div class="listingblock">
<div class="content">
<pre class="highlightjs highlight"><code class="language-yaml hljs" data-lang="yaml"><span class="hljs-attr">management:</span>
  <span class="hljs-attr">metrics:</span>
    <span class="hljs-attr">enable:</span>
      <span class="hljs-attr">example:</span>
        <span class="hljs-attr">remote:</span> <span class="hljs-literal">false</span></code><div class="source-toolbox"><button class="copy-button" title="Copy to clipboard"><span class="copy-toast">Copied!</span></button></div></pre>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>The following properties allow per-meter customization:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<caption class="title">Table 1. Per-meter customizations</caption>
<colgroup>
<col style="width: 50%;"/>
<col style="width: 50%;"/>
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Property</th>
<th class="tableblock halign-left valign-top">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>management.metrics.enable</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Whether to accept meters with certain IDs.
  Meters that are not accepted are filtered from the <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/MeterRegistry.html" target="_blank"><code>MeterRegistry</code></a>.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>management.metrics.distribution.percentiles-histogram</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Whether to publish a histogram suitable for computing aggregable (across dimension) percentile approximations.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>management.metrics.distribution.minimum-expected-value</code>, <code>management.metrics.distribution.maximum-expected-value</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Publish fewer histogram buckets by clamping the range of expected values.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>management.metrics.distribution.percentiles</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Publish percentile values computed in your application</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>management.metrics.distribution.expiry</code>, <code>management.metrics.distribution.buffer-length</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Give greater weight to recent samples by accumulating them in ring buffers which rotate after a configurable expiry, with a
configurable buffer length.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>management.metrics.distribution.slo</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Publish a cumulative histogram with buckets defined by your service-level objectives.</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>For more details on the concepts behind <code>percentiles-histogram</code>, <code>percentiles</code>, and <code>slo</code>, see the <a class="external" href="https://docs.micrometer.io/micrometer/reference/1.14/concepts/histogram-quantiles.html" target="_blank">Histograms and percentiles</a> section of the Micrometer documentation.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.metrics.endpoint"><a class="anchor" href="#actuator.metrics.endpoint"></a>Metrics Endpoint</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Spring Boot provides a <code>metrics</code> endpoint that you can use diagnostically to examine the metrics collected by an application.
The endpoint is not available by default and must be exposed.
See <a class="xref page" href="endpoints.html#actuator.endpoints.exposing">exposing endpoints</a> for more details.</p>
</div>
<div class="paragraph">
<p>Navigating to <code>/actuator/metrics</code> displays a list of available meter names.
You can drill down to view information about a particular meter by providing its name as a selector — for example, <code>/actuator/metrics/jvm.memory.max</code>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<div class="paragraph">
<p>The name you use here should match the name used in the code, not the name after it has been naming-convention normalized for a monitoring system to which it is shipped.
In other words, if <code>jvm.memory.max</code> appears as <code>jvm_memory_max</code> in Prometheus because of its snake case naming convention, you should still use <code>jvm.memory.max</code> as the selector when inspecting the meter in the <code>metrics</code> endpoint.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
<div class="paragraph">
<p>You can also add any number of <code>tag=KEY:VALUE</code> query parameters to the end of the URL to dimensionally drill down on a meter — for example, <code>/actuator/metrics/jvm.memory.max?tag=area:nonheap</code>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tbody><tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<div class="paragraph">
<p>The reported measurements are the <em>sum</em> of the statistics of all meters that match the meter name and any tags that have been applied.
In the preceding example, the returned <code>Value</code> statistic is the sum of the maximum memory footprints of the “Code Cache”, “Compressed Class Space”, and “Metaspace” areas of the heap.
If you wanted to see only the maximum size for the “Metaspace”, you could add an additional <code>tag=id:Metaspace</code> — that is, <code>/actuator/metrics/jvm.memory.max?tag=area:nonheap&amp;tag=id:Metaspace</code>.</p>
</div>
</td>
</tr>
</tbody></table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="actuator.metrics.micrometer-observation"><a class="anchor" href="#actuator.metrics.micrometer-observation"></a>Integration with Micrometer Observation</h2>
<div class="sectionbody">
<div class="paragraph">
<p>A <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-core/1.14.7/io/micrometer/core/instrument/observation/DefaultMeterObservationHandler.html" target="_blank"><code>DefaultMeterObservationHandler</code></a> is automatically registered on the <a class="apiref external" href="https://javadoc.io/doc/io.micrometer/micrometer-observation/1.14.7/io/micrometer/observation/ObservationRegistry.html" target="_blank"><code>ObservationRegistry</code></a>, which creates metrics for every completed observation.</p>
</div>
</div>
</div>
<nav class="pagination">
<span class="prev"><a href="loggers.html">Loggers</a></span>
<span class="next"><a href="tracing.html">Tracing</a></span>
</nav>
</article> </div>
</main>
<div aria-hidden="true" class="modal micromodal-slide" id="modal-versions">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-versions-content">
<button class="modal-versions-close" data-micromodal-close="">
<svg height="28px" viewbox="0 0 32 32" width="28px" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1h{fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs><title></title><g id="cross"><line class="cls-1h" x1="7" x2="25" y1="7" y2="25"></line><line class="cls-1h" x1="7" x2="25" y1="25" y2="7"></line></g></svg>
</button>
<div class="colset">
<div class="col-left">
<ul class="nav-versions">
<li class="component">
<div>
<a class="title" href="../../../index.html">Spring Boot</a>
</div> <div class="version-item is-active">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Stable
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../reference/actuator/metrics.html">
      3.5.0
    </a>
</li>
<li class="version">
<a href="metrics.html">
      3.4.6
    </a>
</li>
<li class="version">
<a href="../../../3.3/reference/actuator/metrics.html">
      3.3.12
    </a>
</li>
</ul> </div>
<div class="version-item">
<div>
<button class="version-toggle" type="button">
<span></span>
                              Snapshot
                            </button>
</div>
<ul class="versions">
<li class="version">
<a href="../../../4.0-SNAPSHOT/reference/actuator/metrics.html">
      4.0.0-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.5-SNAPSHOT/reference/actuator/metrics.html">
      3.5.1-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.4-SNAPSHOT/reference/actuator/metrics.html">
      3.4.7-SNAPSHOT
    </a>
</li>
<li class="version">
<a href="../../../3.3-SNAPSHOT/reference/actuator/metrics.html">
      3.3.13-SNAPSHOT
    </a>
</li>
</ul> </div>
</li>
</ul>
</div>
<div class="col-right">
<ul class="projects">
<li>
    Related Spring Documentation
    <ul class="projects-list">
<li>
<a href="https://docs.spring.io/spring-boot/">
  Spring Boot
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-framework/reference/">
  Spring Framework
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Cloud
<ul>
<li>
<a href="https://docs.spring.io/spring-cloud-build/reference/">
  Spring Cloud Build
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-bus/reference/">
  Spring Cloud Bus
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-circuitbreaker/reference/">
  Spring Cloud Circuit Breaker
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-commons/reference/">
  Spring Cloud Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-config/reference/">
  Spring Cloud Config
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-consul/reference/">
  Spring Cloud Consul
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-contract/reference/">
  Spring Cloud Contract
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-function/reference/">
  Spring Cloud Function
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-gateway/reference/">
  Spring Cloud Gateway
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-kubernetes/reference/">
  Spring Cloud Kubernetes
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-netflix/reference/">
  Spring Cloud Netflix
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-openfeign/reference/">
  Spring Cloud OpenFeign
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-stream/reference/">
  Spring Cloud Stream
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-task/reference/">
  Spring Cloud Task
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-vault/reference/">
  Spring Cloud Vault
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cloud-zookeeper/reference/">
  Spring Cloud Zookeeper
</a>
</li>
</ul>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
  Spring Data
<ul>
<li>
<a href="https://docs.spring.io/spring-data/cassandra/reference/">
  Spring Data Cassandra
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/commons/reference/">
  Spring Data Commons
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/couchbase/reference/">
  Spring Data Couchbase
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/elasticsearch/reference/">
  Spring Data Elasticsearch
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/jpa/reference/">
  Spring Data JPA
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/keyvalue/reference/">
  Spring Data KeyValue
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/ldap/reference/">
  Spring Data LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/mongodb/reference/">
  Spring Data MongoDB
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/neo4j/reference/">
  Spring Data Neo4j
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/redis/reference/">
  Spring Data Redis
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/relational/reference/">
  Spring Data JDBC &amp; R2DBC
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-data/rest/reference/">
  Spring Data REST
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-integration/reference/">
  Spring Integration
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-batch/reference/">
  Spring Batch
</a>
</li>
<li>
<a class="anchor"><i aria-hidden="true" class="fa fa-angle-right"></i></a>
<a href="https://docs.spring.io/spring-security/reference/">
  Spring Security
</a>
<ul>
<li>
<a href="https://docs.spring.io/spring-authorization-server/reference/">
  Spring Authorization Server
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-ldap/reference/">
  Spring LDAP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-security-kerberos/reference/">
  Spring Security Kerberos
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-session/reference/">
  Spring Session
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-vault/reference/">
  Spring Vault
</a>
</li>
</ul>
</li>
<li>
<a href="https://docs.spring.io/spring-ai/reference/">
  Spring AI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-amqp/reference/">
  Spring AMQP
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-cli/reference/">
  Spring CLI
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-graphql/reference/">
  Spring GraphQL
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-kafka/reference/">
  Spring for Apache Kafka
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-modulith/reference/">
  Spring Modulith
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-pulsar/reference/">
  Spring for Apache Pulsar
</a>
</li>
<li>
<a href="https://docs.spring.io/spring-shell/reference/">
  Spring Shell
</a>
</li>
</ul>
</li><a href="../../../spring-projects.html">All Docs...</a>
</ul>
</div>
</div>
</main>
</div>
</div>
</div>
</div>
<footer class="footer flex">
<div id="spring-links flex">
<img alt="Spring" id="springlogo" src="../../../_/img/spring-logo.svg"/>
<p class="smallest antialiased">Copyright © 2005 - 2025 Broadcom. All Rights Reserved. The term "Broadcom" refers to Broadcom Inc. and/or its subsidiaries.<br/><a href="https://www.vmware.com/help/legal.html">Terms of Use</a> • <a href="https://www.vmware.com/help/privacy.html" rel="noopener noreferrer">Privacy</a> • <a href="https://spring.io/trademarks">Trademark Guidelines</a> <span id="thank-you-mobile">• <a href="https://spring.io/thank-you">Thank you</a></span> • <a href="https://www.vmware.com/help/privacy/california-privacy-rights.html">Your California Privacy Rights</a> • <a class="ot-sdk-show-settings">Cookie Settings</a> <span id="teconsent"></span></p>
<p class="smallest antialiased has-gray-text">Apache®, Apache Tomcat®, Apache Kafka®, Apache Cassandra™, and Apache Geode™ are trademarks or registered trademarks of the Apache Software Foundation in the United States and/or other countries. Java™, Java™ SE, Java™ EE, and OpenJDK™ are trademarks of Oracle and/or its affiliates. Kubernetes® is a registered trademark of the Linux Foundation in the United States and other countries. Linux® is the registered trademark of Linus Torvalds in the United States and other countries. Windows® and Microsoft® Azure are registered trademarks of Microsoft Corporation. “AWS” and “Amazon Web Services” are trademarks or registered trademarks of Amazon.com Inc. or its affiliates. All other trademarks and copyrights are property of their respective owners and are only mentioned for informative purposes. Other names may be trademarks of their respective owners.</p>
</div>
<div class="flex jc-between" id="social-icons">
<a href="https://www.youtube.com/user/SpringSourceDev" title="Youtube"><svg id="youtube-icon" viewbox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="20" cy="20" r="20"></circle><path class="cls-2" d="M30.91,14.53a2.89,2.89,0,0,0-2-2C27.12,12,20,12,20,12s-7.12,0-8.9.47a2.9,2.9,0,0,0-2,2A30.56,30.56,0,0,0,8.63,20a30.44,30.44,0,0,0,.46,5.47,2.89,2.89,0,0,0,2,2C12.9,28,20,28,20,28s7.12,0,8.9-.47a2.87,2.87,0,0,0,2-2A30.56,30.56,0,0,0,31.37,20,28.88,28.88,0,0,0,30.91,14.53ZM17.73,23.41V16.59L23.65,20Z"></path></svg></a>
<a href="https://github.com/spring-projects" title="GitHub"><svg id="github-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><path class="cls-1" d="M38,0a38,38,0,1,0,38,38A38,38,0,0,0,38,0Z"></path><path class="cls-2" d="M38,15.59A22.95,22.95,0,0,0,30.71,60.3c1.15.21,1.57-.5,1.57-1.11s0-2,0-3.9c-6.38,1.39-7.73-3.07-7.73-3.07A6.09,6.09,0,0,0,22,48.86c-2.09-1.42.15-1.39.15-1.39a4.81,4.81,0,0,1,3.52,2.36c2,3.5,5.37,2.49,6.67,1.91a4.87,4.87,0,0,1,1.46-3.07c-5.09-.58-10.45-2.55-10.45-11.34a8.84,8.84,0,0,1,2.36-6.15,8.29,8.29,0,0,1,.23-6.07s1.92-.62,6.3,2.35a21.82,21.82,0,0,1,11.49,0c4.38-3,6.3-2.35,6.3-2.35a8.29,8.29,0,0,1,.23,6.07,8.84,8.84,0,0,1,2.36,6.15c0,8.81-5.37,10.75-10.48,11.32a5.46,5.46,0,0,1,1.56,4.25c0,3.07,0,5.54,0,6.29s.42,1.33,1.58,1.1A22.94,22.94,0,0,0,38,15.59Z"></path></svg></a>
<a href="https://twitter.com/springcentral" title="Twitter"><svg id="twitter-icon" viewbox="0 0 75.93 75.93" xmlns="http://www.w3.org/2000/svg"><circle class="cls-1" cx="37.97" cy="37.97" r="37.97"></circle><path class="cls-2" d="M55.2,22.73a15.43,15.43,0,0,1-4.88,1.91,7.56,7.56,0,0,0-5.61-2.49A7.78,7.78,0,0,0,37,30a7.56,7.56,0,0,0,.2,1.79,21.63,21.63,0,0,1-15.84-8.23,8,8,0,0,0,2.37,10.52,7.66,7.66,0,0,1-3.48-1v.09A7.84,7.84,0,0,0,26.45,41a7.54,7.54,0,0,1-2,.28A7.64,7.64,0,0,1,23,41.09a7.71,7.71,0,0,0,7.18,5.47,15.21,15.21,0,0,1-9.55,3.37,15.78,15.78,0,0,1-1.83-.11,21.41,21.41,0,0,0,11.78,3.54c14.13,0,21.86-12,21.86-22.42,0-.34,0-.68,0-1a15.67,15.67,0,0,0,3.83-4.08,14.9,14.9,0,0,1-4.41,1.24A7.8,7.8,0,0,0,55.2,22.73Z" data-name="Twitter" id="Twitter-2"></path></svg></a>
</div>
</footer>




<div aria-hidden="true" class="modal micromodal-slide" id="modal-1">
<div class="modal__overlay" data-micromodal-close="" tabindex="-1">
<div aria-labelledby="modal-1-title" aria-modal="true" class="modal__container" role="dialog">
<main class="modal__content" id="modal-1-content">
<div id="searchbox"></div>
<div id="counter"></div>
<div class="search-link-box">
<a class="search-link" href="../../../search.html">Search in all Spring Docs</a>
</div>
<div class="search-by">
<a aria-label="Search by Algolia" href="https://www.algolia.com/" rel="noopener noreferrer" target="_blank">
<img class="light" src="../../../_/img/algolia-light.svg" width="140"/>
<img class="dark" src="../../../_/img/algolia-dark.svg" width="140"/>
</a>
</div>
<div id="hits"></div>
</main>
</div>
</div>
</div>





</body></html>