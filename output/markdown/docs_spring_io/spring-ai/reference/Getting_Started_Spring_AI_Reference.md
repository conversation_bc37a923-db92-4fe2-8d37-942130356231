Title: Getting Started :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/getting-started.html
HTML: html/docs_spring_io/spring-ai/reference/Getting_Started_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/Getting_Started_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:14:25.264254
---
Search CTRL + k

### Getting Started

  * Spring Initializr
  * Artifact Repositories
  * Milestones - Use Maven Central
  * Snapshots - Add Snapshot Repositories
  * Dependency Management
  * Add dependencies for specific components
  * Spring AI samples



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/getting-started.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](index.html)
  * [Getting Started](getting-started.html)



# Getting Started

### Getting Started

  * Spring Initializr
  * Artifact Repositories
  * Milestones - Use Maven Central
  * Snapshots - Add Snapshot Repositories
  * Dependency Management
  * Add dependencies for specific components
  * Spring AI samples



This section offers jumping off points for how to get started using Spring AI.

You should follow the steps in each of the following sections according to your needs.

__ |  Spring AI supports Spring Boot 3.4.x. When Spring Boot 3.5.x is released, we will support that as well.   
---|---  
  
## Spring Initializr

Head on over to [start.spring.io](https://start.spring.io/) and select the AI Models and Vector Stores that you want to use in your new applications.

## Artifact Repositories

### Milestones - Use Maven Central

As of 1.0.0-M6, releases are available in Maven Central. No changes to your build file are required.

### Snapshots - Add Snapshot Repositories

To use the Snapshot (and pre 1.0.0-M6 milestone) versions, you need to add the following snapshot repositories in your build file.

Add the following repository definitions to your Maven or Gradle build file:

  * Maven

  * Gradle



    
    
    <repositories>
      <repository>
        <id>spring-snapshots</id>
        <name>Spring Snapshots</name>
        <url>https://repo.spring.io/snapshot</url>
        <releases>
          <enabled>false</enabled>
        </releases>
      </repository>
      <repository>
        <name>Central Portal Snapshots</name>
        <id>central-portal-snapshots</id>
        <url>https://central.sonatype.com/repository/maven-snapshots/</url>
        <releases>
          <enabled>false</enabled>
        </releases>
        <snapshots>
          <enabled>true</enabled>
        </snapshots>
      </repository>
    </repositories>
    
    Copied!
    
    
    repositories {
      mavenCentral()
      maven { url 'https://repo.spring.io/milestone' }
      maven { url 'https://repo.spring.io/snapshot' }
      maven {
        name = 'Central Portal Snapshots'
        url = 'https://central.sonatype.com/repository/maven-snapshots/'
      }
    }
    
    Copied!

**NOTE:** When using Maven with Spring AI snapshots, pay attention to your Maven mirror configuration. If you have configured a mirror in your `settings.xml` like this:
    
    
    <mirror>
        <id>my-mirror</id>
        <mirrorOf>*</mirrorOf>
        <url>https://my-company-repository.com/maven</url>
    </mirror>
    
    Copied!

The wildcard `*` will redirect all repository requests to your mirror, preventing access to Spring snapshot repositories. To fix this, modify the `mirrorOf` configuration to exclude Spring repositories:
    
    
    <mirror>
        <id>my-mirror</id>
        <mirrorOf>*,!spring-snapshots,!central-portal-snapshots</mirrorOf>
        <url>https://my-company-repository.com/maven</url>
    </mirror>
    
    Copied!

This configuration allows Maven to access Spring snapshot repositories directly while still using your mirror for other dependencies.

## Dependency Management

The Spring AI Bill of Materials (BOM) declares the recommended versions of all the dependencies used by a given release of Spring AI. This is a BOM-only version and it just contains dependency management and no plugin declarations or direct references to Spring or Spring Boot. You can use the Spring Boot parent POM, or use the BOM from Spring Boot (`spring-boot-dependencies`) to manage Spring Boot versions.

Add the BOM to your project:

  * Maven

  * Gradle



    
    
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    
    Copied!
    
    
    dependencies {
      implementation platform("org.springframework.ai:spring-ai-bom:1.0.0-SNAPSHOT")
      // Replace the following with the starter dependencies of specific modules you wish to use
      implementation 'org.springframework.ai:spring-ai-openai'
    }
    
    Copied!

Gradle users can also use the Spring AI BOM by leveraging Gradle (5.0+) native support for declaring dependency constraints using a Maven BOM. This is implemented by adding a 'platform' dependency handler method to the dependencies section of your Gradle build script.

## Add dependencies for specific components

Each of the following sections in the documentation shows which dependencies you need to add to your project build system.

  * [Chat Models](api/chatmodel.html)

  * [Embeddings Models](api/embeddings.html)

  * [Image Generation Models](api/imageclient.html)

  * [Transcription Models](api/audio/transcriptions.html)

  * [Text-To-Speech (TTS) Models](api/audio/speech.html)

  * [Vector Databases](api/vectordbs.html)




## Spring AI samples

Please refer to [this page](https://github.com/danvega/awesome-spring-ai) for more resources and samples related to Spring AI.

[AI Concepts](concepts.html) [Chat Client API](api/chatclient.html)
---
