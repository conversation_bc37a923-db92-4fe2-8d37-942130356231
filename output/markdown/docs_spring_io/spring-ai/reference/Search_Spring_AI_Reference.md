Title: Search :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/search.html
HTML: html/docs_spring_io/spring-ai/reference/Search_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/Search_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:15:05.815592
---
Search CTRL + k

# Search

Loading...

**287406** results found

[ ![](https://docs.spring.io/spring-framework/reference/_/img/algolia-light.svg) ![](https://docs.spring.io/spring-framework/reference/_/img/algolia-dark.svg) ](https://www.algolia.com/)

  * [ Vault  Spring Vault > Vault ](https://docs.spring.io/spring-vault/reference/vault/vault.html#page-title)
  * [ Supporting for Vault’s Secret Engines  Spring Vault > Vault > Supporting for Vault’s Secret Engines ](https://docs.spring.io/spring-vault/reference/vault/vault-secret-engines.html#page-title)
  * [ Vault Repositories  Spring Vault > Vault > Vault Repositories ](https://docs.spring.io/spring-vault/reference/vault/vault-repositories.html#page-title)
  * [ Spring Security  Spring Vault > Vault > Spring Security ](https://docs.spring.io/spring-vault/reference/vault/spring-security.html#page-title)
  * [ Reactive Infrastructure  Spring Vault > Vault > Reactive Infrastructure ](https://docs.spring.io/spring-vault/reference/vault/reactive-template.html#page-title)
  * [ Property Sources  Spring Vault > Vault > Property Sources ](https://docs.spring.io/spring-vault/reference/vault/propertysource.html#page-title)
  * [ Introduction to VaultTemplate  Spring Vault > Vault > Introduction to VaultTemplate ](https://docs.spring.io/spring-vault/reference/vault/imperative-template.html#page-title)
  * [ Client support  Spring Vault > Vault > Client support ](https://docs.spring.io/spring-vault/reference/vault/client-support.html#page-title)
  * [ Authentication Methods  Spring Vault > Vault > Authentication Methods ](https://docs.spring.io/spring-vault/reference/vault/authentication.html#page-title)
  * [ Spring Projects  ](https://docs.spring.io/spring-vault/reference/spring-projects.html#page-title)
  * [ Search  ](https://docs.spring.io/spring-vault/reference/search.html#page-title)
  * [ New &amp; Noteworthy  Spring Vault > Introduction > New & Noteworthy ](https://docs.spring.io/spring-vault/reference/introduction/new-features.html#page-title)
  * [ Introduction  Spring Vault > Introduction ](https://docs.spring.io/spring-vault/reference/introduction/introduction.html#page-title)
  * [ Getting Started  Spring Vault > Introduction > Getting Started ](https://docs.spring.io/spring-vault/reference/introduction/getting-started.html#page-title)
  * [ Dependencies  Spring Vault > Introduction > Dependencies ](https://docs.spring.io/spring-vault/reference/introduction/dependencies.html#page-title)
  * [ Spring Vault  Spring Vault > Index ](https://docs.spring.io/spring-vault/reference/index.html#page-title)
  * [ Spring Vault  Spring Vault > Index ](https://docs.spring.io/spring-vault/reference/#page-title)
  * [ Upgrading  Spring Shell > Upgrading ](https://docs.spring.io/spring-shell/reference/upgrading.html#page-title)
  * [ StatusBarView  Spring Shell > Terminal UI > Views > StatusBarView ](https://docs.spring.io/spring-shell/reference/tui/views/statusbar.html#page-title)
  * [ ProgressView  Spring Shell > Terminal UI > Views > ProgressView ](https://docs.spring.io/spring-shell/reference/tui/views/progress.html#page-title)



Show more
---
