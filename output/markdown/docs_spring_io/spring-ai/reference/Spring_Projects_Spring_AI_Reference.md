Title: Spring Projects :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/spring-projects.html
HTML: html/docs_spring_io/spring-ai/reference/Spring_Projects_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/Spring_Projects_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:19:10.379589
---
Search CTRL + k

# Spring Projects

  * [ Spring Boot ](https://docs.spring.io/spring-boot/)
  * [ Spring Framework ](https://docs.spring.io/spring-framework/reference/)
  * __Spring Cloud
    * [ Spring Cloud Build ](https://docs.spring.io/spring-cloud-build/reference/)
    * [ Spring Cloud Bus ](https://docs.spring.io/spring-cloud-bus/reference/)
    * [ Spring Cloud Circuit Breaker ](https://docs.spring.io/spring-cloud-circuitbreaker/reference/)
    * [ Spring Cloud Commons ](https://docs.spring.io/spring-cloud-commons/reference/)
    * [ Spring Cloud Config ](https://docs.spring.io/spring-cloud-config/reference/)
    * [ Spring Cloud Consul ](https://docs.spring.io/spring-cloud-consul/reference/)
    * [ Spring Cloud Contract ](https://docs.spring.io/spring-cloud-contract/reference/)
    * [ Spring Cloud Function ](https://docs.spring.io/spring-cloud-function/reference/)
    * [ Spring Cloud Gateway ](https://docs.spring.io/spring-cloud-gateway/reference/)
    * [ Spring Cloud Kubernetes ](https://docs.spring.io/spring-cloud-kubernetes/reference/)
    * [ Spring Cloud Netflix ](https://docs.spring.io/spring-cloud-netflix/reference/)
    * [ Spring Cloud OpenFeign ](https://docs.spring.io/spring-cloud-openfeign/reference/)
    * [ Spring Cloud Stream ](https://docs.spring.io/spring-cloud-stream/reference/)
    * [ Spring Cloud Task ](https://docs.spring.io/spring-cloud-task/reference/)
    * [ Spring Cloud Vault ](https://docs.spring.io/spring-cloud-vault/reference/)
    * [ Spring Cloud Zookeeper ](https://docs.spring.io/spring-cloud-zookeeper/reference/)
  * __Spring Data
    * [ Spring Data Cassandra ](https://docs.spring.io/spring-data/cassandra/reference/)
    * [ Spring Data Commons ](https://docs.spring.io/spring-data/commons/reference/)
    * [ Spring Data Couchbase ](https://docs.spring.io/spring-data/couchbase/reference/)
    * [ Spring Data Elasticsearch ](https://docs.spring.io/spring-data/elasticsearch/reference/)
    * [ Spring Data JPA ](https://docs.spring.io/spring-data/jpa/reference/)
    * [ Spring Data KeyValue ](https://docs.spring.io/spring-data/keyvalue/reference/)
    * [ Spring Data LDAP ](https://docs.spring.io/spring-data/ldap/reference/)
    * [ Spring Data MongoDB ](https://docs.spring.io/spring-data/mongodb/reference/)
    * [ Spring Data Neo4j ](https://docs.spring.io/spring-data/neo4j/reference/)
    * [ Spring Data Redis ](https://docs.spring.io/spring-data/redis/reference/)
    * [ Spring Data JDBC & R2DBC ](https://docs.spring.io/spring-data/relational/reference/)
    * [ Spring Data REST ](https://docs.spring.io/spring-data/rest/reference/)
  * [ Spring Integration ](https://docs.spring.io/spring-integration/reference/)
  * [ Spring Batch ](https://docs.spring.io/spring-batch/reference/)
  * __[ Spring Security ](https://docs.spring.io/spring-security/reference/)
    * [ Spring Authorization Server ](https://docs.spring.io/spring-authorization-server/reference/)
    * [ Spring LDAP ](https://docs.spring.io/spring-ldap/reference/)
    * [ Spring Security Kerberos ](https://docs.spring.io/spring-security-kerberos/reference/)
    * [ Spring Session ](https://docs.spring.io/spring-session/reference/)
    * [ Spring Vault ](https://docs.spring.io/spring-vault/reference/)
  * [ Spring AI ](https://docs.spring.io/spring-ai/reference/)
  * [ Spring AMQP ](https://docs.spring.io/spring-amqp/reference/)
  * [ Spring CLI ](https://docs.spring.io/spring-cli/reference/)
  * [ Spring GraphQL ](https://docs.spring.io/spring-graphql/reference/)
  * [ Spring for Apache Kafka ](https://docs.spring.io/spring-kafka/reference/)
  * [ Spring Modulith ](https://docs.spring.io/spring-modulith/reference/)
  * [ Spring for Apache Pulsar ](https://docs.spring.io/spring-pulsar/reference/)
  * [ Spring Shell ](https://docs.spring.io/spring-shell/reference/)
---
