Title: MiniMax Chat :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/chat/minimax-chat.html
HTML: html/docs_spring_io/spring-ai/reference/api/chat/MiniMax_Chat_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/chat/MiniMax_Chat_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:20:46.401696
---
Search CTRL + k

### MiniMax Chat

  * Prerequisites
  * Add Repositories and BOM
  * Auto-configuration
  * Chat Properties
  * Runtime Options
  * Sample Controller
  * Manual Configuration
  * Low-level MiniMaxApi Client
  * WebSearch chat



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/chat/minimax-chat.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * [Chat Models](../chatmodel.html)
  * [MiniMax](minimax-chat.html)



# MiniMax Chat

### MiniMax Chat

  * Prerequisites
  * Add Repositories and BOM
  * Auto-configuration
  * Chat Properties
  * Runtime Options
  * Sample Controller
  * Manual Configuration
  * Low-level MiniMaxApi Client
  * WebSearch chat



Spring AI supports the various AI language models from MiniMax. You can interact with MiniMax language models and create a multilingual conversational assistant based on MiniMax models.

## Prerequisites

You will need to create an API with MiniMax to access MiniMax language models.

Create an account at [MiniMax registration page](https://www.minimaxi.com/login) and generate the token on the [API Keys page](https://www.minimaxi.com/user-center/basic-information/interface-key).

The Spring AI project defines a configuration property named `spring.ai.minimax.api-key` that you should set to the value of the `API Key` obtained from the API Keys page.

You can set this configuration property in your `application.properties` file:
    
    
    spring.ai.minimax.api-key=<your-minimax-api-key>
    
    Copied!

For enhanced security when handling sensitive information like API keys, you can use Spring Expression Language (SpEL) to reference an environment variable:
    
    
    # In application.yml
    spring:
      ai:
        minimax:
          api-key: ${MINIMAX_API_KEY}
    
    Copied!
    
    
    # In your environment or .env file
    export MINIMAX_API_KEY=<your-minimax-api-key>
    
    Copied!

You can also set this configuration programmatically in your application code:
    
    
    // Retrieve API key from a secure source or environment variable
    String apiKey = System.getenv("MINIMAX_API_KEY");
    
    Copied!

### Add Repositories and BOM

Spring AI artifacts are published in Maven Central and Spring Snapshot repositories. Refer to the [Artifact Repositories](../../getting-started.html#artifact-repositories) section to add these repositories to your build system.

To help with dependency management, Spring AI provides a BOM (bill of materials) to ensure that a consistent version of Spring AI is used throughout the entire project. Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build system.

## Auto-configuration

__ |  There has been a significant change in the Spring AI auto-configuration, starter modules' artifact names. Please refer to the [upgrade notes](https://docs.spring.io/spring-ai/reference/upgrade-notes.html) for more information.  
---|---  
  
Spring AI provides Spring Boot auto-configuration for the MiniMax Chat Client. To enable it add the following dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-model-minimax</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-starter-model-minimax'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
### Chat Properties

#### Retry Properties

The prefix `spring.ai.retry` is used as the property prefix that lets you configure the retry mechanism for the MiniMax chat model.

Property | Description | Default  
---|---|---  
spring.ai.retry.max-attempts | Maximum number of retry attempts. | 10  
spring.ai.retry.backoff.initial-interval | Initial sleep duration for the exponential backoff policy. | 2 sec.  
spring.ai.retry.backoff.multiplier | Backoff interval multiplier. | 5  
spring.ai.retry.backoff.max-interval | Maximum backoff duration. | 3 min.  
spring.ai.retry.on-client-errors | If false, throw a NonTransientAiException, and do not attempt retry for `4xx` client error codes | false  
spring.ai.retry.exclude-on-http-codes | List of HTTP status codes that should not trigger a retry (e.g. to throw NonTransientAiException). | empty  
spring.ai.retry.on-http-codes | List of HTTP status codes that should trigger a retry (e.g. to throw TransientAiException). | empty  
  
#### Connection Properties

The prefix `spring.ai.minimax` is used as the property prefix that lets you connect to MiniMax.

Property | Description | Default  
---|---|---  
spring.ai.minimax.base-url | The URL to connect to | [api.minimax.chat](https://api.minimax.chat)  
spring.ai.minimax.api-key | The API Key | -  
  
#### Configuration Properties

__ |  Enabling and disabling of the chat auto-configurations are now configured via top level properties with the prefix `spring.ai.model.chat`. To enable, spring.ai.model.chat=minimax (It is enabled by default) To disable, spring.ai.model.chat=none (or any value which doesn’t match minimax) This change is done to allow configuration of multiple models.  
---|---  
  
The prefix `spring.ai.minimax.chat` is the property prefix that lets you configure the chat model implementation for MiniMax.

Property | Description | Default  
---|---|---  
spring.ai.minimax.chat.enabled (Removed and no longer valid) | Enable MiniMax chat model. | true  
spring.ai.model.chat | Enable MiniMax chat model. | minimax  
spring.ai.minimax.chat.base-url | Optional overrides the spring.ai.minimax.base-url to provide chat specific url | [api.minimax.chat](https://api.minimax.chat)  
spring.ai.minimax.chat.api-key | Optional overrides the spring.ai.minimax.api-key to provide chat specific api-key | -  
spring.ai.minimax.chat.options.model | This is the MiniMax Chat model to use | `abab6.5g-chat` (the `abab5.5-chat`, `abab5.5s-chat`, `abab6.5-chat`, `abab6.5g-chat`, `abab6.5t-chat` and `abab6.5s-chat` point to the latest model versions)  
spring.ai.minimax.chat.options.maxTokens | The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model’s context length. | -  
spring.ai.minimax.chat.options.temperature | The sampling temperature to use that controls the apparent creativity of generated completions. Higher values will make output more random while lower values will make results more focused and deterministic. It is not recommended to modify temperature and top_p for the same completions request as the interaction of these two settings is difficult to predict. | 0.7  
spring.ai.minimax.chat.options.topP | An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered. We generally recommend altering this or temperature but not both. | 1.0  
spring.ai.minimax.chat.options.n | How many chat completion choices to generate for each input message. Note that you will be charged based on the number of generated tokens across all of the choices. Default value is 1 and cannot be greater than 5. Specifically, when the temperature is very small and close to 0, we can only return 1 result. If n is already set and>1 at this time, service will return an illegal input parameter (invalid_request_error) | 1  
spring.ai.minimax.chat.options.presencePenalty | Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model’s likelihood to talk about new topics. | 0.0f  
spring.ai.minimax.chat.options.frequencyPenalty | Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model’s likelihood to repeat the same line verbatim. | 0.0f  
spring.ai.minimax.chat.options.stop | The model will stop generating characters specified by stop, and currently only supports a single stop word in the format of ["stop_word1"] | -  
  
__ |  You can override the common `spring.ai.minimax.base-url` and `spring.ai.minimax.api-key` for the `ChatModel` implementations. The `spring.ai.minimax.chat.base-url` and `spring.ai.minimax.chat.api-key` properties if set take precedence over the common properties. This is useful if you want to use different MiniMax accounts for different models and different model endpoints.   
---|---  
  
__ |  All properties prefixed with `spring.ai.minimax.chat.options` can be overridden at runtime by adding a request specific Runtime Options to the `Prompt` call.   
---|---  
  
## Runtime Options

The [MiniMaxChatOptions.java](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-minimax/src/main/java/org/springframework/ai/minimax/MiniMaxChatOptions.java) provides model configurations, such as the model to use, the temperature, the frequency penalty, etc.

On start-up, the default options can be configured with the `MiniMaxChatModel(api, options)` constructor or the `spring.ai.minimax.chat.options.*` properties.

At run-time you can override the default options by adding new, request specific, options to the `Prompt` call. For example to override the default model and temperature for a specific request:
    
    
    ChatResponse response = chatModel.call(
        new Prompt(
            "Generate the names of 5 famous pirates.",
            MiniMaxChatOptions.builder()
                .model(MiniMaxApi.ChatModel.ABAB_6_5_S_Chat.getValue())
                .temperature(0.5)
            .build()
        ));
    
    Copied!

__ |  In addition to the model specific [MiniMaxChatOptions](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-minimax/src/main/java/org/springframework/ai/minimax/MiniMaxChatOptions.java) you can use a portable [ChatOptions](https://github.com/spring-projects/spring-ai/blob/main/spring-ai-client-chat/src/main/java/org/springframework/ai/chat/ChatOptions.java) instance, created with the [ChatOptionsBuilder#builder()](https://github.com/spring-projects/spring-ai/blob/main/spring-ai-client-chat/src/main/java/org/springframework/ai/chat/ChatOptionsBuilder.java).   
---|---  
  
## Sample Controller

[Create](https://start.spring.io/) a new Spring Boot project and add the `spring-ai-starter-model-minimax` to your pom (or gradle) dependencies.

Add a `application.properties` file, under the `src/main/resources` directory, to enable and configure the MiniMax chat model:
    
    
    spring.ai.minimax.api-key=YOUR_API_KEY
    spring.ai.minimax.chat.options.model=abab6.5g-chat
    spring.ai.minimax.chat.options.temperature=0.7
    
    Copied!

__ |  replace the `api-key` with your MiniMax credentials.   
---|---  
  
This will create a `MiniMaxChatModel` implementation that you can inject into your class. Here is an example of a simple `@Controller` class that uses the chat model for text generations.
    
    
    @RestController
    public class ChatController {
    
        private final MiniMaxChatModel chatModel;
    
        @Autowired
        public ChatController(MiniMaxChatModel chatModel) {
            this.chatModel = chatModel;
        }
    
        @GetMapping("/ai/generate")
        public Map generate(@RequestParam(value = "message", defaultValue = "Tell me a joke") String message) {
            return Map.of("generation", this.chatModel.call(message));
        }
    
        @GetMapping("/ai/generateStream")
    	public Flux<ChatResponse> generateStream(@RequestParam(value = "message", defaultValue = "Tell me a joke") String message) {
            var prompt = new Prompt(new UserMessage(message));
            return this.chatModel.stream(prompt);
        }
    }
    
    Copied!

## Manual Configuration

The [MiniMaxChatModel](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-minimax/src/main/java/org/springframework/ai/minimax/MiniMaxChatModel.java) implements the `ChatModel` and `StreamingChatModel` and uses the Low-level MiniMaxApi Client to connect to the MiniMax service.

Add the `spring-ai-minimax` dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-minimax</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-minimax'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
Next, create a `MiniMaxChatModel` and use it for text generations:
    
    
    var miniMaxApi = new MiniMaxApi(System.getenv("MINIMAX_API_KEY"));
    
    var chatModel = new MiniMaxChatModel(this.miniMaxApi, MiniMaxChatOptions.builder()
                    .model(MiniMaxApi.ChatModel.ABAB_6_5_S_Chat.getValue())
                    .temperature(0.4)
                    .maxTokens(200)
                    .build());
    
    ChatResponse response = this.chatModel.call(
        new Prompt("Generate the names of 5 famous pirates."));
    
    // Or with streaming responses
    Flux<ChatResponse> streamResponse = this.chatModel.stream(
        new Prompt("Generate the names of 5 famous pirates."));
    
    Copied!

The `MiniMaxChatOptions` provides the configuration information for the chat requests. The `MiniMaxChatOptions.Builder` is fluent options builder.

### Low-level MiniMaxApi Client

The [MiniMaxApi](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-minimax/src/main/java/org/springframework/ai/minimax/api/MiniMaxApi.java) provides is lightweight Java client for [MiniMax API](https://www.minimaxi.com/document/guides/chat-model/V2).

Here is a simple snippet how to use the api programmatically:
    
    
    MiniMaxApi miniMaxApi =
        new MiniMaxApi(System.getenv("MINIMAX_API_KEY"));
    
    ChatCompletionMessage chatCompletionMessage =
        new ChatCompletionMessage("Hello world", Role.USER);
    
    // Sync request
    ResponseEntity<ChatCompletion> response = this.miniMaxApi.chatCompletionEntity(
        new ChatCompletionRequest(List.of(this.chatCompletionMessage), MiniMaxApi.ChatModel.ABAB_6_5_S_Chat.getValue(), 0.7f, false));
    
    // Streaming request
    Flux<ChatCompletionChunk> streamResponse = this.miniMaxApi.chatCompletionStream(
            new ChatCompletionRequest(List.of(this.chatCompletionMessage), MiniMaxApi.ChatModel.ABAB_6_5_S_Chat.getValue(), 0.7f, true));
    
    Copied!

Follow the [MiniMaxApi.java](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-minimax/src/main/java/org/springframework/ai/minimax/api/MiniMaxApi.java)'s JavaDoc for further information.

### WebSearch chat

The MiniMax model supported the web search feature. The web search feature allows you to search the web for information and return the results in the chat response.

About web search follow the [MiniMax ChatCompletion](https://platform.minimaxi.com/document/ChatCompletion%20v2) for further information.

Here is a simple snippet how to use the web search:
    
    
    UserMessage userMessage = new UserMessage(
            "How many gold medals has the United States won in total at the 2024 Olympics?");
    
    List<Message> messages = new ArrayList<>(List.of(this.userMessage));
    
    List<MiniMaxApi.FunctionTool> functionTool = List.of(MiniMaxApi.FunctionTool.webSearchFunctionTool());
    
    MiniMaxChatOptions options = MiniMaxChatOptions.builder()
        .model(MiniMaxApi.ChatModel.ABAB_6_5_S_Chat.value)
        .tools(this.functionTool)
        .build();
    
    
    // Sync request
    ChatResponse response = chatModel.call(new Prompt(this.messages, this.options));
    
    // Streaming request
    Flux<ChatResponse> streamResponse = chatModel.stream(new Prompt(this.messages, this.options));
    
    Copied!

#### MiniMaxApi Samples

  * The [MiniMaxApiIT.java](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-minimax/src/test/java/org/springframework/ai/minimax/api/MiniMaxApiIT.java) test provides some general examples how to use the lightweight library.

  * The [MiniMaxApiToolFunctionCallIT.java](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-minimax/src/test/java/org/springframework/ai/minimax/api/MiniMaxApiToolFunctionCallIT.java) test shows how to use the low-level API to call tool functions.>




[Mistral AI](mistralai-chat.html) [Moonshot AI](moonshot-chat.html)
---
