Title: <PERSON><PERSON><PERSON><PERSON> Chat :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/chat/qianfan-chat.html
HTML: html/docs_spring_io/spring-ai/reference/api/chat/<PERSON><PERSON><PERSON><PERSON>_Chat_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/chat/<PERSON>an<PERSON><PERSON>_Chat_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:20:42.609891
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/chat/qianfan-chat.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * [Chat Models](../chatmodel.html)
  * [<PERSON><PERSON><PERSON><PERSON>](qianfan-chat.html)



# Qian<PERSON>an Chat

This functionality has been moved to the Spring AI Community repository.

Please visit [github.com/spring-ai-community/qianfan](https://github.com/spring-ai-community/qianfan) for the latest version.

[OpenAI](openai-chat.html) [ZhiPu AI](zhipuai-chat.html)
---
