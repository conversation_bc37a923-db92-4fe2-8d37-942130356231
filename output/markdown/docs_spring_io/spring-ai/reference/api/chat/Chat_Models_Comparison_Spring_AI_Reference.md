Title: Chat Models Comparison :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/chat/comparison.html
HTML: html/docs_spring_io/spring-ai/reference/api/chat/Chat_Models_Comparison_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/chat/Chat_Models_Comparison_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:15:09.228523
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/chat/comparison.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * [Chat Models](../chatmodel.html)
  * [Chat Models Comparison](comparison.html)



# Chat Models Comparison

This table compares various Chat Models supported by Spring AI, detailing their capabilities:

  * [Multimodality](../multimodality.html): The types of input the model can process (e.g., text, image, audio, video).

  * [Tools/Function Calling](../tools.html): Whether the model supports function calling or tool use.

  * Streaming: If the model offers streaming responses.

  * Retry: Support for retry mechanisms.

  * [Observability](../../observability/index.html): Features for monitoring and debugging.

  * [Built-in JSON](../structured-output-converter.html#_built_in_json_mode): Native support for JSON output.

  * Local deployment: Whether the model can be run locally.

  * OpenAI API Compatibility: If the model is compatible with OpenAI’s API.




Provider | Multimodality | Tools/Functions | Streaming | Retry | Observability | Built-in JSON | Local | OpenAI API Compatible  
---|---|---|---|---|---|---|---|---  
[Anthropic Claude](anthropic-chat.html) | text, pdf, image |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg)  
[Azure OpenAI](azure-openai-chat.html) | text, image |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![yes](../../_images/yes.svg)  
[DeepSeek (OpenAI-proxy)](deepseek-chat.html) | text |  ![no](../../_images/no.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg)  
[Google VertexAI Gemini](vertexai-gemini-chat.html) | text, pdf, image, audio, video |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![yes](../../_images/yes.svg)  
[Groq (OpenAI-proxy)](groq-chat.html) | text, image |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![yes](../../_images/yes.svg)  
[HuggingFace](huggingface.html) | text |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg)  
[Mistral AI](mistralai-chat.html) | text, image |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![yes](../../_images/yes.svg)  
[MiniMax](minimax-chat.html) | text |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |   
[Moonshot AI](moonshot-chat.html) | text |  ![no](../../_images/no.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |   
[NVIDIA (OpenAI-proxy)](nvidia-chat.html) | text, image |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![yes](../../_images/yes.svg)  
[OCI GenAI/Cohere](oci-genai/cohere-chat.html) | text |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg)  
[Ollama](ollama-chat.html) | text, image |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg)  
[OpenAI](openai-chat.html) |  In: text, image, audio Out: text, audio |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![yes](../../_images/yes.svg)  
[Perplexity (OpenAI-proxy)](perplexity-chat.html) | text |  ![no](../../_images/no.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![yes](../../_images/yes.svg)  
[QianFan](qianfan-chat.html) | text |  ![no](../../_images/no.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg)  
[ZhiPu AI](zhipuai-chat.html) | text |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg)  
[Amazon Bedrock Converse](bedrock-converse.html) | text, image, video, docs (pdf, html, md, docx …​) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![yes](../../_images/yes.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg) |  ![no](../../_images/no.svg)  
[Chat Models](../chatmodel.html) [Amazon Bedrock Converse](bedrock-converse.html)
---
