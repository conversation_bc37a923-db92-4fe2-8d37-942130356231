Title: Moderation :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/moderation/openai-moderation.html
HTML: html/docs_spring_io/spring-ai/reference/api/moderation/Moderation_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/moderation/Moderation_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:19:37.038442
---
Search CTRL + k

### Moderation

  * Introduction
  * Prerequisites
  * Auto-configuration
  * Moderation Properties
  * Connection Properties
  * Configuration Properties
  * Runtime Options
  * Manual Configuration
  * Example Code



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/moderation/openai-moderation.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * Moderation Models
  * [OpenAI](openai-moderation.html)



# Moderation

### Moderation

  * Introduction
  * Prerequisites
  * Auto-configuration
  * Moderation Properties
  * Connection Properties
  * Configuration Properties
  * Runtime Options
  * Manual Configuration
  * Example Code



## Introduction

Spring AI supports OpenAI’s Moderation model, which allows you to detect potentially harmful or sensitive content in text. Follow this [guide](https://platform.openai.com/docs/guides/moderation) to for more information on OpenAI’s moderation model.

## Prerequisites

  1. Create an OpenAI account and obtain an API key. You can sign up at the [OpenAI signup page](https://platform.openai.com/signup) and generate an API key on the [API Keys page](https://platform.openai.com/account/api-keys).

  2. Add the `spring-ai-openai` dependency to your project’s build file. For more information, refer to the [Dependency Management](../../getting-started.html#dependency-management) section.




## Auto-configuration

__ |  There has been a significant change in the Spring AI auto-configuration, starter modules' artifact names. Please refer to the [upgrade notes](https://docs.spring.io/spring-ai/reference/upgrade-notes.html) for more information.  
---|---  
  
Spring AI provides Spring Boot auto-configuration for the OpenAI Moderation Model. To enable it add the following dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-model-openai</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file:
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-starter-model-openai'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
## Moderation Properties

### Connection Properties

The prefix spring.ai.openai is used as the property prefix that lets you connect to OpenAI.

Property | Description | Default  
---|---|---  
spring.ai.openai.base-url | The URL to connect to | [api.openai.com](https://api.openai.com)  
spring.ai.openai.api-key | The API Key | -  
spring.ai.openai.organization-id | Optionally you can specify which organization is used for an API request. | -  
spring.ai.openai.project-id | Optionally, you can specify which project is used for an API request. | -  
  
__ |  For users that belong to multiple organizations (or are accessing their projects through their legacy user API key), optionally, you can specify which organization and project is used for an API request. Usage from these API requests will count as usage for the specified organization and project.   
---|---  
  
### Configuration Properties

__ |  Enabling and disabling of the embedding auto-configurations are now configured via top level properties with the prefix `spring.ai.model.moderation`. To enable, spring.ai.model.moderation=openai (It is enabled by default) To disable, spring.ai.model.moderation=none (or any value which doesn’t match openai) This change is done to allow configuration of multiple models.  
---|---  
  
The prefix spring.ai.openai.moderation is used as the property prefix for configuring the OpenAI moderation model.

Property | Description | Default  
---|---|---  
spring.ai.model.moderation | Enable Moderation model | openai  
spring.ai.openai.moderation.base-url | The URL to connect to | [api.openai.com](https://api.openai.com)  
spring.ai.openai.moderation.api-key | The API Key | -  
spring.ai.openai.moderation.organization-id | Optionally you can specify which organization is used for an API request. | -  
spring.ai.openai.moderation.project-id | Optionally, you can specify which project is used for an API request. | -  
spring.ai.openai.moderation.options.model | ID of the model to use for moderation. | text-moderation-latest  
  
__ |  You can override the common `spring.ai.openai.base-url`, `spring.ai.openai.api-key`, `spring.ai.openai.organization-id` and `spring.ai.openai.project-id` properties. The `spring.ai.openai.moderation.base-url`, `spring.ai.openai.moderation.api-key`, `spring.ai.openai.moderation.organization-id` and `spring.ai.openai.moderation.project-id` properties, if set, take precedence over the common properties. This is useful if you want to use different OpenAI accounts for different models and different model endpoints.   
---|---  
  
__ |  All properties prefixed with `spring.ai.openai.moderation.options` can be overridden at runtime.   
---|---  
  
## Runtime Options

The OpenAiModerationOptions class provides the options to use when making a moderation request. On start-up, the options specified by spring.ai.openai.moderation are used, but you can override these at runtime.

For example:
    
    
    OpenAiModerationOptions moderationOptions = OpenAiModerationOptions.builder()
        .model("text-moderation-latest")
        .build();
    
    ModerationPrompt moderationPrompt = new ModerationPrompt("Text to be moderated", this.moderationOptions);
    ModerationResponse response = openAiModerationModel.call(this.moderationPrompt);
    
    // Access the moderation results
    Moderation moderation = moderationResponse.getResult().getOutput();
    
    // Print general information
    System.out.println("Moderation ID: " + moderation.getId());
    System.out.println("Model used: " + moderation.getModel());
    
    // Access the moderation results (there's usually only one, but it's a list)
    for (ModerationResult result : moderation.getResults()) {
        System.out.println("\nModeration Result:");
        System.out.println("Flagged: " + result.isFlagged());
    
        // Access categories
        Categories categories = this.result.getCategories();
        System.out.println("\nCategories:");
        System.out.println("Sexual: " + categories.isSexual());
        System.out.println("Hate: " + categories.isHate());
        System.out.println("Harassment: " + categories.isHarassment());
        System.out.println("Self-Harm: " + categories.isSelfHarm());
        System.out.println("Sexual/Minors: " + categories.isSexualMinors());
        System.out.println("Hate/Threatening: " + categories.isHateThreatening());
        System.out.println("Violence/Graphic: " + categories.isViolenceGraphic());
        System.out.println("Self-Harm/Intent: " + categories.isSelfHarmIntent());
        System.out.println("Self-Harm/Instructions: " + categories.isSelfHarmInstructions());
        System.out.println("Harassment/Threatening: " + categories.isHarassmentThreatening());
        System.out.println("Violence: " + categories.isViolence());
    
        // Access category scores
        CategoryScores scores = this.result.getCategoryScores();
        System.out.println("\nCategory Scores:");
        System.out.println("Sexual: " + scores.getSexual());
        System.out.println("Hate: " + scores.getHate());
        System.out.println("Harassment: " + scores.getHarassment());
        System.out.println("Self-Harm: " + scores.getSelfHarm());
        System.out.println("Sexual/Minors: " + scores.getSexualMinors());
        System.out.println("Hate/Threatening: " + scores.getHateThreatening());
        System.out.println("Violence/Graphic: " + scores.getViolenceGraphic());
        System.out.println("Self-Harm/Intent: " + scores.getSelfHarmIntent());
        System.out.println("Self-Harm/Instructions: " + scores.getSelfHarmInstructions());
        System.out.println("Harassment/Threatening: " + scores.getHarassmentThreatening());
        System.out.println("Violence: " + scores.getViolence());
    }
    
    Copied!

## Manual Configuration

Add the `spring-ai-openai` dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-openai</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file:
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-openai'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
Next, create an OpenAiModerationModel:
    
    
    OpenAiModerationApi openAiModerationApi = new OpenAiModerationApi(System.getenv("OPENAI_API_KEY"));
    
    OpenAiModerationModel openAiModerationModel = new OpenAiModerationModel(this.openAiModerationApi);
    
    OpenAiModerationOptions moderationOptions = OpenAiModerationOptions.builder()
        .model("text-moderation-latest")
        .build();
    
    ModerationPrompt moderationPrompt = new ModerationPrompt("Text to be moderated", this.moderationOptions);
    ModerationResponse response = this.openAiModerationModel.call(this.moderationPrompt);
    
    Copied!

## Example Code

The `OpenAiModerationModelIT` test provides some general examples of how to use the library. You can refer to this test for more detailed usage examples.

[OpenAI](../audio/speech/openai-speech.html) [Mistral AI](mistral-ai-moderation.html)
---
