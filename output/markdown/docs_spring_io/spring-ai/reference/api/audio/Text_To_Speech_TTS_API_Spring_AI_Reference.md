Title: Text-To-Speech (TTS) API :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/audio/speech.html
HTML: html/docs_spring_io/spring-ai/reference/api/audio/Text_To_Speech_TTS_API_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/audio/Text_To_Speech_TTS_API_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:19:02.955471
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/audio/speech.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * Audio Models
  * [Text-To-Speech (TTS) API](speech.html)



# Text-To-Speech (TTS) API

Spring AI provides support for OpenAI’s Speech API. When additional providers for Speech are implemented, a common `SpeechModel` and `StreamingSpeechModel` interface will be extracted.

[OpenAI](transcriptions/openai-transcriptions.html) [OpenAI](speech/openai-speech.html)
---
