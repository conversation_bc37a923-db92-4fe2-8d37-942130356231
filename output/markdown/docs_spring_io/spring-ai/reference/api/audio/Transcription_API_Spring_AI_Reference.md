Title: Transcription API :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/audio/transcriptions.html
HTML: html/docs_spring_io/spring-ai/reference/api/audio/Transcription_API_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/audio/Transcription_API_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:17:01.958546
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/audio/transcriptions.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * Audio Models
  * [Transcription API](transcriptions.html)



# Transcription API

Spring AI provides support for OpenAI’s Transcription API. When additional providers for Transcription are implemented, a common `AudioTranscriptionModel` interface will be extracted.

[Qian<PERSON>an](../image/qianfan-image.html) [Azure OpenAI](transcriptions/azure-openai-transcriptions.html)
---
