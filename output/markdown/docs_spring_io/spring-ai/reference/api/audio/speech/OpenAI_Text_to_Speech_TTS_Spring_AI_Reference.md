Title: OpenAI Text-to-Speech (TTS) :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/audio/speech/openai-speech.html
HTML: html/docs_spring_io/spring-ai/reference/api/audio/speech/OpenAI_Text_to_Speech_TTS_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/audio/speech/OpenAI_Text_to_Speech_TTS_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:19:33.000970
---
Search CTRL + k

### OpenAI Text-to-Speech (TTS)

  * Introduction
  * Prerequisites
  * Auto-configuration
  * Speech Properties
  * Connection Properties
  * Configuration Properties
  * Runtime Options
  * Manual Configuration
  * Streaming Real-time Audio
  * Example Code



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/audio/speech/openai-speech.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../../index.html)
  * Reference
  * [Models](../../index.html)
  * Audio Models
  * [Text-To-Speech (TTS) API](../speech.html)
  * [OpenAI](openai-speech.html)



# OpenAI Text-to-Speech (TTS)

### OpenAI Text-to-Speech (TTS)

  * Introduction
  * Prerequisites
  * Auto-configuration
  * Speech Properties
  * Connection Properties
  * Configuration Properties
  * Runtime Options
  * Manual Configuration
  * Streaming Real-time Audio
  * Example Code



## Introduction

The Audio API provides a speech endpoint based on OpenAI’s TTS (text-to-speech) model, enabling users to:

  * Narrate a written blog post.

  * Produce spoken audio in multiple languages.

  * Give real-time audio output using streaming.




## Prerequisites

  1. Create an OpenAI account and obtain an API key. You can sign up at the [OpenAI signup page](https://platform.openai.com/signup) and generate an API key on the [API Keys page](https://platform.openai.com/account/api-keys).

  2. Add the `spring-ai-openai` dependency to your project’s build file. For more information, refer to the [Dependency Management](../../../getting-started.html#dependency-management) section.




## Auto-configuration

__ |  There has been a significant change in the Spring AI auto-configuration, starter modules' artifact names. Please refer to the [upgrade notes](https://docs.spring.io/spring-ai/reference/upgrade-notes.html) for more information.  
---|---  
  
Spring AI provides Spring Boot auto-configuration for the OpenAI Text-to-Speech Client. To enable it add the following dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-model-openai</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file:
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-starter-model-openai'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
## Speech Properties

### Connection Properties

The prefix `spring.ai.openai` is used as the property prefix that lets you connect to OpenAI.

Property | Description | Default  
---|---|---  
spring.ai.openai.base-url | The URL to connect to | [api.openai.com](https://api.openai.com)  
spring.ai.openai.api-key | The API Key | -  
spring.ai.openai.organization-id | Optionally you can specify which organization used for an API request. | -  
spring.ai.openai.project-id | Optionally, you can specify which project is used for an API request. | -  
  
__ |  For users that belong to multiple organizations (or are accessing their projects through their legacy user API key), optionally, you can specify which organization and project is used for an API request. Usage from these API requests will count as usage for the specified organization and project.   
---|---  
  
### Configuration Properties

__ |  Enabling and disabling of the audio speech auto-configurations are now configured via top level properties with the prefix `spring.ai.model.audio.speech`. To enable, spring.ai.model.audio.speech=openai (It is enabled by default) To disable, spring.ai.model.audio.speech=none (or any value which doesn’t match openai) This change is done to allow configuration of multiple models.  
---|---  
  
The prefix `spring.ai.openai.audio.speech` is used as the property prefix that lets you configure the OpenAI Text-to-Speech client.

Property | Description | Default  
---|---|---  
spring.ai.model.audio.speech | Enable Audio Speech Model | openai  
spring.ai.openai.audio.speech.base-url | The URL to connect to | [api.openai.com](https://api.openai.com)  
spring.ai.openai.audio.speech.api-key | The API Key | -  
spring.ai.openai.audio.speech.organization-id | Optionally you can specify which organization used for an API request. | -  
spring.ai.openai.audio.speech.project-id | Optionally, you can specify which project is used for an API request. | -  
spring.ai.openai.audio.speech.options.model | ID of the model to use for generating the audio. For OpenAI’s TTS API, use one of the available models: tts-1 or tts-1-hd. | tts-1  
spring.ai.openai.audio.speech.options.voice | The voice to use for synthesis. For OpenAI’s TTS API, One of the available voices for the chosen model: alloy, echo, fable, onyx, nova, and shimmer. | alloy  
spring.ai.openai.audio.speech.options.response-format | The format of the audio output. Supported formats are mp3, opus, aac, flac, wav, and pcm. | mp3  
spring.ai.openai.audio.speech.options.speed | The speed of the voice synthesis. The acceptable range is from 0.25 (slowest) to 4.0 (fastest). | 1.0  
  
__ |  You can override the common `spring.ai.openai.base-url`, `spring.ai.openai.api-key`, `spring.ai.openai.organization-id` and `spring.ai.openai.project-id` properties. The `spring.ai.openai.audio.speech.base-url`, `spring.ai.openai.audio.speech.api-key`, `spring.ai.openai.audio.speech.organization-id` and `spring.ai.openai.audio.speech.project-id` properties if set take precedence over the common properties. This is useful if you want to use different OpenAI accounts for different models and different model endpoints.   
---|---  
  
__ |  All properties prefixed with `spring.ai.openai.image.options` can be overridden at runtime.   
---|---  
  
## Runtime Options

The `OpenAiAudioSpeechOptions` class provides the options to use when making a text-to-speech request. On start-up, the options specified by `spring.ai.openai.audio.speech` are used but you can override these at runtime.

For example:
    
    
    OpenAiAudioSpeechOptions speechOptions = OpenAiAudioSpeechOptions.builder()
        .model("tts-1")
        .voice(OpenAiAudioApi.SpeechRequest.Voice.ALLOY)
        .responseFormat(OpenAiAudioApi.SpeechRequest.AudioResponseFormat.MP3)
        .speed(1.0f)
        .build();
    
    SpeechPrompt speechPrompt = new SpeechPrompt("Hello, this is a text-to-speech example.", speechOptions);
    SpeechResponse response = openAiAudioSpeechModel.call(speechPrompt);
    
    Copied!

## Manual Configuration

Add the `spring-ai-openai` dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-openai</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file:
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-openai'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
Next, create an `OpenAiAudioSpeechModel`:
    
    
    var openAiAudioApi = new OpenAiAudioApi()
        .apiKey(System.getenv("OPENAI_API_KEY"))
        .build();
    
    var openAiAudioSpeechModel = new OpenAiAudioSpeechModel(openAiAudioApi);
    
    var speechOptions = OpenAiAudioSpeechOptions.builder()
        .responseFormat(OpenAiAudioApi.SpeechRequest.AudioResponseFormat.MP3)
        .speed(1.0f)
        .model(OpenAiAudioApi.TtsModel.TTS_1.value)
        .build();
    
    var speechPrompt = new SpeechPrompt("Hello, this is a text-to-speech example.", speechOptions);
    SpeechResponse response = openAiAudioSpeechModel.call(speechPrompt);
    
    // Accessing metadata (rate limit info)
    OpenAiAudioSpeechResponseMetadata metadata = response.getMetadata();
    
    byte[] responseAsBytes = response.getResult().getOutput();
    
    Copied!

## Streaming Real-time Audio

The Speech API provides support for real-time audio streaming using chunk transfer encoding. This means that the audio is able to be played before the full file has been generated and made accessible.
    
    
    var openAiAudioApi = new OpenAiAudioApi()
        .apiKey(System.getenv("OPENAI_API_KEY"))
        .build();
    
    var openAiAudioSpeechModel = new OpenAiAudioSpeechModel(openAiAudioApi);
    
    OpenAiAudioSpeechOptions speechOptions = OpenAiAudioSpeechOptions.builder()
        .voice(OpenAiAudioApi.SpeechRequest.Voice.ALLOY)
        .speed(1.0f)
        .responseFormat(OpenAiAudioApi.SpeechRequest.AudioResponseFormat.MP3)
        .model(OpenAiAudioApi.TtsModel.TTS_1.value)
        .build();
    
    SpeechPrompt speechPrompt = new SpeechPrompt("Today is a wonderful day to build something people love!", speechOptions);
    
    Flux<SpeechResponse> responseStream = openAiAudioSpeechModel.stream(speechPrompt);
    
    Copied!

## Example Code

  * The [OpenAiSpeechModelIT.java](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-openai/src/test/java/org/springframework/ai/openai/audio/speech/OpenAiSpeechModelIT.java) test provides some general examples of how to use the library.




[Text-To-Speech (TTS) API](../speech.html) [OpenAI](../../moderation/openai-moderation.html)
---
