Title: Docker Compose :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/docker-compose.html
HTML: html/docs_spring_io/spring-ai/reference/api/Docker_Compose_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/Docker_Compose_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:15:20.266280
---
Search CTRL + k

### Docker Compose

  * Service Connections



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/docker-compose.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../index.html)
  * Reference
  * [Development-time Services](docker-compose.html)



# Docker Compose

### Docker Compose

  * Service Connections



Spring AI provides Spring Boot auto-configuration for establishing a connection to a model service or vector store running via Docker Compose. To enable it, add the following dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
       <groupId>org.springframework.ai</groupId>
       <artifactId>spring-ai-spring-boot-docker-compose</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-spring-boot-docker-compose'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
## Service Connections

The following service connection factories are provided in the `spring-ai-spring-boot-docker-compose` module:

Connection Details | Matched on  
---|---  
`AwsOpenSearchConnectionDetails` | Containers named `localstack/localstack`  
`ChromaConnectionDetails` | Containers named `chromadb/chroma`, `ghcr.io/chroma-core/chroma`  
`MongoConnectionDetails` | Containers named `mongodb/mongodb-atlas-local`  
`OllamaConnectionDetails` | Containers named `ollama/ollama`  
`OpenSearchConnectionDetails` | Containers named `opensearchproject/opensearch`  
`QdrantConnectionDetails` | Containers named `qdrant/qdrant`  
`TypesenseConnectionDetails` | Containers named `typesense/typesense`  
`WeaviateConnectionDetails` | Containers named `semitechnologies/weaviate`, `cr.weaviate.io/semitechnologies/weaviate`  
  
[Observability](../observability/index.html) [Testcontainers](testcontainers.html)
---
