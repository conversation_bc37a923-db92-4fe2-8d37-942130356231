Title: Cloud Bindings :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/cloud-bindings.html
HTML: html/docs_spring_io/spring-ai/reference/api/Cloud_Bindings_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/Cloud_Bindings_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:18:10.764184
---
Search CTRL + k

### Cloud Bindings

  * Available Cloud Bindings



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/cloud-bindings.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../index.html)
  * Guides
  * [Deploying to the Cloud](cloud-bindings.html)



# Cloud Bindings

### Cloud Bindings

  * Available Cloud Bindings



Spring AI provides support for cloud bindings based on the foundations in [spring-cloud-bindings](https://github.com/spring-cloud/spring-cloud-bindings). This allows applications to specify a binding type for a provider and then express properties using a generic format. The spring-ai cloud bindings will process these properties and bind them to spring-ai native properties.

For example, when using `OpenAi`, the binding type is `openai`. Using the property `spring.ai.cloud.bindings.openai.enabled`, the binding processor can be enabled or disabled. By default, when specifying a binding type, this property will be enabled. Configuration for `api-key`, `uri`, `username`, `password`, etc. can be specified and spring-ai will map them to the corresponding properties in the supported system.

To enable cloud binding support, include the following dependency in the application.
    
    
    <dependency>
       <groupId>org.springframework.ai</groupId>
       <artifactId>spring-ai-spring-cloud-bindings</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-spring-cloud-bindings'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
## Available Cloud Bindings

The following are the components for which the cloud binding support is currently available in the `spring-ai-spring-clou-bindings` module:

Service Type | Binding Type | Source Properties | Target Properties  
---|---|---|---  
`Chroma Vector Store` | `chroma` | `uri`, `username`, `passwor` | `spring.ai.vectorstore.chroma.client.host`, `spring.ai.vectorstore.chroma.client.port`, `spring.ai.vectorstore.chroma.client.username`, `spring.ai.vectorstore.chroma.client.host.password`  
`Mistral AI` | `mistralai` | `api-key`, `uri` | `spring.ai.mistralai.api-key`, `spring.ai.mistralai.base-url`  
`Ollama` | `ollama` | `uri` | `spring.ai.ollama.base-url`  
`OpenAi` | `openai` | `api-key`, `uri` | `spring.ai.openai.api-key`, `spring.ai.openai.base-url`  
`Weaviate` | `weaviate` | `uri`, `api-key` | `spring.ai.vectorstore.weaviate.scheme`, `spring.ai.vectorstore.weaviate.host`, `spring.ai.vectorstore.weaviate.api-key`  
`Tanzu GenAI` | `genai` | `uri`, `api-key`, `model-capabilities` (`chat` and `embedding`), `model-name` | `spring.ai.openai.chat.base-url`, , spring.ai.openai.chat.api-key`, `spring.ai.openai.chat.options.model`, `spring.ai.openai.embedding.base-url`, , spring.ai.openai.embedding.api-key`, `spring.ai.openai.embedding.options.model`  
  
[Building Effective Agents](effective-agents.html) [Upgrade Notes](../upgrade-notes.html)
---
