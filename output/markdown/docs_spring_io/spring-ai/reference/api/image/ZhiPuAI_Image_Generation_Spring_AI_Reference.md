Title: ZhiPuAI Image Generation :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/image/zhipuai-image.html
HTML: html/docs_spring_io/spring-ai/reference/api/image/ZhiPuAI_Image_Generation_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/image/ZhiPuAI_Image_Generation_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:18:43.797187
---
Search CTRL + k

### ZhiPuAI Image Generation

  * Prerequisites
  * Add Repositories and BOM
  * Auto-configuration
  * Image Generation Properties
  * Runtime Options



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/image/zhipuai-image.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * [Image Models](../imageclient.html)
  * [ZhiPuAI](zhipuai-image.html)



# ZhiPuAI Image Generation

### ZhiPuAI Image Generation

  * Prerequisites
  * Add Repositories and BOM
  * Auto-configuration
  * Image Generation Properties
  * Runtime Options



Spring AI supports CogView, the Image generation model from ZhiPuAI.

## Prerequisites

You will need to create an API with ZhiPuAI to access ZhiPu AI language models.

Create an account at [ZhiPu AI registration page](https://open.bigmodel.cn/login) and generate the token on the [API Keys page](https://open.bigmodel.cn/usercenter/apikeys).

The Spring AI project defines a configuration property named `spring.ai.zhipuai.api-key` that you should set to the value of the `API Key` obtained from the API Keys page.

You can set this configuration property in your `application.properties` file:
    
    
    spring.ai.zhipuai.api-key=<your-zhipuai-api-key>
    
    Copied!

For enhanced security when handling sensitive information like API keys, you can use Spring Expression Language (SpEL) to reference a custom environment variable:
    
    
    # In application.yml
    spring:
      ai:
        zhipuai:
          api-key: ${ZHIPUAI_API_KEY}
    
    Copied!
    
    
    # In your environment or .env file
    export ZHIPUAI_API_KEY=<your-zhipuai-api-key>
    
    Copied!

You can also set this configuration programmatically in your application code:
    
    
    // Retrieve API key from a secure source or environment variable
    String apiKey = System.getenv("ZHIPUAI_API_KEY");
    
    Copied!

### Add Repositories and BOM

Spring AI artifacts are published in Maven Central and Spring Snapshot repositories. Refer to the [Artifact Repositories](../../getting-started.html#artifact-repositories) section to add these repositories to your build system.

To help with dependency management, Spring AI provides a BOM (bill of materials) to ensure that a consistent version of Spring AI is used throughout the entire project. Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build system.

## Auto-configuration

__ |  There has been a significant change in the Spring AI auto-configuration, starter modules' artifact names. Please refer to the [upgrade notes](https://docs.spring.io/spring-ai/reference/upgrade-notes.html) for more information.  
---|---  
  
Spring AI provides Spring Boot auto-configuration for the ZhiPuAI Chat Client. To enable it add the following dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-model-zhipuai</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-starter-model-zhipuai'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
### Image Generation Properties

__ |  Enabling and disabling of the image auto-configurations are now configured via top level properties with the prefix `spring.ai.model.image`. To enable, spring.ai.model.image=stabilityai (It is enabled by default) To disable, spring.ai.model.image=none (or any value which doesn’t match stabilityai) This change is done to allow configuration of multiple models.  
---|---  
  
The prefix `spring.ai.zhipuai.image` is the property prefix that lets you configure the `ImageModel` implementation for ZhiPuAI.

Property | Description | Default  
---|---|---  
spring.ai.zhipuai.image.enabled (Removed and no longer valid) | Enable ZhiPuAI image model. | true  
spring.ai.model.image | Enable ZhiPuAI image model. | zhipuai  
spring.ai.zhipuai.image.base-url | Optional overrides the spring.ai.zhipuai.base-url to provide chat specific url | -  
spring.ai.zhipuai.image.api-key | Optional overrides the spring.ai.zhipuai.api-key to provide chat specific api-key | -  
spring.ai.zhipuai.image.options.model | The model to use for image generation. | cogview-3  
spring.ai.zhipuai.image.options.user | A unique identifier representing your end-user, which can help ZhiPuAI to monitor and detect abuse. | -  
  
#### Connection Properties

The prefix `spring.ai.zhipuai` is used as the property prefix that lets you connect to ZhiPuAI.

Property | Description | Default  
---|---|---  
spring.ai.zhipuai.base-url | The URL to connect to | [open.bigmodel.cn/api/paas](https://open.bigmodel.cn/api/paas)  
spring.ai.zhipuai.api-key | The API Key | -  
  
#### Configuration Properties

#### Retry Properties

The prefix `spring.ai.retry` is used as the property prefix that lets you configure the retry mechanism for the ZhiPuAI Image client.

Property | Description | Default  
---|---|---  
spring.ai.retry.max-attempts | Maximum number of retry attempts. | 10  
spring.ai.retry.backoff.initial-interval | Initial sleep duration for the exponential backoff policy. | 2 sec.  
spring.ai.retry.backoff.multiplier | Backoff interval multiplier. | 5  
spring.ai.retry.backoff.max-interval | Maximum backoff duration. | 3 min.  
spring.ai.retry.on-client-errors | If false, throw a NonTransientAiException, and do not attempt retry for `4xx` client error codes | false  
spring.ai.retry.exclude-on-http-codes | List of HTTP status codes that should not trigger a retry (e.g. to throw NonTransientAiException). | empty  
spring.ai.retry.on-http-codes | List of HTTP status codes that should trigger a retry (e.g. to throw TransientAiException). | empty  
  
## Runtime Options

The [ZhiPuAiImageOptions.java](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-zhipuai/src/main/java/org/springframework/ai/zhipuai/ZhiPuAiImageOptions.java) provides model configurations, such as the model to use, the quality, the size, etc.

On start-up, the default options can be configured with the `ZhiPuAiImageModel(ZhiPuAiImageApi zhiPuAiImageApi)` constructor and the `withDefaultOptions(ZhiPuAiImageOptions defaultOptions)` method. Alternatively, use the `spring.ai.zhipuai.image.options.*` properties described previously.

At runtime you can override the default options by adding new, request specific, options to the `ImagePrompt` call. For example to override the ZhiPuAI specific options such as quality and the number of images to create, use the following code example:
    
    
    ImageResponse response = zhiPuAiImageModel.call(
            new ImagePrompt("A light cream colored mini golden doodle",
            ZhiPuAiImageOptions.builder()
                    .quality("hd")
                    .N(4)
                    .height(1024)
                    .width(1024).build())
    
    );
    
    Copied!

__ |  In addition to the model specific [ZhiPuAiImageOptions](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-zhipuai/src/main/java/org/springframework/ai/zhipuai/ZhiPuAiImageOptions.java) you can use a portable [ImageOptions](https://github.com/spring-projects/spring-ai/blob/main/spring-ai-model/src/main/java/org/springframework/ai/image/ImageOptions.java) instance, created with the [ImageOptionsBuilder#builder()](https://github.com/spring-projects/spring-ai/blob/main/spring-ai-model/src/main/java/org/springframework/ai/image/ImageOptionsBuilder.java).   
---|---  
  
[Stability](stabilityai-image.html) [QianFan](qianfan-image.html)
---
