Title: QianFan Image :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/image/qianfan-image.html
HTML: html/docs_spring_io/spring-ai/reference/api/image/Qian<PERSON>an_Image_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/image/Qian<PERSON>an_Image_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:19:22.560648
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/image/qianfan-image.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * [Image Models](../imageclient.html)
  * [<PERSON>an<PERSON><PERSON>](qianfan-image.html)



# Qian<PERSON>an Image

This functionality has been moved to the Spring AI Community repository.

Please visit [github.com/spring-ai-community/qianfan](https://github.com/spring-ai-community/qianfan) for the latest version.

[ZhiPuAI](zhipuai-image.html) [Transcription API](../audio/transcriptions.html)
---
