Title: Spring AI API :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/index.html
HTML: html/docs_spring_io/spring-ai/reference/api/Spring_AI_API_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/Spring_AI_API_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:19:48.598731
---
Search CTRL + k

### Spring AI API

  * Introduction
  * AI Model API
  * Vector Store API
  * Tool Calling API
  * Auto Configuration
  * ETL Data Engineering
  * Feedback and Contributions



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../index.html)
  * Reference
  * [Models](index.html)



# Spring AI API

### Spring AI API

  * Introduction
  * AI Model API
  * Vector Store API
  * Tool Calling API
  * Auto Configuration
  * ETL Data Engineering
  * Feedback and Contributions



## Introduction

The Spring AI API covers a wide range of functionalities. Each major feature is detailed in its own dedicated section. To provide an overview, the following key functionalities are available:

### AI Model API

Portable `Model API` across AI providers for `Chat`, `Text to Image`, `Audio Transcription`, `Text to Speech`, and `Embedding` models. Both `synchronous` and `stream` API options are supported. Dropping down to access model specific features is also supported.

![Model hierarchy](../_images/model-hierarchy.jpg)

With support for AI Models from OpenAI, Microsoft, Amazon, Google, Amazon Bedrock, Hugging Face and more.

![spring ai chat completions clients](../_images/spring-ai-chat-completions-clients.jpg)

### Vector Store API

Portable `Vector Store API` across multiple providers, including a novel `SQL-like metadata filter API` that is also portable. Support for 14 vector databases are available.

### Tool Calling API

Spring AI makes it easy to have the AI model invoke your services as `@Tool`-annotated methods or POJO `java.util.Function` objects.

![The main sequence of actions for tool calling](../_images/tools/tool-calling-01.jpg)

Check the Spring AI [Tool Calling](tools.html) documentation.

### Auto Configuration

Spring Boot Auto Configuration and Starters for AI Models and Vector Stores.

### ETL Data Engineering

ETL framework for Data Engineering. This provides the basis of loading data into a vector database, helping implement the Retrieval Augmented Generation pattern that enables you to bring your data to the AI model to incorporate into its response.

![etl pipeline](../_images/etl-pipeline.jpg)

## Feedback and Contributions

The project’s [GitHub discussions](https://github.com/spring-projects/spring-ai/discussions) is a great place to send feedback.

[Multimodality](multimodality.html) [Chat Models](chatmodel.html)
---
