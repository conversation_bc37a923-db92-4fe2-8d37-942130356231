Title: Typesense :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/vectordbs/typesense.html
HTML: html/docs_spring_io/spring-ai/reference/api/vectordbs/Typesense_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/vectordbs/Typesense_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:19:07.000276
---
Search CTRL + k

### Typesense

  * Prerequisites
  * Auto-configuration
  * Configuration Properties
  * Manual Configuration
  * Metadata Filtering
  * Accessing the Native Client



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/vectordbs/typesense.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Vector Databases](../vectordbs.html)
  * [Typesense](typesense.html)



# Typesense

### Typesense

  * Prerequisites
  * Auto-configuration
  * Configuration Properties
  * Manual Configuration
  * Metadata Filtering
  * Accessing the Native Client



This section walks you through setting up `TypesenseVectorStore` to store document embeddings and perform similarity searches.

[Typesense](https://typesense.org) is an open source typo tolerant search engine that is optimized for instant sub-50ms searches while providing an intuitive developer experience. It provides vector search capabilities that allow you to store and query high-dimensional vectors alongside your regular search data.

## Prerequisites

  * A running Typesense instance. The following options are available:

    * [Typesense Cloud](https://typesense.org/docs/guide/install-typesense.html) (recommended)

    * [Docker](https://hub.docker.com/r/typesense/typesense/) image _typesense/typesense:latest_

  * If required, an API key for the [EmbeddingModel](../embeddings.html#available-implementations) to generate the embeddings stored by the `TypesenseVectorStore`.




## Auto-configuration

__ |  There has been a significant change in the Spring AI auto-configuration, starter modules' artifact names. Please refer to the [upgrade notes](https://docs.spring.io/spring-ai/reference/upgrade-notes.html) for more information.  
---|---  
  
Spring AI provides Spring Boot auto-configuration for the Typesense Vector Store. To enable it add the following dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-vector-store-typesense</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-starter-vector-store-typesense'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
Please have a look at the list of configuration parameters for the vector store to learn about the default values and configuration options.

__ |  Refer to the [Artifact Repositories](../../getting-started.html#artifact-repositories) section to add Maven Central and/or Snapshot Repositories to your build file.   
---|---  
  
The vector store implementation can initialize the requisite schema for you but you must opt-in by setting `…​initialize-schema=true` in the `application.properties` file.

Additionally you will need a configured `EmbeddingModel` bean. Refer to the [EmbeddingModel](../embeddings.html#available-implementations) section for more information.

Now you can auto-wire the `TypesenseVectorStore` as a vector store in your application:
    
    
    @Autowired VectorStore vectorStore;
    
    // ...
    
    List<Document> documents = List.of(
        new Document("Spring AI rocks!! Spring AI rocks!! Spring AI rocks!! Spring AI rocks!! Spring AI rocks!!", Map.of("meta1", "meta1")),
        new Document("The World is Big and Salvation Lurks Around the Corner"),
        new Document("You walk forward facing the past and you turn back toward the future.", Map.of("meta2", "meta2")));
    
    // Add the documents to Typesense
    vectorStore.add(documents);
    
    // Retrieve documents similar to a query
    List<Document> results = vectorStore.similaritySearch(SearchRequest.builder().query("Spring").topK(5).build());
    
    Copied!

### Configuration Properties

To connect to Typesense and use the `TypesenseVectorStore` you need to provide access details for your instance. A simple configuration can be provided via Spring Boot’s `application.yml`:
    
    
    spring:
      ai:
        vectorstore:
          typesense:
            initialize-schema: true
            collection-name: vector_store
            embedding-dimension: 1536
            client:
              protocol: http
              host: localhost
              port: 8108
              api-key: xyz
    
    Copied!

Properties starting with `spring.ai.vectorstore.typesense.*` are used to configure the `TypesenseVectorStore`:

Property | Description | Default Value  
---|---|---  
`spring.ai.vectorstore.typesense.initialize-schema` | Whether to initialize the required schema | `false`  
`spring.ai.vectorstore.typesense.collection-name` | The name of the collection to store vectors | `vector_store`  
`spring.ai.vectorstore.typesense.embedding-dimension` | The number of dimensions in the vector | `1536`  
`spring.ai.vectorstore.typesense.client.protocol` | HTTP Protocol | `http`  
`spring.ai.vectorstore.typesense.client.host` | Hostname | `localhost`  
`spring.ai.vectorstore.typesense.client.port` | Port | `8108`  
`spring.ai.vectorstore.typesense.client.api-key` | API Key | `xyz`  
  
## Manual Configuration

Instead of using the Spring Boot auto-configuration you can manually configure the Typesense vector store. For this you need to add the `spring-ai-typesense-store` to your project:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-typesense-store</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-typesense-store'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
Create a Typesense `Client` bean:
    
    
    @Bean
    public Client typesenseClient() {
        List<Node> nodes = new ArrayList<>();
        nodes.add(new Node("http", "localhost", "8108"));
        Configuration configuration = new Configuration(nodes, Duration.ofSeconds(5), "xyz");
        return new Client(configuration);
    }
    
    Copied!

Then create the `TypesenseVectorStore` bean using the builder pattern:
    
    
    @Bean
    public VectorStore vectorStore(Client client, EmbeddingModel embeddingModel) {
        return TypesenseVectorStore.builder(client, embeddingModel)
            .collectionName("custom_vectors")     // Optional: defaults to "vector_store"
            .embeddingDimension(1536)            // Optional: defaults to 1536
            .initializeSchema(true)              // Optional: defaults to false
            .batchingStrategy(new TokenCountBatchingStrategy()) // Optional: defaults to TokenCountBatchingStrategy
            .build();
    }
    
    // This can be any EmbeddingModel implementation
    @Bean
    public EmbeddingModel embeddingModel() {
        return new OpenAiEmbeddingModel(new OpenAiApi(System.getenv("OPENAI_API_KEY")));
    }
    
    Copied!

## Metadata Filtering

You can leverage the generic portable [metadata filters](../vectordbs.html#metadata-filters) with Typesense store as well.

For example you can use either the text expression language:
    
    
    vectorStore.similaritySearch(
        SearchRequest.builder()
            .query("The World")
            .topK(TOP_K)
            .similarityThreshold(SIMILARITY_THRESHOLD)
            .filterExpression("country in ['UK', 'NL'] && year >= 2020").build());
    
    Copied!

or programmatically using the `Filter.Expression` DSL:
    
    
    FilterExpressionBuilder b = new FilterExpressionBuilder();
    
    vectorStore.similaritySearch(SearchRequest.builder()
        .query("The World")
        .topK(TOP_K)
        .similarityThreshold(SIMILARITY_THRESHOLD)
        .filterExpression(b.and(
            b.in("country", "UK", "NL"),
            b.gte("year", 2020)).build()).build());
    
    Copied!

__ |  Those (portable) filter expressions get automatically converted into [Typesense Search Filters](https://typesense.org/docs/0.24.0/api/search.html#filter-parameters).   
---|---  
  
For example this portable filter expression:
    
    
    country in ['UK', 'NL'] && year >= 2020
    
    Copied!

is converted into the proprietary Typesense filter format:
    
    
    country: ['UK', 'NL'] && year: >=2020
    
    Copied!

__ |  If you are not retrieving the documents in the expected order or the search results are not as expected, check the embedding model you are using. Embedding models can have a significant impact on the search results (i.e. make sure if your data is in Spanish to use a Spanish or multilingual embedding model).  
---|---  
  
## Accessing the Native Client

The Typesense Vector Store implementation provides access to the underlying native Typesense client (`Client`) through the `getNativeClient()` method:
    
    
    TypesenseVectorStore vectorStore = context.getBean(TypesenseVectorStore.class);
    Optional<Client> nativeClient = vectorStore.getNativeClient();
    
    if (nativeClient.isPresent()) {
        Client client = nativeClient.get();
        // Use the native client for Typesense-specific operations
    }
    
    Copied!

The native client gives you access to Typesense-specific features and operations that might not be exposed through the `VectorStore` interface.

[SAP Hana](hana.html) [Weaviate](weaviate.html)
---
