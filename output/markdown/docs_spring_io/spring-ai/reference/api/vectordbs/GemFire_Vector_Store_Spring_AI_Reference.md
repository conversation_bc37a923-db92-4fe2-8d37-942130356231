Title: GemFire Vector Store :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/vectordbs/gemfire.html
HTML: html/docs_spring_io/spring-ai/reference/api/vectordbs/GemFire_Vector_Store_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/vectordbs/GemFire_Vector_Store_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:15:27.395619
---
Search CTRL + k

### GemFire Vector Store

  * Prerequisites
  * Auto-configuration
  * Configuration properties
  * Manual Configuration
  * Usage



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/vectordbs/gemfire.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Vector Databases](../vectordbs.html)
  * [GemFire](gemfire.html)



# GemFire Vector Store

### GemFire Vector Store

  * Prerequisites
  * Auto-configuration
  * Configuration properties
  * Manual Configuration
  * Usage



This section walks you through setting up the `GemFireVectorStore` to store document embeddings and perform similarity searches.

[GemFire](https://tanzu.vmware.com/gemfire) is a distributed, in-memory, key-value store performing read and write operations at blazingly fast speeds. It offers highly available parallel message queues, continuous availability, and an event-driven architecture you can scale dynamically without downtime. As your data size requirements increase to support high-performance, real-time apps, GemFire can easily scale linearly.

[GemFire VectorDB](https://docs.vmware.com/en/VMware-GemFire-VectorDB/1.0/gemfire-vectordb/overview.html) extends GemFire’s capabilities, serving as a versatile vector database that efficiently stores, retrieves, and performs vector similarity searches.

## Prerequisites

  1. A GemFire cluster with the GemFire VectorDB extension enabled

     * [Install GemFire VectorDB extension](https://docs.vmware.com/en/VMware-GemFire-VectorDB/1.0/gemfire-vectordb/install.html)

  2. An `EmbeddingModel` bean to compute the document embeddings. Refer to the [EmbeddingModel](../embeddings.html#available-implementations) section for more information. An option that runs locally on your machine is [ONNX](../embeddings/onnx.html) and the all-MiniLM-L6-v2 Sentence Transformers.




## Auto-configuration

__ |  There has been a significant change in the Spring AI auto-configuration, starter modules' artifact names. Please refer to the [upgrade notes](https://docs.spring.io/spring-ai/reference/upgrade-notes.html) for more information.  
---|---  
  
Add the GemFire VectorStore Spring Boot starter to you project’s Maven build file `pom.xml`:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-vector-store-gemfire</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` file
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-starter-vector-store-gemfire'
    }
    
    Copied!

### Configuration properties

You can use the following properties in your Spring Boot configuration to further configure the `GemFireVectorStore`.

Property | Default value  
---|---  
`spring.ai.vectorstore.gemfire.host` | localhost  
`spring.ai.vectorstore.gemfire.port` | 8080  
`spring.ai.vectorstore.gemfire.initialize-schema` | `false`  
`spring.ai.vectorstore.gemfire.index-name` | spring-ai-gemfire-store  
`spring.ai.vectorstore.gemfire.beam-width` | 100  
`spring.ai.vectorstore.gemfire.max-connections` | 16  
`spring.ai.vectorstore.gemfire.vector-similarity-function` | COSINE  
`spring.ai.vectorstore.gemfire.fields` | []  
`spring.ai.vectorstore.gemfire.buckets` | 0  
  
## Manual Configuration

To use just the `GemFireVectorStore`, without Spring Boot’s Auto-configuration add the following dependency to your project’s Maven `pom.xml`:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-gemfire-store</artifactId>
    </dependency>
    
    Copied!

For Gradle users, add the following to your `build.gradle` file under the dependencies block to use just the `GemFireVectorStore`:
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-gemfire-store'
    }

## Usage

Here is a sample that creates an instance of the `GemfireVectorStore` instead of using AutoConfiguration
    
    
    @Bean
    public GemFireVectorStore vectorStore(EmbeddingModel embeddingModel) {
        return GemFireVectorStore.builder(embeddingModel)
            .host("localhost")
            .port(7071)
            .indexName("my-vector-index")
            .initializeSchema(true)
            .build();
    }
    
    Copied!

__ |  The GemFire VectorStore does not yet support [metadata filters](../vectordbs.html#metadata-filters).  
---|---  
  
__ |  The default configuration connects to a GemFire cluster at `localhost:8080`  
---|---  
  
  * In your application, create a few documents:



    
    
    List<Document> documents = List.of(
       new Document("Spring AI rocks!! Spring AI rocks!! Spring AI rocks!! Spring AI rocks!! Spring AI rocks!!", Map.of("country", "UK", "year", 2020)),
       new Document("The World is Big and Salvation Lurks Around the Corner", Map.of()),
       new Document("You walk forward facing the past and you turn back toward the future.", Map.of("country", "NL", "year", 2023)));
    
    Copied!

  * Add the documents to the vector store:



    
    
    vectorStore.add(documents);
    
    Copied!

  * And to retrieve documents using similarity search:



    
    
    List<Document> results = vectorStore.similaritySearch(
       SearchRequest.builder().query("Spring").topK(5).build());
    
    Copied!

You should retrieve the document containing the text "Spring AI rocks!!".

You can also limit the number of results using a similarity threshold:
    
    
    List<Document> results = vectorStore.similaritySearch(
       SearchRequest.builder().query("Spring").topK(5)
          .similarityThreshold(0.5d).build());
    
    Copied!

[Elasticsearch](elasticsearch.html) [MariaDB Vector Store](mariadb.html)
---
