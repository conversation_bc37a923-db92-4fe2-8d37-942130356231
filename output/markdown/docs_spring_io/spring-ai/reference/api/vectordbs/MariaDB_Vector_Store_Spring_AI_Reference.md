Title: MariaDB Vector Store :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/vectordbs/mariadb.html
HTML: html/docs_spring_io/spring-ai/reference/api/vectordbs/MariaDB_Vector_Store_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/vectordbs/MariaDB_Vector_Store_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:20:39.507685
---
Search CTRL + k

### MariaDB Vector Store

  * Prerequisites
  * Auto-Configuration
  * Configuration Properties
  * Manual Configuration
  * Metadata Filtering
  * Accessing the Native Client



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/vectordbs/mariadb.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Vector Databases](../vectordbs.html)
  * [MariaDB Vector Store](mariadb.html)



# MariaDB Vector Store

### MariaDB Vector Store

  * Prerequisites
  * Auto-Configuration
  * Configuration Properties
  * Manual Configuration
  * Metadata Filtering
  * Accessing the Native Client



This section walks you through setting up `MariaDBVectorStore` to store document embeddings and perform similarity searches.

[MariaDB Vector](https://mariadb.org/projects/mariadb-vector/) is part of MariaDB 11.7 and enables storing and searching over machine learning-generated embeddings. It provides efficient vector similarity search capabilities using vector indexes, supporting both cosine similarity and Euclidean distance metrics.

## Prerequisites

  * A running MariaDB (11.7+) instance. The following options are available:

    * [Docker](https://hub.docker.com/_/mariadb) image

    * [MariaDB Server](https://mariadb.org/download/)

    * [MariaDB SkySQL](https://mariadb.com/products/skysql/)

  * If required, an API key for the [EmbeddingModel](../embeddings.html#available-implementations) to generate the embeddings stored by the `MariaDBVectorStore`.




## Auto-Configuration

__ |  There has been a significant change in the Spring AI auto-configuration, starter modules' artifact names. Please refer to the [upgrade notes](https://docs.spring.io/spring-ai/reference/upgrade-notes.html) for more information.  
---|---  
  
Spring AI provides Spring Boot auto-configuration for the MariaDB Vector Store. To enable it, add the following dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-vector-store-mariadb</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-starter-vector-store-mariadb'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
The vector store implementation can initialize the required schema for you, but you must opt-in by specifying the `initializeSchema` boolean in the appropriate constructor or by setting `…​initialize-schema=true` in the `application.properties` file.

__ |  This is a breaking change! In earlier versions of Spring AI, this schema initialization happened by default.   
---|---  
  
Additionally, you will need a configured `EmbeddingModel` bean. Refer to the [EmbeddingModel](../embeddings.html#available-implementations) section for more information.

For example, to use the [OpenAI EmbeddingModel](../embeddings/openai-embeddings.html), add the following dependency:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-model-openai</artifactId>
    </dependency>
    
    Copied!

__ |  Refer to the [Artifact Repositories](../../getting-started.html#artifact-repositories) section to add Maven Central and/or Snapshot Repositories to your build file.   
---|---  
  
Now you can auto-wire the `MariaDBVectorStore` in your application:
    
    
    @Autowired VectorStore vectorStore;
    
    // ...
    
    List<Document> documents = List.of(
        new Document("Spring AI rocks!! Spring AI rocks!! Spring AI rocks!! Spring AI rocks!! Spring AI rocks!!", Map.of("meta1", "meta1")),
        new Document("The World is Big and Salvation Lurks Around the Corner"),
        new Document("You walk forward facing the past and you turn back toward the future.", Map.of("meta2", "meta2")));
    
    // Add the documents to MariaDB
    vectorStore.add(documents);
    
    // Retrieve documents similar to a query
    List<Document> results = vectorStore.similaritySearch(SearchRequest.builder().query("Spring").topK(5).build());
    
    Copied!

### Configuration Properties

To connect to MariaDB and use the `MariaDBVectorStore`, you need to provide access details for your instance. A simple configuration can be provided via Spring Boot’s `application.yml`:
    
    
    spring:
      datasource:
        url: ***************************
        username: myUser
        password: myPassword
      ai:
        vectorstore:
          mariadb:
            initialize-schema: true
            distance-type: COSINE
            dimensions: 1536
    
    Copied!

__ |  If you run MariaDB Vector as a Spring Boot dev service via [Docker Compose](https://docs.spring.io/spring-boot/reference/features/dev-services.html#features.dev-services.docker-compose) or [Testcontainers](https://docs.spring.io/spring-boot/reference/features/dev-services.html#features.dev-services.testcontainers), you don’t need to configure URL, username and password since they are autoconfigured by Spring Boot.   
---|---  
  
Properties starting with `spring.ai.vectorstore.mariadb.*` are used to configure the `MariaDBVectorStore`:

Property | Description | Default Value  
---|---|---  
`spring.ai.vectorstore.mariadb.initialize-schema` | Whether to initialize the required schema | `false`  
`spring.ai.vectorstore.mariadb.distance-type` | Search distance type. Use `COSINE` (default) or `EUCLIDEAN`. If vectors are normalized to length 1, you can use `EUCLIDEAN` for best performance. | `COSINE`  
`spring.ai.vectorstore.mariadb.dimensions` | Embeddings dimension. If not specified explicitly, will retrieve dimensions from the provided `EmbeddingModel`. | `1536`  
`spring.ai.vectorstore.mariadb.remove-existing-vector-store-table` | Deletes the existing vector store table on startup. | `false`  
`spring.ai.vectorstore.mariadb.schema-name` | Vector store schema name | `null`  
`spring.ai.vectorstore.mariadb.table-name` | Vector store table name | `vector_store`  
`spring.ai.vectorstore.mariadb.schema-validation` | Enables schema and table name validation to ensure they are valid and existing objects. | `false`  
  
__ |  If you configure a custom schema and/or table name, consider enabling schema validation by setting `spring.ai.vectorstore.mariadb.schema-validation=true`. This ensures the correctness of the names and reduces the risk of SQL injection attacks.   
---|---  
  
## Manual Configuration

Instead of using the Spring Boot auto-configuration, you can manually configure the MariaDB vector store. For this you need to add the following dependencies to your project:
    
    
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jdbc</artifactId>
    </dependency>
    
    <dependency>
        <groupId>org.mariadb.jdbc</groupId>
        <artifactId>mariadb-java-client</artifactId>
        <scope>runtime</scope>
    </dependency>
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-mariadb-store</artifactId>
    </dependency>
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
Then create the `MariaDBVectorStore` bean using the builder pattern:
    
    
    @Bean
    public VectorStore vectorStore(JdbcTemplate jdbcTemplate, EmbeddingModel embeddingModel) {
        return MariaDBVectorStore.builder(jdbcTemplate, embeddingModel)
            .dimensions(1536)                      // Optional: defaults to 1536
            .distanceType(MariaDBDistanceType.COSINE) // Optional: defaults to COSINE
            .schemaName("mydb")                    // Optional: defaults to null
            .vectorTableName("custom_vectors")     // Optional: defaults to "vector_store"
            .contentFieldName("text")             // Optional: defaults to "content"
            .embeddingFieldName("embedding")      // Optional: defaults to "embedding"
            .idFieldName("doc_id")                // Optional: defaults to "id"
            .metadataFieldName("meta")           // Optional: defaults to "metadata"
            .initializeSchema(true)               // Optional: defaults to false
            .schemaValidation(true)              // Optional: defaults to false
            .removeExistingVectorStoreTable(false) // Optional: defaults to false
            .maxDocumentBatchSize(10000)         // Optional: defaults to 10000
            .build();
    }
    
    // This can be any EmbeddingModel implementation
    @Bean
    public EmbeddingModel embeddingModel() {
        return new OpenAiEmbeddingModel(new OpenAiApi(System.getenv("OPENAI_API_KEY")));
    }
    
    Copied!

## Metadata Filtering

You can leverage the generic, portable [metadata filters](../vectordbs.html#metadata-filters) with MariaDB Vector store.

For example, you can use either the text expression language:
    
    
    vectorStore.similaritySearch(
        SearchRequest.builder()
            .query("The World")
            .topK(TOP_K)
            .similarityThreshold(SIMILARITY_THRESHOLD)
            .filterExpression("author in ['john', 'jill'] && article_type == 'blog'").build());
    
    Copied!

or programmatically using the `Filter.Expression` DSL:
    
    
    FilterExpressionBuilder b = new FilterExpressionBuilder();
    
    vectorStore.similaritySearch(SearchRequest.builder()
        .query("The World")
        .topK(TOP_K)
        .similarityThreshold(SIMILARITY_THRESHOLD)
        .filterExpression(b.and(
            b.in("author", "john", "jill"),
            b.eq("article_type", "blog")).build()).build());
    
    Copied!

__ |  These filter expressions are automatically converted into the equivalent MariaDB JSON path expressions.   
---|---  
  
## Accessing the Native Client

The MariaDB Vector Store implementation provides access to the underlying native JDBC client (`JdbcTemplate`) through the `getNativeClient()` method:
    
    
    MariaDBVectorStore vectorStore = context.getBean(MariaDBVectorStore.class);
    Optional<JdbcTemplate> nativeClient = vectorStore.getNativeClient();
    
    if (nativeClient.isPresent()) {
        JdbcTemplate jdbc = nativeClient.get();
        // Use the native client for MariaDB-specific operations
    }
    
    Copied!

The native client gives you access to MariaDB-specific features and operations that might not be exposed through the `VectorStore` interface.

[GemFire](gemfire.html) [Milvus](milvus.html)
---
