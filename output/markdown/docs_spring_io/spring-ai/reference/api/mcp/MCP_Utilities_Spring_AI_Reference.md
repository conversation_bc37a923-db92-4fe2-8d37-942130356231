Title: MCP Utilities :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/mcp/mcp-helpers.html
HTML: html/docs_spring_io/spring-ai/reference/api/mcp/MCP_Utilities_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/mcp/MCP_Utilities_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:19:40.639094
---
Search CTRL + k

### MCP Utilities

  * ToolCallback Utility
  * Tool Callback Adapter
  * Tool Callback Providers
  * McpToolUtils
  * ToolCallbacks to ToolSpecifications
  * MCP Clients to ToolCallbacks
  * Native Image Support



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/mcp/mcp-helpers.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Model Context Protocol (MCP)](mcp-overview.html)
  * [MCP Utilities](mcp-helpers.html)



# MCP Utilities

### MCP Utilities

  * ToolCallback Utility
  * Tool Callback Adapter
  * Tool Callback Providers
  * McpToolUtils
  * ToolCallbacks to ToolSpecifications
  * MCP Clients to ToolCallbacks
  * Native Image Support



The MCP utilities provide foundational support for integrating Model Context Protocol with Spring AI applications. These utilities enable seamless communication between Spring AI’s tool system and MCP servers, supporting both synchronous and asynchronous operations. They are typically used for programmatic MCP Client and Server configuration and interaction. For a more streamlined configuration, consider using the boot starters.

## ToolCallback Utility

### Tool Callback Adapter

Adapts MCP tools to Spring AI’s tool interface with both synchronous and asynchronous execution support.

  * Sync

  * Async



    
    
    McpSyncClient mcpClient = // obtain MCP client
    Tool mcpTool = // obtain MCP tool definition
    ToolCallback callback = new SyncMcpToolCallback(mcpClient, mcpTool);
    
    // Use the tool through Spring AI's interfaces
    ToolDefinition definition = callback.getToolDefinition();
    String result = callback.call("{\"param\": \"value\"}");
    
    Copied!
    
    
    McpAsyncClient mcpClient = // obtain MCP client
    Tool mcpTool = // obtain MCP tool definition
    ToolCallback callback = new AsyncMcpToolCallback(mcpClient, mcpTool);
    
    // Use the tool through Spring AI's interfaces
    ToolDefinition definition = callback.getToolDefinition();
    String result = callback.call("{\"param\": \"value\"}");
    
    Copied!

### Tool Callback Providers

Discovers and provides MCP tools from MCP clients.

  * Sync

  * Async



    
    
    McpSyncClient mcpClient = // obtain MCP client
    ToolCallbackProvider provider = new SyncMcpToolCallbackProvider(mcpClient);
    
    // Get all available tools
    ToolCallback[] tools = provider.getToolCallbacks();
    
    Copied!

For multiple clients:
    
    
    List<McpSyncClient> clients = // obtain list of clients
    List<ToolCallback> callbacks = SyncMcpToolCallbackProvider.syncToolCallbacks(clients);
    
    Copied!
    
    
    McpAsyncClient mcpClient = // obtain MCP client
    ToolCallbackProvider provider = new AsyncMcpToolCallbackProvider(mcpClient);
    
    // Get all available tools
    ToolCallback[] tools = provider.getToolCallbacks();
    
    Copied!

For multiple clients:
    
    
    List<McpAsyncClient> clients = // obtain list of clients
    Flux<ToolCallback> callbacks = AsyncMcpToolCallbackProvider.asyncToolCallbacks(clients);
    
    Copied!

## McpToolUtils

### ToolCallbacks to ToolSpecifications

Converting Spring AI tool callbacks to MCP tool specifications:

  * Sync

  * Async



    
    
    List<ToolCallback> toolCallbacks = // obtain tool callbacks
    List<SyncToolSpecifications> syncToolSpecs = McpToolUtils.toSyncToolSpecifications(toolCallbacks);
    
    Copied!

then you can use the `McpServer.SyncSpecification` to register the tool specifications:
    
    
    McpServer.SyncSpecification syncSpec = ...
    syncSpec.tools(syncToolSpecs);
    
    Copied!
    
    
    List<ToolCallback> toolCallbacks = // obtain tool callbacks
    List<AsyncToolSpecification> asyncToolSpecifications = McpToolUtils.toAsyncToolSpecifications(toolCallbacks);
    
    Copied!

then you can use the `McpServer.AsyncSpecification` to register the tool specifications:
    
    
    McpServer.AsyncSpecification asyncSpec = ...
    asyncSpec.tools(asyncToolSpecifications);
    
    Copied!

### MCP Clients to ToolCallbacks

Getting tool callbacks from MCP clients

  * Sync

  * Async



    
    
    List<McpSyncClient> syncClients = // obtain sync clients
    List<ToolCallback> syncCallbacks = McpToolUtils.getToolCallbacksFromSyncClients(syncClients);
    
    Copied!
    
    
    List<McpAsyncClient> asyncClients = // obtain async clients
    List<ToolCallback> asyncCallbacks = McpToolUtils.getToolCallbacksFromAsyncClients(asyncClients);
    
    Copied!

## Native Image Support

The `McpHints` class provides GraalVM native image hints for MCP schema classes. This class automatically registers all necessary reflection hints for MCP schema classes when building native images.

[MCP Server Boot Starters](mcp-server-boot-starter-docs.html) [Retrieval Augmented Generation (RAG)](../retrieval-augmented-generation.html)
---
