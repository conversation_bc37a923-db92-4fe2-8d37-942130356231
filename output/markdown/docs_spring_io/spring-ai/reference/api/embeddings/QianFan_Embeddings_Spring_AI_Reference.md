Title: Qi<PERSON><PERSON>an Embeddings :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/embeddings/qianfan-embeddings.html
HTML: html/docs_spring_io/spring-ai/reference/api/embeddings/Qian<PERSON>an_Embeddings_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/embeddings/Qian<PERSON>an_Embeddings_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:18:35.071764
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/embeddings/qianfan-embeddings.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * [Embedding Models](../embeddings.html)
  * [Qian<PERSON>an](qianfan-embeddings.html)



# Qian<PERSON>an Embeddings

This functionality has been moved to the Spring AI Community repository.

Please visit [github.com/spring-ai-community/qianfan](https://github.com/spring-ai-community/qianfan) for the latest version.

[PostgresML](postgresml-embeddings.html) [Text Embedding](vertexai-embeddings-text.html)
---
