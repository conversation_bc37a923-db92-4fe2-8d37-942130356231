Title: PostgresML Embeddings :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/embeddings/postgresml-embeddings.html
HTML: html/docs_spring_io/spring-ai/reference/api/embeddings/PostgresML_Embeddings_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/embeddings/PostgresML_Embeddings_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:15:01.504084
---
Search CTRL + k

### PostgresML Embeddings

  * Add Repositories and BOM
  * Auto-configuration
  * Embedding Properties
  * Runtime Options
  * Sample Controller
  * Manual configuration



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/embeddings/postgresml-embeddings.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * [Embedding Models](../embeddings.html)
  * [PostgresML](postgresml-embeddings.html)



# PostgresML Embeddings

### PostgresML Embeddings

  * Add Repositories and BOM
  * Auto-configuration
  * Embedding Properties
  * Runtime Options
  * Sample Controller
  * Manual configuration



Spring AI supports the PostgresML text embeddings models.

Embeddings are a numeric representation of text. They are used to represent words and sentences as vectors, an array of numbers. Embeddings can be used to find similar pieces of text, by comparing the similarity of the numeric vectors using a distance measure, or they can be used as input features for other machine learning models, since most algorithms can’t use text directly.

Many pre-trained LLMs can be used to generate embeddings from text within PostgresML. You can browse all the [models](https://huggingface.co/models?library=sentence-transformers) available to find the best solution on Hugging Face.

## Add Repositories and BOM

Spring AI artifacts are published in Maven Central and Spring Snapshot repositories. Refer to the [Artifact Repositories](../../getting-started.html#artifact-repositories) section to add these repositories to your build system.

To help with dependency management, Spring AI provides a BOM (bill of materials) to ensure that a consistent version of Spring AI is used throughout the entire project. Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build system.

## Auto-configuration

__ |  There has been a significant change in the Spring AI auto-configuration, starter modules' artifact names. Please refer to the [upgrade notes](https://docs.spring.io/spring-ai/reference/upgrade-notes.html) for more information.  
---|---  
  
Spring AI provides Spring Boot auto-configuration for the Azure PostgresML Embedding Model. To enable it add the following dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-model-postgresml-embedding</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-starter-model-postgresml-embedding'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
Use the `spring.ai.postgresml.embedding.options.*` properties to configure your `PostgresMlEmbeddingModel`. links

### Embedding Properties

__ |  Enabling and disabling of the embedding auto-configurations are now configured via top level properties with the prefix `spring.ai.model.embedding`. To enable, spring.ai.model.embedding=postgresml (It is enabled by default) To disable, spring.ai.model.embedding=none (or any value which doesn’t match postgresml) This change is done to allow configuration of multiple models.  
---|---  
  
The prefix `spring.ai.postgresml.embedding` is property prefix that configures the `EmbeddingModel` implementation for PostgresML embeddings.

Property | Description | Default  
---|---|---  
spring.ai.postgresml.embedding.enabled (Removed and no longer valid) | Enable PostgresML embedding model. | true  
spring.ai.model.embedding | Enable PostgresML embedding model. | postgresml  
spring.ai.postgresml.embedding.create-extension | Execute the SQL 'CREATE EXTENSION IF NOT EXISTS pgml' to enable the extesnion | false  
spring.ai.postgresml.embedding.options.transformer | The Hugging Face transformer model to use for the embedding. | distilbert-base-uncased  
spring.ai.postgresml.embedding.options.kwargs | Additional transformer specific options. | empty map  
spring.ai.postgresml.embedding.options.vectorType | PostgresML vector type to use for the embedding. Two options are supported: `PG_ARRAY` and `PG_VECTOR`. | PG_ARRAY  
spring.ai.postgresml.embedding.options.metadataMode | Document metadata aggregation mode | EMBED  
  
__ |  All properties prefixed with `spring.ai.postgresml.embedding.options` can be overridden at runtime by adding a request specific Runtime Options to the `EmbeddingRequest` call.   
---|---  
  
## Runtime Options

Use the [PostgresMlEmbeddingOptions.java](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-openai/src/main/java/org/springframework/ai/postgresml/PostgresMlEmbeddingOptions.java) to configure the `PostgresMlEmbeddingModel` with options, such as the model to use and etc.

On start you can pass a `PostgresMlEmbeddingOptions` to the `PostgresMlEmbeddingModel` constructor to configure the default options used for all embedding requests.

At run-time you can override the default options, using a `PostgresMlEmbeddingOptions` in your `EmbeddingRequest`.

For example to override the default model name for a specific request:
    
    
    EmbeddingResponse embeddingResponse = embeddingModel.call(
        new EmbeddingRequest(List.of("Hello World", "World is big and salvation is near"),
                PostgresMlEmbeddingOptions.builder()
                    .transformer("intfloat/e5-small")
                    .vectorType(VectorType.PG_ARRAY)
                    .kwargs(Map.of("device", "gpu"))
                    .build()));
    
    Copied!

## Sample Controller

This will create a `EmbeddingModel` implementation that you can inject into your class. Here is an example of a simple `@Controller` class that uses the `EmbeddingModel` implementation.
    
    
    spring.ai.postgresml.embedding.options.transformer=distilbert-base-uncased
    spring.ai.postgresml.embedding.options.vectorType=PG_ARRAY
    spring.ai.postgresml.embedding.options.metadataMode=EMBED
    spring.ai.postgresml.embedding.options.kwargs.device=cpu
    
    Copied!
    
    
    @RestController
    public class EmbeddingController {
    
        private final EmbeddingModel embeddingModel;
    
        @Autowired
        public EmbeddingController(EmbeddingModel embeddingModel) {
            this.embeddingModel = embeddingModel;
        }
    
        @GetMapping("/ai/embedding")
        public Map embed(@RequestParam(value = "message", defaultValue = "Tell me a joke") String message) {
            EmbeddingResponse embeddingResponse = this.embeddingModel.embedForResponse(List.of(message));
            return Map.of("embedding", embeddingResponse);
        }
    }
    
    Copied!

## Manual configuration

Instead of using the Spring Boot auto-configuration, you can create the `PostgresMlEmbeddingModel` manually. For this add the `spring-ai-postgresml` dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-postgresml</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-postgresml'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
Next, create an `PostgresMlEmbeddingModel` instance and use it to compute the similarity between two input texts:
    
    
    var jdbcTemplate = new JdbcTemplate(dataSource); // your posgresml data source
    
    PostgresMlEmbeddingModel embeddingModel = new PostgresMlEmbeddingModel(this.jdbcTemplate,
            PostgresMlEmbeddingOptions.builder()
                .transformer("distilbert-base-uncased") // huggingface transformer model name.
                .vectorType(VectorType.PG_VECTOR) //vector type in PostgreSQL.
                .kwargs(Map.of("device", "cpu")) // optional arguments.
                .metadataMode(MetadataMode.EMBED) // Document metadata mode.
                .build());
    
    embeddingModel.afterPropertiesSet(); // initialize the jdbc template and database.
    
    EmbeddingResponse embeddingResponse = this.embeddingModel
    	.embedForResponse(List.of("Hello World", "World is big and salvation is near"));
    
    Copied!

__ |  When created manually, you must call the `afterPropertiesSet()` after setting the properties and before using the client. It is more convenient (and preferred) to create the PostgresMlEmbeddingModel as a `@Bean`. Then you don’t have to call the `afterPropertiesSet()` manually:   
---|---  
      
    
    @Bean
    public EmbeddingModel embeddingModel(JdbcTemplate jdbcTemplate) {
        return new PostgresMlEmbeddingModel(jdbcTemplate,
            PostgresMlEmbeddingOptions.builder()
                 ....
                .build());
    }
    
    Copied!

[OpenAI](openai-embeddings.html) [QianFan](qianfan-embeddings.html)
---
