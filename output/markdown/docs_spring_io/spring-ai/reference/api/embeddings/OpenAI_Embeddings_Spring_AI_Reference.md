Title: OpenAI Embeddings :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/embeddings/openai-embeddings.html
HTML: html/docs_spring_io/spring-ai/reference/api/embeddings/OpenAI_Embeddings_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/embeddings/OpenAI_Embeddings_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:19:58.378947
---
Search CTRL + k

### OpenAI Embeddings

  * Prerequisites
  * Add Repositories and BOM
  * Auto-configuration
  * Embedding Properties
  * Runtime Options
  * Sample Controller
  * Manual Configuration



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/embeddings/openai-embeddings.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * [Embedding Models](../embeddings.html)
  * [OpenAI](openai-embeddings.html)



# OpenAI Embeddings

### OpenAI Embeddings

  * Prerequisites
  * Add Repositories and BOM
  * Auto-configuration
  * Embedding Properties
  * Runtime Options
  * Sample Controller
  * Manual Configuration



Spring AI supports the OpenAI’s text embeddings models. OpenAI’s text embeddings measure the relatedness of text strings. An embedding is a vector (list) of floating point numbers. The distance between two vectors measures their relatedness. Small distances suggest high relatedness and large distances suggest low relatedness.

## Prerequisites

You will need to create an API with OpenAI to access OpenAI embeddings models.

Create an account at [OpenAI signup page](https://platform.openai.com/signup) and generate the token on the [API Keys page](https://platform.openai.com/account/api-keys).

The Spring AI project defines a configuration property named `spring.ai.openai.api-key` that you should set to the value of the `API Key` obtained from openai.com.

You can set this configuration property in your `application.properties` file:
    
    
    spring.ai.openai.api-key=<your-openai-api-key>
    
    Copied!

For enhanced security when handling sensitive information like API keys, you can use Spring Expression Language (SpEL) to reference an environment variable:
    
    
    # In application.yml
    spring:
      ai:
        openai:
          api-key: ${OPENAI_API_KEY}
    
    Copied!
    
    
    # In your environment or .env file
    export OPENAI_API_KEY=<your-openai-api-key>
    
    Copied!

You can also set this configuration programmatically in your application code:
    
    
    // Retrieve API key from a secure source or environment variable
    String apiKey = System.getenv("OPENAI_API_KEY");
    
    Copied!

### Add Repositories and BOM

Spring AI artifacts are published in Maven Central and Spring Snapshot repositories. Refer to the [Artifact Repositories](../../getting-started.html#artifact-repositories) section to add these repositories to your build system.

To help with dependency management, Spring AI provides a BOM (bill of materials) to ensure that a consistent version of Spring AI is used throughout the entire project. Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build system.

## Auto-configuration

__ |  There has been a significant change in the Spring AI auto-configuration, starter modules' artifact names. Please refer to the [upgrade notes](https://docs.spring.io/spring-ai/reference/upgrade-notes.html) for more information.  
---|---  
  
Spring AI provides Spring Boot auto-configuration for the OpenAI Embedding Model. To enable it add the following dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-model-openai</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-starter-model-openai'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
### Embedding Properties

#### Retry Properties

The prefix `spring.ai.retry` is used as the property prefix that lets you configure the retry mechanism for the OpenAI Embedding model.

Property | Description | Default  
---|---|---  
spring.ai.retry.max-attempts | Maximum number of retry attempts. | 10  
spring.ai.retry.backoff.initial-interval | Initial sleep duration for the exponential backoff policy. | 2 sec.  
spring.ai.retry.backoff.multiplier | Backoff interval multiplier. | 5  
spring.ai.retry.backoff.max-interval | Maximum backoff duration. | 3 min.  
spring.ai.retry.on-client-errors | If false, throw a NonTransientAiException, and do not attempt retry for `4xx` client error codes | false  
spring.ai.retry.exclude-on-http-codes | List of HTTP status codes that should not trigger a retry (e.g. to throw NonTransientAiException). | empty  
spring.ai.retry.on-http-codes | List of HTTP status codes that should trigger a retry (e.g. to throw TransientAiException). | empty  
  
#### Connection Properties

The prefix `spring.ai.openai` is used as the property prefix that lets you connect to OpenAI.

Property | Description | Default  
---|---|---  
spring.ai.openai.base-url | The URL to connect to | https://api.openai.com  
spring.ai.openai.api-key | The API Key | -  
spring.ai.openai.organization-id | Optionally you can specify which organization used for an API request. | -  
spring.ai.openai.project-id | Optionally, you can specify which project is used for an API request. | -  
  
__ |  For users that belong to multiple organizations (or are accessing their projects through their legacy user API key), optionally, you can specify which organization and project is used for an API request. Usage from these API requests will count as usage for the specified organization and project.   
---|---  
  
#### Configuration Properties

__ |  Enabling and disabling of the embedding auto-configurations are now configured via top level properties with the prefix `spring.ai.model.embedding`. To enable, spring.ai.model.embedding=openai (It is enabled by default) To disable, spring.ai.model.embedding=none (or any value which doesn’t match openai) This change is done to allow configuration of multiple models.  
---|---  
  
The prefix `spring.ai.openai.embedding` is property prefix that configures the `EmbeddingModel` implementation for OpenAI.

Property | Description | Default  
---|---|---  
spring.ai.openai.embedding.enabled (Required and no longer valid) | Enable OpenAI embedding model. | true  
spring.ai.model.embedding | Enable OpenAI embedding model. | openai  
spring.ai.openai.embedding.base-url | Optional overrides the spring.ai.openai.base-url to provide embedding specific url | -  
spring.ai.openai.embedding.embeddings-path | The path to append to the base-url | `/v1/embeddings`  
spring.ai.openai.embedding.api-key | Optional overrides the spring.ai.openai.api-key to provide embedding specific api-key | -  
spring.ai.openai.embedding.organization-id | Optionally you can specify which organization used for an API request. | -  
spring.ai.openai.embedding.project-id | Optionally, you can specify which project is used for an API request. | -  
spring.ai.openai.embedding.metadata-mode | Document content extraction mode. | EMBED  
spring.ai.openai.embedding.options.model | The model to use | text-embedding-ada-002 (other options: text-embedding-3-large, text-embedding-3-small)  
spring.ai.openai.embedding.options.encodingFormat | The format to return the embeddings in. Can be either float or base64. | -  
spring.ai.openai.embedding.options.user | A unique identifier representing your end-user, which can help OpenAI to monitor and detect abuse. | -  
spring.ai.openai.embedding.options.dimensions | The number of dimensions the resulting output embeddings should have. Only supported in `text-embedding-3` and later models. | -  
  
__ |  You can override the common `spring.ai.openai.base-url` and `spring.ai.openai.api-key` for the `ChatModel` and `EmbeddingModel` implementations. The `spring.ai.openai.embedding.base-url` and `spring.ai.openai.embedding.api-key` properties if set take precedence over the common properties. Similarly, the `spring.ai.openai.chat.base-url` and `spring.ai.openai.chat.api-key` properties if set take precedence over the common properties. This is useful if you want to use different OpenAI accounts for different models and different model endpoints.   
---|---  
  
__ |  All properties prefixed with `spring.ai.openai.embedding.options` can be overridden at runtime by adding a request specific Runtime Options to the `EmbeddingRequest` call.   
---|---  
  
## Runtime Options

The [OpenAiEmbeddingOptions.java](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-openai/src/main/java/org/springframework/ai/openai/OpenAiEmbeddingOptions.java) provides the OpenAI configurations, such as the model to use and etc.

The default options can be configured using the `spring.ai.openai.embedding.options` properties as well.

At start-time use the `OpenAiEmbeddingModel` constructor to set the default options used for all embedding requests. At run-time you can override the default options, using a `OpenAiEmbeddingOptions` instance as part of your `EmbeddingRequest`.

For example to override the default model name for a specific request:
    
    
    EmbeddingResponse embeddingResponse = embeddingModel.call(
        new EmbeddingRequest(List.of("Hello World", "World is big and salvation is near"),
            OpenAiEmbeddingOptions.builder()
                .model("Different-Embedding-Model-Deployment-Name")
            .build()));
    
    Copied!

## Sample Controller

This will create a `EmbeddingModel` implementation that you can inject into your class. Here is an example of a simple `@Controller` class that uses the `EmbeddingModel` implementation.
    
    
    spring.ai.openai.api-key=YOUR_API_KEY
    spring.ai.openai.embedding.options.model=text-embedding-ada-002
    
    Copied!
    
    
    @RestController
    public class EmbeddingController {
    
        private final EmbeddingModel embeddingModel;
    
        @Autowired
        public EmbeddingController(EmbeddingModel embeddingModel) {
            this.embeddingModel = embeddingModel;
        }
    
        @GetMapping("/ai/embedding")
        public Map embed(@RequestParam(value = "message", defaultValue = "Tell me a joke") String message) {
            EmbeddingResponse embeddingResponse = this.embeddingModel.embedForResponse(List.of(message));
            return Map.of("embedding", embeddingResponse);
        }
    }
    
    Copied!

## Manual Configuration

If you are not using Spring Boot, you can manually configure the OpenAI Embedding Model. For this add the `spring-ai-openai` dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-openai</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-openai'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
__ |  The `spring-ai-openai` dependency provides access also to the `OpenAiChatModel`. For more information about the `OpenAiChatModel` refer to the [OpenAI Chat Client](../chat/openai-chat.html) section.   
---|---  
  
Next, create an `OpenAiEmbeddingModel` instance and use it to compute the similarity between two input texts:
    
    
    var openAiApi = OpenAiApi.builder()
                    .apiKey(System.getenv("OPENAI_API_KEY"))
                    .build();
    
    var embeddingModel = new OpenAiEmbeddingModel(
    		this.openAiApi,
            MetadataMode.EMBED,
            OpenAiEmbeddingOptions.builder()
                    .model("text-embedding-ada-002")
                    .user("user-6")
                    .build(),
            RetryUtils.DEFAULT_RETRY_TEMPLATE);
    
    EmbeddingResponse embeddingResponse = this.embeddingModel
            .embedForResponse(List.of("Hello World", "World is big and salvation is near"));
    
    Copied!

The `OpenAiEmbeddingOptions` provides the configuration information for the embedding requests. The api and options class offers a `builder()` for easy options creation.

[(ONNX) Transformers](onnx.html) [PostgresML](postgresml-embeddings.html)
---
