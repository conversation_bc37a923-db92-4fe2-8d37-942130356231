Title: Mistral AI Embeddings :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/embeddings/mistralai-embeddings.html
HTML: html/docs_spring_io/spring-ai/reference/api/embeddings/Mistral_AI_Embeddings_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/embeddings/Mistral_AI_Embeddings_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:15:56.172384
---
Search CTRL + k

### Mistral AI Embeddings

  * Prerequisites
  * Add Repositories and BOM
  * Auto-configuration
  * Embedding Properties
  * Runtime Options
  * Sample Controller
  * Manual Configuration



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/embeddings/mistralai-embeddings.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * [Embedding Models](../embeddings.html)
  * [Mistral AI](mistralai-embeddings.html)



# Mistral AI Embeddings

### Mistral AI Embeddings

  * Prerequisites
  * Add Repositories and BOM
  * Auto-configuration
  * Embedding Properties
  * Runtime Options
  * Sample Controller
  * Manual Configuration



Spring AI supports the Mistral AI’s text embeddings models. Embeddings are vectorial representations of text that capture the semantic meaning of paragraphs through their position in a high dimensional vector space. Mistral AI Embeddings API offers cutting-edge, state-of-the-art embeddings for text, which can be used for many NLP tasks.

## Prerequisites

You will need to create an API with MistralAI to access MistralAI embeddings models.

Create an account at [MistralAI registration page](https://auth.mistral.ai/ui/registration) and generate the token on the [API Keys page](https://console.mistral.ai/api-keys/).

The Spring AI project defines a configuration property named `spring.ai.mistralai.api-key` that you should set to the value of the `API Key` obtained from console.mistral.ai.

You can set this configuration property in your `application.properties` file:
    
    
    spring.ai.mistralai.api-key=<your-mistralai-api-key>
    
    Copied!

For enhanced security when handling sensitive information like API keys, you can use Spring Expression Language (SpEL) to reference an environment variable:
    
    
    # In application.yml
    spring:
      ai:
        mistralai:
          api-key: ${MISTRALAI_API_KEY}
    
    Copied!
    
    
    # In your environment or .env file
    export MISTRALAI_API_KEY=<your-mistralai-api-key>
    
    Copied!

You can also set this configuration programmatically in your application code:
    
    
    // Retrieve API key from a secure source or environment variable
    String apiKey = System.getenv("MISTRALAI_API_KEY");
    
    Copied!

### Add Repositories and BOM

Spring AI artifacts are published in Maven Central and Spring Snapshot repositories. Refer to the [Artifact Repositories](../../getting-started.html#artifact-repositories) section to add these repositories to your build system.

To help with dependency management, Spring AI provides a BOM (bill of materials) to ensure that a consistent version of Spring AI is used throughout the entire project. Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build system.

## Auto-configuration

__ |  There has been a significant change in the Spring AI auto-configuration, starter modules' artifact names. Please refer to the [upgrade notes](https://docs.spring.io/spring-ai/reference/upgrade-notes.html) for more information.  
---|---  
  
Spring AI provides Spring Boot auto-configuration for the MistralAI Embedding Model. To enable it add the following dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-model-mistral-ai</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-starter-model-mistral-ai'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
### Embedding Properties

#### Retry Properties

The prefix `spring.ai.retry` is used as the property prefix that lets you configure the retry mechanism for the Mistral AI Embedding model.

Property | Description | Default  
---|---|---  
spring.ai.retry.max-attempts | Maximum number of retry attempts. | 10  
spring.ai.retry.backoff.initial-interval | Initial sleep duration for the exponential backoff policy. | 2 sec.  
spring.ai.retry.backoff.multiplier | Backoff interval multiplier. | 5  
spring.ai.retry.backoff.max-interval | Maximum backoff duration. | 3 min.  
spring.ai.retry.on-client-errors | If false, throw a NonTransientAiException, and do not attempt retry for `4xx` client error codes | false  
spring.ai.retry.exclude-on-http-codes | List of HTTP status codes that should not trigger a retry (e.g. to throw NonTransientAiException). | empty  
spring.ai.retry.on-http-codes | List of HTTP status codes that should trigger a retry (e.g. to throw TransientAiException). | empty  
  
#### Connection Properties

The prefix `spring.ai.mistralai` is used as the property prefix that lets you connect to MistralAI.

Property | Description | Default  
---|---|---  
spring.ai.mistralai.base-url | The URL to connect to | [api.mistral.ai](https://api.mistral.ai)  
spring.ai.mistralai.api-key | The API Key | -  
  
#### Configuration Properties

__ |  Enabling and disabling of the embedding auto-configurations are now configured via top level properties with the prefix `spring.ai.model.embedding`. To enable, spring.ai.model.embedding=mistral (It is enabled by default) To disable, spring.ai.model.embedding=none (or any value which doesn’t match mistral) This change is done to allow configuration of multiple models.  
---|---  
  
The prefix `spring.ai.mistralai.embedding` is property prefix that configures the `EmbeddingModel` implementation for MistralAI.

Property | Description | Default  
---|---|---  
spring.ai.mistralai.embedding.enabled (Removed and no longer valid) | Enable OpenAI embedding model. | true  
spring.ai.model.embedding | Enable OpenAI embedding model. | true  
spring.ai.mistralai.embedding.base-url | Optional overrides the spring.ai.mistralai.base-url to provide embedding specific url | -  
spring.ai.mistralai.embedding.api-key | Optional overrides the spring.ai.mistralai.api-key to provide embedding specific api-key | -  
spring.ai.mistralai.embedding.metadata-mode | Document content extraction mode. | EMBED  
spring.ai.mistralai.embedding.options.model | The model to use | mistral-embed  
spring.ai.mistralai.embedding.options.encodingFormat | The format to return the embeddings in. Can be either float or base64. | -  
  
__ |  You can override the common `spring.ai.mistralai.base-url` and `spring.ai.mistralai.api-key` for the `ChatModel` and `EmbeddingModel` implementations. The `spring.ai.mistralai.embedding.base-url` and `spring.ai.mistralai.embedding.api-key` properties if set take precedence over the common properties. Similarly, the `spring.ai.mistralai.chat.base-url` and `spring.ai.mistralai.chat.api-key` properties if set take precedence over the common properties. This is useful if you want to use different MistralAI accounts for different models and different model endpoints.   
---|---  
  
__ |  All properties prefixed with `spring.ai.mistralai.embedding.options` can be overridden at runtime by adding a request specific Runtime Options to the `EmbeddingRequest` call.   
---|---  
  
## Runtime Options

The [MistralAiEmbeddingOptions.java](https://github.com/spring-projects/spring-ai/blob/main/models/spring-ai-mistral-ai/src/main/java/org/springframework/ai/mistralai/MistralAiEmbeddingOptions.java) provides the MistralAI configurations, such as the model to use and etc.

The default options can be configured using the `spring.ai.mistralai.embedding.options` properties as well.

At start-time use the `MistralAiEmbeddingModel` constructor to set the default options used for all embedding requests. At run-time you can override the default options, using a `MistralAiEmbeddingOptions` instance as part of your `EmbeddingRequest`.

For example to override the default model name for a specific request:
    
    
    EmbeddingResponse embeddingResponse = embeddingModel.call(
        new EmbeddingRequest(List.of("Hello World", "World is big and salvation is near"),
            MistralAiEmbeddingOptions.builder()
                .withModel("Different-Embedding-Model-Deployment-Name")
            .build()));
    
    Copied!

## Sample Controller

This will create a `EmbeddingModel` implementation that you can inject into your class. Here is an example of a simple `@Controller` class that uses the `EmbeddingModel` implementation.
    
    
    spring.ai.mistralai.api-key=YOUR_API_KEY
    spring.ai.mistralai.embedding.options.model=mistral-embed
    
    Copied!
    
    
    @RestController
    public class EmbeddingController {
    
        private final EmbeddingModel embeddingModel;
    
        @Autowired
        public EmbeddingController(EmbeddingModel embeddingModel) {
            this.embeddingModel = embeddingModel;
        }
    
        @GetMapping("/ai/embedding")
        public Map embed(@RequestParam(value = "message", defaultValue = "Tell me a joke") String message) {
            var embeddingResponse = this.embeddingModel.embedForResponse(List.of(message));
            return Map.of("embedding", embeddingResponse);
        }
    }
    
    Copied!

## Manual Configuration

If you are not using Spring Boot, you can manually configure the OpenAI Embedding Model. For this add the `spring-ai-mistral-ai` dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-mistral-ai</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-mistral-ai'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
__ |  The `spring-ai-mistral-ai` dependency provides access also to the `MistralAiChatModel`. For more information about the `MistralAiChatModel` refer to the [MistralAI Chat Client](../chat/mistralai-chat.html) section.   
---|---  
  
Next, create an `MistralAiEmbeddingModel` instance and use it to compute the similarity between two input texts:
    
    
    var mistralAiApi = new MistralAiApi(System.getenv("MISTRAL_AI_API_KEY"));
    
    var embeddingModel = new MistralAiEmbeddingModel(this.mistralAiApi,
            MistralAiEmbeddingOptions.builder()
                    .withModel("mistral-embed")
                    .withEncodingFormat("float")
                    .build());
    
    EmbeddingResponse embeddingResponse = this.embeddingModel
            .embedForResponse(List.of("Hello World", "World is big and salvation is near"));
    
    Copied!

The `MistralAiEmbeddingOptions` provides the configuration information for the embedding requests. The options class offers a `builder()` for easy options creation.

[Azure OpenAI](azure-openai-embeddings.html) [MiniMax](minimax-embeddings.html)
---
