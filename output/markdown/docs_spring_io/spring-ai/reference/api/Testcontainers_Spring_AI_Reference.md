Title: Testcontainers :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/api/testcontainers.html
HTML: html/docs_spring_io/spring-ai/reference/api/Testcontainers_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/api/Testcontainers_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:17:44.065467
---
Search CTRL + k

### Testcontainers

  * Service Connections



[ Edit this Page ](https://github.com/spring-projects/spring-ai/blob/v1.0.0/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/testcontainers.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../index.html)
  * Reference
  * Testing
  * [Testcontainers](testcontainers.html)



# Testcontainers

### Testcontainers

  * Service Connections



Spring AI provides Spring Boot auto-configuration for establishing a connection to a model service or vector store running via Testcontainers. To enable it, add the following dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
       <groupId>org.springframework.ai</groupId>
       <artifactId>spring-ai-spring-boot-testcontainers</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-spring-boot-testcontainers'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
## Service Connections

The following service connection factories are provided in the `spring-ai-spring-boot-testcontainers` module:

Connection Details | Matched on  
---|---  
`AwsOpenSearchConnectionDetails` | Containers of type `LocalStackContainer`  
`ChromaConnectionDetails` | Containers of type `ChromaDBContainer`  
`MilvusServiceClientConnectionDetails` | Containers of type `MilvusContainer`  
`MongoConnectionDetails` | Containers of type `MongoDBAtlasLocalContainer`  
`OllamaConnectionDetails` | Containers of type `OllamaContainer`  
`OpenSearchConnectionDetails` | Containers of type `OpensearchContainer`  
`QdrantConnectionDetails` | Containers of type `QdrantContainer`  
`TypesenseConnectionDetails` | Containers of type `TypesenseContainer`  
`WeaviateConnectionDetails` | Containers of type `WeaviateContainer`  
  
[Development-time Services](docker-compose.html) [Prompt Engineering Patterns](chat/prompt-engineering-patterns.html)
---
