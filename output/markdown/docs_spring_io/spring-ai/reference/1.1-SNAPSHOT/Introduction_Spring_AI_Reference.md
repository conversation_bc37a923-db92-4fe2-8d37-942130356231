Title: Introduction :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/index.html
HTML: html/docs_spring_io/spring-ai/reference/1.1-SNAPSHOT/Introduction_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/1.1-SNAPSHOT/Introduction_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:14:49.974978
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-ai/edit/main/spring-ai-docs/src/main/antora/modules/ROOT/pages/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](index.html)
  * [Overview](index.html)



__ |  This version is still in development and is not considered stable yet. For the latest snapshot version, please use [Spring AI 1.0.0](../index.html)!  
---|---  
  
# Introduction

![Integration Problem](_images/spring_ai_logo_with_text.svg)

The `Spring AI` project aims to streamline the development of applications that incorporate artificial intelligence functionality without unnecessary complexity.

The project draws inspiration from notable Python projects, such as LangChain and LlamaIndex, but Spring AI is not a direct port of those projects. The project was founded with the belief that the next wave of Generative AI applications will not be only for Python developers but will be ubiquitous across many programming languages.

__ |  Spring AI addresses the fundamental challenge of AI integration: `Connecting your enterprise Data and APIs with AI Models`.   
---|---  
  
Interactive

Spring AI provides abstractions that serve as the foundation for developing AI applications. These abstractions have multiple implementations, enabling easy component swapping with minimal code changes.

Spring AI provides the following features:

  * Portable API support across AI providers for Chat, text-to-image, and Embedding models. Both synchronous and streaming API options are supported. Access to model-specific features is also available.

  * Support for all major [AI Model providers](api/index.html) such as Anthropic, OpenAI, Microsoft, Amazon, Google, and Ollama. Supported model types include:

    * [Chat Completion](api/chatmodel.html)

    * [Embedding](api/embeddings.html)

    * [Text to Image](api/imageclient.html)

    * [Audio Transcription](api/audio/transcriptions.html)

    * [Text to Speech](api/audio/speech.html)

    * Moderation

  * [Structured Outputs](api/structured-output-converter.html) \- Mapping of AI Model output to POJOs.

  * Support for all major [Vector Database providers](api/vectordbs.html) such as Apache Cassandra, Azure Cosmos DB, Azure Vector Search, Chroma, Elasticsearch, GemFire, MariaDB, Milvus, MongoDB Atlas, Neo4j, OpenSearch, Oracle, PostgreSQL/PGVector, PineCone, Qdrant, Redis, SAP Hana, Typesense and Weaviate.

  * Portable API across Vector Store providers, including a novel SQL-like metadata filter API.

  * [Tools/Function Calling](api/tools.html) \- Permits the model to request the execution of client-side tools and functions, thereby accessing necessary real-time information as required and taking action.

  * [Observability](observability/index.html) \- Provides insights into AI-related operations.

  * Document ingestion [ETL framework](api/etl-pipeline.html) for Data Engineering.

  * [AI Model Evaluation](api/testing.html) \- Utilities to help evaluate generated content and protect against hallucinated response.

  * Spring Boot Auto Configuration and Starters for AI Models and Vector Stores.

  * [ChatClient API](api/chatclient.html) \- Fluent API for communicating with AI Chat Models, idiomatically similar to the WebClient and RestClient APIs.

  * [Advisors API](api/advisors.html) \- Encapsulates recurring Generative AI patterns, transforms data sent to and from Language Models (LLMs), and provides portability across various models and use cases.

  * Support for [Chat Conversation Memory](api/chatclient.html#_chat_memory) and [Retrieval Augmented Generation (RAG)](api/chatclient.html#_retrieval_augmented_generation).




This feature set lets you implement common use cases, such as “Q&A over your documentation” or “Chat with your documentation.”

The [concepts section](concepts.html) provides a high-level overview of AI concepts and their representation in Spring AI.

The [Getting Started](getting-started.html) section shows you how to create your first AI application. Subsequent sections delve into each component and common use cases with a code-focused approach.

[AI Concepts](concepts.html)
---
