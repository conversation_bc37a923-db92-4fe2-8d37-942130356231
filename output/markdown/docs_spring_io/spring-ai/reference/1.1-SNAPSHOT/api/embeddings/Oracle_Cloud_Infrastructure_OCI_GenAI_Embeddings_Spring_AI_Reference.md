Title: Oracle Cloud Infrastructure (OCI) GenAI Embeddings :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/oci-genai-embeddings.html
HTML: html/docs_spring_io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/Oracle_Cloud_Infrastructure_OCI_GenAI_Embeddings_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/1.1-SNAPSHOT/api/embeddings/Oracle_Cloud_Infrastructure_OCI_GenAI_Embeddings_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:21:53.818594
---
Search CTRL + k

### Oracle Cloud Infrastructure (OCI) GenAI Embeddings

  * Prerequisites
  * Add Repositories and BOM
  * Auto-configuration
  * Embedding Properties
  * Runtime Options
  * Sample Code
  * Manual Configuration



[ Edit this Page ](https://github.com/spring-projects/spring-ai/edit/main/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/embeddings/oci-genai-embeddings.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * [Embedding Models](../embeddings.html)
  * [OCI GenAI](oci-genai-embeddings.html)



__ |  This version is still in development and is not considered stable yet. For the latest snapshot version, please use [Spring AI 1.0.0](../../../api/embeddings/oci-genai-embeddings.html)!  
---|---  
  
# Oracle Cloud Infrastructure (OCI) GenAI Embeddings

### Oracle Cloud Infrastructure (OCI) GenAI Embeddings

  * Prerequisites
  * Add Repositories and BOM
  * Auto-configuration
  * Embedding Properties
  * Runtime Options
  * Sample Code
  * Manual Configuration



[OCI GenAI Service](https://www.oracle.com/artificial-intelligence/generative-ai/generative-ai-service/) offers text embedding with on-demand models, or dedicated AI clusters.

The [OCI Embedding Models Page](https://docs.oracle.com/en-us/iaas/Content/generative-ai/embed-models.htm) and [OCI Text Embeddings Page](https://docs.oracle.com/en-us/iaas/Content/generative-ai/use-playground-embed.htm) provide detailed information about using and hosting embedding models on OCI.

## Prerequisites

### Add Repositories and BOM

Spring AI artifacts are published in Maven Central and Spring Snapshot repositories. Refer to the [Artifact Repositories](../../getting-started.html#artifact-repositories) section to add these repositories to your build system.

To help with dependency management, Spring AI provides a BOM (bill of materials) to ensure that a consistent version of Spring AI is used throughout the entire project. Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build system.

## Auto-configuration

__ |  There has been a significant change in the Spring AI auto-configuration, starter modules' artifact names. Please refer to the [upgrade notes](https://docs.spring.io/spring-ai/reference/upgrade-notes.html) for more information.  
---|---  
  
Spring AI provides Spring Boot auto-configuration for the OCI GenAI Embedding Client. To enable it add the following dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-model-oci-genai</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-starter-model-oci-genai'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
### Embedding Properties

The prefix `spring.ai.oci.genai` is the property prefix to configure the connection to OCI GenAI.

Property | Description | Default  
---|---|---  
spring.ai.oci.genai.authenticationType | The type of authentication to use when authenticating to OCI. May be `file`, `instance-principal`, `workload-identity`, or `simple`. | file  
spring.ai.oci.genai.region | OCI service region. | us-chicago-1  
spring.ai.oci.genai.tenantId | OCI tenant OCID, used when authenticating with `simple` auth. | -  
spring.ai.oci.genai.userId | OCI user OCID, used when authenticating with `simple` auth. | -  
spring.ai.oci.genai.fingerprint | Private key fingerprint, used when authenticating with `simple` auth. | -  
spring.ai.oci.genai.privateKey | Private key content, used when authenticating with `simple` auth. | -  
spring.ai.oci.genai.passPhrase | Optional private key passphrase, used when authenticating with `simple` auth and a passphrase protected private key. | -  
spring.ai.oci.genai.file | Path to OCI config file. Used when authenticating with `file` auth. | <user’s home directory>/.oci/config  
spring.ai.oci.genai.profile | OCI profile name. Used when authenticating with `file` auth. | DEFAULT  
spring.ai.oci.genai.endpoint | Optional OCI GenAI endpoint. | -  
  
__ |  Enabling and disabling of the embedding auto-configurations are now configured via top level properties with the prefix `spring.ai.model.embedding`. To enable, spring.ai.model.embedding=oci-genai (It is enabled by default) To disable, spring.ai.model.embedding=none (or any value which doesn’t match oci-genai) This change is done to allow configuration of multiple models.  
---|---  
  
The prefix `spring.ai.oci.genai.embedding` is the property prefix that configures the `EmbeddingModel` implementation for OCI GenAI

Property | Description | Default  
---|---|---  
spring.ai.oci.genai.embedding.enabled (Removed and no longer valid) | Enable OCI GenAI embedding model. | true  
spring.ai.model.embedding | Enable OCI GenAI embedding model. | oci-genai  
spring.ai.oci.genai.embedding.compartment | Model compartment OCID. | -  
spring.ai.oci.genai.embedding.servingMode | The model serving mode to be used. May be `on-demand`, or `dedicated`. | on-demand  
spring.ai.oci.genai.embedding.truncate | How to truncate text if it overruns the embedding context. May be `START`, or `END`. | END  
spring.ai.oci.genai.embedding.model | The model or model endpoint used for embeddings. | -  
  
__ |  All properties prefixed with `spring.ai.oci.genai.embedding.options` can be overridden at runtime by adding a request specific Runtime Options to the `EmbeddingRequest` call.   
---|---  
  
## Runtime Options

The `OCIEmbeddingOptions` provides the configuration information for the embedding requests. The `OCIEmbeddingOptions` offers a builder to create the options.

At start time use the `OCIEmbeddingOptions` constructor to set the default options used for all embedding requests. At run-time you can override the default options, by passing a `OCIEmbeddingOptions` instance with your to the `EmbeddingRequest` request.

For example to override the default model name for a specific request:
    
    
    EmbeddingResponse embeddingResponse = embeddingModel.call(
        new EmbeddingRequest(List.of("Hello World", "World is big and salvation is near"),
            OCIEmbeddingOptions.builder()
                .model("my-other-embedding-model")
                .build()
    ));
    
    Copied!

## Sample Code

This will create a `EmbeddingModel` implementation that you can inject into your class. Here is an example of a simple `@Controller` class that uses the `EmbeddingModel` implementation.
    
    
    spring.ai.oci.genai.embedding.model=<your model>
    spring.ai.oci.genai.embedding.compartment=<your model compartment>
    
    Copied!
    
    
    @RestController
    public class EmbeddingController {
    
        private final EmbeddingModel embeddingModel;
    
        @Autowired
        public EmbeddingController(EmbeddingModel embeddingModel) {
            this.embeddingModel = embeddingModel;
        }
    
        @GetMapping("/ai/embedding")
        public Map embed(@RequestParam(value = "message", defaultValue = "Tell me a joke") String message) {
            EmbeddingResponse embeddingResponse = this.embeddingModel.embedForResponse(List.of(message));
            return Map.of("embedding", embeddingResponse);
        }
    }
    
    Copied!

## Manual Configuration

If you prefer not to use the Spring Boot auto-configuration, you can manually configure the `OCIEmbeddingModel` in your application. For this add the `spring-oci-genai-openai` dependency to your project’s Maven `pom.xml` file:
    
    
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-oci-genai-openai</artifactId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-oci-genai-openai'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
Next, create an `OCIEmbeddingModel` instance and use it to compute the similarity between two input texts:
    
    
    final String EMBEDDING_MODEL = "cohere.embed-english-light-v2.0";
    final String CONFIG_FILE = Paths.get(System.getProperty("user.home"), ".oci", "config").toString();
    final String PROFILE = "DEFAULT";
    final String REGION = "us-chicago-1";
    final String COMPARTMENT_ID = System.getenv("OCI_COMPARTMENT_ID");
    
    var authProvider = new ConfigFileAuthenticationDetailsProvider(
    		this.CONFIG_FILE, this.PROFILE);
    var aiClient = GenerativeAiInferenceClient.builder()
        .region(Region.valueOf(this.REGION))
        .build(this.authProvider);
    var options = OCIEmbeddingOptions.builder()
        .model(this.EMBEDDING_MODEL)
        .compartment(this.COMPARTMENT_ID)
        .servingMode("on-demand")
        .build();
    var embeddingModel = new OCIEmbeddingModel(this.aiClient, this.options);
    List<Double> embedding = this.embeddingModel.embed(new Document("How many provinces are in Canada?"));
    
    Copied!

[MiniMax](minimax-embeddings.html) [Ollama](ollama-embeddings.html)
---
