Title: Amazon Bedrock :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/bedrock.html
HTML: html/docs_spring_io/spring-ai/reference/1.1-SNAPSHOT/api/Amazon_Bedrock_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/1.1-SNAPSHOT/api/Amazon_Bedrock_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:21:05.149945
---
Search CTRL + k

### Amazon Bedrock

  * Getting Started
  * Project Dependencies
  * Connect to AWS Bedrock
  * Enable selected Bedrock model



[ Edit this Page ](https://github.com/spring-projects/spring-ai/edit/main/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/bedrock.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../index.html)
  * Reference
  * [Models](index.html)
  * [Embedding Models](embeddings.html)
  * [Amazon Bedrock](bedrock.html)



__ |  This version is still in development and is not considered stable yet. For the latest snapshot version, please use [Spring AI 1.0.0](../../api/bedrock.html)!  
---|---  
  
# Amazon Bedrock

### Amazon Bedrock

  * Getting Started
  * Project Dependencies
  * Connect to AWS Bedrock
  * Enable selected Bedrock model



__ |  Following the Bedrock recommendations, Spring AI transitioned to using Amazon Bedrock’s Converse API for all Chat conversation implementations in Spring AI. The [Bedrock Converse API](chat/bedrock-converse.html) has the following key benefits:

  * Unified Interface: Write your code once and use it with any supported Amazon Bedrock model
  * Model Flexibility: Seamlessly switch between different conversation models without code changes
  * Extended Functionality: Support for model-specific parameters through dedicated structures
  * Tool Support: Native integration with function calling and tool usage capabilities
  * Multimodal Capabilities: Built-in support for vision and other multimodal features
  * Future-Proof: Aligned with Amazon Bedrock’s recommended best practices

The Converse API does not support embedding operations, so these will remain in the current API and the embedding model functionality in the existing `InvokeModel API` will be maintained  
---|---  
  
[Amazon Bedrock](https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-bedrock.html) is a managed service that provides foundation models from various AI providers, available through a unified API.

Spring AI supports [the Embedding AI models](https://docs.aws.amazon.com/bedrock/latest/userguide/model-ids-arns.html) available through Amazon Bedrock by implementing the Spring `EmbeddingModel` interface.

Additionally, Spring AI provides Spring Auto-Configurations and Boot Starters for all clients, making it easy to bootstrap and configure for the Bedrock models.

## Getting Started

There are a few steps to get started

  * Add the Spring Boot starter for Bedrock to your project.

  * Obtain AWS credentials: If you don’t have an AWS account and AWS CLI configured yet, this video guide can help you configure it: [AWS CLI & SDK Setup in Less Than 4 Minutes!](https://youtu.be/gswVHTrRX8I?si=buaY7aeI0l3-bBVb). You should be able to obtain your access and security keys.

  * Enable the Models to use: Go to [Amazon Bedrock](https://us-east-1.console.aws.amazon.com/bedrock/home) and from the [Model Access](https://us-east-1.console.aws.amazon.com/bedrock/home?region=us-east-1#/modelaccess) menu on the left, configure access to the models you are going to use.




### Project Dependencies

Then add the Spring Boot Starter dependency to your project’s Maven `pom.xml` build file:
    
    
    <dependency>
     <artifactId>spring-ai-starter-model-bedrock</artifactId>
     <groupId>org.springframework.ai</groupId>
    </dependency>
    
    Copied!

or to your Gradle `build.gradle` build file.
    
    
    dependencies {
        implementation 'org.springframework.ai:spring-ai-starter-model-bedrock'
    }
    
    Copied!

__ |  Refer to the [Dependency Management](../getting-started.html#dependency-management) section to add the Spring AI BOM to your build file.   
---|---  
  
### Connect to AWS Bedrock

Use the `BedrockAwsConnectionProperties` to configure AWS credentials and region:
    
    
    spring.ai.bedrock.aws.region=us-east-1
    
    spring.ai.bedrock.aws.access-key=YOUR_ACCESS_KEY
    spring.ai.bedrock.aws.secret-key=YOUR_SECRET_KEY
    
    spring.ai.bedrock.aws.timeout=10m
    
    Copied!

The `region` property is compulsory.

AWS credentials are resolved in the following order:

  1. Spring-AI Bedrock `spring.ai.bedrock.aws.access-key` and `spring.ai.bedrock.aws.secret-key` properties.

  2. Java System Properties - `aws.accessKeyId` and `aws.secretAccessKey`.

  3. Environment Variables - `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY`.

  4. Web Identity Token credentials from system properties or environment variables.

  5. Credential profiles file at the default location (`~/.aws/credentials`) shared by all AWS SDKs and the AWS CLI.

  6. Credentials delivered through the Amazon EC2 container service if the `AWS_CONTAINER_CREDENTIALS_RELATIVE_URI` environment variable is set and the security manager has permission to access the variable.

  7. Instance profile credentials delivered through the Amazon EC2 metadata service or set the `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY` environment variables.




AWS region is resolved in the following order:

  1. Spring-AI Bedrock `spring.ai.bedrock.aws.region` property.

  2. Java System Properties - `aws.region`.

  3. Environment Variables - `AWS_REGION`.

  4. Credential profiles file at the default location (`~/.aws/credentials`) shared by all AWS SDKs and the AWS CLI.

  5. Instance profile region delivered through the Amazon EC2 metadata service.




In addition to the standard Spring-AI Bedrock credentials and region properties configuration, Spring-AI provides support for custom `AwsCredentialsProvider` and `AwsRegionProvider` beans.

__ |  For example, using Spring-AI and [Spring Cloud for Amazon Web Services](https://spring.io/projects/spring-cloud-aws) at the same time. Spring-AI is compatible with Spring Cloud for Amazon Web Services credential configuration.   
---|---  
  
### Enable selected Bedrock model

__ |  By default, all models are disabled. You have to enable the chosen Bedrock models explicitly using the `spring.ai.bedrock.<model>.embedding.enabled=true` property.   
---|---  
  
Here are the supported `<model>`s:

Model  
---  
cohere  
titan (no batch support yet)  
  
For example, to enable the Bedrock Cohere embedding model, you need to set `spring.ai.bedrock.cohere.embedding.enabled=true`.

Next, you can use the `spring.ai.bedrock.<model>.embedding.*` properties to configure each model as provided.

For more information, refer to the documentation below for each supported model.

  * [Spring AI Bedrock Cohere Embeddings](embeddings/bedrock-cohere-embedding.html): `spring.ai.bedrock.cohere.embedding.enabled=true`

  * [Spring AI Bedrock Titan Embeddings](embeddings/bedrock-titan-embedding.html): `spring.ai.bedrock.titan.embedding.enabled=true`




[Embedding Models](embeddings.html) [Cohere](embeddings/bedrock-cohere-embedding.html)
---
