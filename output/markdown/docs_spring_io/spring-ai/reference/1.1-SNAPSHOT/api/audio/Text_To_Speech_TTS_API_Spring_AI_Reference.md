Title: Text-To-Speech (TTS) API :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/audio/speech.html
HTML: html/docs_spring_io/spring-ai/reference/1.1-SNAPSHOT/api/audio/Text_To_Speech_TTS_API_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/1.1-SNAPSHOT/api/audio/Text_To_Speech_TTS_API_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:21:11.680434
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-ai/edit/main/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/audio/speech.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * Audio Models
  * [Text-To-Speech (TTS) API](speech.html)



__ |  This version is still in development and is not considered stable yet. For the latest snapshot version, please use [Spring AI 1.0.0](../../../api/audio/speech.html)!  
---|---  
  
# Text-To-Speech (TTS) API

Spring AI provides support for OpenAI’s Speech API. When additional providers for Speech are implemented, a common `SpeechModel` and `StreamingSpeechModel` interface will be extracted.

[OpenAI](transcriptions/openai-transcriptions.html) [OpenAI](speech/openai-speech.html)
---
