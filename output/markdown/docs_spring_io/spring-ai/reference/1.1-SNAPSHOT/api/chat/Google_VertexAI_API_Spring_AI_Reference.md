Title: Google VertexAI API :: Spring AI Reference
Source: https://docs.spring.io/spring-ai/reference/1.1-SNAPSHOT/api/chat/google-vertexai.html
HTML: html/docs_spring_io/spring-ai/reference/1.1-SNAPSHOT/api/chat/Google_VertexAI_API_Spring_AI_Reference.html
Screenshot: screenshot/docs_spring_io/spring-ai/reference/1.1-SNAPSHOT/api/chat/Google_VertexAI_API_Spring_AI_Reference.png
crawled_at: 2025-06-04T18:21:56.594315
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-ai/edit/main/spring-ai-docs/src/main/antora/modules/ROOT/pages/api/chat/google-vertexai.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-ai "GitHub") [ Stack Overflow ](https://stackoverflow.com/questions/tagged/spring)

  * [Spring AI](../../index.html)
  * Reference
  * [Models](../index.html)
  * [Chat Models](../chatmodel.html)
  * [Google VertexAI](google-vertexai.html)



__ |  This version is still in development and is not considered stable yet. For the latest snapshot version, please use [Spring AI 1.0.0](../../../api/chat/google-vertexai.html)!  
---|---  
  
# Google VertexAI API

[VertexAI API](https://cloud.google.com/vertex-ai/docs/reference) provides high-quality custom machine learning models with minimal machine learning expertise and effort.

Spring AI provides integration with VertexAI API through the following client(s):

  * [VertexAI Gemini Chat](vertexai-gemini-chat.html)




[Docker Model Runner](dmr-chat.html) [VertexAI Gemini](vertexai-gemini-chat.html)
---
