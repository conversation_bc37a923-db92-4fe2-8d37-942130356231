Title: Upgrading Spring Boot :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/upgrading.html
HTML: html/docs_spring_io/spring-boot/3.4/Upgrading_Spring_Boot_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/Upgrading_Spring_Boot_Spring_Boot.png
crawled_at: 2025-06-04T16:05:41.694013
---
Search CTRL + k

### Upgrading Spring Boot

  * Upgrading From 1.x
  * Upgrading From 2.x
  * Upgrading to a New Feature Release
  * Upgrading the Spring Boot CLI



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/ROOT/pages/upgrading.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](index.html)
  * [Upgrading Spring Boot](upgrading.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../upgrading.html)!  
---|---  
  
# Upgrading Spring Boot

### Upgrading Spring Boot

  * Upgrading From 1.x
  * Upgrading From 2.x
  * Upgrading to a New Feature Release
  * Upgrading the Spring Boot CLI



Instructions for how to upgrade from earlier versions of Spring Boot are provided on the project [wiki](https://github.com/spring-projects/spring-boot/wiki). Follow the links in the [release notes](https://github.com/spring-projects/spring-boot/wiki#release-notes) section to find the version that you want to upgrade to.

Upgrading instructions are always the first item in the release notes. If you are more than one release behind, please make sure that you also review the release notes of the versions that you jumped.

## Upgrading From 1.x

If you are upgrading from the `1.x` release of Spring Boot, check the [migration guide](https://github.com/spring-projects/spring-boot/wiki/Spring-Boot-2.0-Migration-Guide) on the project wiki that provides detailed upgrade instructions to upgrade to Spring Boot 2.x. Check also the [release notes](https://github.com/spring-projects/spring-boot/wiki) for a list of “new and noteworthy” features for each release.

## Upgrading From 2.x

If you are upgrading from the `2.x` release of Spring Boot, check the [migration guide](https://github.com/spring-projects/spring-boot/wiki/Spring-Boot-3.0-Migration-Guide) on the project wiki that provides detailed upgrade instructions. Check also the [release notes](https://github.com/spring-projects/spring-boot/wiki) for a list of “new and noteworthy” features for each release.

## Upgrading to a New Feature Release

When upgrading to a new feature release, some properties may have been renamed or removed. Spring Boot provides a way to analyze your application’s environment and print diagnostics at startup, but also temporarily migrate properties at runtime for you. To enable that feature, add the following dependency to your project:
    
    
    <dependency>
    	<groupId>org.springframework.boot</groupId>
    	<artifactId>spring-boot-properties-migrator</artifactId>
    	<scope>runtime</scope>
    </dependency>
    
    Copied!

__ |  Properties that are added late to the environment, such as when using [`@PropertySource`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/PropertySource.html), will not be taken into account.   
---|---  
  
__ |  Once you finish the migration, please make sure to remove this module from your project’s dependencies.   
---|---  
  
## Upgrading the Spring Boot CLI

To upgrade an existing CLI installation, use the appropriate package manager command (for example, `brew upgrade`). If you manually installed the CLI, follow the [standard instructions](installing.html#getting-started.installing.cli.manual-installation), remembering to update your `PATH` environment variable to remove any older references.

[Installing Spring Boot](installing.html) [Tutorials](tutorial/index.html)
---
