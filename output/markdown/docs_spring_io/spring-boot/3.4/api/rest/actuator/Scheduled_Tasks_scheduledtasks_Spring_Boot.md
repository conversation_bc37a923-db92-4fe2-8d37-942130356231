Title: Scheduled Tasks (scheduledtasks) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/scheduledtasks.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Scheduled_Tasks_scheduledtasks_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Scheduled_Tasks_scheduledtasks_Spring_Boot.png
crawled_at: 2025-06-04T16:08:04.791982
---
Search CTRL + k

### Scheduled Tasks (scheduledtasks)

  * Retrieving the Scheduled Tasks
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/scheduledtasks.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Scheduled Tasks (`scheduledtasks`)](scheduledtasks.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/scheduledtasks.html)!  
---|---  
  
# Scheduled Tasks (`scheduledtasks`)

### Scheduled Tasks (scheduledtasks)

  * Retrieving the Scheduled Tasks
  * Response Structure



The `scheduledtasks` endpoint provides information about the application’s scheduled tasks.

## Retrieving the Scheduled Tasks

To retrieve the scheduled tasks, make a `GET` request to `/actuator/scheduledtasks`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/scheduledtasks' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 1220
    
    {
      "cron" : [ {
        "runnable" : {
          "target" : "com.example.Processor.processOrders"
        },
        "expression" : "0 0 0/3 1/1 * ?",
        "nextExecution" : {
          "time" : "2025-05-22T11:59:59.999594999Z"
        }
      } ],
      "fixedDelay" : [ {
        "runnable" : {
          "target" : "com.example.Processor.purge"
        },
        "initialDelay" : 0,
        "interval" : 5000,
        "nextExecution" : {
          "time" : "2025-05-22T10:00:15.714197677Z"
        },
        "lastExecution" : {
          "time" : "2025-05-22T10:00:10.703261703Z",
          "status" : "SUCCESS"
        }
      } ],
      "fixedRate" : [ {
        "runnable" : {
          "target" : "com.example.Processor.retrieveIssues"
        },
        "initialDelay" : 10000,
        "interval" : 3000,
        "nextExecution" : {
          "time" : "2025-05-22T10:00:20.695646557Z"
        }
      } ],
      "custom" : [ {
        "runnable" : {
          "target" : "com.example.Processor$CustomTriggeredRunnable@e4afa08"
        },
        "trigger" : "com.example.Processor$CustomTrigger@5a9fa35",
        "lastExecution" : {
          "exception" : {
            "message" : "Failed while running custom task",
            "type" : "java.lang.IllegalStateException"
          },
          "time" : "2025-05-22T10:00:10.755680937Z",
          "status" : "ERROR"
        }
      } ]
    }
    
    Copied!

### Response Structure

The response contains details of the application’s scheduled tasks. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`cron` | `Array` | Cron tasks, if any.  
`cron.[].runnable.target` | `String` | Target that will be executed.  
`cron.[].nextExecution.time` | `String` | Time of the next scheduled execution.  
`cron.[].expression` | `String` | Cron expression.  
`fixedDelay` | `Array` | Fixed delay tasks, if any.  
`fixedDelay.[].runnable.target` | `String` | Target that will be executed.  
`fixedDelay.[].initialDelay` | `Number` | Delay, in milliseconds, before first execution.  
`fixedDelay.[].nextExecution.time` | `String` | Time of the next scheduled execution, if known.  
`fixedDelay.[].interval` | `Number` | Interval, in milliseconds, between the end of the last execution and the start of the next.  
`fixedRate` | `Array` | Fixed rate tasks, if any.  
`fixedRate.[].runnable.target` | `String` | Target that will be executed.  
`fixedRate.[].interval` | `Number` | Interval, in milliseconds, between the start of each execution.  
`fixedRate.[].initialDelay` | `Number` | Delay, in milliseconds, before first execution.  
`fixedRate.[].nextExecution.time` | `String` | Time of the next scheduled execution, if known.  
`custom` | `Array` | Tasks with custom triggers, if any.  
`custom.[].runnable.target` | `String` | Target that will be executed.  
`custom.[].trigger` | `String` | Trigger for the task.  
`*.[].lastExecution` | `Object` | Last execution of this task, if any.  
`*.[].lastExecution.status` | `String` | Status of the last execution (STARTED, SUCCESS, ERROR).  
`*.[].lastExecution.time` | `String` | Time of the last execution.  
`*.[].lastExecution.exception.type` | `String` | Exception type thrown by the task, if any.  
`*.[].lastExecution.exception.message` | `String` | Message of the exception thrown by the task, if any.  
  
[Software Bill of Materials (`sbom`)](sbom.html) [Sessions (`sessions`)](sessions.html)
---
