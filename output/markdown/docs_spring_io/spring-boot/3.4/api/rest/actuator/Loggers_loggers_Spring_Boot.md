Title: Loggers (loggers) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/loggers.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Loggers_loggers_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Loggers_loggers_Spring_Boot.png
crawled_at: 2025-06-04T16:06:31.233218
---
Search CTRL + k

### Loggers (loggers)

  * Retrieving All Loggers
  * Response Structure
  * Retrieving a Single Logger
  * Response Structure
  * Retrieving a Single Group
  * Response Structure
  * Setting a Log Level
  * Request Structure
  * Setting a Log Level for a Group
  * Request Structure
  * Clearing a Log Level



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/loggers.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Loggers (`loggers`)](loggers.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/loggers.html)!  
---|---  
  
# Loggers (`loggers`)

### Loggers (loggers)

  * Retrieving All Loggers
  * Response Structure
  * Retrieving a Single Logger
  * Response Structure
  * Retrieving a Single Group
  * Response Structure
  * Setting a Log Level
  * Request Structure
  * Setting a Log Level for a Group
  * Request Structure
  * Clearing a Log Level



The `loggers` endpoint provides access to the application’s loggers and the configuration of their levels.

## Retrieving All Loggers

To retrieve the application’s loggers, make a `GET` request to `/actuator/loggers`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/loggers' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 791
    
    {
      "levels" : [ "OFF", "FATAL", "ERROR", "WARN", "INFO", "DEBUG", "TRACE" ],
      "loggers" : {
        "ROOT" : {
          "configuredLevel" : "INFO",
          "effectiveLevel" : "INFO"
        },
        "com.example" : {
          "configuredLevel" : "DEBUG",
          "effectiveLevel" : "DEBUG"
        }
      },
      "groups" : {
        "test" : {
          "configuredLevel" : "INFO",
          "members" : [ "test.member1", "test.member2" ]
        },
        "web" : {
          "members" : [ "org.springframework.core.codec", "org.springframework.http", "org.springframework.web", "org.springframework.boot.actuate.endpoint.web", "org.springframework.boot.web.servlet.ServletContextInitializerBeans" ]
        },
        "sql" : {
          "members" : [ "org.springframework.jdbc.core", "org.hibernate.SQL", "org.jooq.tools.LoggerListener" ]
        }
      }
    }
    
    Copied!

### Response Structure

The response contains details of the application’s loggers. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`levels` | `Array` | Levels support by the logging system.  
`loggers` | `Object` | Loggers keyed by name.  
`groups` | `Object` | Logger groups keyed by name  
`loggers.*.configuredLevel` | `String` | Configured level of the logger, if any.  
`loggers.*.effectiveLevel` | `String` | Effective level of the logger.  
`groups.*.configuredLevel` | `String` | Configured level of the logger group, if any.  
`groups.*.members` | `Array` | Loggers that are part of this group  
  
## Retrieving a Single Logger

To retrieve a single logger, make a `GET` request to `/actuator/loggers/{logger.name}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/loggers/com.example' -i -X GET
    
    Copied!

The preceding example retrieves information about the logger named `com.example`. The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Disposition: inline;filename=f.txt
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 61
    
    {
      "configuredLevel" : "INFO",
      "effectiveLevel" : "INFO"
    }
    
    Copied!

### Response Structure

The response contains details of the requested logger. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`configuredLevel` | `String` | Configured level of the logger, if any.  
`effectiveLevel` | `String` | Effective level of the logger.  
  
## Retrieving a Single Group

To retrieve a single group, make a `GET` request to `/actuator/loggers/{group.name}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/loggers/test' -i -X GET
    
    Copied!

The preceding example retrieves information about the logger group named `test`. The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 82
    
    {
      "configuredLevel" : "INFO",
      "members" : [ "test.member1", "test.member2" ]
    }
    
    Copied!

### Response Structure

The response contains details of the requested group. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`configuredLevel` | `String` | Configured level of the logger group, if any.  
`members` | `Array` | Loggers that are part of this group  
  
## Setting a Log Level

To set the level of a logger, make a `POST` request to `/actuator/loggers/{logger.name}` with a JSON body that specifies the configured level for the logger, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/loggers/com.example' -i -X POST \
        -H 'Content-Type: application/json' \
        -d '{"configuredLevel":"debug"}'
    
    Copied!

The preceding example sets the `configuredLevel` of the `com.example` logger to `DEBUG`.

### Request Structure

The request specifies the desired level of the logger. The following table describes the structure of the request:

Path | Type | Description  
---|---|---  
`configuredLevel` | `String` | Level for the logger. May be omitted to clear the level.  
  
## Setting a Log Level for a Group

To set the level of a logger, make a `POST` request to `/actuator/loggers/{group.name}` with a JSON body that specifies the configured level for the logger group, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/loggers/test' -i -X POST \
        -H 'Content-Type: application/json' \
        -d '{"configuredLevel":"debug"}'
    
    Copied!

The preceding example sets the `configuredLevel` of the `test` logger group to `DEBUG`.

### Request Structure

The request specifies the desired level of the logger group. The following table describes the structure of the request:

Path | Type | Description  
---|---|---  
`configuredLevel` | `String` | Level for the logger. May be omitted to clear the level.  
  
## Clearing a Log Level

To clear the level of a logger, make a `POST` request to `/actuator/loggers/{logger.name}` with a JSON body containing an empty object, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/loggers/com.example' -i -X POST \
        -H 'Content-Type: application/json' \
        -d '{}'
    
    Copied!

The preceding example clears the configured level of the `com.example` logger.

[Log File (`logfile`)](logfile.html) [Mappings (`mappings`)](mappings.html)
---
