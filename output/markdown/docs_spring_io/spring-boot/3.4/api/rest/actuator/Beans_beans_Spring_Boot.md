Title: Beans (beans) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/beans.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Beans_beans_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Beans_beans_Spring_Boot.png
crawled_at: 2025-06-04T19:22:18.943895
---
Search CTRL + k

### Beans (beans)

  * Retrieving the Beans
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/beans.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Beans (`beans`)](beans.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/beans.html)!  
---|---  
  
# Beans (`beans`)

### Beans (beans)

  * Retrieving the Beans
  * Response Structure



The `beans` endpoint provides information about the application’s beans.

## Retrieving the Beans

To retrieve the beans, make a `GET` request to `/actuator/beans`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/beans' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 1089
    
    {
      "contexts" : {
        "application" : {
          "beans" : {
            "org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration" : {
              "aliases" : [ ],
              "scope" : "singleton",
              "type" : "org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration",
              "dependencies" : [ ]
            },
            "org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration" : {
              "aliases" : [ ],
              "scope" : "singleton",
              "type" : "org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration",
              "dependencies" : [ ]
            },
            "org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration" : {
              "aliases" : [ ],
              "scope" : "singleton",
              "type" : "org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration",
              "dependencies" : [ ]
            }
          }
        }
      }
    }
    
    Copied!

### Response Structure

The response contains details of the application’s beans. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`contexts` | `Object` | Application contexts keyed by id.  
`contexts.*.parentId` | `String` | Id of the parent application context, if any.  
`contexts.*.beans` | `Object` | Beans in the application context keyed by name.  
`contexts.*.beans.*.aliases` | `Array` | Names of any aliases.  
`contexts.*.beans.*.scope` | `String` | Scope of the bean.  
`contexts.*.beans.*.type` | `String` | Fully qualified type of the bean.  
`contexts.*.beans.*.resource` | `String` | Resource in which the bean was defined, if any.  
`contexts.*.beans.*.dependencies` | `Array` | Names of any dependencies.  
  
[Audit Events (`auditevents`)](auditevents.html) [Caches (`caches`)](caches.html)
---
