Title: HTTP Exchanges (httpexchanges) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/httpexchanges.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/HTTP_Exchanges_httpexchanges_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/HTTP_Exchanges_httpexchanges_Spring_Boot.png
crawled_at: 2025-06-04T16:05:06.073790
---
Search CTRL + k

### HTTP Exchanges (httpexchanges)

  * Retrieving the HTTP Exchanges
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/httpexchanges.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [HTTP Exchanges (`httpexchanges`)](httpexchanges.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/httpexchanges.html)!  
---|---  
  
# HTTP Exchanges (`httpexchanges`)

### HTTP Exchanges (httpexchanges)

  * Retrieving the HTTP Exchanges
  * Response Structure



The `httpexchanges` endpoint provides information about HTTP request-response exchanges.

## Retrieving the HTTP Exchanges

To retrieve the HTTP exchanges, make a `GET` request to `/actuator/httpexchanges`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/httpexchanges' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 511
    
    {
      "exchanges" : [ {
        "timestamp" : "2022-12-22T13:43:41Z",
        "request" : {
          "uri" : "https://api.example.com",
          "method" : "GET",
          "headers" : {
            "Accept" : [ "application/json" ]
          }
        },
        "response" : {
          "status" : 200,
          "headers" : {
            "Content-Type" : [ "application/json" ]
          }
        },
        "principal" : {
          "name" : "alice"
        },
        "session" : {
          "id" : "e225fb02-067a-4f7a-b062-75517f30ef1e"
        },
        "timeTaken" : "PT0.023S"
      } ]
    }
    
    Copied!

### Response Structure

The response contains details of the traced HTTP request-response exchanges. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`exchanges` | `Array` | An array of HTTP request-response exchanges.  
`exchanges.[].timestamp` | `String` | Timestamp of when the exchange occurred.  
`exchanges.[].principal` | `Object` | Principal of the exchange, if any.  
`exchanges.[].principal.name` | `String` | Name of the principal.  
`exchanges.[].request.method` | `String` | HTTP method of the request.  
`exchanges.[].request.remoteAddress` | `String` | Remote address from which the request was received, if known.  
`exchanges.[].request.uri` | `String` | URI of the request.  
`exchanges.[].request.headers` | `Object` | Headers of the request, keyed by header name.  
`exchanges.[].request.headers.*.[]` | `Array` | Values of the header  
`exchanges.[].response.status` | `Number` | Status of the response  
`exchanges.[].response.headers` | `Object` | Headers of the response, keyed by header name.  
`exchanges.[].response.headers.*.[]` | `Array` | Values of the header  
`exchanges.[].session` | `Object` | Session associated with the exchange, if any.  
`exchanges.[].session.id` | `String` | ID of the session.  
`exchanges.[].timeTaken` | `String` | Time taken to handle the exchange.  
  
[Heap Dump (`heapdump`)](heapdump.html) [Info (`info`)](info.html)
---
