Title: Sessions (sessions) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/sessions.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Sessions_sessions_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Sessions_sessions_Spring_Boot.png
crawled_at: 2025-06-04T19:21:49.522577
---
Search CTRL + k

### Sessions (sessions)

  * Retrieving Sessions
  * Query Parameters
  * Response Structure
  * Retrieving a Single Session
  * Response Structure
  * Deleting a Session



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/sessions.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Sessions (`sessions`)](sessions.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/sessions.html)!  
---|---  
  
# Sessions (`sessions`)

### Sessions (sessions)

  * Retrieving Sessions
  * Query Parameters
  * Response Structure
  * Retrieving a Single Session
  * Response Structure
  * Deleting a Session



The `sessions` endpoint provides information about the application’s HTTP sessions that are managed by Spring Session.

## Retrieving Sessions

To retrieve the sessions, make a `GET` request to `/actuator/sessions`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/sessions?username=alice' -i -X GET
    
    Copied!

The preceding examples retrieves all of the sessions for the user whose username is `alice`. The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 789
    
    {
      "sessions" : [ {
        "id" : "07e9851a-cced-44b2-8f11-7792c72817f2",
        "attributeNames" : [ ],
        "creationTime" : "2025-05-21T22:00:10.826026879Z",
        "lastAccessedTime" : "2025-05-22T09:59:25.826033552Z",
        "maxInactiveInterval" : 1800,
        "expired" : false
      }, {
        "id" : "fc2d07ad-3867-4769-ba99-77933e1f2a79",
        "attributeNames" : [ ],
        "creationTime" : "2025-05-22T08:00:10.827666728Z",
        "lastAccessedTime" : "2025-05-22T09:59:58.827667540Z",
        "maxInactiveInterval" : 1800,
        "expired" : false
      }, {
        "id" : "4db5efcc-99cb-4d05-a52c-b49acfbb7ea9",
        "attributeNames" : [ ],
        "creationTime" : "2025-05-22T05:00:10.827660897Z",
        "lastAccessedTime" : "2025-05-22T09:59:33.827663542Z",
        "maxInactiveInterval" : 1800,
        "expired" : false
      } ]
    }
    
    Copied!

### Query Parameters

The endpoint uses query parameters to limit the sessions that it returns. The following table shows the single required query parameter:

Parameter | Description  
---|---  
`username` | Name of the user.  
  
### Response Structure

The response contains details of the matching sessions. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`sessions` | `Array` | Sessions for the given username.  
`sessions.[].id` | `String` | ID of the session.  
`sessions.[].attributeNames` | `Array` | Names of the attributes stored in the session.  
`sessions.[].creationTime` | `String` | Timestamp of when the session was created.  
`sessions.[].lastAccessedTime` | `String` | Timestamp of when the session was last accessed.  
`sessions.[].maxInactiveInterval` | `Number` | Maximum permitted period of inactivity, in seconds, before the session will expire.  
`sessions.[].expired` | `Boolean` | Whether the session has expired.  
  
## Retrieving a Single Session

To retrieve a single session, make a `GET` request to `/actuator/sessions/{id}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/sessions/4db5efcc-99cb-4d05-a52c-b49acfbb7ea9' -i -X GET
    
    Copied!

The preceding example retrieves the session with the `id` of `4db5efcc-99cb-4d05-a52c-b49acfbb7ea9`. The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 208
    
    {"id":"4db5efcc-99cb-4d05-a52c-b49acfbb7ea9","attributeNames":[],"creationTime":"2025-05-22T05:00:10.827660897Z","lastAccessedTime":"2025-05-22T09:59:33.827663542Z","maxInactiveInterval":1800,"expired":false}
    
    Copied!

### Response Structure

The response contains details of the requested session. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`id` | `String` | ID of the session.  
`attributeNames` | `Array` | Names of the attributes stored in the session.  
`creationTime` | `String` | Timestamp of when the session was created.  
`lastAccessedTime` | `String` | Timestamp of when the session was last accessed.  
`maxInactiveInterval` | `Number` | Maximum permitted period of inactivity, in seconds, before the session will expire.  
`expired` | `Boolean` | Whether the session has expired.  
  
## Deleting a Session

To delete a session, make a `DELETE` request to `/actuator/sessions/{id}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/sessions/4db5efcc-99cb-4d05-a52c-b49acfbb7ea9' -i -X DELETE
    
    Copied!

The preceding example deletes the session with the `id` of `4db5efcc-99cb-4d05-a52c-b49acfbb7ea9`.

[Scheduled Tasks (`scheduledtasks`)](scheduledtasks.html) [Shutdown (`shutdown`)](shutdown.html)
---
