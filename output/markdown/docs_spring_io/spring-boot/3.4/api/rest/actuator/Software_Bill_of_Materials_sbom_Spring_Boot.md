Title: Software Bill of Materials (sbom) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/sbom.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Software_Bill_of_Materials_sbom_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Software_Bill_of_Materials_sbom_Spring_Boot.png
crawled_at: 2025-06-04T16:06:07.019978
---
Search CTRL + k

### Software Bill of Materials (sbom)

  * Retrieving the Available SBOMs
  * Response Structure
  * Retrieving a Single SBOM
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/sbom.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Software Bill of Materials (`sbom`)](sbom.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/sbom.html)!  
---|---  
  
# Software Bill of Materials (`sbom`)

### Software Bill of Materials (sbom)

  * Retrieving the Available SBOMs
  * Response Structure
  * Retrieving a Single SBOM
  * Response Structure



The `sbom` endpoint provides information about the software bill of materials (SBOM).

## Retrieving the Available SBOMs

To retrieve the available SBOMs, make a `GET` request to `/actuator/sbom`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/sbom' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 31
    
    {
      "ids" : [ "application" ]
    }
    
    Copied!

### Response Structure

The response contains the available SBOMs. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`ids` | `Array` | An array of available SBOM ids.  
  
## Retrieving a Single SBOM

To retrieve the available SBOMs, make a `GET` request to `/actuator/sbom/{id}`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/sbom/application' -i -X GET
    
    Copied!

The preceding example retrieves the SBOM named application. The resulting response depends on the format of the SBOM. This example uses the CycloneDX format.
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.cyclonedx+json
    Accept-Ranges: bytes
    Content-Length: 160316
    
    {
      "bomFormat" : "CycloneDX",
      "specVersion" : "1.5",
      "serialNumber" : "urn:uuid:13862013-3360-43e5-8055-3645aa43c548",
      "version" : 1,
      // ...
    }
    
    Copied!

### Response Structure

The response depends on the format of the SBOM:

  * [CycloneDX](https://cyclonedx.org/specification/overview/)




[Quartz (`quartz`)](quartz.html) [Scheduled Tasks (`scheduledtasks`)](scheduledtasks.html)
---
