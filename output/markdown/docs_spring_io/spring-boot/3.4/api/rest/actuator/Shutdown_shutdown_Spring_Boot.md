Title: Shutdown (shutdown) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/shutdown.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Shutdown_shutdown_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Shutdown_shutdown_Spring_Boot.png
crawled_at: 2025-06-04T16:00:25.281022
---
Search CTRL + k

### Shutdown (shutdown)

  * Shutting Down the Application
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/shutdown.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Shutdown (`shutdown`)](shutdown.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/shutdown.html)!  
---|---  
  
# Shutdown (`shutdown`)

### Shutdown (shutdown)

  * Shutting Down the Application
  * Response Structure



The `shutdown` endpoint is used to shut down the application.

## Shutting Down the Application

To shut down the application, make a `POST` request to `/actuator/shutdown`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/shutdown' -i -X POST
    
    Copied!

A response similar to the following is produced:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 41
    
    {
      "message" : "Shutting down, bye..."
    }
    
    Copied!

### Response Structure

The response contains details of the result of the shutdown request. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`message` | `String` | Message describing the result of the request.  
  
[Sessions (`sessions`)](sessions.html) [Application Startup (`startup`)](startup.html)
---
