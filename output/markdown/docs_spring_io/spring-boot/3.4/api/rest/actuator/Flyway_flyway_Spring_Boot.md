Title: Flyway (flyway) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/flyway.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Flyway_flyway_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Flyway_flyway_Spring_Boot.png
crawled_at: 2025-06-04T16:02:09.387382
---
Search CTRL + k

### Flyway (flyway)

  * Retrieving the Migrations
  * Response Structure



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/flyway.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Flyway (`flyway`)](flyway.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/flyway.html)!  
---|---  
  
# Flyway (`flyway`)

### Flyway (flyway)

  * Retrieving the Migrations
  * Response Structure



The `flyway` endpoint provides information about database migrations performed by Flyway.

## Retrieving the Migrations

To retrieve the migrations, make a `GET` request to `/actuator/flyway`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/flyway' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 515
    
    {
      "contexts" : {
        "application" : {
          "flywayBeans" : {
            "flyway" : {
              "migrations" : [ {
                "type" : "SQL",
                "checksum" : -156244537,
                "version" : "1",
                "description" : "init",
                "script" : "V1__init.sql",
                "state" : "SUCCESS",
                "installedBy" : "SA",
                "installedOn" : "2025-05-22T09:59:51.677Z",
                "installedRank" : 1,
                "executionTime" : 7
              } ]
            }
          }
        }
      }
    }
    
    Copied!

### Response Structure

The response contains details of the application’s Flyway migrations. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`contexts` | `Object` | Application contexts keyed by id  
`contexts.*.flywayBeans.*.migrations` | `Array` | Migrations performed by the Flyway instance, keyed by Flyway bean name.  
`contexts.*.flywayBeans.*.migrations.[].checksum` | `Number` | Checksum of the migration, if any.  
`contexts.*.flywayBeans.*.migrations.[].description` | `String` | Description of the migration, if any.  
`contexts.*.flywayBeans.*.migrations.[].executionTime` | `Number` | Execution time in milliseconds of an applied migration.  
`contexts.*.flywayBeans.*.migrations.[].installedBy` | `String` | User that installed the applied migration, if any.  
`contexts.*.flywayBeans.*.migrations.[].installedOn` | `String` | Timestamp of when the applied migration was installed, if any.  
`contexts.*.flywayBeans.*.migrations.[].installedRank` | `Number` | Rank of the applied migration, if any. Later migrations have higher ranks.  
`contexts.*.flywayBeans.*.migrations.[].script` | `String` | Name of the script used to execute the migration, if any.  
`contexts.*.flywayBeans.*.migrations.[].state` | `String` | State of the migration. (`PENDING`, `ABOVE_TARGET`, `BELOW_BASELINE`, `BASELINE_IGNORED`, `BASELINE`, `IGNORED`, `MISSING_SUCCESS`, `MISSING_FAILED`, `SUCCESS`, `UNDONE`, `AVAILABLE`, `FAILED`, `OUT_OF_ORDER`, `FUTURE_SUCCESS`, `FUTURE_FAILED`, `OUTDATED`, `SUPERSEDED`, `DELETED`)  
`contexts.*.flywayBeans.*.migrations.[].type` | `String` | Type of the migration.  
`contexts.*.flywayBeans.*.migrations.[].version` | `String` | Version of the database after applying the migration, if any.  
`contexts.*.parentId` | `String` | Id of the parent application context, if any.  
  
[Environment (`env`)](env.html) [Health (`health`)](health.html)
---
