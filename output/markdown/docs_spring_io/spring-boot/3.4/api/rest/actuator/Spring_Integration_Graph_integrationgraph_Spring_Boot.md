Title: Spring Integration Graph (integrationgraph) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/integrationgraph.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Spring_Integration_Graph_integrationgraph_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Spring_Integration_Graph_integrationgraph_Spring_Boot.png
crawled_at: 2025-06-04T16:07:39.270894
---
Search CTRL + k

### Spring Integration Graph (integrationgraph)

  * Retrieving the Spring Integration Graph
  * Response Structure
  * Rebuilding the Spring Integration Graph



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/integrationgraph.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Spring Integration Graph (`integrationgraph`)](integrationgraph.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/integrationgraph.html)!  
---|---  
  
# Spring Integration Graph (`integrationgraph`)

### Spring Integration Graph (integrationgraph)

  * Retrieving the Spring Integration Graph
  * Response Structure
  * Rebuilding the Spring Integration Graph



The `integrationgraph` endpoint exposes a graph containing all Spring Integration components.

## Retrieving the Spring Integration Graph

To retrieve the information about the application, make a `GET` request to `/actuator/integrationgraph`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/integrationgraph' -i -X GET
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/vnd.spring-boot.actuator.v3+json
    Content-Length: 1033
    
    {
      "contentDescriptor" : {
        "providerVersion" : "6.4.5",
        "providerFormatVersion" : 1.2,
        "provider" : "spring-integration"
      },
      "nodes" : [ {
        "nodeId" : 1,
        "componentType" : "null-channel",
        "integrationPatternType" : "null_channel",
        "integrationPatternCategory" : "messaging_channel",
        "properties" : { },
        "name" : "nullChannel",
        "observed" : false
      }, {
        "nodeId" : 2,
        "componentType" : "publish-subscribe-channel",
        "integrationPatternType" : "publish_subscribe_channel",
        "integrationPatternCategory" : "messaging_channel",
        "properties" : { },
        "name" : "errorChannel",
        "observed" : false
      }, {
        "nodeId" : 3,
        "componentType" : "logging-channel-adapter",
        "integrationPatternType" : "outbound_channel_adapter",
        "integrationPatternCategory" : "messaging_endpoint",
        "properties" : { },
        "input" : "errorChannel",
        "name" : "errorLogger",
        "observed" : false
      } ],
      "links" : [ {
        "from" : 2,
        "to" : 3,
        "type" : "input"
      } ]
    }
    
    Copied!

### Response Structure

The response contains all Spring Integration components used within the application, as well as the links between them. More information about the structure can be found in the [reference documentation](https://docs.spring.io/spring-integration/reference/6.4/graph.html).

## Rebuilding the Spring Integration Graph

To rebuild the exposed graph, make a `POST` request to `/actuator/integrationgraph`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/integrationgraph' -i -X POST
    
    Copied!

This will result in a `204 - No Content` response:
    
    
    HTTP/1.1 204 No Content
    
    Copied!

[Info (`info`)](info.html) [Liquibase (`liquibase`)](liquibase.html)
---
