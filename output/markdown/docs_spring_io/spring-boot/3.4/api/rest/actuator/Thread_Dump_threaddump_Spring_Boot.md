Title: Thread Dump (threaddump) :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/api/rest/actuator/threaddump.html
HTML: html/docs_spring_io/spring-boot/3.4/api/rest/actuator/Thread_Dump_threaddump_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/api/rest/actuator/Thread_Dump_threaddump_Spring_Boot.png
crawled_at: 2025-06-04T16:07:09.021796
---
Search CTRL + k

### Thread Dump (threaddump)

  * Retrieving the Thread Dump as JSON
  * Response Structure
  * Retrieving the Thread Dump as Text



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-actuator-autoconfigure/src/docs/antora/modules/api/pages/rest/actuator/threaddump.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * Rest APIs
  * [Actuator](index.html)
  * [Thread Dump (`threaddump`)](threaddump.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../api/rest/actuator/threaddump.html)!  
---|---  
  
# Thread Dump (`threaddump`)

### Thread Dump (threaddump)

  * Retrieving the Thread Dump as JSON
  * Response Structure
  * Retrieving the Thread Dump as Text



The `threaddump` endpoint provides a thread dump from the application’s JVM.

## Retrieving the Thread Dump as JSON

To retrieve the thread dump as JSON, make a `GET` request to `/actuator/threaddump` with an appropriate `Accept` header, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/threaddump' -i -X GET \
        -H 'Accept: application/json'
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: application/json
    Content-Length: 41131
    
    {
      "threads" : [ {
        "threadName" : "Test worker",
        "threadId" : 1,
        "blockedTime" : -1,
        "blockedCount" : 32,
        "waitedTime" : -1,
        "waitedCount" : 141,
        "lockOwnerId" : -1,
        "daemon" : false,
        "inNative" : false,
        "suspended" : false,
        "threadState" : "RUNNABLE",
        "priority" : 5,
        "stackTrace" : [ {
          "moduleName" : "java.management",
          "moduleVersion" : "17.0.15",
          "methodName" : "dumpThreads0",
          "fileName" : "ThreadImpl.java",
          "lineNumber" : -2,
          "nativeMethod" : true,
          "className" : "sun.management.ThreadImpl"
        }, {
          "moduleName" : "java.management",
          "moduleVersion" : "17.0.15",
          "methodName" : "dumpAllThreads",
          "fileName" : "ThreadImpl.java",
          "lineNumber" : 528,
          "nativeMethod" : false,
          "className" : "sun.management.ThreadImpl"
        }, {
          "moduleName" : "java.management",
          "moduleVersion" : "17.0.15",
          "methodName" : "dumpAllThreads",
          "fileName" : "ThreadImpl.java",
          "lineNumber" : 516,
          "nativeMethod" : false,
          "className" : "sun.management.ThreadImpl"
        }, {
          "classLoaderName" : "app",
          "methodName" : "getFormattedThreadDump",
          "fileName" : "ThreadDumpEndpoint.java",
          "lineNumber" : 52,
          "nativeMethod" : false,
          "className" : "org.springframework.boot.actuate.management.ThreadDumpEndpoint"
        }, {
          "classLoaderName" : "app",
          "methodName" : "threadDump",
          "fileName" : "ThreadDumpEndpoint.java",
          "lineNumber" : 43,
          "nativeMethod" : false,
          "className" : "org.springframework.boot.actuate.management.ThreadDumpEndpoint"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke0",
          "fileName" : "NativeMethodAccessorImpl.java",
          "lineNumber" : -2,
          "nativeMethod" : true,
          "className" : "jdk.internal.reflect.NativeMethodAccessorImpl"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke",
          "fileName" : "NativeMethodAccessorImpl.java",
          "lineNumber" : 77,
          "nativeMethod" : false,
          "className" : "jdk.internal.reflect.NativeMethodAccessorImpl"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke",
          "fileName" : "DelegatingMethodAccessorImpl.java",
          "lineNumber" : 43,
          "nativeMethod" : false,
          "className" : "jdk.internal.reflect.DelegatingMethodAccessorImpl"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke",
          "fileName" : "Method.java",
          "lineNumber" : 569,
          "nativeMethod" : false,
          "className" : "java.lang.reflect.Method"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invokeMethod",
          "fileName" : "ReflectionUtils.java",
          "lineNumber" : 281,
          "nativeMethod" : false,
          "className" : "org.springframework.util.ReflectionUtils"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invoke",
          "fileName" : "ReflectiveOperationInvoker.java",
          "lineNumber" : 74,
          "nativeMethod" : false,
          "className" : "org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invoke",
          "fileName" : "AbstractDiscoveredOperation.java",
          "lineNumber" : 60,
          "nativeMethod" : false,
          "className" : "org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation"
        }, {
          "classLoaderName" : "app",
          "methodName" : "handle",
          "fileName" : "AbstractWebMvcEndpointHandlerMapping.java",
          "lineNumber" : 327,
          "nativeMethod" : false,
          "className" : "org.springframework.boot.actuate.endpoint.web.servlet.AbstractWebMvcEndpointHandlerMapping$ServletWebOperationAdapter"
        }, {
          "classLoaderName" : "app",
          "methodName" : "handle",
          "fileName" : "AbstractWebMvcEndpointHandlerMapping.java",
          "lineNumber" : 434,
          "nativeMethod" : false,
          "className" : "org.springframework.boot.actuate.endpoint.web.servlet.AbstractWebMvcEndpointHandlerMapping$OperationHandler"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke0",
          "fileName" : "NativeMethodAccessorImpl.java",
          "lineNumber" : -2,
          "nativeMethod" : true,
          "className" : "jdk.internal.reflect.NativeMethodAccessorImpl"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke",
          "fileName" : "NativeMethodAccessorImpl.java",
          "lineNumber" : 77,
          "nativeMethod" : false,
          "className" : "jdk.internal.reflect.NativeMethodAccessorImpl"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke",
          "fileName" : "DelegatingMethodAccessorImpl.java",
          "lineNumber" : 43,
          "nativeMethod" : false,
          "className" : "jdk.internal.reflect.DelegatingMethodAccessorImpl"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke",
          "fileName" : "Method.java",
          "lineNumber" : 569,
          "nativeMethod" : false,
          "className" : "java.lang.reflect.Method"
        }, {
          "classLoaderName" : "app",
          "methodName" : "doInvoke",
          "fileName" : "InvocableHandlerMethod.java",
          "lineNumber" : 258,
          "nativeMethod" : false,
          "className" : "org.springframework.web.method.support.InvocableHandlerMethod"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invokeForRequest",
          "fileName" : "InvocableHandlerMethod.java",
          "lineNumber" : 191,
          "nativeMethod" : false,
          "className" : "org.springframework.web.method.support.InvocableHandlerMethod"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invokeAndHandle",
          "fileName" : "ServletInvocableHandlerMethod.java",
          "lineNumber" : 118,
          "nativeMethod" : false,
          "className" : "org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invokeHandlerMethod",
          "fileName" : "RequestMappingHandlerAdapter.java",
          "lineNumber" : 986,
          "nativeMethod" : false,
          "className" : "org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter"
        }, {
          "classLoaderName" : "app",
          "methodName" : "handleInternal",
          "fileName" : "RequestMappingHandlerAdapter.java",
          "lineNumber" : 891,
          "nativeMethod" : false,
          "className" : "org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter"
        }, {
          "classLoaderName" : "app",
          "methodName" : "handle",
          "fileName" : "AbstractHandlerMethodAdapter.java",
          "lineNumber" : 87,
          "nativeMethod" : false,
          "className" : "org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter"
        }, {
          "classLoaderName" : "app",
          "methodName" : "doDispatch",
          "fileName" : "DispatcherServlet.java",
          "lineNumber" : 1089,
          "nativeMethod" : false,
          "className" : "org.springframework.web.servlet.DispatcherServlet"
        }, {
          "classLoaderName" : "app",
          "methodName" : "doService",
          "fileName" : "DispatcherServlet.java",
          "lineNumber" : 979,
          "nativeMethod" : false,
          "className" : "org.springframework.web.servlet.DispatcherServlet"
        }, {
          "classLoaderName" : "app",
          "methodName" : "processRequest",
          "fileName" : "FrameworkServlet.java",
          "lineNumber" : 1014,
          "nativeMethod" : false,
          "className" : "org.springframework.web.servlet.FrameworkServlet"
        }, {
          "classLoaderName" : "app",
          "methodName" : "doGet",
          "fileName" : "FrameworkServlet.java",
          "lineNumber" : 903,
          "nativeMethod" : false,
          "className" : "org.springframework.web.servlet.FrameworkServlet"
        }, {
          "classLoaderName" : "app",
          "methodName" : "service",
          "fileName" : "HttpServlet.java",
          "lineNumber" : 527,
          "nativeMethod" : false,
          "className" : "jakarta.servlet.http.HttpServlet"
        }, {
          "classLoaderName" : "app",
          "methodName" : "service",
          "fileName" : "FrameworkServlet.java",
          "lineNumber" : 885,
          "nativeMethod" : false,
          "className" : "org.springframework.web.servlet.FrameworkServlet"
        }, {
          "classLoaderName" : "app",
          "methodName" : "service",
          "fileName" : "TestDispatcherServlet.java",
          "lineNumber" : 72,
          "nativeMethod" : false,
          "className" : "org.springframework.test.web.servlet.TestDispatcherServlet"
        }, {
          "classLoaderName" : "app",
          "methodName" : "service",
          "fileName" : "HttpServlet.java",
          "lineNumber" : 614,
          "nativeMethod" : false,
          "className" : "jakarta.servlet.http.HttpServlet"
        }, {
          "classLoaderName" : "app",
          "methodName" : "doFilter",
          "fileName" : "MockFilterChain.java",
          "lineNumber" : 165,
          "nativeMethod" : false,
          "className" : "org.springframework.mock.web.MockFilterChain$ServletFilterProxy"
        }, {
          "classLoaderName" : "app",
          "methodName" : "doFilter",
          "fileName" : "MockFilterChain.java",
          "lineNumber" : 132,
          "nativeMethod" : false,
          "className" : "org.springframework.mock.web.MockFilterChain"
        }, {
          "classLoaderName" : "app",
          "methodName" : "perform",
          "fileName" : "MockMvc.java",
          "lineNumber" : 201,
          "nativeMethod" : false,
          "className" : "org.springframework.test.web.servlet.MockMvc"
        }, {
          "classLoaderName" : "app",
          "methodName" : "getMvcResultOrFailure",
          "fileName" : "MockMvcTester.java",
          "lineNumber" : 387,
          "nativeMethod" : false,
          "className" : "org.springframework.test.web.servlet.assertj.MockMvcTester"
        }, {
          "classLoaderName" : "app",
          "methodName" : "perform",
          "fileName" : "MockMvcTester.java",
          "lineNumber" : 376,
          "nativeMethod" : false,
          "className" : "org.springframework.test.web.servlet.assertj.MockMvcTester"
        }, {
          "classLoaderName" : "app",
          "methodName" : "exchange",
          "fileName" : "MockMvcTester.java",
          "lineNumber" : 402,
          "nativeMethod" : false,
          "className" : "org.springframework.test.web.servlet.assertj.MockMvcTester"
        }, {
          "classLoaderName" : "app",
          "methodName" : "exchange",
          "fileName" : "MockMvcTester.java",
          "lineNumber" : 461,
          "nativeMethod" : false,
          "className" : "org.springframework.test.web.servlet.assertj.MockMvcTester$MockMvcRequestBuilder"
        }, {
          "classLoaderName" : "app",
          "methodName" : "assertThat",
          "fileName" : "MockMvcTester.java",
          "lineNumber" : 487,
          "nativeMethod" : false,
          "className" : "org.springframework.test.web.servlet.assertj.MockMvcTester$MockMvcRequestBuilder"
        }, {
          "classLoaderName" : "app",
          "methodName" : "assertThat",
          "fileName" : "MockMvcTester.java",
          "lineNumber" : 426,
          "nativeMethod" : false,
          "className" : "org.springframework.test.web.servlet.assertj.MockMvcTester$MockMvcRequestBuilder"
        }, {
          "classLoaderName" : "app",
          "methodName" : "assertThat",
          "fileName" : "AssertionsForInterfaceTypes.java",
          "lineNumber" : 82,
          "nativeMethod" : false,
          "className" : "org.assertj.core.api.AssertionsForInterfaceTypes"
        }, {
          "classLoaderName" : "app",
          "methodName" : "assertThat",
          "fileName" : "Assertions.java",
          "lineNumber" : 3371,
          "nativeMethod" : false,
          "className" : "org.assertj.core.api.Assertions"
        }, {
          "classLoaderName" : "app",
          "methodName" : "jsonThreadDump",
          "fileName" : "ThreadDumpEndpointDocumentationTests.java",
          "lineNumber" : 65,
          "nativeMethod" : false,
          "className" : "org.springframework.boot.actuate.autoconfigure.management.ThreadDumpEndpointDocumentationTests"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke0",
          "fileName" : "NativeMethodAccessorImpl.java",
          "lineNumber" : -2,
          "nativeMethod" : true,
          "className" : "jdk.internal.reflect.NativeMethodAccessorImpl"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke",
          "fileName" : "NativeMethodAccessorImpl.java",
          "lineNumber" : 77,
          "nativeMethod" : false,
          "className" : "jdk.internal.reflect.NativeMethodAccessorImpl"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke",
          "fileName" : "DelegatingMethodAccessorImpl.java",
          "lineNumber" : 43,
          "nativeMethod" : false,
          "className" : "jdk.internal.reflect.DelegatingMethodAccessorImpl"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke",
          "fileName" : "Method.java",
          "lineNumber" : 569,
          "nativeMethod" : false,
          "className" : "java.lang.reflect.Method"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invokeMethod",
          "fileName" : "ReflectionUtils.java",
          "lineNumber" : 767,
          "nativeMethod" : false,
          "className" : "org.junit.platform.commons.util.ReflectionUtils"
        }, {
          "classLoaderName" : "app",
          "methodName" : "proceed",
          "fileName" : "MethodInvocation.java",
          "lineNumber" : 60,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.execution.MethodInvocation"
        }, {
          "classLoaderName" : "app",
          "methodName" : "proceed",
          "fileName" : "InvocationInterceptorChain.java",
          "lineNumber" : 131,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation"
        }, {
          "classLoaderName" : "app",
          "methodName" : "intercept",
          "fileName" : "TimeoutExtension.java",
          "lineNumber" : 156,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.extension.TimeoutExtension"
        }, {
          "classLoaderName" : "app",
          "methodName" : "interceptTestableMethod",
          "fileName" : "TimeoutExtension.java",
          "lineNumber" : 147,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.extension.TimeoutExtension"
        }, {
          "classLoaderName" : "app",
          "methodName" : "interceptTestMethod",
          "fileName" : "TimeoutExtension.java",
          "lineNumber" : 86,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.extension.TimeoutExtension"
        }, {
          "classLoaderName" : "app",
          "methodName" : "apply",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor$$Lambda$220/0x00007f8e1825c000"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$ofVoidMethod$0",
          "fileName" : "InterceptingExecutableInvoker.java",
          "lineNumber" : 103,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall"
        }, {
          "classLoaderName" : "app",
          "methodName" : "apply",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall$$Lambda$221/0x00007f8e1825c420"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$invoke$0",
          "fileName" : "InterceptingExecutableInvoker.java",
          "lineNumber" : 93,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.execution.InterceptingExecutableInvoker"
        }, {
          "classLoaderName" : "app",
          "methodName" : "apply",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$$Lambda$543/0x00007f8e1830f930"
        }, {
          "classLoaderName" : "app",
          "methodName" : "proceed",
          "fileName" : "InvocationInterceptorChain.java",
          "lineNumber" : 106,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation"
        }, {
          "classLoaderName" : "app",
          "methodName" : "proceed",
          "fileName" : "InvocationInterceptorChain.java",
          "lineNumber" : 64,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.execution.InvocationInterceptorChain"
        }, {
          "classLoaderName" : "app",
          "methodName" : "chainAndInvoke",
          "fileName" : "InvocationInterceptorChain.java",
          "lineNumber" : 45,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.execution.InvocationInterceptorChain"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invoke",
          "fileName" : "InvocationInterceptorChain.java",
          "lineNumber" : 37,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.execution.InvocationInterceptorChain"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invoke",
          "fileName" : "InterceptingExecutableInvoker.java",
          "lineNumber" : 92,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.execution.InterceptingExecutableInvoker"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invoke",
          "fileName" : "InterceptingExecutableInvoker.java",
          "lineNumber" : 86,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.execution.InterceptingExecutableInvoker"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$invokeTestMethod$8",
          "fileName" : "TestMethodTestDescriptor.java",
          "lineNumber" : 217,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor$$Lambda$1196/0x00007f8e18731bc0"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "ThrowableCollector.java",
          "lineNumber" : 73,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.ThrowableCollector"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invokeTestMethod",
          "fileName" : "TestMethodTestDescriptor.java",
          "lineNumber" : 213,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "TestMethodTestDescriptor.java",
          "lineNumber" : 138,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "TestMethodTestDescriptor.java",
          "lineNumber" : 68,
          "nativeMethod" : false,
          "className" : "org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$executeRecursively$6",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 156,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$342/0x00007f8e1827fb68"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "ThrowableCollector.java",
          "lineNumber" : 73,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.ThrowableCollector"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$executeRecursively$8",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 146,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invoke",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$341/0x00007f8e1827f940"
        }, {
          "classLoaderName" : "app",
          "methodName" : "around",
          "fileName" : "Node.java",
          "lineNumber" : 137,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.Node"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$executeRecursively$9",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 144,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$340/0x00007f8e1827f518"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "ThrowableCollector.java",
          "lineNumber" : 73,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.ThrowableCollector"
        }, {
          "classLoaderName" : "app",
          "methodName" : "executeRecursively",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 143,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 100,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "accept",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService$$Lambda$346/0x00007f8e182806a8"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "forEach",
          "fileName" : "ArrayList.java",
          "lineNumber" : 1511,
          "nativeMethod" : false,
          "className" : "java.util.ArrayList"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invokeAll",
          "fileName" : "SameThreadHierarchicalTestExecutorService.java",
          "lineNumber" : 41,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$executeRecursively$6",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 160,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$342/0x00007f8e1827fb68"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "ThrowableCollector.java",
          "lineNumber" : 73,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.ThrowableCollector"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$executeRecursively$8",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 146,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invoke",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$341/0x00007f8e1827f940"
        }, {
          "classLoaderName" : "app",
          "methodName" : "around",
          "fileName" : "Node.java",
          "lineNumber" : 137,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.Node"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$executeRecursively$9",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 144,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$340/0x00007f8e1827f518"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "ThrowableCollector.java",
          "lineNumber" : 73,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.ThrowableCollector"
        }, {
          "classLoaderName" : "app",
          "methodName" : "executeRecursively",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 143,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 100,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "accept",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService$$Lambda$346/0x00007f8e182806a8"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "forEach",
          "fileName" : "ArrayList.java",
          "lineNumber" : 1511,
          "nativeMethod" : false,
          "className" : "java.util.ArrayList"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invokeAll",
          "fileName" : "SameThreadHierarchicalTestExecutorService.java",
          "lineNumber" : 41,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$executeRecursively$6",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 160,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$342/0x00007f8e1827fb68"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "ThrowableCollector.java",
          "lineNumber" : 73,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.ThrowableCollector"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$executeRecursively$8",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 146,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "invoke",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$341/0x00007f8e1827f940"
        }, {
          "classLoaderName" : "app",
          "methodName" : "around",
          "fileName" : "Node.java",
          "lineNumber" : 137,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.Node"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$executeRecursively$9",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 144,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$340/0x00007f8e1827f518"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "ThrowableCollector.java",
          "lineNumber" : 73,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.ThrowableCollector"
        }, {
          "classLoaderName" : "app",
          "methodName" : "executeRecursively",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 143,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "NodeTestTask.java",
          "lineNumber" : 100,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.NodeTestTask"
        }, {
          "classLoaderName" : "app",
          "methodName" : "submit",
          "fileName" : "SameThreadHierarchicalTestExecutorService.java",
          "lineNumber" : 35,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "HierarchicalTestExecutor.java",
          "lineNumber" : 57,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "HierarchicalTestEngine.java",
          "lineNumber" : 54,
          "nativeMethod" : false,
          "className" : "org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "EngineExecutionOrchestrator.java",
          "lineNumber" : 198,
          "nativeMethod" : false,
          "className" : "org.junit.platform.launcher.core.EngineExecutionOrchestrator"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "EngineExecutionOrchestrator.java",
          "lineNumber" : 169,
          "nativeMethod" : false,
          "className" : "org.junit.platform.launcher.core.EngineExecutionOrchestrator"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "EngineExecutionOrchestrator.java",
          "lineNumber" : 93,
          "nativeMethod" : false,
          "className" : "org.junit.platform.launcher.core.EngineExecutionOrchestrator"
        }, {
          "classLoaderName" : "app",
          "methodName" : "lambda$execute$0",
          "fileName" : "EngineExecutionOrchestrator.java",
          "lineNumber" : 58,
          "nativeMethod" : false,
          "className" : "org.junit.platform.launcher.core.EngineExecutionOrchestrator"
        }, {
          "classLoaderName" : "app",
          "methodName" : "accept",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "org.junit.platform.launcher.core.EngineExecutionOrchestrator$$Lambda$292/0x00007f8e18268d20"
        }, {
          "classLoaderName" : "app",
          "methodName" : "withInterceptedStreams",
          "fileName" : "EngineExecutionOrchestrator.java",
          "lineNumber" : 141,
          "nativeMethod" : false,
          "className" : "org.junit.platform.launcher.core.EngineExecutionOrchestrator"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "EngineExecutionOrchestrator.java",
          "lineNumber" : 57,
          "nativeMethod" : false,
          "className" : "org.junit.platform.launcher.core.EngineExecutionOrchestrator"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "DefaultLauncher.java",
          "lineNumber" : 103,
          "nativeMethod" : false,
          "className" : "org.junit.platform.launcher.core.DefaultLauncher"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "DefaultLauncher.java",
          "lineNumber" : 85,
          "nativeMethod" : false,
          "className" : "org.junit.platform.launcher.core.DefaultLauncher"
        }, {
          "classLoaderName" : "app",
          "methodName" : "execute",
          "fileName" : "DelegatingLauncher.java",
          "lineNumber" : 47,
          "nativeMethod" : false,
          "className" : "org.junit.platform.launcher.core.DelegatingLauncher"
        }, {
          "methodName" : "processAllTestClasses",
          "fileName" : "JUnitPlatformTestClassProcessor.java",
          "lineNumber" : 124,
          "nativeMethod" : false,
          "className" : "org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor"
        }, {
          "methodName" : "access$000",
          "fileName" : "JUnitPlatformTestClassProcessor.java",
          "lineNumber" : 99,
          "nativeMethod" : false,
          "className" : "org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor"
        }, {
          "methodName" : "stop",
          "fileName" : "JUnitPlatformTestClassProcessor.java",
          "lineNumber" : 94,
          "nativeMethod" : false,
          "className" : "org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor"
        }, {
          "methodName" : "stop",
          "fileName" : "SuiteTestClassProcessor.java",
          "lineNumber" : 63,
          "nativeMethod" : false,
          "className" : "org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke0",
          "fileName" : "NativeMethodAccessorImpl.java",
          "lineNumber" : -2,
          "nativeMethod" : true,
          "className" : "jdk.internal.reflect.NativeMethodAccessorImpl"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke",
          "fileName" : "NativeMethodAccessorImpl.java",
          "lineNumber" : 77,
          "nativeMethod" : false,
          "className" : "jdk.internal.reflect.NativeMethodAccessorImpl"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke",
          "fileName" : "DelegatingMethodAccessorImpl.java",
          "lineNumber" : 43,
          "nativeMethod" : false,
          "className" : "jdk.internal.reflect.DelegatingMethodAccessorImpl"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "invoke",
          "fileName" : "Method.java",
          "lineNumber" : 569,
          "nativeMethod" : false,
          "className" : "java.lang.reflect.Method"
        }, {
          "methodName" : "dispatch",
          "fileName" : "ReflectionDispatch.java",
          "lineNumber" : 36,
          "nativeMethod" : false,
          "className" : "org.gradle.internal.dispatch.ReflectionDispatch"
        }, {
          "methodName" : "dispatch",
          "fileName" : "ReflectionDispatch.java",
          "lineNumber" : 24,
          "nativeMethod" : false,
          "className" : "org.gradle.internal.dispatch.ReflectionDispatch"
        }, {
          "methodName" : "dispatch",
          "fileName" : "ContextClassLoaderDispatch.java",
          "lineNumber" : 33,
          "nativeMethod" : false,
          "className" : "org.gradle.internal.dispatch.ContextClassLoaderDispatch"
        }, {
          "methodName" : "invoke",
          "fileName" : "ProxyDispatchAdapter.java",
          "lineNumber" : 92,
          "nativeMethod" : false,
          "className" : "org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler"
        }, {
          "moduleName" : "jdk.proxy1",
          "methodName" : "stop",
          "lineNumber" : -1,
          "nativeMethod" : false,
          "className" : "jdk.proxy1.$Proxy4"
        }, {
          "methodName" : "run",
          "fileName" : "TestWorker.java",
          "lineNumber" : 200,
          "nativeMethod" : false,
          "className" : "org.gradle.api.internal.tasks.testing.worker.TestWorker$3"
        }, {
          "methodName" : "executeAndMaintainThreadName",
          "fileName" : "TestWorker.java",
          "lineNumber" : 132,
          "nativeMethod" : false,
          "className" : "org.gradle.api.internal.tasks.testing.worker.TestWorker"
        }, {
          "methodName" : "execute",
          "fileName" : "TestWorker.java",
          "lineNumber" : 103,
          "nativeMethod" : false,
          "className" : "org.gradle.api.internal.tasks.testing.worker.TestWorker"
        }, {
          "methodName" : "execute",
          "fileName" : "TestWorker.java",
          "lineNumber" : 63,
          "nativeMethod" : false,
          "className" : "org.gradle.api.internal.tasks.testing.worker.TestWorker"
        }, {
          "methodName" : "execute",
          "fileName" : "ActionExecutionWorker.java",
          "lineNumber" : 56,
          "nativeMethod" : false,
          "className" : "org.gradle.process.internal.worker.child.ActionExecutionWorker"
        }, {
          "methodName" : "call",
          "fileName" : "SystemApplicationClassLoaderWorker.java",
          "lineNumber" : 122,
          "nativeMethod" : false,
          "className" : "org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker"
        }, {
          "methodName" : "call",
          "fileName" : "SystemApplicationClassLoaderWorker.java",
          "lineNumber" : 72,
          "nativeMethod" : false,
          "className" : "org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker"
        }, {
          "classLoaderName" : "app",
          "methodName" : "run",
          "fileName" : "GradleWorkerMain.java",
          "lineNumber" : 69,
          "nativeMethod" : false,
          "className" : "worker.org.gradle.process.internal.worker.GradleWorkerMain"
        }, {
          "classLoaderName" : "app",
          "methodName" : "main",
          "fileName" : "GradleWorkerMain.java",
          "lineNumber" : 74,
          "nativeMethod" : false,
          "className" : "worker.org.gradle.process.internal.worker.GradleWorkerMain"
        } ],
        "lockedMonitors" : [ ],
        "lockedSynchronizers" : [ ]
      }, {
        "threadName" : "Reference Handler",
        "threadId" : 2,
        "blockedTime" : -1,
        "blockedCount" : 0,
        "waitedTime" : -1,
        "waitedCount" : 0,
        "lockOwnerId" : -1,
        "daemon" : true,
        "inNative" : false,
        "suspended" : false,
        "threadState" : "RUNNABLE",
        "priority" : 10,
        "stackTrace" : [ {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "waitForReferencePendingList",
          "fileName" : "Reference.java",
          "lineNumber" : -2,
          "nativeMethod" : true,
          "className" : "java.lang.ref.Reference"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "processPendingReferences",
          "fileName" : "Reference.java",
          "lineNumber" : 253,
          "nativeMethod" : false,
          "className" : "java.lang.ref.Reference"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "run",
          "fileName" : "Reference.java",
          "lineNumber" : 215,
          "nativeMethod" : false,
          "className" : "java.lang.ref.Reference$ReferenceHandler"
        } ],
        "lockedMonitors" : [ ],
        "lockedSynchronizers" : [ ]
      }, {
        "threadName" : "Finalizer",
        "threadId" : 3,
        "blockedTime" : -1,
        "blockedCount" : 1,
        "waitedTime" : -1,
        "waitedCount" : 2,
        "lockName" : "java.lang.ref.ReferenceQueue$Lock@4f92ded0",
        "lockOwnerId" : -1,
        "daemon" : true,
        "inNative" : false,
        "suspended" : false,
        "threadState" : "WAITING",
        "priority" : 8,
        "stackTrace" : [ {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "wait",
          "lineNumber" : -2,
          "nativeMethod" : true,
          "className" : "java.lang.Object"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "remove",
          "fileName" : "ReferenceQueue.java",
          "lineNumber" : 155,
          "nativeMethod" : false,
          "className" : "java.lang.ref.ReferenceQueue"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "remove",
          "fileName" : "ReferenceQueue.java",
          "lineNumber" : 176,
          "nativeMethod" : false,
          "className" : "java.lang.ref.ReferenceQueue"
        }, {
          "moduleName" : "java.base",
          "moduleVersion" : "17.0.15",
          "methodName" : "run",
          "fileName" : "Finalizer.java",
          "lineNumber" : 172,
          "nativeMethod" : false,
          "className" : "java.lang.ref.Finalizer$FinalizerThread"
        } ],
        "lockedMonitors" : [ ],
        "lockedSynchronizers" : [ ],
        "lockInfo" : {
          "className" : "java.lang.ref.ReferenceQueue$Lock",
          "identityHashCode" : 1335025360
        }
      } ]
    }
    
    Copied!

### Response Structure

The response contains details of the JVM’s threads. The following table describes the structure of the response:

Path | Type | Description  
---|---|---  
`threads` | `Array` | JVM’s threads.  
`threads.[].blockedCount` | `Number` | Total number of times that the thread has been blocked.  
`threads.[].blockedTime` | `Number` | Time in milliseconds that the thread has spent blocked. -1 if thread contention monitoring is disabled.  
`threads.[].daemon` | `Boolean` | Whether the thread is a daemon thread. Only available on Java 9 or later.  
`threads.[].inNative` | `Boolean` | Whether the thread is executing native code.  
`threads.[].lockName` | `String` | Description of the object on which the thread is blocked, if any.  
`threads.[].lockInfo` | `Object` | Object for which the thread is blocked waiting.  
`threads.[].lockInfo.className` | `String` | Fully qualified class name of the lock object.  
`threads.[].lockInfo.identityHashCode` | `Number` | Identity hash code of the lock object.  
`threads.[].lockedMonitors` | `Array` | Monitors locked by this thread, if any  
`threads.[].lockedMonitors.[].className` | `String` | Class name of the lock object.  
`threads.[].lockedMonitors.[].identityHashCode` | `Number` | Identity hash code of the lock object.  
`threads.[].lockedMonitors.[].lockedStackDepth` | `Number` | Stack depth where the monitor was locked.  
`threads.[].lockedMonitors.[].lockedStackFrame` | `Object` | Stack frame that locked the monitor.  
`threads.[].lockedSynchronizers` | `Array` | Synchronizers locked by this thread.  
`threads.[].lockedSynchronizers.[].className` | `String` | Class name of the locked synchronizer.  
`threads.[].lockedSynchronizers.[].identityHashCode` | `Number` | Identity hash code of the locked synchronizer.  
`threads.[].lockOwnerId` | `Number` | ID of the thread that owns the object on which the thread is blocked. `-1` if the thread is not blocked.  
`threads.[].lockOwnerName` | `String` | Name of the thread that owns the object on which the thread is blocked, if any.  
`threads.[].priority` | `Number` | Priority of the thread. Only available on Java 9 or later.  
`threads.[].stackTrace` | `Array` | Stack trace of the thread.  
`threads.[].stackTrace.[].classLoaderName` | `String` | Name of the class loader of the class that contains the execution point identified by this entry, if any. Only available on Java 9 or later.  
`threads.[].stackTrace.[].className` | `String` | Name of the class that contains the execution point identified by this entry.  
`threads.[].stackTrace.[].fileName` | `String` | Name of the source file that contains the execution point identified by this entry, if any.  
`threads.[].stackTrace.[].lineNumber` | `Number` | Line number of the execution point identified by this entry. Negative if unknown.  
`threads.[].stackTrace.[].methodName` | `String` | Name of the method.  
`threads.[].stackTrace.[].moduleName` | `String` | Name of the module that contains the execution point identified by this entry, if any. Only available on Java 9 or later.  
`threads.[].stackTrace.[].moduleVersion` | `String` | Version of the module that contains the execution point identified by this entry, if any. Only available on Java 9 or later.  
`threads.[].stackTrace.[].nativeMethod` | `Boolean` | Whether the execution point is a native method.  
`threads.[].suspended` | `Boolean` | Whether the thread is suspended.  
`threads.[].threadId` | `Number` | ID of the thread.  
`threads.[].threadName` | `String` | Name of the thread.  
`threads.[].threadState` | `String` | State of the thread (`NEW`, `RUNNABLE`, `BLOCKED`, `WAITING`, `TIMED_WAITING`, `TERMINATED`).  
`threads.[].waitedCount` | `Number` | Total number of times that the thread has waited for notification.  
`threads.[].waitedTime` | `Number` | Time in milliseconds that the thread has spent waiting. -1 if thread contention monitoring is disabled  
  
## Retrieving the Thread Dump as Text

To retrieve the thread dump as text, make a `GET` request to `/actuator/threaddump` that accepts `text/plain`, as shown in the following curl-based example:
    
    
    $ curl 'http://localhost:8080/actuator/threaddump' -i -X GET \
        -H 'Accept: text/plain'
    
    Copied!

The resulting response is similar to the following:
    
    
    HTTP/1.1 200 OK
    Content-Type: text/plain;charset=UTF-8
    Content-Length: 25523
    
    2025-05-22 10:00:03
    Full thread dump OpenJDK 64-Bit Server VM (17.0.15+10-LTS mixed mode, sharing):
    
    "Test worker" - Thread t@1
       java.lang.Thread.State: RUNNABLE
    	at java.management@17.0.15/sun.management.ThreadImpl.dumpThreads0(Native Method)
    	at java.management@17.0.15/sun.management.ThreadImpl.dumpAllThreads(ThreadImpl.java:528)
    	at java.management@17.0.15/sun.management.ThreadImpl.dumpAllThreads(ThreadImpl.java:516)
    	at app//org.springframework.boot.actuate.management.ThreadDumpEndpoint.getFormattedThreadDump(ThreadDumpEndpoint.java:52)
    	at app//org.springframework.boot.actuate.management.ThreadDumpEndpoint.textThreadDump(ThreadDumpEndpoint.java:48)
    	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
    	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
    	at java.base@17.0.15/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
    	at java.base@17.0.15/java.lang.reflect.Method.invoke(Method.java:569)
    	at app//org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
    	at app//org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
    	at app//org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
    	at app//org.springframework.boot.actuate.endpoint.web.servlet.AbstractWebMvcEndpointHandlerMapping$ServletWebOperationAdapter.handle(AbstractWebMvcEndpointHandlerMapping.java:327)
    	at app//org.springframework.boot.actuate.endpoint.web.servlet.AbstractWebMvcEndpointHandlerMapping$OperationHandler.handle(AbstractWebMvcEndpointHandlerMapping.java:434)
    	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
    	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
    	at java.base@17.0.15/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
    	at java.base@17.0.15/java.lang.reflect.Method.invoke(Method.java:569)
    	at app//org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
    	at app//org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
    	at app//org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
    	at app//org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
    	at app//org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
    	at app//org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
    	at app//org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
    	at app//org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
    	at app//org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
    	at app//org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
    	at app//jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
    	at app//org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
    	at app//org.springframework.test.web.servlet.TestDispatcherServlet.service(TestDispatcherServlet.java:72)
    	at app//jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
    	at app//org.springframework.mock.web.MockFilterChain$ServletFilterProxy.doFilter(MockFilterChain.java:165)
    	at app//org.springframework.mock.web.MockFilterChain.doFilter(MockFilterChain.java:132)
    	at app//org.springframework.test.web.servlet.MockMvc.perform(MockMvc.java:201)
    	at app//org.springframework.test.web.servlet.assertj.MockMvcTester.getMvcResultOrFailure(MockMvcTester.java:387)
    	at app//org.springframework.test.web.servlet.assertj.MockMvcTester.perform(MockMvcTester.java:376)
    	at app//org.springframework.test.web.servlet.assertj.MockMvcTester.exchange(MockMvcTester.java:402)
    	at app//org.springframework.test.web.servlet.assertj.MockMvcTester$MockMvcRequestBuilder.exchange(MockMvcTester.java:461)
    	at app//org.springframework.test.web.servlet.assertj.MockMvcTester$MockMvcRequestBuilder.assertThat(MockMvcTester.java:487)
    	at app//org.springframework.test.web.servlet.assertj.MockMvcTester$MockMvcRequestBuilder.assertThat(MockMvcTester.java:426)
    	at app//org.assertj.core.api.AssertionsForInterfaceTypes.assertThat(AssertionsForInterfaceTypes.java:82)
    	at app//org.assertj.core.api.Assertions.assertThat(Assertions.java:3371)
    	at app//org.springframework.boot.actuate.autoconfigure.management.ThreadDumpEndpointDocumentationTests.textThreadDump(ThreadDumpEndpointDocumentationTests.java:180)
    	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
    	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
    	at java.base@17.0.15/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
    	at java.base@17.0.15/java.lang.reflect.Method.invoke(Method.java:569)
    	at app//org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:767)
    	at app//org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
    	at app//org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
    	at app//org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
    	at app//org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
    	at app//org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
    	at app//org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor$$Lambda$220/0x00007f8e1825c000.apply(Unknown Source)
    	at app//org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
    	at app//org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall$$Lambda$221/0x00007f8e1825c420.apply(Unknown Source)
    	at app//org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
    	at app//org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$$Lambda$543/0x00007f8e1830f930.apply(Unknown Source)
    	at app//org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
    	at app//org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
    	at app//org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
    	at app//org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
    	at app//org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
    	at app//org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
    	at app//org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$8(TestMethodTestDescriptor.java:217)
    	at app//org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor$$Lambda$1196/0x00007f8e18731bc0.execute(Unknown Source)
    	at app//org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
    	at app//org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
    	at app//org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
    	at app//org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$342/0x00007f8e1827fb68.execute(Unknown Source)
    	at app//org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$341/0x00007f8e1827f940.invoke(Unknown Source)
    	at app//org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$340/0x00007f8e1827f518.execute(Unknown Source)
    	at app//org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
    	at app//org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService$$Lambda$346/0x00007f8e182806a8.accept(Unknown Source)
    	at java.base@17.0.15/java.util.ArrayList.forEach(ArrayList.java:1511)
    	at app//org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$342/0x00007f8e1827fb68.execute(Unknown Source)
    	at app//org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$341/0x00007f8e1827f940.invoke(Unknown Source)
    	at app//org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$340/0x00007f8e1827f518.execute(Unknown Source)
    	at app//org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
    	at app//org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService$$Lambda$346/0x00007f8e182806a8.accept(Unknown Source)
    	at java.base@17.0.15/java.util.ArrayList.forEach(ArrayList.java:1511)
    	at app//org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$342/0x00007f8e1827fb68.execute(Unknown Source)
    	at app//org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$341/0x00007f8e1827f940.invoke(Unknown Source)
    	at app//org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$340/0x00007f8e1827f518.execute(Unknown Source)
    	at app//org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
    	at app//org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
    	at app//org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
    	at app//org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
    	at app//org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
    	at app//org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
    	at app//org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
    	at app//org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
    	at app//org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
    	at app//org.junit.platform.launcher.core.EngineExecutionOrchestrator$$Lambda$292/0x00007f8e18268d20.accept(Unknown Source)
    	at app//org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
    	at app//org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
    	at app//org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
    	at app//org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
    	at app//org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
    	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
    	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
    	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
    	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
    	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
    	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
    	at java.base@17.0.15/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
    	at java.base@17.0.15/java.lang.reflect.Method.invoke(Method.java:569)
    	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
    	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
    	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
    	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
    	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
    	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
    	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
    	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
    	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
    	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
    	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:122)
    	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:72)
    	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
    	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
    
       Locked ownable synchronizers:
    	- None
    
    "Reference Handler" - Thread t@2
       java.lang.Thread.State: RUNNABLE
    	at java.base@17.0.15/java.lang.ref.Reference.waitForReferencePendingList(Native Method)
    	at java.base@17.0.15/java.lang.ref.Reference.processPendingReferences(Reference.java:253)
    	at java.base@17.0.15/java.lang.ref.Reference$ReferenceHandler.run(Reference.java:215)
    
       Locked ownable synchronizers:
    	- None
    
    "Finalizer" - Thread t@3
       java.lang.Thread.State: WAITING
    	at java.base@17.0.15/java.lang.Object.wait(Native Method)
    	- waiting on <4f92ded0> (a java.lang.ref.ReferenceQueue$Lock)
    	at java.base@17.0.15/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
    	at java.base@17.0.15/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:176)
    	at java.base@17.0.15/java.lang.ref.Finalizer$FinalizerThread.run(Finalizer.java:172)
    
       Locked ownable synchronizers:
    	- None
    
    "Signal Dispatcher" - Thread t@4
       java.lang.Thread.State: RUNNABLE
    
       Locked ownable synchronizers:
    	- None
    
    "Common-Cleaner" - Thread t@11
       java.lang.Thread.State: TIMED_WAITING
    	at java.base@17.0.15/java.lang.Object.wait(Native Method)
    	- waiting on <248ad8a1> (a java.lang.ref.ReferenceQueue$Lock)
    	at java.base@17.0.15/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
    	at java.base@17.0.15/jdk.internal.ref.CleanerImpl.run(CleanerImpl.java:140)
    	at java.base@17.0.15/java.lang.Thread.run(Thread.java:840)
    	at java.base@17.0.15/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:162)
    
       Locked ownable synchronizers:
    	- None
    
    "Notification Thread" - Thread t@12
       java.lang.Thread.State: RUNNABLE
    
       Locked ownable synchronizers:
    	- None
    
    "/127.0.0.1:52480 to /127.0.0.1:39481 workers" - Thread t@14
       java.lang.Thread.State: WAITING
    	at java.base@17.0.15/jdk.internal.misc.Unsafe.park(Native Method)
    	- parking to wait for <6eb1a122> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
    	at java.base@17.0.15/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
    	at java.base@17.0.15/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
    	at java.base@17.0.15/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
    	at java.base@17.0.15/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
    	at java.base@17.0.15/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
    	at org.gradle.internal.remote.internal.hub.queue.EndPointQueue.take(EndPointQueue.java:49)
    	at org.gradle.internal.remote.internal.hub.MessageHub$Handler.run(MessageHub.java:403)
    	at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
    	at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)
    	at java.base@17.0.15/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
    	at java.base@17.0.15/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
    	at java.base@17.0.15/java.lang.Thread.run(Thread.java:840)
    
       Locked ownable synchronizers:
    	- Locked <3e84448c> (a java.util.concurrent.ThreadPoolExecutor$Worker)
    
    "/127.0.0.1:52480 to /127.0.0.1:39481 workers Thread 2" - Thread t@15
       java.lang.Thread.State: WAITING
    	at java.base@17.0.15/jdk.internal.misc.Unsafe.park(Native Method)
    	- parking to wait for <4c669afb> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
    	at java.base@17.0.15/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
    	at java.base@17.0.15/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
    	at java.base@17.0.15/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
    	at java.base@17.0.15/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
    	at java.base@17.0.15/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
    	at org.gradle.internal.remote.internal.hub.queue.EndPointQueue.take(EndPointQueue.java:49)
    	at org.gradle.internal.remote.internal.hub.MessageHub$ConnectionDispatch.run(MessageHub.java:322)
    	at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
    	at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)
    	at java.base@17.0.15/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
    	at java.base@17.0.15/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
    	at java.base@17.0.15/java.lang.Thread.run(Thread.java:840)
    
       Locked ownable synchronizers:
    	- Locked <32502377> (a java.util.concurrent.ThreadPoolExecutor$Worker)
    
    "/127.0.0.1:52480 to /127.0.0.1:39481 workers Thread 3" - Thread t@16
       java.lang.Thread.State: RUNNABLE
    	at java.base@17.0.15/sun.nio.ch.EPoll.wait(Native Method)
    	at java.base@17.0.15/sun.nio.ch.EPollSelectorImpl.doSelect(EPollSelectorImpl.java:118)
    	at java.base@17.0.15/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
    	- locked <15dba9f9> (a sun.nio.ch.Util$2)
    	- locked <4bd214c> (a sun.nio.ch.EPollSelectorImpl)
    	at java.base@17.0.15/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
    	at org.gradle.internal.remote.internal.inet.SocketConnection$SocketInputStream.read(SocketConnection.java:187)
    	at com.esotericsoftware.kryo.io.Input.fill(Input.java:146)
    	at com.esotericsoftware.kryo.io.Input.require(Input.java:178)
    	at com.esotericsoftware.kryo.io.Input.readByte(Input.java:295)
    	at org.gradle.internal.serialize.kryo.KryoBackedDecoder.readByte(KryoBackedDecoder.java:88)
    	at org.gradle.internal.remote.internal.hub.InterHubMessageSerializer$MessageReader.read(InterHubMessageSerializer.java:64)
    	at org.gradle.internal.remote.internal.hub.InterHubMessageSerializer$MessageReader.read(InterHubMessageSerializer.java:52)
    	at org.gradle.internal.remote.internal.inet.SocketConnection.receive(SocketConnection.java:83)
    	at org.gradle.internal.remote.internal.hub.MessageHub$ConnectionReceive.run(MessageHub.java:270)
    	at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
    	at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)
    	at java.base@17.0.15/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
    	at java.base@17.0.15/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
    	at java.base@17.0.15/java.lang.Thread.run(Thread.java:840)
    
       Locked ownable synchronizers:
    	- Locked <550dbc7a> (a java.util.concurrent.ThreadPoolExecutor$Worker)
    
    "process reaper" - Thread t@20
       java.lang.Thread.State: TIMED_WAITING
    	at java.base@17.0.15/jdk.internal.misc.Unsafe.park(Native Method)
    	- parking to wait for <7c74d262> (a java.util.concurrent.SynchronousQueue$TransferStack)
    	at java.base@17.0.15/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
    	at java.base@17.0.15/java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:401)
    	at java.base@17.0.15/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:903)
    	at java.base@17.0.15/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1061)
    	at java.base@17.0.15/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
    	at java.base@17.0.15/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
    	at java.base@17.0.15/java.lang.Thread.run(Thread.java:840)
    
       Locked ownable synchronizers:
    	- None
    
    "Attach Listener" - Thread t@21
       java.lang.Thread.State: RUNNABLE
    
       Locked ownable synchronizers:
    	- None
    
    "HikariPool-1 housekeeper" - Thread t@32
       java.lang.Thread.State: TIMED_WAITING
    	at java.base@17.0.15/jdk.internal.misc.Unsafe.park(Native Method)
    	- parking to wait for <34a82874> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
    	at java.base@17.0.15/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
    	at java.base@17.0.15/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1679)
    	at java.base@17.0.15/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
    	at java.base@17.0.15/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
    	at java.base@17.0.15/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
    	at java.base@17.0.15/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
    	at java.base@17.0.15/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
    	at java.base@17.0.15/java.lang.Thread.run(Thread.java:840)
    
       Locked ownable synchronizers:
    	- None
    
    Copied!

[Application Startup (`startup`)](startup.html) [Spring Boot](../../java/index.html)
---
