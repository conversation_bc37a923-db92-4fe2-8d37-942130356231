Title: Spring Boot :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/index.html
HTML: html/docs_spring_io/spring-boot/3.4/Spring_Boot_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/Spring_Boot_Spring_Boot.png
crawled_at: 2025-06-04T16:00:00.660255
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/ROOT/pages/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](index.html)
  * [Overview](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../index.html)!  
---|---  
  
# Spring Boot

Spring Boot helps you to create stand-alone, production-grade Spring-based applications that you can run. We take an opinionated view of the Spring platform and third-party libraries, so that you can get started with minimum fuss. Most Spring Boot applications need very little Spring configuration.

You can use Spring Boot to create Java applications that can be started by using `java -jar` or more traditional war deployments.

Our primary goals are:

  * Provide a radically faster and widely accessible getting-started experience for all Spring development.

  * Be opinionated out of the box but get out of the way quickly as requirements start to diverge from the defaults.

  * Provide a range of non-functional features that are common to large classes of projects (such as embedded servers, security, metrics, health checks, and externalized configuration).

  * Absolutely no code generation (when not targeting native image) and no requirement for XML configuration.




[Documentation](documentation.html)
---
