Title: Alternative Single Jar Solutions :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/specification/executable-jar/alternatives.html
HTML: html/docs_spring_io/spring-boot/3.4/specification/executable-jar/Alternative_Single_Jar_Solutions_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/specification/executable-jar/Alternative_Single_Jar_Solutions_Spring_Boot.png
crawled_at: 2025-06-04T16:05:26.359807
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/specification/pages/executable-jar/alternatives.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * Specifications
  * [The Executable Jar Format](index.html)
  * [Alternative Single Jar Solutions](alternatives.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../specification/executable-jar/alternatives.html)!  
---|---  
  
# Alternative Single Jar Solutions

If the preceding restrictions mean that you cannot use Spring Boot Loader, consider the following alternatives:

  * [Maven Shade Plugin](https://maven.apache.org/plugins/maven-shade-plugin/)

  * [JarClassLoader](http://www.jdotsoft.com/JarClassLoader.php)

  * [OneJar](https://sourceforge.net/projects/one-jar/)

  * [Gradle Shadow Plugin](https://imperceptiblethoughts.com/shadow/)




[Executable Jar Restrictions](restrictions.html) [Common Application Properties](../../appendix/application-properties/index.html)
---
