Title: PropertiesLauncher Features :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/specification/executable-jar/property-launcher.html
HTML: html/docs_spring_io/spring-boot/3.4/specification/executable-jar/PropertiesLauncher_Features_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/specification/executable-jar/PropertiesLauncher_Features_Spring_Boot.png
crawled_at: 2025-06-04T16:03:28.414688
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/specification/pages/executable-jar/property-launcher.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * Specifications
  * [The Executable Jar Format](index.html)
  * [PropertiesLauncher Features](property-launcher.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../specification/executable-jar/property-launcher.html)!  
---|---  
  
# PropertiesLauncher Features

[`PropertiesLauncher`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/loader/launch/PropertiesLauncher.html) has a few special features that can be enabled with external properties (System properties, environment variables, manifest entries, or `loader.properties`). The following table describes these properties:

Key | Purpose  
---|---  
`loader.path` | Comma-separated Classpath, such as `lib,${HOME}/app/lib`. Earlier entries take precedence, like a regular `-classpath` on the `javac` command line.  
`loader.home` | Used to resolve relative paths in `loader.path`. For example, given `loader.path=lib`, then `${loader.home}/lib` is a classpath location (along with all jar files in that directory). This property is also used to locate a `loader.properties` file, as in the following example `[/opt/app](file:///opt/app)` It defaults to `${user.dir}`.  
`loader.args` | Default arguments for the main method (space separated).  
`loader.main` | Name of main class to launch (for example, `com.app.Application`).  
`loader.config.name` | Name of properties file (for example, `launcher`). It defaults to `loader`.  
`loader.config.location` | Path to properties file (for example, `classpath:loader.properties`). It defaults to `loader.properties`.  
`loader.system` | Boolean flag to indicate that all properties should be added to System properties. It defaults to `false`.  
  
When specified as environment variables or manifest entries, the following names should be used:

Key | Manifest entry | Environment variable  
---|---|---  
`loader.path` | `Loader-Path` | `LOADER_PATH`  
`loader.home` | `Loader-Home` | `LOADER_HOME`  
`loader.args` | `Loader-Args` | `LOADER_ARGS`  
`loader.main` | `Start-Class` | `LOADER_MAIN`  
`loader.config.location` | `Loader-Config-Location` | `LOADER_CONFIG_LOCATION`  
`loader.system` | `Loader-System` | `LOADER_SYSTEM`  
  
__ |  Build plugins automatically move the `Main-Class` attribute to `Start-Class` when the uber jar is built. If you use that, specify the name of the class to launch by using the `Main-Class` attribute and leaving out `Start-Class`.   
---|---  
  
The following rules apply to working with [`PropertiesLauncher`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/loader/launch/PropertiesLauncher.html):

  * `loader.properties` is searched for in `loader.home`, then in the root of the classpath, and then in `classpath:/BOOT-INF/classes`. The first location where a file with that name exists is used.

  * `loader.home` is the directory location of an additional properties file (overriding the default) only when `loader.config.location` is not specified.

  * `loader.path` can contain directories (which are scanned recursively for jar and zip files), archive paths, a directory within an archive that is scanned for jar files (for example, `dependencies.jar!/lib`), or wildcard patterns (for the default JVM behavior). Archive paths can be relative to `loader.home` or anywhere in the file system with a `jar:file:` prefix.

  * `loader.path` (if empty) defaults to `BOOT-INF/lib` (meaning a local directory or a nested one if running from an archive). Because of this, [`PropertiesLauncher`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/loader/launch/PropertiesLauncher.html) behaves the same as [`JarLauncher`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/loader/launch/JarLauncher.html) when no additional configuration is provided.

  * `loader.path` can not be used to configure the location of `loader.properties` (the classpath used to search for the latter is the JVM classpath when [`PropertiesLauncher`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/loader/launch/PropertiesLauncher.html) is launched).

  * Placeholder replacement is done from System and environment variables plus the properties file itself on all values before use.

  * The search order for properties (where it makes sense to look in more than one place) is environment variables, system properties, `loader.properties`, the exploded archive manifest, and the archive manifest.




[Launching Executable Jars](launching.html) [Executable Jar Restrictions](restrictions.html)
---
