Title: Executable Jar Restrictions :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/specification/executable-jar/restrictions.html
HTML: html/docs_spring_io/spring-boot/3.4/specification/executable-jar/Executable_Jar_Restrictions_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/specification/executable-jar/Executable_Jar_Restrictions_Spring_Boot.png
crawled_at: 2025-06-04T19:23:34.478212
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/specification/pages/executable-jar/restrictions.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * Specifications
  * [The Executable Jar Format](index.html)
  * [Executable Jar Restrictions](restrictions.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../specification/executable-jar/restrictions.html)!  
---|---  
  
# Executable Jar Restrictions

You need to consider the following restrictions when working with a Spring Boot Loader packaged application:

  * Zip entry compression: The [`ZipEntry`](https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/zip/ZipEntry.html) for a nested jar must be saved by using the [`ZipEntry.STORED`](https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/zip/ZipEntry.html#STORED) method. This is required so that we can seek directly to individual content within the nested jar. The content of the nested jar file itself can still be compressed, as can any other entry in the outer jar.




  * System classLoader: Launched applications should use `Thread.getContextClassLoader()` when loading classes (most libraries and frameworks do so by default). Trying to load nested jar classes with `ClassLoader.getSystemClassLoader()` fails. `java.util.Logging` always uses the system classloader. For this reason, you should consider a different logging implementation.




[PropertiesLauncher Features](property-launcher.html) [Alternative Single Jar Solutions](alternatives.html)
---
