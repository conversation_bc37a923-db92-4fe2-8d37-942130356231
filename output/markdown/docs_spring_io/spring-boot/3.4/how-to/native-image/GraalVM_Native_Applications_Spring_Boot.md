Title: GraalVM Native Applications :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/native-image/index.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/native-image/GraalVM_Native_Applications_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/native-image/GraalVM_Native_Applications_Spring_Boot.png
crawled_at: 2025-06-04T16:03:22.966605
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/native-image/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [How-to Guides](../index.html)
  * [GraalVM Native Applications](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../how-to/native-image/index.html)!  
---|---  
  
# GraalVM Native Applications

This section contains details on developing and testing Spring Boot applications as GraalVM native images.

__ |  For an overview of GraalVM native image concepts, see the [Introducing GraalVM Native Images](../../reference/packaging/native-image/introducing-graalvm-native-images.html) section.   
---|---  
  
[Ahead-of-Time Processing](../aot.html) [Developing Your First GraalVM Native Application](developing-your-first-application.html)
---
