Title: Ahead-of-Time Processing :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/aot.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/Ahead_of_Time_Processing_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/Ahead_of_Time_Processing_Spring_Boot.png
crawled_at: 2025-06-04T16:05:39.352035
---
Search CTRL + k

### Ahead-of-Time Processing

  * Conditions



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/aot.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [How-to Guides](index.html)
  * [Ahead-of-Time Processing](aot.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../how-to/aot.html)!  
---|---  
  
# Ahead-of-Time Processing

### Ahead-of-Time Processing

  * Conditions



A number of questions often arise when people use the ahead-of-time processing of Spring Boot applications. This section addresses those questions.

## Conditions

Ahead-of-time processing optimizes the application and evaluates [`@Conditional`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Conditional.html) annotations based on the environment at build time. [Profiles](../reference/features/profiles.html) are implemented through conditions and are therefore affected, too.

If you want beans that are created based on a condition in an ahead-of-time optimized application, you have to set up the environment when building the application. The beans which are created while ahead-of-time processing at build time are then always created when running the application and can’t be switched off. To do this, you can set the profiles which should be used when building the application.

For Maven, this works by setting the `profiles` configuration of the `spring-boot-maven-plugin:process-aot` execution:
    
    
    <profile>
        <id>native</id>
        <build>
            <pluginManagement>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>process-aot</id>
                                <configuration>
                                    <profiles>profile-a,profile-b</profiles>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </pluginManagement>
        </build>
    </profile>
    
    Copied!

For Gradle, you need to configure the `ProcessAot` task:
    
    
    tasks.withType(org.springframework.boot.gradle.tasks.aot.ProcessAot).configureEach {
        args('--spring.profiles.active=profile-a,profile-b')
    }
    
    Copied!

Profiles which only change configuration properties that don’t influence conditions are supported without limitations when running ahead-of-time optimized applications.

[Build](build.html) [GraalVM Native Applications](native-image/index.html)
---
