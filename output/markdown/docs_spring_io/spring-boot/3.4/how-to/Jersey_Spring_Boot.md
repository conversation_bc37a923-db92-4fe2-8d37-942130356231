Title: Jersey :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/jersey.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/Jersey_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/Jersey_Spring_Boot.png
crawled_at: 2025-06-04T16:01:35.439244
---
Search CTRL + k

### Jersey

  * Secure Jersey Endpoints with Spring Security
  * Use Jersey Alongside Another Web Framework



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/jersey.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [How-to Guides](index.html)
  * [Jersey](jersey.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../how-to/jersey.html)!  
---|---  
  
# Jersey

### Jersey

  * Secure Jersey Endpoints with Spring Security
  * Use Jersey Alongside Another Web Framework



## Secure Jersey Endpoints with Spring Security

Spring Security can be used to secure a Jersey-based web application in much the same way as it can be used to secure a Spring MVC-based web application. However, if you want to use Spring Security’s method-level security with Jersey, you must configure Jersey to use `setStatus(int)` rather `sendError(int)`. This prevents Jersey from committing the response before Spring Security has had an opportunity to report an authentication or authorization failure to the client.

The `jersey.config.server.response.setStatusOverSendError` property must be set to `true` on the application’s [`ResourceConfig`](https://javadoc.io/doc/org.glassfish.jersey.core/jersey-server/3.1.10/org/glassfish/jersey/server/ResourceConfig.html) bean, as shown in the following example:
    
    
    import java.util.Collections;
    
    import org.glassfish.jersey.server.ResourceConfig;
    
    import org.springframework.stereotype.Component;
    
    @Component
    public class JerseySetStatusOverSendErrorConfig extends ResourceConfig {
    
    	public JerseySetStatusOverSendErrorConfig() {
    		register(Endpoint.class);
    		setProperties(Collections.singletonMap("jersey.config.server.response.setStatusOverSendError", true));
    	}
    
    }
    
    Copied!

## Use Jersey Alongside Another Web Framework

To use Jersey alongside another web framework, such as Spring MVC, it should be configured so that it will allow the other framework to handle requests that it cannot handle. First, configure Jersey to use a filter rather than a servlet by configuring the `spring.jersey.type` application property with a value of `filter`. Second, configure your [`ResourceConfig`](https://javadoc.io/doc/org.glassfish.jersey.core/jersey-server/3.1.10/org/glassfish/jersey/server/ResourceConfig.html) to forward requests that would have resulted in a 404, as shown in the following example.
    
    
    import org.glassfish.jersey.server.ResourceConfig;
    import org.glassfish.jersey.servlet.ServletProperties;
    
    import org.springframework.stereotype.Component;
    
    @Component
    public class JerseyConfig extends ResourceConfig {
    
    	public JerseyConfig() {
    		register(Endpoint.class);
    		property(ServletProperties.FILTER_FORWARD_ON_404, true);
    	}
    
    }
    
    Copied!

[Spring MVC](spring-mvc.html) [HTTP Clients](http-clients.html)
---
