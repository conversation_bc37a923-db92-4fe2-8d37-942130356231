Title: Deploying Spring Boot Applications :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/deployment/index.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/deployment/Deploying_Spring_Boot_Applications_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/deployment/Deploying_Spring_Boot_Applications_Spring_Boot.png
crawled_at: 2025-06-04T19:22:10.373919
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/deployment/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [How-to Guides](../index.html)
  * [Deploying Spring Boot Applications](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../how-to/deployment/index.html)!  
---|---  
  
# Deploying Spring Boot Applications

Spring Boot’s flexible packaging options provide a great deal of choice when it comes to deploying your application. You can deploy Spring Boot applications to a variety of cloud platforms, to virtual/real machines, or make them fully executable for Unix systems.

This section covers some of the more common deployment scenarios.

[Class Data Sharing](../class-data-sharing.html) [Traditional Deployment](traditional-deployment.html)
---
