Title: Logging :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/logging.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/Logging_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/Logging_Spring_Boot.png
crawled_at: 2025-06-04T16:02:22.255934
---
Search CTRL + k

### Logging

  * Configure Logback for Logging
  * Configure Logback for File-only Output
  * Configure Log4j for Logging
  * Use YAML or JSON to Configure Log4j 2
  * Use Composite Configuration to Configure Log4j 2



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/logging.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [How-to Guides](index.html)
  * [Logging](logging.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../how-to/logging.html)!  
---|---  
  
# Logging

### Logging

  * Configure Logback for Logging
  * Configure Logback for File-only Output
  * Configure Log4j for Logging
  * Use YAML or JSON to Configure Log4j 2
  * Use Composite Configuration to Configure Log4j 2



Spring Boot has no mandatory logging dependency, except for the Commons Logging API, which is typically provided by Spring Framework’s `spring-jcl` module. To use [Logback](https://logback.qos.ch), you need to include it and `spring-jcl` on the classpath. The recommended way to do that is through the starters, which all depend on `spring-boot-starter-logging`. For a web application, you only need `spring-boot-starter-web`, since it depends transitively on the logging starter. If you use Maven, the following dependency adds logging for you:
    
    
    <dependency>
    	<groupId>org.springframework.boot</groupId>
    	<artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    Copied!

Spring Boot has a [`LoggingSystem`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/logging/LoggingSystem.html) abstraction that attempts to configure logging based on the content of the classpath. If Logback is available, it is the first choice.

If the only change you need to make to logging is to set the levels of various loggers, you can do so in `application.properties` by using the "logging.level" prefix, as shown in the following example:

  * Properties

  * YAML



    
    
    logging.level.org.springframework.web=debug
    logging.level.org.hibernate=error
    
    Copied!
    
    
    logging:
      level:
        org.springframework.web: "debug"
        org.hibernate: "error"
    
    Copied!

You can also set the location of a file to which the log will be written (in addition to the console) by using `logging.file.name`.

To configure the more fine-grained settings of a logging system, you need to use the native configuration format supported by the [`LoggingSystem`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/logging/LoggingSystem.html) in question. By default, Spring Boot picks up the native configuration from its default location for the system (such as `classpath:logback.xml` for Logback), but you can set the location of the config file by using the `logging.config` property.

## Configure Logback for Logging

If you need to apply customizations to logback beyond those that can be achieved with `application.properties`, you will need to add a standard logback configuration file. You can add a `logback.xml` file to the root of your classpath for logback to find. You can also use `logback-spring.xml` if you want to use the Spring Boot [Logback Extensions](../reference/features/logging.html#features.logging.logback-extensions).

__ |  The Logback documentation has a [dedicated section that covers configuration](https://logback.qos.ch/manual/configuration.html) in some detail.   
---|---  
  
Spring Boot provides a number of logback configurations that can be `included` in your own configuration. These includes are designed to allow certain common Spring Boot conventions to be re-applied.

The following files are provided under `org/springframework/boot/logging/logback/`:

  * `defaults.xml` \- Provides conversion rules, pattern properties and common logger configurations.

  * `console-appender.xml` \- Adds a [`ConsoleAppender`](https://logback.qos.ch/apidocs/ch.qos.logback.core/ch/qos/logback/core/ConsoleAppender.html) using the `CONSOLE_LOG_PATTERN`.

  * `structured-console-appender.xml` \- Adds a [`ConsoleAppender`](https://logback.qos.ch/apidocs/ch.qos.logback.core/ch/qos/logback/core/ConsoleAppender.html) using structured logging in the `CONSOLE_LOG_STRUCTURED_FORMAT`.

  * `file-appender.xml` \- Adds a [`RollingFileAppender`](https://logback.qos.ch/apidocs/ch.qos.logback.core/ch/qos/logback/core/rolling/RollingFileAppender.html) using the `FILE_LOG_PATTERN` and `ROLLING_FILE_NAME_PATTERN` with appropriate settings.

  * `structured-file-appender.xml` \- Adds a [`RollingFileAppender`](https://logback.qos.ch/apidocs/ch.qos.logback.core/ch/qos/logback/core/rolling/RollingFileAppender.html) using the `ROLLING_FILE_NAME_PATTERN` with structured logging in the `FILE_LOG_STRUCTURED_FORMAT`.




In addition, a legacy `base.xml` file is provided for compatibility with earlier versions of Spring Boot.

A typical custom `logback.xml` file would look something like this:
    
    
    <?xml version="1.0" encoding="UTF-8"?>
    <configuration>
    	<include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    	<include resource="org/springframework/boot/logging/logback/console-appender.xml" />
    	<root level="INFO">
    		<appender-ref ref="CONSOLE" />
    	</root>
    	<logger name="org.springframework.web" level="DEBUG"/>
    </configuration>
    
    Copied!

Your logback configuration file can also make use of System properties that the [`LoggingSystem`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/logging/LoggingSystem.html) takes care of creating for you:

  * `${PID}`: The current process ID.

  * `${LOG_FILE}`: Whether `logging.file.name` was set in Boot’s external configuration.

  * `${LOG_PATH}`: Whether `logging.file.path` (representing a directory for log files to live in) was set in Boot’s external configuration.

  * `${LOG_EXCEPTION_CONVERSION_WORD}`: Whether `logging.exception-conversion-word` was set in Boot’s external configuration.

  * `${ROLLING_FILE_NAME_PATTERN}`: Whether `logging.pattern.rolling-file-name` was set in Boot’s external configuration.




Spring Boot also provides some nice ANSI color terminal output on a console (but not in a log file) by using a custom Logback converter. See the `CONSOLE_LOG_PATTERN` in the `defaults.xml` configuration for an example.

If Groovy is on the classpath, you should be able to configure Logback with `logback.groovy` as well. If present, this setting is given preference.

__ |  Spring extensions are not supported with Groovy configuration. Any `logback-spring.groovy` files will not be detected.   
---|---  
  
### Configure Logback for File-only Output

If you want to disable console logging and write output only to a file, you need a custom `logback-spring.xml` that imports `file-appender.xml` but not `console-appender.xml`, as shown in the following example:
    
    
    <?xml version="1.0" encoding="UTF-8"?>
    <configuration>
    	<include resource="org/springframework/boot/logging/logback/defaults.xml" />
    	<property name="LOG_FILE" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}/}spring.log}"/>
    	<include resource="org/springframework/boot/logging/logback/file-appender.xml" />
    	<root level="INFO">
    		<appender-ref ref="FILE" />
    	</root>
    </configuration>
    
    Copied!

You also need to add `logging.file.name` to your `application.properties` or `application.yaml`, as shown in the following example:

  * Properties

  * YAML



    
    
    logging.file.name=myapplication.log
    
    Copied!
    
    
    logging:
      file:
        name: "myapplication.log"
    
    Copied!

## Configure Log4j for Logging

Spring Boot supports [Log4j 2](https://logging.apache.org/log4j/2.x/) for logging configuration if it is on the classpath. If you use the starters for assembling dependencies, you have to exclude Logback and then include Log4j 2 instead. If you do not use the starters, you need to provide (at least) `spring-jcl` in addition to Log4j 2.

The recommended path is through the starters, even though it requires some jiggling. The following example shows how to set up the starters in Maven:
    
    
    <dependency>
    	<groupId>org.springframework.boot</groupId>
    	<artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
    	<groupId>org.springframework.boot</groupId>
    	<artifactId>spring-boot-starter</artifactId>
    	<exclusions>
    		<exclusion>
    			<groupId>org.springframework.boot</groupId>
    			<artifactId>spring-boot-starter-logging</artifactId>
    		</exclusion>
    	</exclusions>
    </dependency>
    <dependency>
    	<groupId>org.springframework.boot</groupId>
    	<artifactId>spring-boot-starter-log4j2</artifactId>
    </dependency>
    
    Copied!

Gradle provides a few different ways to set up the starters. One way is to use a [module replacement](https://docs.gradle.org/current/userguide/resolution_rules.html#sec:module_replacement). To do so, declare a dependency on the Log4j 2 starter and tell Gradle that any occurrences of the default logging starter should be replaced by the Log4j 2 starter, as shown in the following example:
    
    
    dependencies {
    	implementation "org.springframework.boot:spring-boot-starter-log4j2"
    	modules {
    		module("org.springframework.boot:spring-boot-starter-logging") {
    			replacedBy("org.springframework.boot:spring-boot-starter-log4j2", "Use Log4j2 instead of Logback")
    		}
    	}
    }
    
    Copied!

__ |  The Log4j starters gather together the dependencies for common logging requirements (such as having Tomcat use `java.util.logging` but configuring the output using Log4j 2).   
---|---  
  
__ |  To ensure that debug logging performed using `java.util.logging` is routed into Log4j 2, configure its [JDK logging adapter](https://logging.apache.org/log4j/2.x/log4j-jul.html) by setting the `java.util.logging.manager` system property to `org.apache.logging.log4j.jul.LogManager`.   
---|---  
  
### Use YAML or JSON to Configure Log4j 2

In addition to its default XML configuration format, Log4j 2 also supports YAML and JSON configuration files. To configure Log4j 2 to use an alternative configuration file format, add the appropriate dependencies to the classpath and name your configuration files to match your chosen file format, as shown in the following example:

Format | Dependencies | File names  
---|---|---  
YAML |  `com.fasterxml.jackson.core:jackson-databind` \+ `com.fasterxml.jackson.dataformat:jackson-dataformat-yaml` |  `log4j2.yaml` \+ `log4j2.yml`  
JSON |  `com.fasterxml.jackson.core:jackson-databind` |  `log4j2.json` \+ `log4j2.jsn`  
  
### Use Composite Configuration to Configure Log4j 2

Log4j 2 has support for combining multiple configuration files into a single composite configuration. To use this support in Spring Boot, configure `logging.log4j2.config.override` with the locations of one or more secondary configuration files. The secondary configuration files will be merged with the primary configuration, whether the primary’s source is Spring Boot’s defaults, a standard location such as `log4j.xml`, or the location configured by the `logging.config` property.

[HTTP Clients](http-clients.html) [Data Access](data-access.html)
---
