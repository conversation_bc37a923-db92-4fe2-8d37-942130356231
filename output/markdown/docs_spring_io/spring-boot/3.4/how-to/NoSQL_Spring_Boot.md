Title: NoSQL :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/nosql.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/NoSQL_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/NoSQL_Spring_Boot.png
crawled_at: 2025-06-04T16:05:11.758842
---
Search CTRL + k

### NoSQL

  * Use Jedis Instead of Lettuce



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/nosql.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [How-to Guides](index.html)
  * [NoSQL](nosql.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../how-to/nosql.html)!  
---|---  
  
# NoSQL

### NoSQL

  * Use Jedis Instead of Lettuce



Spring Boot offers a number of starters that support NoSQL technologies. This section answers questions that arise from using NoSQL with Spring Boot.

## Use Jedis Instead of Lettuce

By default, the Spring Boot starter (`spring-boot-starter-data-redis`) uses [Lettuce](https://github.com/lettuce-io/lettuce-core/). You need to exclude that dependency and include the [Jedis](https://github.com/xetorthio/jedis/) one instead. Spring Boot manages both of these dependencies, allowing you to switch to Jedis without specifying a version.

The following example shows how to accomplish this in Maven:
    
    
    <dependency>
    	<groupId>org.springframework.boot</groupId>
    	<artifactId>spring-boot-starter-data-redis</artifactId>
    	<exclusions>
    		<exclusion>
    			<groupId>io.lettuce</groupId>
    			<artifactId>lettuce-core</artifactId>
    		</exclusion>
    	</exclusions>
    </dependency>
    <dependency>
    	<groupId>redis.clients</groupId>
    	<artifactId>jedis</artifactId>
    </dependency>
    
    Copied!

The following example shows how to accomplish this in Gradle:
    
    
    dependencies {
    	implementation('org.springframework.boot:spring-boot-starter-data-redis') {
    	    exclude group: 'io.lettuce', module: 'lettuce-core'
    	}
    	implementation 'redis.clients:jedis'
    	// ...
    }
    
    Copied!

[Database Initialization](data-initialization.html) [Messaging](messaging.html)
---
