Title: Class Data Sharing :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/class-data-sharing.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/Class_Data_Sharing_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/Class_Data_Sharing_Spring_Boot.png
crawled_at: 2025-06-04T16:05:00.796560
---
Search CTRL + k

### Class Data Sharing

  * Packaging an Application Using CDS and Buildpacks
  * Packaging an Application Using CDS and Dockerfiles
  * Preventing Remote Services Interaction During the Training Run



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/class-data-sharing.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [How-to Guides](index.html)
  * [Class Data Sharing](class-data-sharing.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../how-to/class-data-sharing.html)!  
---|---  
  
# Class Data Sharing

### Class Data Sharing

  * Packaging an Application Using CDS and Buildpacks
  * Packaging an Application Using CDS and Dockerfiles
  * Preventing Remote Services Interaction During the Training Run



This section includes information about using Class Data Sharing (CDS) with Spring Boot applications. For an overview of Spring Boot support for CDS, see [Class Data Sharing](../reference/packaging/class-data-sharing.html).

## Packaging an Application Using CDS and Buildpacks

Spring Boot’s [support for Cloud Native Buildpacks](../reference/packaging/container-images/cloud-native-buildpacks.html) along with the [Paketo Java buildpack](https://paketo.io/docs/reference/java-reference) and its [Spring Boot support](https://paketo.io/docs/reference/java-reference/#spring-boot-applications) can be used to generate a Docker image containing a CDS-optimized application.

To enable CDS optimization in a generated Docker image, the buildpack environment variable `BP_JVM_CDS_ENABLED` should be set to `true` when building the image as described in the [Maven plugin](../maven-plugin/build-image.html#build-image.examples.builder-configuration) and [Gradle plugin](../gradle-plugin/packaging-oci-image.html#build-image.examples.builder-configuration) documentation. This will cause the buildpack to do a training run of the application, save the CDS archive in the image, and use the CDS archive when launching the application.

The Paketo Buildpack for Spring Boot [documentation](https://github.com/paketo-buildpacks/spring-boot?tab=readme-ov-file#configuration) has information on other configuration options that can be enabled with builder environment variables, like `CDS_TRAINING_JAVA_TOOL_OPTIONS` that allows to override the default `JAVA_TOOL_OPTIONS`, only for the CDS training run.

## Packaging an Application Using CDS and Dockerfiles

If you don’t want to use Cloud Native Buildpacks, it is also possible to use CDS with a `Dockerfile`. For more information about that, please see the [Dockerfiles reference documentation](../reference/packaging/container-images/dockerfiles.html#packaging.container-images.dockerfiles.cds).

## Preventing Remote Services Interaction During the Training Run

When performing the training run, it may be needed to customize the Spring Boot application configuration to prevent connections to remote services that may happen before the Spring lifecycle is started. This can typically happen with early database interactions and can be handled via related configuration that can be applied by default to your application (or specifically to the training run) to prevent such interactions, see [related documentation](https://github.com/spring-projects/spring-lifecycle-smoke-tests/blob/main/README.adoc#training-run-configuration).

[Testing GraalVM Native Images](native-image/testing-native-applications.html) [Deploying Spring Boot Applications](deployment/index.html)
---
