Title: Docker Compose :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/docker-compose.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/Docker_Compose_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/Docker_Compose_Spring_Boot.png
crawled_at: 2025-06-04T16:01:14.844888
---
Search CTRL + k

### Docker Compose

  * Customizing the JDBC URL
  * Sharing Services Between Multiple Applications



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/docker-compose.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [How-to Guides](index.html)
  * [Docker Compose](docker-compose.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../how-to/docker-compose.html)!  
---|---  
  
# Docker Compose

### Docker Compose

  * Customizing the JDBC URL
  * Sharing Services Between Multiple Applications



This section includes topics relating to the Docker Compose support in Spring Boot.

## Customizing the JDBC URL

When using [`JdbcConnectionDetails`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/jdbc/JdbcConnectionDetails.html) with Docker Compose, the parameters of the JDBC URL can be customized by applying the `org.springframework.boot.jdbc.parameters` label to the service. For example:
    
    
    services:
      postgres:
        image: 'postgres:15.3'
        environment:
          - 'POSTGRES_USER=myuser'
          - 'POSTGRES_PASSWORD=secret'
          - 'POSTGRES_DB=mydb'
        ports:
          - '5432:5432'
        labels:
          org.springframework.boot.jdbc.parameters: 'ssl=true&sslmode=require'
    
    Copied!

With this Docker Compose file in place, the JDBC URL used is `**************************************************************`.

## Sharing Services Between Multiple Applications

If you want to share services between multiple applications, create the `compose.yaml` file in one of the applications and then use the configuration property `spring.docker.compose.file` in the other applications to reference the `compose.yaml` file. You should also set `spring.docker.compose.lifecycle-management` to `start-only`, as it defaults to `start-and-stop` and stopping one application would shut down the shared services for the other still running applications as well. Setting it to `start-only` won’t stop the shared services on application stop, but a caveat is that if you shut down all applications, the services remain running. You can stop the services manually by running `docker compose stop` on the command line in the directory which contains the `compose.yaml` file.

[Installing Spring Boot Applications](deployment/installing.html) [Build Tool Plugins](../build-tool-plugin/index.html)
---
