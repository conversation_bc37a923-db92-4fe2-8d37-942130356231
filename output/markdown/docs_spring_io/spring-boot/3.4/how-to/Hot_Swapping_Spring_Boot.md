Title: Hot Swapping :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/how-to/hotswapping.html
HTML: html/docs_spring_io/spring-boot/3.4/how-to/Hot_Swapping_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/how-to/Hot_Swapping_Spring_Boot.png
crawled_at: 2025-06-04T16:03:20.169450
---
Search CTRL + k

### Hot Swapping

  * Reload Static Content
  * Reload Templates without Restarting the Container
  * Thymeleaf Templates
  * FreeMarker Templates
  * Groovy Templates
  * Fast Application Restarts
  * Reload Java Classes without Restarting the Container



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/how-to/pages/hotswapping.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [How-to Guides](index.html)
  * [Hot Swapping](hotswapping.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../how-to/hotswapping.html)!  
---|---  
  
# Hot Swapping

### Hot Swapping

  * Reload Static Content
  * Reload Templates without Restarting the Container
  * Thymeleaf Templates
  * FreeMarker Templates
  * Groovy Templates
  * Fast Application Restarts
  * Reload Java Classes without Restarting the Container



Spring Boot supports hot swapping. This section answers questions about how it works.

## Reload Static Content

There are several options for hot reloading. The recommended approach is to use [`spring-boot-devtools`](../reference/using/devtools.html), as it provides additional development-time features, such as support for fast application restarts and LiveReload as well as sensible development-time configuration (such as template caching). Devtools works by monitoring the classpath for changes. This means that static resource changes must be "built" for the change to take effect. By default, this happens automatically in Eclipse when you save your changes. In IntelliJ IDEA, the Make Project command triggers the necessary build. Due to the [default restart exclusions](../reference/using/devtools.html#using.devtools.restart.excluding-resources), changes to static resources do not trigger a restart of your application. They do, however, trigger a live reload.

Alternatively, running in an IDE (especially with debugging on) is a good way to do development (all modern IDEs allow reloading of static resources and usually also allow hot-swapping of Java class changes).

Finally, the [Maven and Gradle plugins](../build-tool-plugin/index.html) can be configured (see the `addResources` property) to support running from the command line with reloading of static files directly from source. You can use that with an external css/js compiler process if you are writing that code with higher-level tools.

## Reload Templates without Restarting the Container

Most of the templating technologies supported by Spring Boot include a configuration option to disable caching (described later in this document). If you use the `spring-boot-devtools` module, these properties are [automatically configured](../reference/using/devtools.html#using.devtools.property-defaults) for you at development time.

### Thymeleaf Templates

If you use Thymeleaf, set `spring.thymeleaf.cache` to `false`. See [`ThymeleafAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/thymeleaf/ThymeleafAutoConfiguration.java) for other Thymeleaf customization options.

### FreeMarker Templates

If you use FreeMarker, set `spring.freemarker.cache` to `false`. See [`FreeMarkerAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/freemarker/FreeMarkerAutoConfiguration.java) for other FreeMarker customization options.

__ |  Template caching for FreeMarker is not supported with WebFlux.   
---|---  
  
### Groovy Templates

If you use Groovy templates, set `spring.groovy.template.cache` to `false`. See [`GroovyTemplateAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/groovy/template/GroovyTemplateAutoConfiguration.java) for other Groovy customization options.

## Fast Application Restarts

The `spring-boot-devtools` module includes support for automatic application restarts. While not as fast as technologies such as [JRebel](https://www.jrebel.com/products/jrebel) it is usually significantly faster than a “cold start”. You should probably give it a try before investigating some of the more complex reload options discussed later in this document.

For more details, see the [Developer Tools](../reference/using/devtools.html) section.

## Reload Java Classes without Restarting the Container

Many modern IDEs (Eclipse, IDEA, and others) support hot swapping of bytecode. Consequently, if you make a change that does not affect class or method signatures, it should reload cleanly with no side effects.

[Security](security.html) [Testing](testing.html)
---
