Title: Documentation Overview :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/documentation.html
HTML: html/docs_spring_io/spring-boot/3.4/Documentation_Overview_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/Documentation_Overview_Spring_Boot.png
crawled_at: 2025-06-04T16:07:19.757019
---
Search CTRL + k

### Documentation Overview

  * First Steps
  * Upgrading From an Earlier Version
  * Developing With Spring Boot
  * Learning About Spring Boot Features
  * Web
  * Data
  * Messaging
  * IO
  * Container Images
  * Moving to Production
  * Optimizing for Production
  * Advanced Topics



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/ROOT/pages/documentation.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](index.html)
  * [Documentation](documentation.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../documentation.html)!  
---|---  
  
# Documentation Overview

### Documentation Overview

  * First Steps
  * Upgrading From an Earlier Version
  * Developing With Spring Boot
  * Learning About Spring Boot Features
  * Web
  * Data
  * Messaging
  * IO
  * Container Images
  * Moving to Production
  * Optimizing for Production
  * Advanced Topics



This section provides a brief overview of Spring Boot reference documentation. It serves as a map for the rest of the document.

## First Steps

If you are getting started with Spring Boot or 'Spring' in general, start with the following topics:

  * **From scratch:** [Overview](index.html) | [Requirements](system-requirements.html) | [Installation](installing.html)

  * **Tutorial:** [Part 1](tutorial/first-application/index.html) | [Part 2](tutorial/first-application/index.html#getting-started.first-application.code)

  * **Running your example:** [Part 1](tutorial/first-application/index.html#getting-started.first-application.run) | [Part 2](tutorial/first-application/index.html#getting-started.first-application.executable-jar)




## Upgrading From an Earlier Version

You should always ensure that you are running a [supported version](https://github.com/spring-projects/spring-boot/wiki/Supported-Versions) of Spring Boot.

Depending on the version that you are upgrading to, you can find some additional tips here:

  * **From 1.x to 2.x:** [Upgrading from 1.x](upgrading.html#upgrading.from-1x)

  * **From 2.x:** [Upgrading from 2.x](upgrading.html#upgrading.from-2x)

  * **To a new feature release:** [Upgrading to New Feature Release](upgrading.html#upgrading.to-feature)

  * **Spring Boot CLI:** [Upgrading the Spring Boot CLI](upgrading.html#upgrading.cli)




## Developing With Spring Boot

Ready to actually start using Spring Boot? [We have you covered](reference/using/index.html):

  * **Build systems:** [Maven](reference/using/build-systems.html#using.build-systems.maven) | [Gradle](reference/using/build-systems.html#using.build-systems.gradle) | [Ant](reference/using/build-systems.html#using.build-systems.ant) | [Starters](reference/using/build-systems.html#using.build-systems.starters)

  * **Best practices:** [Code Structure](reference/using/structuring-your-code.html) | [@Configuration](reference/using/configuration-classes.html) | [@EnableAutoConfiguration](reference/using/auto-configuration.html) | [Beans and Dependency Injection](reference/using/spring-beans-and-dependency-injection.html)

  * **Running your code:** [IDE](reference/using/running-your-application.html#using.running-your-application.from-an-ide) | [Packaged](reference/using/running-your-application.html#using.running-your-application.as-a-packaged-application) | [Maven](reference/using/running-your-application.html#using.running-your-application.with-the-maven-plugin) | [Gradle](reference/using/running-your-application.html#using.running-your-application.with-the-gradle-plugin)

  * **Packaging your app:** [Production jars](reference/using/packaging-for-production.html)

  * **Spring Boot CLI:** [Using the CLI](cli/index.html)




## Learning About Spring Boot Features

Need more details about Spring Boot’s core features? [The following content is for you](reference/features/index.html):

  * **Spring Application:** [SpringApplication](reference/features/spring-application.html)

  * **External Configuration:** [External Configuration](reference/features/external-config.html)

  * **Profiles:** [Profiles](reference/features/profiles.html)

  * **Logging:** [Logging](reference/features/logging.html)




## Web

If you develop Spring Boot web applications, take a look at the following content:

  * **Servlet Web Applications:** [Spring MVC, Jersey, Embedded Servlet Containers](reference/web/servlet.html)

  * **Reactive Web Applications:** [Spring Webflux, Embedded Servlet Containers](reference/web/reactive.html)

  * **Graceful Shutdown:** [Graceful Shutdown](reference/web/graceful-shutdown.html)

  * **Spring Security:** [Default Security Configuration, Auto-configuration for OAuth2, SAML](reference/web/spring-security.html)

  * **Spring Session:** [Auto-configuration for Spring Session](reference/web/spring-session.html)

  * **Spring HATEOAS:** [Auto-configuration for Spring HATEOAS](reference/web/spring-hateoas.html)




## Data

If your application deals with a datastore, you can see how to configure that here:

  * **SQL:** [Configuring a SQL Datastore, Embedded Database support, Connection pools, and more.](reference/data/sql.html)

  * **NOSQL:** [Auto-configuration for NOSQL stores such as Redis, MongoDB, Neo4j, and others.](reference/data/nosql.html)




## Messaging

If your application uses any messaging protocol, see one or more of the following sections:

  * **JMS:** [Auto-configuration for ActiveMQ and Artemis, Sending and Receiving messages through JMS](reference/messaging/jms.html)

  * **AMQP:** [Auto-configuration for RabbitMQ](reference/messaging/amqp.html)

  * **Kafka:** [Auto-configuration for Spring Kafka](reference/messaging/kafka.html)

  * **Pulsar:** [Auto-configuration for Spring for Apache Pulsar](reference/messaging/pulsar.html)

  * **RSocket:** [Auto-configuration for Spring Framework’s RSocket Support](reference/messaging/rsocket.html)

  * **Spring Integration:** [Auto-configuration for Spring Integration](reference/messaging/spring-integration.html)




## IO

If your application needs IO capabilities, see one or more of the following sections:

  * **Caching:** [Caching support with EhCache, Hazelcast, Infinispan, and more](reference/io/caching.html)

  * **Quartz:** [Quartz Scheduling](reference/io/quartz.html)

  * **Mail:** [Sending Email](reference/io/email.html)

  * **Validation:** [JSR-303 Validation](reference/io/validation.html)

  * **REST Clients:** [Calling REST Services with RestTemplate and WebClient](reference/io/rest-client.html)

  * **Webservices:** [Auto-configuration for Spring Web Services](reference/io/webservices.html)

  * **JTA:** [Distributed Transactions with JTA](reference/io/jta.html)




## Container Images

Spring Boot provides first-class support for building efficient container images. You can read more about it here:

  * **Efficient Container Images:** [Tips to optimize container images such as Docker images](reference/packaging/container-images/efficient-images.html)

  * **Dockerfiles:** [Building container images using dockerfiles](reference/packaging/container-images/dockerfiles.html)

  * **Cloud Native Buildpacks:** [Support for Cloud Native Buildpacks with Maven and Gradle](reference/packaging/container-images/cloud-native-buildpacks.html)




## Moving to Production

When you are ready to push your Spring Boot application to production, we have [some tricks](how-to/actuator.html) that you might like:

  * **Management endpoints:** [Overview](reference/actuator/endpoints.html)

  * **Connection options:** [HTTP](reference/actuator/monitoring.html) | [JMX](reference/actuator/jmx.html)

  * **Monitoring:** [Metrics](reference/actuator/metrics.html) | [Auditing](reference/actuator/auditing.html) | [HTTP Exchanges](reference/actuator/http-exchanges.html) | [Process](reference/actuator/process-monitoring.html)




## Optimizing for Production

Spring Boot applications can be optimized for production using technologies described in these sections:

  * **Efficient Deployments:** [Unpacking the Executable JAR](reference/packaging/efficient.html#packaging.efficient.unpacking)

  * **GraalVM Native Images:** [Introduction](reference/packaging/native-image/introducing-graalvm-native-images.html) | [Advanced Topics](reference/packaging/native-image/advanced-topics.html) | [Getting Started](how-to/native-image/developing-your-first-application.html) | [Testing](how-to/native-image/testing-native-applications.html)

  * **Class Data Sharing:** [Overview](reference/packaging/class-data-sharing.html)

  * **Checkpoint and Restore** [Overview](reference/packaging/checkpoint-restore.html)




## Advanced Topics

Finally, we have a few topics for more advanced users:

  * **Spring Boot Applications Deployment:** [Cloud Deployment](how-to/deployment/cloud.html) | [OS Service](how-to/deployment/installing.html)

  * **Build tool plugins:** [Maven](maven-plugin/index.html) | [Gradle](gradle-plugin/index.html)

  * **Appendix:** [Application Properties](appendix/application-properties/index.html) | [Configuration Metadata](specification/configuration-metadata/index.html) | [Auto-configuration Classes](appendix/auto-configuration-classes/index.html) | [Test Auto-configuration Annotations](appendix/test-auto-configuration/index.html) | [Executable Jars](specification/executable-jar/index.html) | [Dependency Versions](appendix/dependency-versions/index.html)




[Overview](index.html) [Community](community.html)
---
