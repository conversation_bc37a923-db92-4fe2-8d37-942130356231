Title: Publishing your Application :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/gradle-plugin/publishing.html
HTML: html/docs_spring_io/spring-boot/3.4/gradle-plugin/Publishing_your_Application_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/gradle-plugin/Publishing_your_Application_Spring_Boot.png
crawled_at: 2025-06-04T16:52:35.804930
---
Search CTRL + k

### Publishing your Application

  * Publishing with the Maven-publish Plugin
  * Distributing with the Application Plugin



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-gradle-plugin/src/docs/antora/modules/gradle-plugin/pages/publishing.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Gradle Plugin](index.html)
  * [Publishing your Application](publishing.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../gradle-plugin/publishing.html)!  
---|---  
  
# Publishing your Application

### Publishing your Application

  * Publishing with the Maven-publish Plugin
  * Distributing with the Application Plugin



## Publishing with the Maven-publish Plugin

To publish your Spring Boot jar or war, add it to the publication using the `artifact` method on `MavenPublication`. Pass the task that produces that artifact that you wish to publish to the `artifact` method. For example, to publish the artifact produced by the default `bootJar` task:

  * Groovy

  * Kotlin



    
    
    publishing {
    	publications {
    		bootJava(MavenPublication) {
    			artifact tasks.named("bootJar")
    		}
    	}
    	repositories {
    		maven {
    			url = 'https://repo.example.com'
    		}
    	}
    }
    
    Copied!
    
    
    publishing {
    	publications {
    		create<MavenPublication>("bootJava") {
    			artifact(tasks.named("bootJar"))
    		}
    	}
    	repositories {
    		maven {
    			url = uri("https://repo.example.com")
    		}
    	}
    }
    
    Copied!

## Distributing with the Application Plugin

When the [`application` plugin](https://docs.gradle.org/current/userguide/application_plugin.html) is applied a distribution named `boot` is created. This distribution contains the archive produced by the `bootJar` or `bootWar` task and scripts to launch it on Unix-like platforms and Windows. Zip and tar distributions can be built by the `bootDistZip` and `bootDistTar` tasks respectively. To use the `application` plugin, its `mainClassName` property must be configured with the name of your application’s main class.

[Packaging OCI Images](packaging-oci-image.html) [Running your Application with Gradle](running.html)
---
