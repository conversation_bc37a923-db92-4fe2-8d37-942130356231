Title: Ahead-of-Time Processing :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/gradle-plugin/aot.html
HTML: html/docs_spring_io/spring-boot/3.4/gradle-plugin/Ahead_of_Time_Processing_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/gradle-plugin/Ahead_of_Time_Processing_Spring_Boot.png
crawled_at: 2025-06-04T16:52:26.720622
---
Search CTRL + k

### Ahead-of-Time Processing

  * Processing Applications
  * Processing Tests



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-gradle-plugin/src/docs/antora/modules/gradle-plugin/pages/aot.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Gradle Plugin](index.html)
  * [Ahead-of-Time Processing](aot.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../gradle-plugin/aot.html)!  
---|---  
  
# Ahead-of-Time Processing

### Ahead-of-Time Processing

  * Processing Applications
  * Processing Tests



Spring AOT is a process that analyzes your code at build-time in order to generate an optimized version of it. It is most often used to help generate GraalVM native images.

The Spring Boot Gradle plugin provides tasks that can be used to perform AOT processing on both application and test code. The tasks are configured automatically when the [GraalVM Native Image plugin](https://graalvm.github.io/native-build-tools/0.10.6/gradle-plugin.html) is applied:

  * Groovy

  * Kotlin



    
    
    plugins {
    	id 'org.springframework.boot' version '3.4.6'
    	id 'org.graalvm.buildtools.native' version '0.10.6'
    	id 'java'
    }
    
    Copied!
    
    
    plugins {
    	id("org.springframework.boot") version "3.4.6"
    	id("org.graalvm.buildtools.native") version "0.10.6"
    	java
    }
    
    Copied!

## Processing Applications

Based on your `@SpringBootApplication`-annotated main class, the `processAot` task generates a persistent view of the beans that are going to be contributed at runtime in a way that bean instantiation is as straightforward as possible. Additional post-processing of the factory is possible using callbacks. For instance, these are used to generate the necessary reflection configuration that GraalVM needs to initialize the context in a native image.

As the `BeanFactory` is fully prepared at build-time, conditions are also evaluated. This has an important difference compared to what a regular Spring Boot application does at runtime. For instance, if you want to opt-in or opt-out for certain features, you need to configure the environment used at build time to do so. To this end, the `processAot` task is a [`JavaExec`](https://docs.gradle.org/current/dsl/org.gradle.api.tasks.JavaExec.html) task and can be configured with environment variables, system properties, and arguments as needed.

The `nativeCompile` task of the GraalVM Native Image plugin is automatically configured to use the output of the `processAot` task.

## Processing Tests

The AOT engine can be applied to JUnit 5 tests that use Spring’s Test Context Framework. Suitable tests are processed by the `processTestAot` task to generate `ApplicationContextInitializer` code. As with application AOT processing, the `BeanFactory` is fully prepared at build-time. As with `processAot`, the `processTestAot` task is `JavaExec` subclass and can be configured as needed to influence this processing.

The `nativeTest` task of the GraalVM Native Image plugin is automatically configured to use the output of the `processAot` and `processTestAot` tasks.

[Running your Application with Gradle](running.html) [Integrating with Actuator](integrating-with-actuator.html)
---
