Title: Running your Application with Gradle :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/gradle-plugin/running.html
HTML: html/docs_spring_io/spring-boot/3.4/gradle-plugin/Running_your_Application_with_Gradle_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/gradle-plugin/Running_your_Application_with_Gradle_Spring_Boot.png
crawled_at: 2025-06-04T16:52:44.069513
---
Search CTRL + k

### Running your Application with Gradle

  * Passing Arguments to Your Application
  * Passing System Properties to Your application
  * Reloading Resources
  * Using a Test Main Class



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-gradle-plugin/src/docs/antora/modules/gradle-plugin/pages/running.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Gradle Plugin](index.html)
  * [Running your Application with Gradle](running.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../gradle-plugin/running.html)!  
---|---  
  
# Running your Application with Gradle

### Running your Application with Gradle

  * Passing Arguments to Your Application
  * Passing System Properties to Your application
  * Reloading Resources
  * Using a Test Main Class



To run your application without first building an archive use the `bootRun` task:
    
    
    $ ./gradlew bootRun
    
    Copied!

The `bootRun` task is an instance of [`BootRun`](api/java/org/springframework/boot/gradle/tasks/run/BootRun.html) which is a `JavaExec` subclass. As such, all of the [usual configuration options](https://docs.gradle.org/current/dsl/org.gradle.api.tasks.JavaExec.html) for executing a Java process in Gradle are available to you. The task is automatically configured to use the runtime classpath of the main source set.

By default, the main class will be configured automatically by looking for a class with a `public static void main(String[])` method in the main source set’s output.

The main class can also be configured explicitly using the task’s `main` property:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootRun") {
    	mainClass = 'com.example.ExampleApplication'
    }
    
    Copied!
    
    
    tasks.named<BootRun>("bootRun") {
    	mainClass.set("com.example.ExampleApplication")
    }
    
    Copied!

Alternatively, the main class name can be configured project-wide using the `mainClass` property of the Spring Boot DSL:

  * Groovy

  * Kotlin



    
    
    springBoot {
    	mainClass = 'com.example.ExampleApplication'
    }
    
    Copied!
    
    
    springBoot {
    	mainClass.set("com.example.ExampleApplication")
    }
    
    Copied!

By default, `bootRun` will configure the JVM to optimize its launch for faster startup during development. This behavior can be disabled by using the `optimizedLaunch` property, as shown in the following example:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootRun") {
    	optimizedLaunch = false
    }
    
    Copied!
    
    
    tasks.named<BootRun>("bootRun") {
    	optimizedLaunch.set(false)
    }
    
    Copied!

If the [`application` plugin](https://docs.gradle.org/current/userguide/application_plugin.html) has been applied, its `mainClass` property must be configured and can be used for the same purpose:

  * Groovy

  * Kotlin



    
    
    application {
    	mainClass = 'com.example.ExampleApplication'
    }
    
    Copied!
    
    
    application {
    	mainClass.set("com.example.ExampleApplication")
    }
    
    Copied!

## Passing Arguments to Your Application

Like all `JavaExec` tasks, arguments can be passed into `bootRun` from the command line using `--args='<arguments>'` when using Gradle 4.9 or later. For example, to run your application with a profile named `dev` active the following command can be used:
    
    
    $ ./gradlew bootRun --args='--spring.profiles.active=dev'
    
    Copied!

See [the javadoc for `JavaExec.setArgsString`](https://docs.gradle.org/current/javadoc/org/gradle/api/tasks/JavaExec.html#setArgsString\(java.lang.String\)) for further details.

## Passing System Properties to Your application

Since `bootRun` is a standard `JavaExec` task, system properties can be passed to the application’s JVM by specifying them in the build script. To make that value of a system property to be configurable set its value using a [project property](https://docs.gradle.org/current/dsl/org.gradle.api.Project.html#N14FE1). To allow a project property to be optional, reference it using `findProperty`. Doing so also allows a default value to be provided using the `?:` Elvis operator, as shown in the following example:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootRun") {
    	systemProperty 'com.example.property', findProperty('example') ?: 'default'
    }
    
    Copied!
    
    
    tasks.named<BootRun>("bootRun") {
    	systemProperty("com.example.property", findProperty("example") ?: "default")
    }
    
    Copied!

The preceding example sets that `com.example.property` system property to the value of the `example` project property. If the `example` project property has not been set, the value of the system property will be `default`.

Gradle allows project properties to be set in a variety of ways, including on the command line using the `-P` flag, as shown in the following example:
    
    
    $ ./gradlew bootRun -Pexample=custom
    
    Copied!

The preceding example sets the value of the `example` project property to `custom`. `bootRun` will then use this as the value of the `com.example.property` system property.

## Reloading Resources

If devtools has been added to your project it will automatically monitor your application’s classpath for changes. Note that modified files need to be recompiled for the classpath to update in order to trigger reloading with devtools. For more details on using devtools, refer to [this section of the reference documentation](../reference/using/devtools.html#using.devtools.restart).

Alternatively, you can configure `bootRun` such that your application’s static resources are loaded from their source location:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootRun") {
    	sourceResources sourceSets.main
    }
    
    Copied!
    
    
    tasks.named<BootRun>("bootRun") {
    	sourceResources(sourceSets["main"])
    }
    
    Copied!

This makes them reloadable in the live application which can be helpful at development time.

## Using a Test Main Class

In addition to `bootRun` a `bootTestRun` task is also registered. Like `bootRun`, `bootTestRun` is an instance of `BootRun` but it’s configured to use a main class found in the output of the test source set rather than the main source set. It also uses the test source set’s runtime classpath rather than the main source set’s runtime classpath. As `bootTestRun` is an instance of `BootRun`, all of the configuration options described above for `bootRun` can also be used with `bootTestRun`.

[Publishing your Application](publishing.html) [Ahead-of-Time Processing](aot.html)
---
