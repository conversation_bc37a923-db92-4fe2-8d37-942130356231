Title: Managing Dependencies :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/gradle-plugin/managing-dependencies.html
HTML: html/docs_spring_io/spring-boot/3.4/gradle-plugin/Managing_Dependencies_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/gradle-plugin/Managing_Dependencies_Spring_Boot.png
crawled_at: 2025-06-04T16:52:41.495565
---
Search CTRL + k

### Managing Dependencies

  * Managing Dependencies with the Dependency Management Plugin
  * Customizing Managed Versions
  * Using Spring Boot’s Dependency Management in Isolation
  * Learning More
  * Managing Dependencies with Gradle’s Bom Support
  * Customizing Managed Versions



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-gradle-plugin/src/docs/antora/modules/gradle-plugin/pages/managing-dependencies.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Gradle Plugin](index.html)
  * [Managing Dependencies](managing-dependencies.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../gradle-plugin/managing-dependencies.html)!  
---|---  
  
# Managing Dependencies

### Managing Dependencies

  * Managing Dependencies with the Dependency Management Plugin
  * Customizing Managed Versions
  * Using Spring Boot’s Dependency Management in Isolation
  * Learning More
  * Managing Dependencies with Gradle’s Bom Support
  * Customizing Managed Versions



To manage dependencies in your Spring Boot application, you can either apply the [`io.spring.dependency-management`](https://github.com/spring-gradle-plugins/dependency-management-plugin) plugin or use Gradle’s native bom support. The primary benefit of the former is that it offers property-based customization of managed versions, while using the latter will likely result in faster builds.

## Managing Dependencies with the Dependency Management Plugin

When you apply the [`io.spring.dependency-management`](https://github.com/spring-gradle-plugins/dependency-management-plugin) plugin, Spring Boot’s plugin will automatically [import the `spring-boot-dependencies` bom](reacting.html#reacting-to-other-plugins.dependency-management) from the version of Spring Boot that you are using. This provides a similar dependency management experience to the one that’s enjoyed by Maven users. For example, it allows you to omit version numbers when declaring dependencies that are managed in the bom. To make use of this functionality, declare dependencies in the usual way but omit the version number:

  * Groovy

  * Kotlin



    
    
    dependencies {
    	implementation('org.springframework.boot:spring-boot-starter-web')
    	implementation('org.springframework.boot:spring-boot-starter-data-jpa')
    }
    
    Copied!
    
    
    dependencies {
    	implementation("org.springframework.boot:spring-boot-starter-web")
    	implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    }
    
    Copied!

### Customizing Managed Versions

The `spring-boot-dependencies` bom that is automatically imported when the dependency management plugin is applied uses properties to control the versions of the dependencies that it manages. Browse the [Dependency Versions Properties](../appendix/dependency-versions/properties.html) section in the Spring Boot reference for a complete list of these properties.

To customize a managed version you set its corresponding property. For example, to customize the version of SLF4J which is controlled by the `slf4j.version` property:

  * Groovy

  * Kotlin



    
    
    ext['slf4j.version'] = '1.7.20'
    
    Copied!
    
    
    extra["slf4j.version"] = "1.7.20"
    
    Copied!

__ |  Each Spring Boot release is designed and tested against a specific set of third-party dependencies. Overriding versions may cause compatibility issues and should be done with care.   
---|---  
  
### Using Spring Boot’s Dependency Management in Isolation

Spring Boot’s dependency management can be used in a project without applying Spring Boot’s plugin to that project. The `SpringBootPlugin` class provides a `BOM_COORDINATES` constant that can be used to import the bom without having to know its group ID, artifact ID, or version.

First, configure the project to depend on the Spring Boot plugin but do not apply it:

  * Groovy

  * Kotlin



    
    
    plugins {
    	id 'org.springframework.boot' version '3.4.6' apply false
    }
    
    Copied!
    
    
    plugins {
    	id("org.springframework.boot") version "3.4.6" apply false
    }
    
    Copied!

The Spring Boot plugin’s dependency on the dependency management plugin means that you can use the dependency management plugin without having to declare a dependency on it. This also means that you will automatically use the same version of the dependency management plugin as Spring Boot uses.

Apply the dependency management plugin and then configure it to import Spring Boot’s bom:

  * Groovy

  * Kotlin



    
    
    apply plugin: 'io.spring.dependency-management'
    
    dependencyManagement {
    	imports {
    		mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
    	}
    }
    
    Copied!
    
    
    apply(plugin = "io.spring.dependency-management")
    
    the<DependencyManagementExtension>().apply {
    	imports {
    		mavenBom(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES)
    	}
    }
    
    Copied!

The Kotlin code above is a bit awkward. That’s because we’re using the imperative way of applying the dependency management plugin.

We can make the code less awkward by applying the plugin from the root parent project, or by using the `plugins` block as we’re doing for the Spring Boot plugin. A downside of this method is that it forces us to specify the version of the dependency management plugin:
    
    
    plugins {
    	java
    	id("org.springframework.boot") version "3.4.6" apply false
    	id("io.spring.dependency-management") version "1.1.7"
    }
    
    dependencyManagement {
    	imports {
    		mavenBom(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES)
    	}
    }
    
    Copied!

### Learning More

To learn more about the capabilities of the dependency management plugin, please refer to its [documentation](https://docs.spring.io/dependency-management-plugin/docs/1.1.7/reference/html).

## Managing Dependencies with Gradle’s Bom Support

Gradle allows a bom to be used to manage a project’s versions by declaring it as a `platform` or `enforcedPlatform` dependency. A `platform` dependency treats the versions in the bom as recommendations and other versions and constraints in the dependency graph may cause a version of a dependency other than that declared in the bom to be used. An `enforcedPlatform` dependency treats the versions in the bom as requirements and they will override any other version found in the dependency graph.

The `SpringBootPlugin` class provides a `BOM_COORDINATES` constant that can be used to declare a dependency upon Spring Boot’s bom without having to know its group ID, artifact ID, or version, as shown in the following example:

  * Groovy

  * Kotlin



    
    
    dependencies {
    	implementation platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES)
    }
    
    Copied!
    
    
    dependencies {
    	implementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    }
    
    Copied!

A platform or enforced platform will only constrain the versions of the configuration in which it has been declared or that extend from the configuration in which it has been declared. As a result, in may be necessary to declare the same dependency in more than one configuration.

### Customizing Managed Versions

When using Gradle’s bom support, you cannot use the properties from `spring-boot-dependencies` to control the versions of the dependencies that it manages. Instead, you must use one of the mechanisms that Gradle provides. One such mechanism is a resolution strategy. SLF4J’s modules are all in the `org.slf4j` group so their version can be controlled by configuring every dependency in that group to use a particular version, as shown in the following example:

  * Groovy

  * Kotlin



    
    
    configurations.all {
    	resolutionStrategy.eachDependency { DependencyResolveDetails details ->
    		if (details.requested.group == 'org.slf4j') {
    			details.useVersion '1.7.20'
    		}
    	}
    }
    
    Copied!
    
    
    configurations.all {
        resolutionStrategy.eachDependency {
            if (requested.group == "org.slf4j") {
                useVersion("1.7.20")
            }
        }
    }
    
    Copied!

__ |  Each Spring Boot release is designed and tested against a specific set of third-party dependencies. Overriding versions may cause compatibility issues and should be done with care.   
---|---  
  
[Getting Started](getting-started.html) [Packaging Executable Archives](packaging.html)
---
