Title: Getting Started :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/gradle-plugin/getting-started.html
HTML: html/docs_spring_io/spring-boot/3.4/gradle-plugin/Getting_Started_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/gradle-plugin/Getting_Started_Spring_Boot.png
crawled_at: 2025-06-04T19:23:21.091614
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-gradle-plugin/src/docs/antora/modules/gradle-plugin/pages/getting-started.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Gradle Plugin](index.html)
  * [Getting Started](getting-started.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../gradle-plugin/getting-started.html)!  
---|---  
  
# Getting Started

To get started with the plugin it needs to be applied to your project.

The plugin is [published to Gradle’s plugin portal](https://plugins.gradle.org/plugin/org.springframework.boot) and can be applied using the `plugins` block:

  * Groovy

  * Kotlin



    
    
    plugins {
    	id 'org.springframework.boot' version '3.4.6'
    }
    
    Copied!
    
    
    plugins {
    	id("org.springframework.boot") version "3.4.6"
    }
    
    Copied!

Applied in isolation the plugin makes few changes to a project. Instead, the plugin detects when certain other plugins are applied and reacts accordingly. For example, when the `java` plugin is applied a task for building an executable jar is automatically configured. A typical Spring Boot project will apply the [`groovy`](https://docs.gradle.org/current/userguide/groovy_plugin.html), [`java`](https://docs.gradle.org/current/userguide/java_plugin.html), or [`org.jetbrains.kotlin.jvm`](https://kotlinlang.org/docs/reference/using-gradle.html) plugin as a minimum and also use the [`io.spring.dependency-management`](https://github.com/spring-gradle-plugins/dependency-management-plugin) plugin or Gradle’s native bom support for dependency management. For example:

  * Groovy

  * Kotlin



    
    
    plugins {
    	id 'java'
    	id 'org.springframework.boot' version '3.4.6'
    }
    
    apply plugin: 'io.spring.dependency-management'
    
    Copied!
    
    
    plugins {
    	java
    	id("org.springframework.boot") version "3.4.6"
    }
    
    apply(plugin = "io.spring.dependency-management")
    
    Copied!

To learn more about how the Spring Boot plugin behaves when other plugins are applied please see the section on [reacting to other plugins](reacting.html).

[Gradle Plugin](index.html) [Managing Dependencies](managing-dependencies.html)
---
