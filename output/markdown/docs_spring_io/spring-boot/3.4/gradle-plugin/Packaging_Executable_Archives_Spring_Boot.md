Title: Packaging Executable Archives :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/gradle-plugin/packaging.html
HTML: html/docs_spring_io/spring-boot/3.4/gradle-plugin/Packaging_Executable_Archives_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/gradle-plugin/Packaging_Executable_Archives_Spring_Boot.png
crawled_at: 2025-06-04T16:52:38.855510
---
Search CTRL + k

### Packaging Executable Archives

  * Packaging Executable Jars
  * Packaging Executable Wars
  * Packaging Executable and Deployable Wars
  * Packaging Executable and Plain Archives
  * Configuring Executable Archive Packaging
  * Configuring the Main Class
  * Including Development-only Dependencies
  * Configuring Libraries that Require Unpacking
  * Making an Archive Fully Executable
  * Using the PropertiesLauncher
  * Packaging Layered Jar or War



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-gradle-plugin/src/docs/antora/modules/gradle-plugin/pages/packaging.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Gradle Plugin](index.html)
  * [Packaging Executable Archives](packaging.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../gradle-plugin/packaging.html)!  
---|---  
  
# Packaging Executable Archives

### Packaging Executable Archives

  * Packaging Executable Jars
  * Packaging Executable Wars
  * Packaging Executable and Deployable Wars
  * Packaging Executable and Plain Archives
  * Configuring Executable Archive Packaging
  * Configuring the Main Class
  * Including Development-only Dependencies
  * Configuring Libraries that Require Unpacking
  * Making an Archive Fully Executable
  * Using the PropertiesLauncher
  * Packaging Layered Jar or War



The plugin can create executable archives (jar files and war files) that contain all of an application’s dependencies and can then be run with `java -jar`.

## Packaging Executable Jars

Executable jars can be built using the `bootJar` task. The task is automatically created when the `java` plugin is applied and is an instance of [`BootJar`](api/java/org/springframework/boot/gradle/tasks/bundling/BootJar.html). The `assemble` task is automatically configured to depend upon the `bootJar` task so running `assemble` (or `build`) will also run the `bootJar` task.

## Packaging Executable Wars

Executable wars can be built using the `bootWar` task. The task is automatically created when the `war` plugin is applied and is an instance of [`BootWar`](api/java/org/springframework/boot/gradle/tasks/bundling/BootWar.html). The `assemble` task is automatically configured to depend upon the `bootWar` task so running `assemble` (or `build`) will also run the `bootWar` task.

### Packaging Executable and Deployable Wars

A war file can be packaged such that it can be executed using `java -jar` and deployed to an external container. To do so, the embedded servlet container dependencies should be added to the `providedRuntime` configuration, for example:

  * Groovy

  * Kotlin



    
    
    dependencies {
    	implementation('org.springframework.boot:spring-boot-starter-web')
    	providedRuntime('org.springframework.boot:spring-boot-starter-tomcat')
    }
    
    Copied!
    
    
    dependencies {
    	implementation("org.springframework.boot:spring-boot-starter-web")
    	providedRuntime("org.springframework.boot:spring-boot-starter-tomcat")
    }
    
    Copied!

This ensures that they are package in the war file’s `WEB-INF/lib-provided` directory from where they will not conflict with the external container’s own classes.

__ |  `providedRuntime` is preferred to Gradle’s `compileOnly` configuration as, among other limitations, `compileOnly` dependencies are not on the test classpath so any web-based integration tests will fail.   
---|---  
  
## Packaging Executable and Plain Archives

By default, when the `bootJar` or `bootWar` tasks are configured, the `jar` or `war` tasks are configured to use `plain` as the convention for their archive classifier. This ensures that `bootJar` and `jar` or `bootWar` and `war` have different output locations, allowing both the executable archive and the plain archive to be built at the same time.

If you prefer that the executable archive, rather than the plain archive, uses a classifier, configure the classifiers as shown in the following example for the `jar` and `bootJar` tasks:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootJar") {
    	archiveClassifier = 'boot'
    }
    
    tasks.named("jar") {
    	archiveClassifier = ''
    }
    
    Copied!
    
    
    tasks.named<BootJar>("bootJar") {
    	archiveClassifier.set("boot")
    }
    
    tasks.named<Jar>("jar") {
    	archiveClassifier.set("")
    }
    
    Copied!

Alternatively, if you prefer that the plain archive isn’t built at all, disable its task as shown in the following example for the `jar` task:

  * Groovy

  * Kotlin



    
    
    tasks.named("jar") {
    	enabled = false
    }
    
    Copied!
    
    
    tasks.named<Jar>("jar") {
    	enabled = false
    }
    
    Copied!

__ |  Do not disable the `jar` task when creating native images. See [#33238](https://github.com/spring-projects/spring-boot/issues/33238) for details.   
---|---  
  
## Configuring Executable Archive Packaging

The [`BootJar`](api/java/org/springframework/boot/gradle/tasks/bundling/BootJar.html) and [`BootWar`](api/java/org/springframework/boot/gradle/tasks/bundling/BootWar.html) tasks are subclasses of Gradle’s `Jar` and `War` tasks respectively. As a result, all of the standard configuration options that are available when packaging a jar or war are also available when packaging an executable jar or war. A number of configuration options that are specific to executable jars and wars are also provided.

### Configuring the Main Class

By default, the executable archive’s main class will be configured automatically by looking for a class with a `public static void main(String[])` method in the main source set’s output.

The main class can also be configured explicitly using the task’s `mainClass` property:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootJar") {
    	mainClass = 'com.example.ExampleApplication'
    }
    
    Copied!
    
    
    tasks.named<BootJar>("bootJar") {
    	mainClass.set("com.example.ExampleApplication")
    }
    
    Copied!

Alternatively, the main class name can be configured project-wide using the `mainClass` property of the Spring Boot DSL:

  * Groovy

  * Kotlin



    
    
    springBoot {
    	mainClass = 'com.example.ExampleApplication'
    }
    
    Copied!
    
    
    springBoot {
    	mainClass.set("com.example.ExampleApplication")
    }
    
    Copied!

If the [`application` plugin](https://docs.gradle.org/current/userguide/application_plugin.html) has been applied its `mainClass` property must be configured and can be used for the same purpose:

  * Groovy

  * Kotlin



    
    
    application {
    	mainClass = 'com.example.ExampleApplication'
    }
    
    Copied!
    
    
    application {
    	mainClass.set("com.example.ExampleApplication")
    }
    
    Copied!

Lastly, the `Start-Class` attribute can be configured on the task’s manifest:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootJar") {
    	manifest {
    		attributes 'Start-Class': 'com.example.ExampleApplication'
    	}
    }
    
    Copied!
    
    
    tasks.named<BootJar>("bootJar") {
    	manifest {
    		attributes("Start-Class" to "com.example.ExampleApplication")
    	}
    }
    
    Copied!

__ |  If the main class is written in Kotlin, the name of the generated Java class should be used. By default, this is the name of the Kotlin class with the `Kt` suffix added. For example, `ExampleApplication` becomes `ExampleApplicationKt`. If another name is defined using `@JvmName` then that name should be used.   
---|---  
  
### Including Development-only Dependencies

By default all dependencies declared in the `developmentOnly` configuration will be excluded from an executable jar or war.

If you want to include dependencies declared in the `developmentOnly` configuration in your archive, configure the classpath of its task to include the configuration, as shown in the following example for the `bootWar` task:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootWar") {
    	classpath configurations.developmentOnly
    }
    
    Copied!
    
    
    tasks.named<BootWar>("bootWar") {
    	classpath(configurations["developmentOnly"])
    }
    
    Copied!

### Configuring Libraries that Require Unpacking

Most libraries can be used directly when nested in an executable archive, however certain libraries can have problems. For example, JRuby includes its own nested jar support which assumes that `jruby-complete.jar` is always directly available on the file system.

To deal with any problematic libraries, an executable archive can be configured to unpack specific nested jars to a temporary directory when the executable archive is run. Libraries can be identified as requiring unpacking using Ant-style patterns that match against the absolute path of the source jar file:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootJar") {
    	requiresUnpack '**/jruby-complete-*.jar'
    }
    
    Copied!
    
    
    tasks.named<BootJar>("bootJar") {
    	requiresUnpack("**/jruby-complete-*.jar")
    }
    
    Copied!

For more control a closure can also be used. The closure is passed a `FileTreeElement` and should return a `boolean` indicating whether or not unpacking is required.

### Making an Archive Fully Executable

Spring Boot provides support for fully executable archives. An archive is made fully executable by prepending a shell script that knows how to launch the application. On Unix-like platforms, this launch script allows the archive to be run directly like any other executable or to be installed as a service.

__ |  Currently, some tools do not accept this format so you may not always be able to use this technique. For example, `jar -xf` may silently fail to extract a jar or war that has been made fully-executable. It is recommended that you only enable this option if you intend to execute it directly, rather than running it with `java -jar` or deploying it to a servlet container.   
---|---  
  
To use this feature, the inclusion of the launch script must be enabled:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootJar") {
    	launchScript()
    }
    
    Copied!
    
    
    tasks.named<BootJar>("bootJar") {
    	launchScript()
    }
    
    Copied!

This will add Spring Boot’s default launch script to the archive. The default launch script includes several properties with sensible default values. The values can be customized using the `properties` property:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootJar") {
    	launchScript {
    		properties 'logFilename': 'example-app.log'
    	}
    }
    
    Copied!
    
    
    tasks.named<BootJar>("bootJar") {
    	launchScript {
    		properties(mapOf("logFilename" to "example-app.log"))
    	}
    }
    
    Copied!

If the default launch script does not meet your needs, the `script` property can be used to provide a custom launch script:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootJar") {
    	launchScript {
    		script = file('src/custom.script')
    	}
    }
    
    Copied!
    
    
    tasks.named<BootJar>("bootJar") {
    	launchScript {
    		script = file("src/custom.script")
    	}
    }
    
    Copied!

### Using the PropertiesLauncher

To use the `PropertiesLauncher` to launch an executable jar or war, configure the task’s manifest to set the `Main-Class` attribute:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootWar") {
    	manifest {
    		attributes 'Main-Class': 'org.springframework.boot.loader.launch.PropertiesLauncher'
    	}
    }
    
    Copied!
    
    
    tasks.named<BootWar>("bootWar") {
    	manifest {
    		attributes("Main-Class" to "org.springframework.boot.loader.launch.PropertiesLauncher")
    	}
    }
    
    Copied!

### Packaging Layered Jar or War

By default, the `bootJar` task builds an archive that contains the application’s classes and dependencies in `BOOT-INF/classes` and `BOOT-INF/lib` respectively. Similarly, `bootWar` builds an archive that contains the application’s classes in `WEB-INF/classes` and dependencies in `WEB-INF/lib` and `WEB-INF/lib-provided`. For cases where a docker image needs to be built from the contents of the jar, it’s useful to be able to separate these directories further so that they can be written into distinct layers.

Layered jars use the same layout as regular boot packaged jars, but include an additional meta-data file that describes each layer.

By default, the following layers are defined:

  * `dependencies` for any non-project dependency whose version does not contain `SNAPSHOT`.

  * `spring-boot-loader` for the jar loader classes.

  * `snapshot-dependencies` for any non-project dependency whose version contains `SNAPSHOT`.

  * `application` for project dependencies, application classes, and resources.




The layers order is important as it determines how likely previous layers can be cached when part of the application changes. The default order is `dependencies`, `spring-boot-loader`, `snapshot-dependencies`, `application`. Content that is least likely to change should be added first, followed by layers that are more likely to change.

To disable this feature, you can do so in the following manner:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootJar") {
    	layered {
    		enabled = false
    	}
    }
    
    Copied!
    
    
    tasks.named<BootJar>("bootJar") {
    	layered {
    		enabled.set(false)
    	}
    }
    
    Copied!

When a layered jar or war is created, the `spring-boot-jarmode-tools` jar will be added as a dependency to your archive. With this jar on the classpath, you can launch your application in a special mode which allows the bootstrap code to run something entirely different from your application, for example, something that extracts the layers. If you wish to exclude this dependency, you can do so in the following manner:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootJar") {
    	includeTools = false
    }
    
    Copied!
    
    
    tasks.named<BootJar>("bootJar") {
    	includeTools.set(false)
    }
    
    Copied!

#### Custom Layers Configuration

Depending on your application, you may want to tune how layers are created and add new ones.

This can be done using configuration that describes how the jar or war can be separated into layers, and the order of those layers. The following example shows how the default ordering described above can be defined explicitly:

  * Groovy

  * Kotlin



    
    
    tasks.named("bootJar") {
    	layered {
    		application {
    			intoLayer("spring-boot-loader") {
    				include "org/springframework/boot/loader/**"
    			}
    			intoLayer("application")
    		}
    		dependencies {
    			intoLayer("application") {
    				includeProjectDependencies()
    			}
    			intoLayer("snapshot-dependencies") {
    				include "*:*:*SNAPSHOT"
    			}
    			intoLayer("dependencies")
    		}
    		layerOrder = ["dependencies", "spring-boot-loader", "snapshot-dependencies", "application"]
    	}
    }
    
    Copied!
    
    
    tasks.named<BootJar>("bootJar") {
    	layered {
    		application {
    			intoLayer("spring-boot-loader") {
    				include("org/springframework/boot/loader/**")
    			}
    			intoLayer("application")
    		}
    		dependencies {
    			intoLayer("application") {
    				includeProjectDependencies()
    			}
    			intoLayer("snapshot-dependencies") {
    				include("*:*:*SNAPSHOT")
    			}
    			intoLayer("dependencies")
    		}
    		layerOrder.set(listOf("dependencies", "spring-boot-loader", "snapshot-dependencies", "application"))
    	}
    }
    
    Copied!

The `layered` DSL is defined using three parts:

  * The `application` closure defines how the application classes and resources should be layered.

  * The `dependencies` closure defines how dependencies should be layered.

  * The `layerOrder` method defines the order that the layers should be written.




Nested `intoLayer` closures are used within `application` and `dependencies` sections to claim content for a layer. These closures are evaluated in the order that they are defined, from top to bottom. Any content not claimed by an earlier `intoLayer` closure remains available for subsequent ones to consider.

The `intoLayer` closure claims content using nested `include` and `exclude` calls. The `application` closure uses Ant-style path matching for include/exclude parameters. The `dependencies` section uses `group:artifact[:version]` patterns. It also provides `includeProjectDependencies()` and `excludeProjectDependencies()` methods that can be used to include or exclude project dependencies.

If no `include` call is made, then all content (not claimed by an earlier closure) is considered.

If no `exclude` call is made, then no exclusions are applied.

Looking at the `dependencies` closure in the example above, we can see that the first `intoLayer` will claim all project dependencies for the `application` layer. The next `intoLayer` will claim all SNAPSHOT dependencies for the `snapshot-dependencies` layer. The third and final `intoLayer` will claim anything left (in this case, any dependency that is not a project dependency or a SNAPSHOT) for the `dependencies` layer.

The `application` closure has similar rules. First claiming `org/springframework/boot/loader/**` content for the `spring-boot-loader` layer. Then claiming any remaining classes and resources for the `application` layer.

__ |  The order that `intoLayer` closures are added is often different from the order that the layers are written. For this reason the `layerOrder` method must always be called and _must_ cover all layers referenced by the `intoLayer` calls.   
---|---  
  
[Managing Dependencies](managing-dependencies.html) [Packaging OCI Images](packaging-oci-image.html)
---
