Title: Gradle Plugin :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/gradle-plugin/index.html
HTML: html/docs_spring_io/spring-boot/3.4/gradle-plugin/Gradle_Plugin_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/gradle-plugin/Gradle_Plugin_Spring_Boot.png
crawled_at: 2025-06-04T16:37:30.038945
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-gradle-plugin/src/docs/antora/modules/gradle-plugin/pages/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Gradle Plugin](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../gradle-plugin/index.html)!  
---|---  
  
# Gradle Plugin

The Spring Boot Gradle Plugin provides Spring Boot support in [Gradle](https://gradle.org). It allows you to package executable jar or war archives, run Spring Boot applications, and use the dependency management provided by `spring-boot-dependencies`. Spring Boot’s Gradle plugin requires Gradle 7.x (7.6.4 or later) or 8.x (8.4 or later) and can be used with Gradle’s [configuration cache](https://docs.gradle.org/current/userguide/configuration_cache.html).

In addition to this user guide, [API documentation](api/java/index.html) is also available.

[Help Information](../maven-plugin/help.html) [Getting Started](getting-started.html)
---
