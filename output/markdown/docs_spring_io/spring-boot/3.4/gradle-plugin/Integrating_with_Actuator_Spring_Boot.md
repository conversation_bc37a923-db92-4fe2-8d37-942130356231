Title: Integrating with Actuator :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/gradle-plugin/integrating-with-actuator.html
HTML: html/docs_spring_io/spring-boot/3.4/gradle-plugin/Integrating_with_Actuator_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/gradle-plugin/Integrating_with_Actuator_Spring_Boot.png
crawled_at: 2025-06-04T16:52:49.302953
---
Search CTRL + k

### Integrating with Actuator

  * Generating Build Information



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-gradle-plugin/src/docs/antora/modules/gradle-plugin/pages/integrating-with-actuator.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Gradle Plugin](index.html)
  * [Integrating with Actuator](integrating-with-actuator.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../gradle-plugin/integrating-with-actuator.html)!  
---|---  
  
# Integrating with Actuator

### Integrating with Actuator

  * Generating Build Information



## Generating Build Information

Spring Boot Actuator’s `info` endpoint automatically publishes information about your build in the presence of a `META-INF/build-info.properties` file. A [`BuildInfo`](api/java/org/springframework/boot/gradle/tasks/buildinfo/BuildInfo.html) task is provided to generate this file. The easiest way to use the task is through the plugin’s DSL:

  * Groovy

  * Kotlin



    
    
    springBoot {
    	buildInfo()
    }
    
    Copied!
    
    
    springBoot {
    	buildInfo()
    }
    
    Copied!

This will configure a [`BuildInfo`](api/java/org/springframework/boot/gradle/tasks/buildinfo/BuildInfo.html) task named `bootBuildInfo` and, if it exists, make the Java plugin’s `classes` task depend upon it. The task’s destination directory will be `META-INF` in the output directory of the main source set’s resources (typically `build/resources/main`).

By default, the generated build information is derived from the project:

Property | Default value  
---|---  
`build.artifact` | The base name of the `bootJar` or `bootWar` task  
`build.group` | The group of the project  
`build.name` | The name of the project  
`build.version` | The version of the project  
`build.time` | The time at which the project is being built  
  
The properties can be customized using the DSL:

  * Groovy

  * Kotlin



    
    
    springBoot {
    	buildInfo {
    		properties {
    			artifact = 'example-app'
    			version = '1.2.3'
    			group = 'com.example'
    			name = 'Example application'
    		}
    	}
    }
    
    Copied!
    
    
    springBoot {
    	buildInfo {
    		properties {
    			artifact.set("example-app")
    			version.set("1.2.3")
    			group.set("com.example")
    			name.set("Example application")
    		}
    	}
    }
    
    Copied!

To exclude any of the default properties from the generated build information, add its name to the excludes. For example, the `time` property can be excluded as follows:

  * Groovy

  * Kotlin



    
    
    springBoot {
    	buildInfo {
    		excludes = ['time']
    	}
    }
    
    Copied!
    
    
    springBoot {
    	buildInfo {
    		excludes.set(setOf("time"))
    	}
    }
    
    Copied!

The default value for `build.time` is the instant at which the project is being built. A side-effect of this is that the task will never be up-to-date. As a result, builds will take longer as more tasks, including the project’s tests, will have to be executed. Another side-effect is that the task’s output will always change and, therefore, the build will not be truly repeatable. If you value build performance or repeatability more highly than the accuracy of the `build.time` property, exclude the `time` property as shown in the preceding example.

Additional properties can also be added to the build information:

  * Groovy

  * Kotlin



    
    
    springBoot {
    	buildInfo {
    		properties {
    			additional = [
    				'a': 'alpha',
    				'b': 'bravo'
    			]
    		}
    	}
    }
    
    Copied!
    
    
    springBoot {
    	buildInfo {
    		properties {
    			additional.set(mapOf(
    				"a" to "alpha",
    				"b" to "bravo"
    			))
    		}
    	}
    }
    
    Copied!

An additional property’s value can be computed lazily by using a `Provider`.

[Ahead-of-Time Processing](aot.html) [Reacting to Other Plugins](reacting.html)
---
