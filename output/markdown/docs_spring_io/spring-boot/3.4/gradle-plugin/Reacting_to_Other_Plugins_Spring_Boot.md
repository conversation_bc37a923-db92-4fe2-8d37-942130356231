Title: Reacting to Other Plugins :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/gradle-plugin/reacting.html
HTML: html/docs_spring_io/spring-boot/3.4/gradle-plugin/Reacting_to_Other_Plugins_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/gradle-plugin/Reacting_to_Other_Plugins_Spring_Boot.png
crawled_at: 2025-06-04T19:23:29.089811
---
Search CTRL + k

### Reacting to Other Plugins

  * Reacting to the Java Plugin
  * Reacting to the Kotlin Plugin
  * Reacting to the War Plugin
  * Reacting to the Dependency Management Plugin
  * Reacting to the Application Plugin
  * Reacting to the GraalVM Native Image Plugin
  * Reacting to the CycloneDX Plugin



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-gradle-plugin/src/docs/antora/modules/gradle-plugin/pages/reacting.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Gradle Plugin](index.html)
  * [Reacting to Other Plugins](reacting.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../gradle-plugin/reacting.html)!  
---|---  
  
# Reacting to Other Plugins

### Reacting to Other Plugins

  * Reacting to the Java Plugin
  * Reacting to the Kotlin Plugin
  * Reacting to the War Plugin
  * Reacting to the Dependency Management Plugin
  * Reacting to the Application Plugin
  * Reacting to the GraalVM Native Image Plugin
  * Reacting to the CycloneDX Plugin



When another plugin is applied the Spring Boot plugin reacts by making various changes to the project’s configuration. This section describes those changes.

## Reacting to the Java Plugin

When Gradle’s [`java` plugin](https://docs.gradle.org/current/userguide/java_plugin.html) is applied to a project, the Spring Boot plugin:

  1. Creates a [`BootJar`](api/java/org/springframework/boot/gradle/tasks/bundling/BootJar.html) task named `bootJar` that will create an executable, uber jar for the project. The jar will contain everything on the runtime classpath of the main source set; classes are packaged in `BOOT-INF/classes` and jars are packaged in `BOOT-INF/lib`

  2. Configures the `assemble` task to depend on the `bootJar` task.

  3. Configures the `jar` task to use `plain` as the convention for its archive classifier.

  4. Creates a [`BootBuildImage`](api/java/org/springframework/boot/gradle/tasks/bundling/BootBuildImage.html) task named `bootBuildImage` that will create a OCI image using a [buildpack](https://buildpacks.io).

  5. Creates a [`BootRun`](api/java/org/springframework/boot/gradle/tasks/run/BootRun.html) task named `bootRun` that can be used to run your application using the `main` source set to find its main method and provide its runtime classpath.

  6. Creates a [`BootRun`](api/java/org/springframework/boot/gradle/tasks/run/BootRun.html) task named `bootTestRun` that can be used to run your application using the `test` source set to find its main method and provide its runtime classpath.

  7. Creates a configuration named `bootArchives` that contains the artifact produced by the `bootJar` task.

  8. Creates a configuration named `developmentOnly` for dependencies that are only required at development time, such as Spring Boot’s Devtools, and should not be packaged in executable jars and wars.

  9. Creates a configuration named `testAndDevelopmentOnly` for dependencies that are only required at development time and when writing and running tests and that should not be packaged in executable jars and wars.

  10. Creates a configuration named `productionRuntimeClasspath`. It is equivalent to `runtimeClasspath` minus any dependencies that only appear in the `developmentOnly` or `testDevelopmentOnly` configurations.

  11. Configures any `JavaCompile` tasks with no configured encoding to use `UTF-8`.

  12. Configures any `JavaCompile` tasks to use the `-parameters` compiler argument.




## Reacting to the Kotlin Plugin

When [Kotlin’s Gradle plugin](https://kotlinlang.org/docs/reference/using-gradle.html) is applied to a project, the Spring Boot plugin:

  1. Aligns the Kotlin version used in Spring Boot’s dependency management with the version of the plugin. This is achieved by setting the `kotlin.version` property with a value that matches the version of the Kotlin plugin.

  2. Configures any `KotlinCompile` tasks to use the `-java-parameters` compiler argument.




## Reacting to the War Plugin

When Gradle’s [`war` plugin](https://docs.gradle.org/current/userguide/war_plugin.html) is applied to a project, the Spring Boot plugin:

  1. Creates a [`BootWar`](api/java/org/springframework/boot/gradle/tasks/bundling/BootWar.html) task named `bootWar` that will create an executable, fat war for the project. In addition to the standard packaging, everything in the `providedRuntime` configuration will be packaged in `WEB-INF/lib-provided`.

  2. Configures the `assemble` task to depend on the `bootWar` task.

  3. Configures the `war` task to use `plain` as the convention for its archive classifier.

  4. Configures the `bootArchives` configuration to contain the artifact produced by the `bootWar` task.




## Reacting to the Dependency Management Plugin

When the [`io.spring.dependency-management` plugin](https://github.com/spring-gradle-plugins/dependency-management-plugin) is applied to a project, the Spring Boot plugin will automatically import the `spring-boot-dependencies` bom.

## Reacting to the Application Plugin

When Gradle’s [`application` plugin](https://docs.gradle.org/current/userguide/application_plugin.html) is applied to a project, the Spring Boot plugin:

  1. Creates a `CreateStartScripts` task named `bootStartScripts` that will create scripts that launch the artifact in the `bootArchives` configuration using `java -jar`. The task is configured to use the `applicationDefaultJvmArgs` property as a convention for its `defaultJvmOpts` property.

  2. Creates a new distribution named `boot` and configures it to contain the artifact in the `bootArchives` configuration in its `lib` directory and the start scripts in its `bin` directory.

  3. Configures the `bootRun` task to use the `mainClassName` property as a convention for its `main` property.

  4. Configures the `bootRun` and `bootTestRun` tasks to use the `applicationDefaultJvmArgs` property as a convention for their `jvmArgs` property.

  5. Configures the `bootJar` task to use the `mainClassName` property as a convention for the `Start-Class` entry in its manifest.

  6. Configures the `bootWar` task to use the `mainClassName` property as a convention for the `Start-Class` entry in its manifest.




## Reacting to the GraalVM Native Image Plugin

When the [GraalVM Native Image plugin](https://graalvm.github.io/native-build-tools/0.10.6/gradle-plugin.html) is applied to a project, the Spring Boot plugin:

  1. Applies the `org.springframework.boot.aot` plugin that:

     1. Registers `aot` and `aotTest` source sets.

     2. Registers a `ProcessAot` task named `processAot` that will generate AOT-optimized source for the application in the `aot` source set.

     3. Configures the Java compilation and process resources tasks for the `aot` source set to depend upon `processAot`.

     4. Registers a `ProcessTestAot` task named `processTestAot` that will generated AOT-optimized source for the application’s tests in the `aotTest` source set.

     5. Configures the Java compilation and process resources tasks for the `aotTest` source set to depend upon `processTestAot`.

  2. Adds the output of the `aot` source set to the classpath of the `main` GraalVM native binary.

  3. Adds the output of the `aotTest` source set to the classpath of the `test` GraalVM native binary.

  4. Configures the GraalVM extension to disable Toolchain detection.

  5. Configures each GraalVM native binary to require GraalVM 22.3 or later.

  6. Configures the `bootJar` task to include the reachability metadata produced by the `collectReachabilityMetadata` task in its jar.

  7. Configures the `bootJar` task to add the `Spring-Boot-Native-Processed: true` manifest entry.




## Reacting to the CycloneDX Plugin

When the [CycloneDX plugin](https://github.com/CycloneDX/cyclonedx-gradle-plugin) is applied to a project, the Spring Boot plugin:

  1. Configures the `cyclonedxBom` task to use the `application` project type and output the SBOM to the `application.cdx` file in JSON format without full license texts.

  2. Adds the SBOM under `META-INF/sbom` in the generated jar or war file.

  3. Adds the `Sbom-Format` and `Sbom-Location` to the manifest of the jar or war file.




[Integrating with Actuator](integrating-with-actuator.html) [Spring Boot AntLib Module](../build-tool-plugin/antlib.html)
---
