Title: spring-boot-autoconfigure :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/appendix/auto-configuration-classes/core.html
HTML: html/docs_spring_io/spring-boot/3.4/appendix/auto-configuration-classes/spring_boot_autoconfigure_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/appendix/auto-configuration-classes/spring_boot_autoconfigure_Spring_Boot.png
crawled_at: 2025-06-04T16:00:48.896193
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/appendix/pages/auto-configuration-classes/core.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * Appendix
  * [Auto-configuration Classes](index.html)
  * [spring-boot-autoconfigure](core.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../appendix/auto-configuration-classes/core.html)!  
---|---  
  
# spring-boot-autoconfigure

The following auto-configuration classes are from the `spring-boot-autoconfigure` module:

Configuration Class | Links  
---|---  
[`ActiveMQAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jms/activemq/ActiveMQAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jms/activemq/ActiveMQAutoConfiguration.html)  
[`AopAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/aop/AopAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/aop/AopAutoConfiguration.html)  
[`ApplicationAvailabilityAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/availability/ApplicationAvailabilityAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/availability/ApplicationAvailabilityAutoConfiguration.html)  
[`ArtemisAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jms/artemis/ArtemisAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jms/artemis/ArtemisAutoConfiguration.html)  
[`BatchAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/batch/BatchAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/batch/BatchAutoConfiguration.html)  
[`CacheAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/cache/CacheAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/cache/CacheAutoConfiguration.html)  
[`CassandraAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/cassandra/CassandraAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/cassandra/CassandraAutoConfiguration.html)  
[`CassandraDataAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/cassandra/CassandraDataAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/cassandra/CassandraDataAutoConfiguration.html)  
[`CassandraReactiveDataAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/cassandra/CassandraReactiveDataAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/cassandra/CassandraReactiveDataAutoConfiguration.html)  
[`CassandraReactiveRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/cassandra/CassandraReactiveRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/cassandra/CassandraReactiveRepositoriesAutoConfiguration.html)  
[`CassandraRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/cassandra/CassandraRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/cassandra/CassandraRepositoriesAutoConfiguration.html)  
[`ClientHttpConnectorAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/reactive/function/client/ClientHttpConnectorAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/reactive/function/client/ClientHttpConnectorAutoConfiguration.html)  
[`CodecsAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/http/codec/CodecsAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/http/codec/CodecsAutoConfiguration.html)  
[`ConfigurationPropertiesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/context/ConfigurationPropertiesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/context/ConfigurationPropertiesAutoConfiguration.html)  
[`CouchbaseAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/couchbase/CouchbaseAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/couchbase/CouchbaseAutoConfiguration.html)  
[`CouchbaseDataAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/couchbase/CouchbaseDataAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/couchbase/CouchbaseDataAutoConfiguration.html)  
[`CouchbaseReactiveDataAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/couchbase/CouchbaseReactiveDataAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/couchbase/CouchbaseReactiveDataAutoConfiguration.html)  
[`CouchbaseReactiveRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/couchbase/CouchbaseReactiveRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/couchbase/CouchbaseReactiveRepositoriesAutoConfiguration.html)  
[`CouchbaseRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/couchbase/CouchbaseRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/couchbase/CouchbaseRepositoriesAutoConfiguration.html)  
[`DataSourceAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jdbc/DataSourceAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jdbc/DataSourceAutoConfiguration.html)  
[`DataSourceTransactionManagerAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jdbc/DataSourceTransactionManagerAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jdbc/DataSourceTransactionManagerAutoConfiguration.html)  
[`DispatcherServletAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/servlet/DispatcherServletAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/servlet/DispatcherServletAutoConfiguration.html)  
[`ElasticsearchClientAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/elasticsearch/ElasticsearchClientAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/elasticsearch/ElasticsearchClientAutoConfiguration.html)  
[`ElasticsearchDataAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/elasticsearch/ElasticsearchDataAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/elasticsearch/ElasticsearchDataAutoConfiguration.html)  
[`ElasticsearchRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/elasticsearch/ElasticsearchRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/elasticsearch/ElasticsearchRepositoriesAutoConfiguration.html)  
[`ElasticsearchRestClientAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/elasticsearch/ElasticsearchRestClientAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/elasticsearch/ElasticsearchRestClientAutoConfiguration.html)  
[`EmbeddedLdapAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/ldap/embedded/EmbeddedLdapAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/ldap/embedded/EmbeddedLdapAutoConfiguration.html)  
[`EmbeddedWebServerFactoryCustomizerAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/embedded/EmbeddedWebServerFactoryCustomizerAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/embedded/EmbeddedWebServerFactoryCustomizerAutoConfiguration.html)  
[`ErrorMvcAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/servlet/error/ErrorMvcAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/servlet/error/ErrorMvcAutoConfiguration.html)  
[`ErrorWebFluxAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/reactive/error/ErrorWebFluxAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/reactive/error/ErrorWebFluxAutoConfiguration.html)  
[`FlywayAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration.html)  
[`FreeMarkerAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/freemarker/FreeMarkerAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/freemarker/FreeMarkerAutoConfiguration.html)  
[`GraphQlAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/graphql/GraphQlAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/graphql/GraphQlAutoConfiguration.html)  
[`GraphQlQueryByExampleAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/graphql/data/GraphQlQueryByExampleAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/graphql/data/GraphQlQueryByExampleAutoConfiguration.html)  
[`GraphQlQuerydslAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/graphql/data/GraphQlQuerydslAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/graphql/data/GraphQlQuerydslAutoConfiguration.html)  
[`GraphQlRSocketAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/graphql/rsocket/GraphQlRSocketAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/graphql/rsocket/GraphQlRSocketAutoConfiguration.html)  
[`GraphQlReactiveQueryByExampleAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/graphql/data/GraphQlReactiveQueryByExampleAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/graphql/data/GraphQlReactiveQueryByExampleAutoConfiguration.html)  
[`GraphQlReactiveQuerydslAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/graphql/data/GraphQlReactiveQuerydslAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/graphql/data/GraphQlReactiveQuerydslAutoConfiguration.html)  
[`GraphQlWebFluxAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/graphql/reactive/GraphQlWebFluxAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/graphql/reactive/GraphQlWebFluxAutoConfiguration.html)  
[`GraphQlWebFluxSecurityAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/graphql/security/GraphQlWebFluxSecurityAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/graphql/security/GraphQlWebFluxSecurityAutoConfiguration.html)  
[`GraphQlWebMvcAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/graphql/servlet/GraphQlWebMvcAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/graphql/servlet/GraphQlWebMvcAutoConfiguration.html)  
[`GraphQlWebMvcSecurityAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/graphql/security/GraphQlWebMvcSecurityAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/graphql/security/GraphQlWebMvcSecurityAutoConfiguration.html)  
[`GroovyTemplateAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/groovy/template/GroovyTemplateAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/groovy/template/GroovyTemplateAutoConfiguration.html)  
[`GsonAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/gson/GsonAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/gson/GsonAutoConfiguration.html)  
[`H2ConsoleAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/h2/H2ConsoleAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/h2/H2ConsoleAutoConfiguration.html)  
[`HazelcastAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/hazelcast/HazelcastAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/hazelcast/HazelcastAutoConfiguration.html)  
[`HazelcastJpaDependencyAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/hazelcast/HazelcastJpaDependencyAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/hazelcast/HazelcastJpaDependencyAutoConfiguration.html)  
[`HibernateJpaAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaAutoConfiguration.html)  
[`HttpClientAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/http/client/HttpClientAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/http/client/HttpClientAutoConfiguration.html)  
[`HttpEncodingAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/servlet/HttpEncodingAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/servlet/HttpEncodingAutoConfiguration.html)  
[`HttpHandlerAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/reactive/HttpHandlerAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/reactive/HttpHandlerAutoConfiguration.html)  
[`HttpMessageConvertersAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/http/HttpMessageConvertersAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/http/HttpMessageConvertersAutoConfiguration.html)  
[`HypermediaAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/hateoas/HypermediaAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/hateoas/HypermediaAutoConfiguration.html)  
[`IntegrationAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/integration/IntegrationAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/integration/IntegrationAutoConfiguration.html)  
[`JacksonAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration.html)  
[`JdbcClientAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jdbc/JdbcClientAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jdbc/JdbcClientAutoConfiguration.html)  
[`JdbcRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/jdbc/JdbcRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/jdbc/JdbcRepositoriesAutoConfiguration.html)  
[`JdbcTemplateAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jdbc/JdbcTemplateAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jdbc/JdbcTemplateAutoConfiguration.html)  
[`JerseyAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jersey/JerseyAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jersey/JerseyAutoConfiguration.html)  
[`JmsAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jms/JmsAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jms/JmsAutoConfiguration.html)  
[`JmxAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jmx/JmxAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jmx/JmxAutoConfiguration.html)  
[`JndiConnectionFactoryAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jms/JndiConnectionFactoryAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jms/JndiConnectionFactoryAutoConfiguration.html)  
[`JndiDataSourceAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jdbc/JndiDataSourceAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jdbc/JndiDataSourceAutoConfiguration.html)  
[`JooqAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jooq/JooqAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jooq/JooqAutoConfiguration.html)  
[`JpaRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/jpa/JpaRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/jpa/JpaRepositoriesAutoConfiguration.html)  
[`JsonbAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jsonb/JsonbAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jsonb/JsonbAutoConfiguration.html)  
[`JtaAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/transaction/jta/JtaAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/transaction/jta/JtaAutoConfiguration.html)  
[`KafkaAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/kafka/KafkaAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/kafka/KafkaAutoConfiguration.html)  
[`LdapAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/ldap/LdapAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/ldap/LdapAutoConfiguration.html)  
[`LdapRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/ldap/LdapRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/ldap/LdapRepositoriesAutoConfiguration.html)  
[`LifecycleAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/context/LifecycleAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/context/LifecycleAutoConfiguration.html)  
[`LiquibaseAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/liquibase/LiquibaseAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/liquibase/LiquibaseAutoConfiguration.html)  
[`MailSenderAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/mail/MailSenderAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/mail/MailSenderAutoConfiguration.html)  
[`MailSenderValidatorAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/mail/MailSenderValidatorAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/mail/MailSenderValidatorAutoConfiguration.html)  
[`MessageSourceAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/context/MessageSourceAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/context/MessageSourceAutoConfiguration.html)  
[`MongoAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/mongo/MongoAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/mongo/MongoAutoConfiguration.html)  
[`MongoDataAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/mongo/MongoDataAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/mongo/MongoDataAutoConfiguration.html)  
[`MongoReactiveAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/mongo/MongoReactiveAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/mongo/MongoReactiveAutoConfiguration.html)  
[`MongoReactiveDataAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/mongo/MongoReactiveDataAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/mongo/MongoReactiveDataAutoConfiguration.html)  
[`MongoReactiveRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/mongo/MongoReactiveRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/mongo/MongoReactiveRepositoriesAutoConfiguration.html)  
[`MongoRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/mongo/MongoRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/mongo/MongoRepositoriesAutoConfiguration.html)  
[`MultipartAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/servlet/MultipartAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/servlet/MultipartAutoConfiguration.html)  
[`MustacheAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/mustache/MustacheAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/mustache/MustacheAutoConfiguration.html)  
[`Neo4jAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/neo4j/Neo4jAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/neo4j/Neo4jAutoConfiguration.html)  
[`Neo4jDataAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/neo4j/Neo4jDataAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/neo4j/Neo4jDataAutoConfiguration.html)  
[`Neo4jReactiveDataAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/neo4j/Neo4jReactiveDataAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/neo4j/Neo4jReactiveDataAutoConfiguration.html)  
[`Neo4jReactiveRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/neo4j/Neo4jReactiveRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/neo4j/Neo4jReactiveRepositoriesAutoConfiguration.html)  
[`Neo4jRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/neo4j/Neo4jRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/neo4j/Neo4jRepositoriesAutoConfiguration.html)  
[`NettyAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/netty/NettyAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/netty/NettyAutoConfiguration.html)  
[`OAuth2AuthorizationServerAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/oauth2/server/servlet/OAuth2AuthorizationServerAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/oauth2/server/servlet/OAuth2AuthorizationServerAutoConfiguration.html)  
[`OAuth2AuthorizationServerJwtAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/oauth2/server/servlet/OAuth2AuthorizationServerJwtAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/oauth2/server/servlet/OAuth2AuthorizationServerJwtAutoConfiguration.html)  
[`OAuth2ClientAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/oauth2/client/servlet/OAuth2ClientAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/oauth2/client/servlet/OAuth2ClientAutoConfiguration.html)  
[`OAuth2ResourceServerAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/oauth2/resource/servlet/OAuth2ResourceServerAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/oauth2/resource/servlet/OAuth2ResourceServerAutoConfiguration.html)  
[`PersistenceExceptionTranslationAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/dao/PersistenceExceptionTranslationAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/dao/PersistenceExceptionTranslationAutoConfiguration.html)  
[`ProjectInfoAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/info/ProjectInfoAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/info/ProjectInfoAutoConfiguration.html)  
[`PropertyPlaceholderAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/context/PropertyPlaceholderAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/context/PropertyPlaceholderAutoConfiguration.html)  
[`PulsarAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/pulsar/PulsarAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/pulsar/PulsarAutoConfiguration.html)  
[`PulsarReactiveAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/pulsar/PulsarReactiveAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/pulsar/PulsarReactiveAutoConfiguration.html)  
[`QuartzAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/quartz/QuartzAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/quartz/QuartzAutoConfiguration.html)  
[`R2dbcAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/r2dbc/R2dbcAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/r2dbc/R2dbcAutoConfiguration.html)  
[`R2dbcDataAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/r2dbc/R2dbcDataAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/r2dbc/R2dbcDataAutoConfiguration.html)  
[`R2dbcProxyAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/r2dbc/R2dbcProxyAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/r2dbc/R2dbcProxyAutoConfiguration.html)  
[`R2dbcRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/r2dbc/R2dbcRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/r2dbc/R2dbcRepositoriesAutoConfiguration.html)  
[`R2dbcTransactionManagerAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/r2dbc/R2dbcTransactionManagerAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/r2dbc/R2dbcTransactionManagerAutoConfiguration.html)  
[`RSocketGraphQlClientAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/graphql/rsocket/RSocketGraphQlClientAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/graphql/rsocket/RSocketGraphQlClientAutoConfiguration.html)  
[`RSocketMessagingAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/rsocket/RSocketMessagingAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/rsocket/RSocketMessagingAutoConfiguration.html)  
[`RSocketRequesterAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/rsocket/RSocketRequesterAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/rsocket/RSocketRequesterAutoConfiguration.html)  
[`RSocketSecurityAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/rsocket/RSocketSecurityAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/rsocket/RSocketSecurityAutoConfiguration.html)  
[`RSocketServerAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/rsocket/RSocketServerAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/rsocket/RSocketServerAutoConfiguration.html)  
[`RSocketStrategiesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/rsocket/RSocketStrategiesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/rsocket/RSocketStrategiesAutoConfiguration.html)  
[`RabbitAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/amqp/RabbitAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/amqp/RabbitAutoConfiguration.html)  
[`ReactiveElasticsearchClientAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/elasticsearch/ReactiveElasticsearchClientAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/elasticsearch/ReactiveElasticsearchClientAutoConfiguration.html)  
[`ReactiveElasticsearchRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/elasticsearch/ReactiveElasticsearchRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/elasticsearch/ReactiveElasticsearchRepositoriesAutoConfiguration.html)  
[`ReactiveMultipartAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/reactive/ReactiveMultipartAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/reactive/ReactiveMultipartAutoConfiguration.html)  
[`ReactiveOAuth2ClientAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/oauth2/client/reactive/ReactiveOAuth2ClientAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/oauth2/client/reactive/ReactiveOAuth2ClientAutoConfiguration.html)  
[`ReactiveOAuth2ResourceServerAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/oauth2/resource/reactive/ReactiveOAuth2ResourceServerAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/oauth2/resource/reactive/ReactiveOAuth2ResourceServerAutoConfiguration.html)  
[`ReactiveSecurityAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/reactive/ReactiveSecurityAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/reactive/ReactiveSecurityAutoConfiguration.html)  
[`ReactiveUserDetailsServiceAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/reactive/ReactiveUserDetailsServiceAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/reactive/ReactiveUserDetailsServiceAutoConfiguration.html)  
[`ReactiveWebServerFactoryAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/reactive/ReactiveWebServerFactoryAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/reactive/ReactiveWebServerFactoryAutoConfiguration.html)  
[`ReactorAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/reactor/ReactorAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/reactor/ReactorAutoConfiguration.html)  
[`RedisAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/redis/RedisAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/redis/RedisAutoConfiguration.html)  
[`RedisReactiveAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/redis/RedisReactiveAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/redis/RedisReactiveAutoConfiguration.html)  
[`RedisRepositoriesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/redis/RedisRepositoriesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/redis/RedisRepositoriesAutoConfiguration.html)  
[`RepositoryRestMvcAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/rest/RepositoryRestMvcAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/rest/RepositoryRestMvcAutoConfiguration.html)  
[`RestClientAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/client/RestClientAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/client/RestClientAutoConfiguration.html)  
[`RestTemplateAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/client/RestTemplateAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/client/RestTemplateAutoConfiguration.html)  
[`Saml2RelyingPartyAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/saml2/Saml2RelyingPartyAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/saml2/Saml2RelyingPartyAutoConfiguration.html)  
[`SecurityAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/servlet/SecurityAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/servlet/SecurityAutoConfiguration.html)  
[`SecurityFilterAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/servlet/SecurityFilterAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/servlet/SecurityFilterAutoConfiguration.html)  
[`SendGridAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/sendgrid/SendGridAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/sendgrid/SendGridAutoConfiguration.html)  
[`ServletWebServerFactoryAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/servlet/ServletWebServerFactoryAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/servlet/ServletWebServerFactoryAutoConfiguration.html)  
[`SessionAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/session/SessionAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/session/SessionAutoConfiguration.html)  
[`SpringApplicationAdminJmxAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/admin/SpringApplicationAdminJmxAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/admin/SpringApplicationAdminJmxAutoConfiguration.html)  
[`SpringDataWebAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/data/web/SpringDataWebAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/data/web/SpringDataWebAutoConfiguration.html)  
[`SqlInitializationAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/sql/init/SqlInitializationAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/sql/init/SqlInitializationAutoConfiguration.html)  
[`SslAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/ssl/SslAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/ssl/SslAutoConfiguration.html)  
[`TaskExecutionAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/task/TaskExecutionAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/task/TaskExecutionAutoConfiguration.html)  
[`TaskSchedulingAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/task/TaskSchedulingAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/task/TaskSchedulingAutoConfiguration.html)  
[`ThymeleafAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/thymeleaf/ThymeleafAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/thymeleaf/ThymeleafAutoConfiguration.html)  
[`TransactionAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/transaction/TransactionAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/transaction/TransactionAutoConfiguration.html)  
[`TransactionManagerCustomizationAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/transaction/TransactionManagerCustomizationAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/transaction/TransactionManagerCustomizationAutoConfiguration.html)  
[`UserDetailsServiceAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/security/servlet/UserDetailsServiceAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/security/servlet/UserDetailsServiceAutoConfiguration.html)  
[`ValidationAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/validation/ValidationAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/validation/ValidationAutoConfiguration.html)  
[`WebClientAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/reactive/function/client/WebClientAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/reactive/function/client/WebClientAutoConfiguration.html)  
[`WebFluxAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/reactive/WebFluxAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/reactive/WebFluxAutoConfiguration.html)  
[`WebMvcAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration.html)  
[`WebServiceTemplateAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/webservices/client/WebServiceTemplateAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/webservices/client/WebServiceTemplateAutoConfiguration.html)  
[`WebServicesAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/webservices/WebServicesAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/webservices/WebServicesAutoConfiguration.html)  
[`WebSessionIdResolverAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/web/reactive/WebSessionIdResolverAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/web/reactive/WebSessionIdResolverAutoConfiguration.html)  
[`WebSocketMessagingAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/websocket/servlet/WebSocketMessagingAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/websocket/servlet/WebSocketMessagingAutoConfiguration.html)  
[`WebSocketReactiveAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/websocket/reactive/WebSocketReactiveAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/websocket/reactive/WebSocketReactiveAutoConfiguration.html)  
[`WebSocketServletAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/websocket/servlet/WebSocketServletAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/websocket/servlet/WebSocketServletAutoConfiguration.html)  
[`XADataSourceAutoConfiguration`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-autoconfigure/src/main/java/org/springframework/boot/autoconfigure/jdbc/XADataSourceAutoConfiguration.java) | [javadoc](../../api/java/org/springframework/boot/autoconfigure/jdbc/XADataSourceAutoConfiguration.html)  
[Auto-configuration Classes](index.html) [spring-boot-actuator-autoconfigure](actuator.html)
---
