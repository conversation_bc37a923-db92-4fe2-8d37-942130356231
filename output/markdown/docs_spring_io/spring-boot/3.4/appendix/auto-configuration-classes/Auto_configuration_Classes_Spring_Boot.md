Title: Auto-configuration Classes :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/appendix/auto-configuration-classes/index.html
HTML: html/docs_spring_io/spring-boot/3.4/appendix/auto-configuration-classes/Auto_configuration_Classes_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/appendix/auto-configuration-classes/Auto_configuration_Classes_Spring_Boot.png
crawled_at: 2025-06-04T16:01:52.944411
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/appendix/pages/auto-configuration-classes/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * Appendix
  * [Auto-configuration Classes](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../appendix/auto-configuration-classes/index.html)!  
---|---  
  
# Auto-configuration Classes

This appendix contains details of all of the auto-configuration classes provided by Spring Boot, with links to documentation and source code. Remember to also look at the conditions report in your application for more details of which features are switched on. (To do so, start the app with `--debug` or `-Ddebug` or, in an Actuator application, use the `conditions` endpoint).

[Common Application Properties](../application-properties/index.html) [spring-boot-autoconfigure](core.html)
---
