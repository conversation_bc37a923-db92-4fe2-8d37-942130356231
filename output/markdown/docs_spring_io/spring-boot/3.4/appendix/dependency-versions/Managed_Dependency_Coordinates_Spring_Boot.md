Title: Managed Dependency Coordinates :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/appendix/dependency-versions/coordinates.html
HTML: html/docs_spring_io/spring-boot/3.4/appendix/dependency-versions/Managed_Dependency_Coordinates_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/appendix/dependency-versions/Managed_Dependency_Coordinates_Spring_Boot.png
crawled_at: 2025-06-04T16:04:46.222109
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/appendix/pages/dependency-versions/coordinates.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * Appendix
  * [Dependency Versions](index.html)
  * [Managed Dependency Coordinates](coordinates.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../appendix/dependency-versions/coordinates.html)!  
---|---  
  
# Managed Dependency Coordinates

The following table provides details of all of the dependency versions that are provided by Spring Boot in its CLI (Command Line Interface), Maven dependency management, and Gradle plugin. When you declare a dependency on one of these artifacts without declaring a version, the version listed in the table is used.

Group ID | Artifact ID | Version  
---|---|---  
`biz.aQute.bnd` | `biz.aQute.bnd.annotation` | `7.0.0`  
`ch.qos.logback` | `logback-classic` | `1.5.18`  
`ch.qos.logback` | `logback-core` | `1.5.18`  
`co.elastic.clients` | `elasticsearch-java` | `8.15.5`  
`com.couchbase.client` | `java-client` | `3.7.9`  
`com.datastax.oss` | `java-driver-shaded-guava` | `25.1-jre-graal-sub-1`  
`com.datastax.oss` | `native-protocol` | `1.5.1`  
`com.fasterxml` | `classmate` | `1.7.0`  
`com.fasterxml.jackson.core` | `jackson-annotations` | `2.18.4`  
`com.fasterxml.jackson.core` | `jackson-core` | `2.18.4`  
`com.fasterxml.jackson.core` | `jackson-databind` | `2.18.4`  
`com.fasterxml.jackson.dataformat` | `jackson-dataformat-avro` | `2.18.4`  
`com.fasterxml.jackson.dataformat` | `jackson-dataformat-cbor` | `2.18.4`  
`com.fasterxml.jackson.dataformat` | `jackson-dataformat-csv` | `2.18.4`  
`com.fasterxml.jackson.dataformat` | `jackson-dataformat-ion` | `2.18.4`  
`com.fasterxml.jackson.dataformat` | `jackson-dataformat-properties` | `2.18.4`  
`com.fasterxml.jackson.dataformat` | `jackson-dataformat-protobuf` | `2.18.4`  
`com.fasterxml.jackson.dataformat` | `jackson-dataformat-smile` | `2.18.4`  
`com.fasterxml.jackson.dataformat` | `jackson-dataformat-toml` | `2.18.4`  
`com.fasterxml.jackson.dataformat` | `jackson-dataformat-xml` | `2.18.4`  
`com.fasterxml.jackson.dataformat` | `jackson-dataformat-yaml` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-eclipse-collections` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-guava` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-hibernate4` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-hibernate5` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-hibernate5-jakarta` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-hibernate6` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-hppc` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-jakarta-jsonp` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-jaxrs` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-jdk8` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-joda` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-joda-money` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-json-org` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-jsr310` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-jsr353` | `2.18.4`  
`com.fasterxml.jackson.datatype` | `jackson-datatype-pcollections` | `2.18.4`  
`com.fasterxml.jackson.jakarta.rs` | `jackson-jakarta-rs-base` | `2.18.4`  
`com.fasterxml.jackson.jakarta.rs` | `jackson-jakarta-rs-cbor-provider` | `2.18.4`  
`com.fasterxml.jackson.jakarta.rs` | `jackson-jakarta-rs-json-provider` | `2.18.4`  
`com.fasterxml.jackson.jakarta.rs` | `jackson-jakarta-rs-smile-provider` | `2.18.4`  
`com.fasterxml.jackson.jakarta.rs` | `jackson-jakarta-rs-xml-provider` | `2.18.4`  
`com.fasterxml.jackson.jakarta.rs` | `jackson-jakarta-rs-yaml-provider` | `2.18.4`  
`com.fasterxml.jackson.jaxrs` | `jackson-jaxrs-base` | `2.18.4`  
`com.fasterxml.jackson.jaxrs` | `jackson-jaxrs-cbor-provider` | `2.18.4`  
`com.fasterxml.jackson.jaxrs` | `jackson-jaxrs-json-provider` | `2.18.4`  
`com.fasterxml.jackson.jaxrs` | `jackson-jaxrs-smile-provider` | `2.18.4`  
`com.fasterxml.jackson.jaxrs` | `jackson-jaxrs-xml-provider` | `2.18.4`  
`com.fasterxml.jackson.jaxrs` | `jackson-jaxrs-yaml-provider` | `2.18.4`  
`com.fasterxml.jackson.jr` | `jackson-jr-all` | `2.18.4`  
`com.fasterxml.jackson.jr` | `jackson-jr-annotation-support` | `2.18.4`  
`com.fasterxml.jackson.jr` | `jackson-jr-extension-javatime` | `2.18.4`  
`com.fasterxml.jackson.jr` | `jackson-jr-objects` | `2.18.4`  
`com.fasterxml.jackson.jr` | `jackson-jr-retrofit2` | `2.18.4`  
`com.fasterxml.jackson.jr` | `jackson-jr-stree` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-afterburner` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-android-record` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-blackbird` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-guice` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-guice7` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-jakarta-xmlbind-annotations` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-jaxb-annotations` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-jsonSchema` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-jsonSchema-jakarta` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-kotlin` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-mrbean` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-no-ctor-deser` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-osgi` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-parameter-names` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-paranamer` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-scala_2.11` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-scala_2.12` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-scala_2.13` | `2.18.4`  
`com.fasterxml.jackson.module` | `jackson-module-scala_3` | `2.18.4`  
`com.github.ben-manes.caffeine` | `caffeine` | `3.1.8`  
`com.github.ben-manes.caffeine` | `guava` | `3.1.8`  
`com.github.ben-manes.caffeine` | `jcache` | `3.1.8`  
`com.github.ben-manes.caffeine` | `simulator` | `3.1.8`  
`com.github.mxab.thymeleaf.extras` | `thymeleaf-extras-data-attribute` | `2.0.1`  
`com.github.spotbugs` | `spotbugs-annotations` | `4.8.6`  
`com.google.code.gson` | `gson` | `2.11.0`  
`com.graphql-java` | `graphql-java` | `22.3`  
`com.h2database` | `h2` | `2.3.232`  
`com.hazelcast` | `hazelcast` | `5.5.0`  
`com.hazelcast` | `hazelcast-spring` | `5.5.0`  
`com.ibm.db2` | `jcc` | `11.5.9.0`  
`com.jayway.jsonpath` | `json-path` | `2.9.0`  
`com.jayway.jsonpath` | `json-path-assert` | `2.9.0`  
`com.microsoft.sqlserver` | `mssql-jdbc` | `12.8.1.jre11`  
`com.mysql` | `mysql-connector-j` | `9.1.0`  
`com.oracle.database.ha` | `ons` | `23.5.0.24.07`  
`com.oracle.database.ha` | `simplefan` | `23.5.0.24.07`  
`com.oracle.database.jdbc` | `ojdbc11` | `23.5.0.24.07`  
`com.oracle.database.jdbc` | `ojdbc11-production` | `23.5.0.24.07`  
`com.oracle.database.jdbc` | `ojdbc8` | `23.5.0.24.07`  
`com.oracle.database.jdbc` | `ojdbc8-production` | `23.5.0.24.07`  
`com.oracle.database.jdbc` | `rsi` | `23.5.0.24.07`  
`com.oracle.database.jdbc` | `ucp` | `23.5.0.24.07`  
`com.oracle.database.jdbc` | `ucp11` | `23.5.0.24.07`  
`com.oracle.database.nls` | `orai18n` | `23.5.0.24.07`  
`com.oracle.database.r2dbc` | `oracle-r2dbc` | `1.2.0`  
`com.oracle.database.security` | `oraclepki` | `23.5.0.24.07`  
`com.oracle.database.xml` | `xdb` | `23.5.0.24.07`  
`com.oracle.database.xml` | `xmlparserv2` | `23.5.0.24.07`  
`com.querydsl` | `codegen-utils` | `5.1.0`  
`com.querydsl` | `querydsl-apt` | `5.1.0`  
`com.querydsl` | `querydsl-codegen` | `5.1.0`  
`com.querydsl` | `querydsl-collections` | `5.1.0`  
`com.querydsl` | `querydsl-core` | `5.1.0`  
`com.querydsl` | `querydsl-guava` | `5.1.0`  
`com.querydsl` | `querydsl-hibernate-search` | `5.1.0`  
`com.querydsl` | `querydsl-jdo` | `5.1.0`  
`com.querydsl` | `querydsl-jpa` | `5.1.0`  
`com.querydsl` | `querydsl-jpa-codegen` | `5.1.0`  
`com.querydsl` | `querydsl-kotlin` | `5.1.0`  
`com.querydsl` | `querydsl-kotlin-codegen` | `5.1.0`  
`com.querydsl` | `querydsl-lucene3` | `5.1.0`  
`com.querydsl` | `querydsl-lucene4` | `5.1.0`  
`com.querydsl` | `querydsl-lucene5` | `5.1.0`  
`com.querydsl` | `querydsl-mongodb` | `5.1.0`  
`com.querydsl` | `querydsl-scala` | `5.1.0`  
`com.querydsl` | `querydsl-spatial` | `5.1.0`  
`com.querydsl` | `querydsl-sql` | `5.1.0`  
`com.querydsl` | `querydsl-sql-codegen` | `5.1.0`  
`com.querydsl` | `querydsl-sql-spatial` | `5.1.0`  
`com.querydsl` | `querydsl-sql-spring` | `5.1.0`  
`com.rabbitmq` | `amqp-client` | `5.22.0`  
`com.rabbitmq` | `stream-client` | `0.18.0`  
`com.redis` | `testcontainers-redis` | `2.2.4`  
`com.samskivert` | `jmustache` | `1.16`  
`com.sendgrid` | `sendgrid-java` | `4.10.3`  
`com.sun.istack` | `istack-commons-runtime` | `4.1.2`  
`com.sun.xml.bind` | `jaxb-core` | `4.0.5`  
`com.sun.xml.bind` | `jaxb-impl` | `4.0.5`  
`com.sun.xml.bind` | `jaxb-jxc` | `4.0.5`  
`com.sun.xml.bind` | `jaxb-osgi` | `4.0.5`  
`com.sun.xml.bind` | `jaxb-xjc` | `4.0.5`  
`com.sun.xml.fastinfoset` | `FastInfoset` | `2.1.1`  
`com.sun.xml.messaging.saaj` | `saaj-impl` | `3.0.4`  
`com.unboundid` | `unboundid-ldapsdk` | `6.0.11`  
`com.zaxxer` | `HikariCP` | `5.1.0`  
`commons-codec` | `commons-codec` | `1.17.2`  
`commons-pool` | `commons-pool` | `1.6`  
`io.asyncer` | `r2dbc-mysql` | `1.3.2`  
`io.lettuce` | `lettuce-core` | `6.4.2.RELEASE`  
`io.micrometer` | `context-propagation` | `1.1.3`  
`io.micrometer` | `docs` | `1.4.6`  
`io.micrometer` | `micrometer-commons` | `1.14.7`  
`io.micrometer` | `micrometer-core` | `1.14.7`  
`io.micrometer` | `micrometer-jakarta9` | `1.14.7`  
`io.micrometer` | `micrometer-java11` | `1.14.7`  
`io.micrometer` | `micrometer-java21` | `1.14.7`  
`io.micrometer` | `micrometer-jetty11` | `1.14.7`  
`io.micrometer` | `micrometer-jetty12` | `1.14.7`  
`io.micrometer` | `micrometer-observation` | `1.14.7`  
`io.micrometer` | `micrometer-observation-test` | `1.14.7`  
`io.micrometer` | `micrometer-registry-appoptics` | `1.14.7`  
`io.micrometer` | `micrometer-registry-atlas` | `1.14.7`  
`io.micrometer` | `micrometer-registry-azure-monitor` | `1.14.7`  
`io.micrometer` | `micrometer-registry-cloudwatch2` | `1.14.7`  
`io.micrometer` | `micrometer-registry-datadog` | `1.14.7`  
`io.micrometer` | `micrometer-registry-dynatrace` | `1.14.7`  
`io.micrometer` | `micrometer-registry-elastic` | `1.14.7`  
`io.micrometer` | `micrometer-registry-ganglia` | `1.14.7`  
`io.micrometer` | `micrometer-registry-graphite` | `1.14.7`  
`io.micrometer` | `micrometer-registry-health` | `1.14.7`  
`io.micrometer` | `micrometer-registry-humio` | `1.14.7`  
`io.micrometer` | `micrometer-registry-influx` | `1.14.7`  
`io.micrometer` | `micrometer-registry-jmx` | `1.14.7`  
`io.micrometer` | `micrometer-registry-kairos` | `1.14.7`  
`io.micrometer` | `micrometer-registry-new-relic` | `1.14.7`  
`io.micrometer` | `micrometer-registry-opentsdb` | `1.14.7`  
`io.micrometer` | `micrometer-registry-otlp` | `1.14.7`  
`io.micrometer` | `micrometer-registry-prometheus` | `1.14.7`  
`io.micrometer` | `micrometer-registry-prometheus-simpleclient` | `1.14.7`  
`io.micrometer` | `micrometer-registry-signalfx` | `1.14.7`  
`io.micrometer` | `micrometer-registry-stackdriver` | `1.14.7`  
`io.micrometer` | `micrometer-registry-statsd` | `1.14.7`  
`io.micrometer` | `micrometer-registry-wavefront` | `1.14.7`  
`io.micrometer` | `micrometer-test` | `1.14.7`  
`io.micrometer` | `micrometer-tracing` | `1.4.6`  
`io.micrometer` | `micrometer-tracing-bridge-brave` | `1.4.6`  
`io.micrometer` | `micrometer-tracing-bridge-otel` | `1.4.6`  
`io.micrometer` | `micrometer-tracing-integration-test` | `1.4.6`  
`io.micrometer` | `micrometer-tracing-reporter-wavefront` | `1.4.6`  
`io.micrometer` | `micrometer-tracing-test` | `1.4.6`  
`io.netty` | `netty-all` | `4.1.121.Final`  
`io.netty` | `netty-buffer` | `4.1.121.Final`  
`io.netty` | `netty-codec` | `4.1.121.Final`  
`io.netty` | `netty-codec-dns` | `4.1.121.Final`  
`io.netty` | `netty-codec-haproxy` | `4.1.121.Final`  
`io.netty` | `netty-codec-http` | `4.1.121.Final`  
`io.netty` | `netty-codec-http2` | `4.1.121.Final`  
`io.netty` | `netty-codec-memcache` | `4.1.121.Final`  
`io.netty` | `netty-codec-mqtt` | `4.1.121.Final`  
`io.netty` | `netty-codec-redis` | `4.1.121.Final`  
`io.netty` | `netty-codec-smtp` | `4.1.121.Final`  
`io.netty` | `netty-codec-socks` | `4.1.121.Final`  
`io.netty` | `netty-codec-stomp` | `4.1.121.Final`  
`io.netty` | `netty-codec-xml` | `4.1.121.Final`  
`io.netty` | `netty-common` | `4.1.121.Final`  
`io.netty` | `netty-dev-tools` | `4.1.121.Final`  
`io.netty` | `netty-example` | `4.1.121.Final`  
`io.netty` | `netty-handler` | `4.1.121.Final`  
`io.netty` | `netty-handler-proxy` | `4.1.121.Final`  
`io.netty` | `netty-handler-ssl-ocsp` | `4.1.121.Final`  
`io.netty` | `netty-resolver` | `4.1.121.Final`  
`io.netty` | `netty-resolver-dns` | `4.1.121.Final`  
`io.netty` | `netty-resolver-dns-classes-macos` | `4.1.121.Final`  
`io.netty` | `netty-resolver-dns-native-macos` | `4.1.121.Final`  
`io.netty` | `netty-tcnative` | `2.0.70.Final`  
`io.netty` | `netty-tcnative-boringssl-static` | `2.0.70.Final`  
`io.netty` | `netty-tcnative-classes` | `2.0.70.Final`  
`io.netty` | `netty-transport` | `4.1.121.Final`  
`io.netty` | `netty-transport-classes-epoll` | `4.1.121.Final`  
`io.netty` | `netty-transport-classes-kqueue` | `4.1.121.Final`  
`io.netty` | `netty-transport-native-epoll` | `4.1.121.Final`  
`io.netty` | `netty-transport-native-kqueue` | `4.1.121.Final`  
`io.netty` | `netty-transport-native-unix-common` | `4.1.121.Final`  
`io.netty` | `netty-transport-rxtx` | `4.1.121.Final`  
`io.netty` | `netty-transport-sctp` | `4.1.121.Final`  
`io.netty` | `netty-transport-udt` | `4.1.121.Final`  
`io.opentelemetry` | `opentelemetry-api` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-context` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-exporter-common` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-exporter-logging` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-exporter-logging-otlp` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-exporter-otlp` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-exporter-otlp-common` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-exporter-sender-grpc-managed-channel` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-exporter-sender-jdk` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-exporter-sender-okhttp` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-exporter-zipkin` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-extension-kotlin` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-extension-trace-propagators` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-opentracing-shim` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-sdk` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-sdk-common` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-sdk-extension-autoconfigure` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-sdk-extension-autoconfigure-spi` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-sdk-extension-jaeger-remote-sampler` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-sdk-logs` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-sdk-metrics` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-sdk-testing` | `1.43.0`  
`io.opentelemetry` | `opentelemetry-sdk-trace` | `1.43.0`  
`io.projectreactor` | `reactor-core` | `3.7.6`  
`io.projectreactor` | `reactor-core-micrometer` | `1.2.6`  
`io.projectreactor` | `reactor-test` | `3.7.6`  
`io.projectreactor` | `reactor-tools` | `3.7.6`  
`io.projectreactor.addons` | `reactor-adapter` | `3.5.2`  
`io.projectreactor.addons` | `reactor-extra` | `3.5.2`  
`io.projectreactor.addons` | `reactor-pool` | `1.1.2`  
`io.projectreactor.addons` | `reactor-pool-micrometer` | `0.2.2`  
`io.projectreactor.kafka` | `reactor-kafka` | `1.3.23`  
`io.projectreactor.kotlin` | `reactor-kotlin-extensions` | `1.2.3`  
`io.projectreactor.netty` | `reactor-netty` | `1.2.6`  
`io.projectreactor.netty` | `reactor-netty-core` | `1.2.6`  
`io.projectreactor.netty` | `reactor-netty-http` | `1.2.6`  
`io.projectreactor.netty` | `reactor-netty-http-brave` | `1.2.6`  
`io.prometheus` | `prometheus-metrics-config` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-core` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-exporter-common` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-exporter-httpserver` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-exporter-opentelemetry` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-exporter-pushgateway` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-exporter-servlet-jakarta` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-exporter-servlet-javax` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-exposition-formats` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-instrumentation-caffeine` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-instrumentation-dropwizard` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-instrumentation-dropwizard5` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-instrumentation-guava` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-instrumentation-jvm` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-model` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-simpleclient-bridge` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-tracer` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-tracer-common` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-tracer-initializer` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-tracer-otel` | `1.3.6`  
`io.prometheus` | `prometheus-metrics-tracer-otel-agent` | `1.3.6`  
`io.prometheus` | `simpleclient` | `0.16.0`  
`io.prometheus` | `simpleclient_caffeine` | `0.16.0`  
`io.prometheus` | `simpleclient_common` | `0.16.0`  
`io.prometheus` | `simpleclient_dropwizard` | `0.16.0`  
`io.prometheus` | `simpleclient_graphite_bridge` | `0.16.0`  
`io.prometheus` | `simpleclient_guava` | `0.16.0`  
`io.prometheus` | `simpleclient_hibernate` | `0.16.0`  
`io.prometheus` | `simpleclient_hotspot` | `0.16.0`  
`io.prometheus` | `simpleclient_httpserver` | `0.16.0`  
`io.prometheus` | `simpleclient_jetty` | `0.16.0`  
`io.prometheus` | `simpleclient_jetty_jdk8` | `0.16.0`  
`io.prometheus` | `simpleclient_log4j` | `0.16.0`  
`io.prometheus` | `simpleclient_log4j2` | `0.16.0`  
`io.prometheus` | `simpleclient_logback` | `0.16.0`  
`io.prometheus` | `simpleclient_pushgateway` | `0.16.0`  
`io.prometheus` | `simpleclient_servlet` | `0.16.0`  
`io.prometheus` | `simpleclient_servlet_jakarta` | `0.16.0`  
`io.prometheus` | `simpleclient_spring_boot` | `0.16.0`  
`io.prometheus` | `simpleclient_spring_web` | `0.16.0`  
`io.prometheus` | `simpleclient_tracer_common` | `0.16.0`  
`io.prometheus` | `simpleclient_tracer_otel` | `0.16.0`  
`io.prometheus` | `simpleclient_tracer_otel_agent` | `0.16.0`  
`io.prometheus` | `simpleclient_vertx` | `0.16.0`  
`io.r2dbc` | `r2dbc-h2` | `1.0.0.RELEASE`  
`io.r2dbc` | `r2dbc-mssql` | `1.0.2.RELEASE`  
`io.r2dbc` | `r2dbc-pool` | `1.0.2.RELEASE`  
`io.r2dbc` | `r2dbc-proxy` | `1.1.6.RELEASE`  
`io.r2dbc` | `r2dbc-spi` | `1.0.0.RELEASE`  
`io.reactivex.rxjava3` | `rxjava` | `3.1.10`  
`io.rest-assured` | `json-path` | `5.5.2`  
`io.rest-assured` | `json-schema-validator` | `5.5.2`  
`io.rest-assured` | `kotlin-extensions` | `5.5.2`  
`io.rest-assured` | `rest-assured` | `5.5.2`  
`io.rest-assured` | `rest-assured-all` | `5.5.2`  
`io.rest-assured` | `rest-assured-common` | `5.5.2`  
`io.rest-assured` | `scala-extensions` | `5.5.2`  
`io.rest-assured` | `scala-support` | `5.5.2`  
`io.rest-assured` | `spring-commons` | `5.5.2`  
`io.rest-assured` | `spring-mock-mvc` | `5.5.2`  
`io.rest-assured` | `spring-mock-mvc-kotlin-extensions` | `5.5.2`  
`io.rest-assured` | `spring-web-test-client` | `5.5.2`  
`io.rest-assured` | `spring-web-test-client-kotlin-extensions` | `5.5.2`  
`io.rest-assured` | `xml-path` | `5.5.2`  
`io.rsocket` | `rsocket-core` | `1.1.5`  
`io.rsocket` | `rsocket-load-balancer` | `1.1.5`  
`io.rsocket` | `rsocket-micrometer` | `1.1.5`  
`io.rsocket` | `rsocket-test` | `1.1.5`  
`io.rsocket` | `rsocket-transport-local` | `1.1.5`  
`io.rsocket` | `rsocket-transport-netty` | `1.1.5`  
`io.spring.gradle` | `dependency-management-plugin` | `1.1.7`  
`io.undertow` | `undertow-core` | `2.3.18.Final`  
`io.undertow` | `undertow-servlet` | `2.3.18.Final`  
`io.undertow` | `undertow-websockets-jsr` | `2.3.18.Final`  
`io.zipkin.brave` | `brave` | `6.0.3`  
`io.zipkin.brave` | `brave-context-jfr` | `6.0.3`  
`io.zipkin.brave` | `brave-context-log4j12` | `6.0.3`  
`io.zipkin.brave` | `brave-context-log4j2` | `6.0.3`  
`io.zipkin.brave` | `brave-context-slf4j` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-benchmarks` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-dubbo` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-grpc` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-http` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-http-tests` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-http-tests-jakarta` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-httpasyncclient` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-httpclient` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-httpclient5` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-jakarta-jms` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-jaxrs2` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-jersey-server` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-jms` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-jms-jakarta` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-kafka-clients` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-kafka-streams` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-messaging` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-mongodb` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-mysql` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-mysql6` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-mysql8` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-netty-codec-http` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-okhttp3` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-rpc` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-servlet` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-servlet-jakarta` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-spring-rabbit` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-spring-web` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-spring-webmvc` | `6.0.3`  
`io.zipkin.brave` | `brave-instrumentation-vertx-web` | `6.0.3`  
`io.zipkin.brave` | `brave-spring-beans` | `6.0.3`  
`io.zipkin.brave` | `brave-tests` | `6.0.3`  
`io.zipkin.reporter2` | `zipkin-reporter` | `3.4.3`  
`io.zipkin.reporter2` | `zipkin-reporter-brave` | `3.4.3`  
`io.zipkin.reporter2` | `zipkin-reporter-metrics-micrometer` | `3.4.3`  
`io.zipkin.reporter2` | `zipkin-reporter-spring-beans` | `3.4.3`  
`io.zipkin.reporter2` | `zipkin-sender-activemq-client` | `3.4.3`  
`io.zipkin.reporter2` | `zipkin-sender-amqp-client` | `3.4.3`  
`io.zipkin.reporter2` | `zipkin-sender-kafka` | `3.4.3`  
`io.zipkin.reporter2` | `zipkin-sender-libthrift` | `3.4.3`  
`io.zipkin.reporter2` | `zipkin-sender-okhttp3` | `3.4.3`  
`io.zipkin.reporter2` | `zipkin-sender-urlconnection` | `3.4.3`  
`io.zipkin.zipkin2` | `zipkin` | `2.27.1`  
`jakarta.activation` | `jakarta.activation-api` | `2.1.3`  
`jakarta.annotation` | `jakarta.annotation-api` | `2.1.1`  
`jakarta.inject` | `jakarta.inject-api` | `2.0.1`  
`jakarta.jms` | `jakarta.jms-api` | `3.1.0`  
`jakarta.json` | `jakarta.json-api` | `2.1.3`  
`jakarta.json.bind` | `jakarta.json.bind-api` | `3.0.1`  
`jakarta.mail` | `jakarta.mail-api` | `2.1.3`  
`jakarta.management.j2ee` | `jakarta.management.j2ee-api` | `1.1.4`  
`jakarta.persistence` | `jakarta.persistence-api` | `3.1.0`  
`jakarta.servlet` | `jakarta.servlet-api` | `6.0.0`  
`jakarta.servlet.jsp.jstl` | `jakarta.servlet.jsp.jstl-api` | `3.0.2`  
`jakarta.transaction` | `jakarta.transaction-api` | `2.0.1`  
`jakarta.validation` | `jakarta.validation-api` | `3.0.2`  
`jakarta.websocket` | `jakarta.websocket-api` | `2.1.1`  
`jakarta.websocket` | `jakarta.websocket-client-api` | `2.1.1`  
`jakarta.ws.rs` | `jakarta.ws.rs-api` | `3.1.0`  
`jakarta.xml.bind` | `jakarta.xml.bind-api` | `4.0.2`  
`jakarta.xml.soap` | `jakarta.xml.soap-api` | `3.0.2`  
`jakarta.xml.ws` | `jakarta.xml.ws-api` | `4.0.2`  
`javax.cache` | `cache-api` | `1.1.1`  
`javax.money` | `money-api` | `1.1`  
`jaxen` | `jaxen` | `2.0.0`  
`junit` | `junit` | `4.13.2`  
`net.bytebuddy` | `byte-buddy` | `1.15.11`  
`net.bytebuddy` | `byte-buddy-agent` | `1.15.11`  
`net.minidev` | `json-smart` | `2.5.2`  
`net.sourceforge.jtds` | `jtds` | `1.3.1`  
`net.sourceforge.nekohtml` | `nekohtml` | `1.9.22`  
`nz.net.ultraq.thymeleaf` | `thymeleaf-layout-dialect` | `3.3.0`  
`org.apache.activemq` | `activemq-all` | `6.1.6`  
`org.apache.activemq` | `activemq-amqp` | `6.1.6`  
`org.apache.activemq` | `activemq-blueprint` | `6.1.6`  
`org.apache.activemq` | `activemq-branding` | `2.37.0`  
`org.apache.activemq` | `activemq-broker` | `6.1.6`  
`org.apache.activemq` | `activemq-client` | `6.1.6`  
`org.apache.activemq` | `activemq-console` | `6.1.6`  
`org.apache.activemq` | `activemq-http` | `6.1.6`  
`org.apache.activemq` | `activemq-jaas` | `6.1.6`  
`org.apache.activemq` | `activemq-jdbc-store` | `6.1.6`  
`org.apache.activemq` | `activemq-jms-pool` | `6.1.6`  
`org.apache.activemq` | `activemq-kahadb-store` | `6.1.6`  
`org.apache.activemq` | `activemq-karaf` | `6.1.6`  
`org.apache.activemq` | `activemq-log4j-appender` | `6.1.6`  
`org.apache.activemq` | `activemq-mqtt` | `6.1.6`  
`org.apache.activemq` | `activemq-openwire-generator` | `6.1.6`  
`org.apache.activemq` | `activemq-openwire-legacy` | `6.1.6`  
`org.apache.activemq` | `activemq-osgi` | `6.1.6`  
`org.apache.activemq` | `activemq-pool` | `6.1.6`  
`org.apache.activemq` | `activemq-ra` | `6.1.6`  
`org.apache.activemq` | `activemq-rar` | `6.1.6`  
`org.apache.activemq` | `activemq-run` | `6.1.6`  
`org.apache.activemq` | `activemq-runtime-config` | `6.1.6`  
`org.apache.activemq` | `activemq-shiro` | `6.1.6`  
`org.apache.activemq` | `activemq-spring` | `6.1.6`  
`org.apache.activemq` | `activemq-stomp` | `6.1.6`  
`org.apache.activemq` | `activemq-web` | `6.1.6`  
`org.apache.activemq` | `activemq-web-console` | `6.1.6`  
`org.apache.activemq` | `activemq-web-demo` | `6.1.6`  
`org.apache.activemq` | `artemis-amqp-protocol` | `2.37.0`  
`org.apache.activemq` | `artemis-boot` | `2.37.0`  
`org.apache.activemq` | `artemis-cdi-client` | `2.37.0`  
`org.apache.activemq` | `artemis-cli` | `2.37.0`  
`org.apache.activemq` | `artemis-commons` | `2.37.0`  
`org.apache.activemq` | `artemis-console` | `2.37.0`  
`org.apache.activemq` | `artemis-core-client` | `2.37.0`  
`org.apache.activemq` | `artemis-core-client-all` | `2.37.0`  
`org.apache.activemq` | `artemis-core-client-osgi` | `2.37.0`  
`org.apache.activemq` | `artemis-dto` | `2.37.0`  
`org.apache.activemq` | `artemis-features` | `2.37.0`  
`org.apache.activemq` | `artemis-hornetq-protocol` | `2.37.0`  
`org.apache.activemq` | `artemis-hqclient-protocol` | `2.37.0`  
`org.apache.activemq` | `artemis-jakarta-client` | `2.37.0`  
`org.apache.activemq` | `artemis-jakarta-client-all` | `2.37.0`  
`org.apache.activemq` | `artemis-jakarta-openwire-protocol` | `2.37.0`  
`org.apache.activemq` | `artemis-jakarta-ra` | `2.37.0`  
`org.apache.activemq` | `artemis-jakarta-server` | `2.37.0`  
`org.apache.activemq` | `artemis-jakarta-service-extensions` | `2.37.0`  
`org.apache.activemq` | `artemis-jdbc-store` | `2.37.0`  
`org.apache.activemq` | `artemis-jms-client` | `2.37.0`  
`org.apache.activemq` | `artemis-jms-client-all` | `2.37.0`  
`org.apache.activemq` | `artemis-jms-client-osgi` | `2.37.0`  
`org.apache.activemq` | `artemis-jms-server` | `2.37.0`  
`org.apache.activemq` | `artemis-journal` | `2.37.0`  
`org.apache.activemq` | `artemis-lockmanager-api` | `2.37.0`  
`org.apache.activemq` | `artemis-lockmanager-ri` | `2.37.0`  
`org.apache.activemq` | `artemis-mqtt-protocol` | `2.37.0`  
`org.apache.activemq` | `artemis-openwire-protocol` | `2.37.0`  
`org.apache.activemq` | `artemis-plugin` | `2.37.0`  
`org.apache.activemq` | `artemis-ra` | `2.37.0`  
`org.apache.activemq` | `artemis-selector` | `2.37.0`  
`org.apache.activemq` | `artemis-server` | `2.37.0`  
`org.apache.activemq` | `artemis-server-osgi` | `2.37.0`  
`org.apache.activemq` | `artemis-service-extensions` | `2.37.0`  
`org.apache.activemq` | `artemis-stomp-protocol` | `2.37.0`  
`org.apache.activemq` | `artemis-web` | `2.37.0`  
`org.apache.activemq` | `artemis-website` | `2.37.0`  
`org.apache.cassandra` | `java-driver-core` | `4.18.1`  
`org.apache.cassandra` | `java-driver-core-shaded` | `4.18.1`  
`org.apache.cassandra` | `java-driver-mapper-processor` | `4.18.1`  
`org.apache.cassandra` | `java-driver-mapper-runtime` | `4.18.1`  
`org.apache.cassandra` | `java-driver-metrics-micrometer` | `4.18.1`  
`org.apache.cassandra` | `java-driver-metrics-microprofile` | `4.18.1`  
`org.apache.cassandra` | `java-driver-query-builder` | `4.18.1`  
`org.apache.cassandra` | `java-driver-test-infra` | `4.18.1`  
`org.apache.commons` | `commons-dbcp2` | `2.12.0`  
`org.apache.commons` | `commons-lang3` | `3.17.0`  
`org.apache.commons` | `commons-pool2` | `2.12.1`  
`org.apache.derby` | `derby` | `10.16.1.1`  
`org.apache.derby` | `derbyclient` | `10.16.1.1`  
`org.apache.derby` | `derbynet` | `10.16.1.1`  
`org.apache.derby` | `derbyoptionaltools` | `10.16.1.1`  
`org.apache.derby` | `derbyshared` | `10.16.1.1`  
`org.apache.derby` | `derbytools` | `10.16.1.1`  
`org.apache.groovy` | `groovy` | `4.0.26`  
`org.apache.groovy` | `groovy-ant` | `4.0.26`  
`org.apache.groovy` | `groovy-astbuilder` | `4.0.26`  
`org.apache.groovy` | `groovy-cli-commons` | `4.0.26`  
`org.apache.groovy` | `groovy-cli-picocli` | `4.0.26`  
`org.apache.groovy` | `groovy-console` | `4.0.26`  
`org.apache.groovy` | `groovy-contracts` | `4.0.26`  
`org.apache.groovy` | `groovy-datetime` | `4.0.26`  
`org.apache.groovy` | `groovy-dateutil` | `4.0.26`  
`org.apache.groovy` | `groovy-docgenerator` | `4.0.26`  
`org.apache.groovy` | `groovy-ginq` | `4.0.26`  
`org.apache.groovy` | `groovy-groovydoc` | `4.0.26`  
`org.apache.groovy` | `groovy-groovysh` | `4.0.26`  
`org.apache.groovy` | `groovy-jmx` | `4.0.26`  
`org.apache.groovy` | `groovy-json` | `4.0.26`  
`org.apache.groovy` | `groovy-jsr223` | `4.0.26`  
`org.apache.groovy` | `groovy-macro` | `4.0.26`  
`org.apache.groovy` | `groovy-macro-library` | `4.0.26`  
`org.apache.groovy` | `groovy-nio` | `4.0.26`  
`org.apache.groovy` | `groovy-servlet` | `4.0.26`  
`org.apache.groovy` | `groovy-sql` | `4.0.26`  
`org.apache.groovy` | `groovy-swing` | `4.0.26`  
`org.apache.groovy` | `groovy-templates` | `4.0.26`  
`org.apache.groovy` | `groovy-test` | `4.0.26`  
`org.apache.groovy` | `groovy-test-junit5` | `4.0.26`  
`org.apache.groovy` | `groovy-testng` | `4.0.26`  
`org.apache.groovy` | `groovy-toml` | `4.0.26`  
`org.apache.groovy` | `groovy-typecheckers` | `4.0.26`  
`org.apache.groovy` | `groovy-xml` | `4.0.26`  
`org.apache.groovy` | `groovy-yaml` | `4.0.26`  
`org.apache.httpcomponents` | `httpasyncclient` | `4.1.5`  
`org.apache.httpcomponents` | `httpcore` | `4.4.16`  
`org.apache.httpcomponents` | `httpcore-nio` | `4.4.16`  
`org.apache.httpcomponents.client5` | `httpclient5` | `5.4.4`  
`org.apache.httpcomponents.client5` | `httpclient5-cache` | `5.4.4`  
`org.apache.httpcomponents.client5` | `httpclient5-fluent` | `5.4.4`  
`org.apache.httpcomponents.core5` | `httpcore5` | `5.3.4`  
`org.apache.httpcomponents.core5` | `httpcore5-h2` | `5.3.4`  
`org.apache.httpcomponents.core5` | `httpcore5-reactive` | `5.3.4`  
`org.apache.kafka` | `connect` | `3.8.1`  
`org.apache.kafka` | `connect-api` | `3.8.1`  
`org.apache.kafka` | `connect-basic-auth-extension` | `3.8.1`  
`org.apache.kafka` | `connect-file` | `3.8.1`  
`org.apache.kafka` | `connect-json` | `3.8.1`  
`org.apache.kafka` | `connect-mirror` | `3.8.1`  
`org.apache.kafka` | `connect-mirror-client` | `3.8.1`  
`org.apache.kafka` | `connect-runtime` | `3.8.1`  
`org.apache.kafka` | `connect-transforms` | `3.8.1`  
`org.apache.kafka` | `generator` | `3.8.1`  
`org.apache.kafka` | `kafka-clients` | `3.8.1`  
`org.apache.kafka` | `kafka-log4j-appender` | `3.8.1`  
`org.apache.kafka` | `kafka-metadata` | `3.8.1`  
`org.apache.kafka` | `kafka-raft` | `3.8.1`  
`org.apache.kafka` | `kafka-server` | `3.8.1`  
`org.apache.kafka` | `kafka-server-common` | `3.8.1`  
`org.apache.kafka` | `kafka-shell` | `3.8.1`  
`org.apache.kafka` | `kafka-storage` | `3.8.1`  
`org.apache.kafka` | `kafka-storage-api` | `3.8.1`  
`org.apache.kafka` | `kafka-streams` | `3.8.1`  
`org.apache.kafka` | `kafka-streams-scala_2.12` | `3.8.1`  
`org.apache.kafka` | `kafka-streams-scala_2.13` | `3.8.1`  
`org.apache.kafka` | `kafka-streams-test-utils` | `3.8.1`  
`org.apache.kafka` | `kafka-tools` | `3.8.1`  
`org.apache.kafka` | `kafka_2.12` | `3.8.1`  
`org.apache.kafka` | `kafka_2.13` | `3.8.1`  
`org.apache.kafka` | `trogdor` | `3.8.1`  
`org.apache.logging` | `logging-parent` | `11.0.0`  
`org.apache.logging.log4j` | `log4j-1.2-api` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-api` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-api-test` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-appserver` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-cassandra` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-core` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-core-test` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-couchdb` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-docker` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-flume-ng` | `2.23.1`  
`org.apache.logging.log4j` | `log4j-iostreams` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-jakarta-smtp` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-jakarta-web` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-jcl` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-jpa` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-jpl` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-jul` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-layout-template-json` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-mongodb` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-mongodb4` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-slf4j-impl` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-slf4j2-impl` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-spring-boot` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-spring-cloud-config-client` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-taglib` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-to-jul` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-to-slf4j` | `2.24.3`  
`org.apache.logging.log4j` | `log4j-web` | `2.24.3`  
`org.apache.maven.plugin-tools` | `maven-plugin-annotations` | `3.13.1`  
`org.apache.pulsar` | `bouncy-castle-bc` | `3.3.6`  
`org.apache.pulsar` | `bouncy-castle-bcfips` | `3.3.6`  
`org.apache.pulsar` | `bouncy-castle-parent` | `3.3.6`  
`org.apache.pulsar` | `buildtools` | `3.3.6`  
`org.apache.pulsar` | `distribution` | `3.3.6`  
`org.apache.pulsar` | `docker-images` | `3.3.6`  
`org.apache.pulsar` | `jclouds-shaded` | `3.3.6`  
`org.apache.pulsar` | `managed-ledger` | `3.3.6`  
`org.apache.pulsar` | `pulsar` | `3.3.6`  
`org.apache.pulsar` | `pulsar-all-docker-image` | `3.3.6`  
`org.apache.pulsar` | `pulsar-broker` | `3.3.6`  
`org.apache.pulsar` | `pulsar-broker-auth-athenz` | `3.3.6`  
`org.apache.pulsar` | `pulsar-broker-auth-oidc` | `3.3.6`  
`org.apache.pulsar` | `pulsar-broker-auth-sasl` | `3.3.6`  
`org.apache.pulsar` | `pulsar-broker-common` | `3.3.6`  
`org.apache.pulsar` | `pulsar-cli-utils` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-1x` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-1x-base` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-2x-shaded` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-admin` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-admin-api` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-admin-original` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-all` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-api` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-auth-athenz` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-auth-sasl` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-messagecrypto-bc` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-original` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-reactive-adapter` | `0.5.10`  
`org.apache.pulsar` | `pulsar-client-reactive-api` | `0.5.10`  
`org.apache.pulsar` | `pulsar-client-reactive-jackson` | `0.5.10`  
`org.apache.pulsar` | `pulsar-client-reactive-producer-cache-caffeine` | `0.5.10`  
`org.apache.pulsar` | `pulsar-client-reactive-producer-cache-caffeine-shaded` | `0.5.10`  
`org.apache.pulsar` | `pulsar-client-tools` | `3.3.6`  
`org.apache.pulsar` | `pulsar-client-tools-api` | `3.3.6`  
`org.apache.pulsar` | `pulsar-common` | `3.3.6`  
`org.apache.pulsar` | `pulsar-config-validation` | `3.3.6`  
`org.apache.pulsar` | `pulsar-docker-image` | `3.3.6`  
`org.apache.pulsar` | `pulsar-docs-tools` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions-api` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions-api-examples` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions-api-examples-builtin` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions-instance` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions-local-runner` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions-local-runner-original` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions-proto` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions-runtime` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions-runtime-all` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions-secrets` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions-utils` | `3.3.6`  
`org.apache.pulsar` | `pulsar-functions-worker` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-aerospike` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-alluxio` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-aws` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-batch-data-generator` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-batch-discovery-triggerers` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-canal` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-cassandra` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-common` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-core` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-data-generator` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-debezium` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-debezium-core` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-debezium-mongodb` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-debezium-mssql` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-debezium-mysql` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-debezium-oracle` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-debezium-postgres` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-distribution` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-docs` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-dynamodb` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-elastic-search` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-file` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-flume` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-hbase` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-hdfs3` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-http` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-influxdb` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-jdbc` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-jdbc-clickhouse` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-jdbc-core` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-jdbc-mariadb` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-jdbc-openmldb` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-jdbc-postgres` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-jdbc-sqlite` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-kafka` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-kafka-connect-adaptor` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-kafka-connect-adaptor-nar` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-kinesis` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-mongo` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-netty` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-nsq` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-rabbitmq` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-redis` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-solr` | `3.3.6`  
`org.apache.pulsar` | `pulsar-io-twitter` | `3.3.6`  
`org.apache.pulsar` | `pulsar-metadata` | `3.3.6`  
`org.apache.pulsar` | `pulsar-offloader-distribution` | `3.3.6`  
`org.apache.pulsar` | `pulsar-package-bookkeeper-storage` | `3.3.6`  
`org.apache.pulsar` | `pulsar-package-core` | `3.3.6`  
`org.apache.pulsar` | `pulsar-package-filesystem-storage` | `3.3.6`  
`org.apache.pulsar` | `pulsar-package-management` | `3.3.6`  
`org.apache.pulsar` | `pulsar-proxy` | `3.3.6`  
`org.apache.pulsar` | `pulsar-server-distribution` | `3.3.6`  
`org.apache.pulsar` | `pulsar-shell-distribution` | `3.3.6`  
`org.apache.pulsar` | `pulsar-testclient` | `3.3.6`  
`org.apache.pulsar` | `pulsar-transaction-common` | `3.3.6`  
`org.apache.pulsar` | `pulsar-transaction-coordinator` | `3.3.6`  
`org.apache.pulsar` | `pulsar-transaction-parent` | `3.3.6`  
`org.apache.pulsar` | `pulsar-websocket` | `3.3.6`  
`org.apache.pulsar` | `structured-event-log` | `3.3.6`  
`org.apache.pulsar` | `testmocks` | `3.3.6`  
`org.apache.pulsar` | `tiered-storage-file-system` | `3.3.6`  
`org.apache.pulsar` | `tiered-storage-jcloud` | `3.3.6`  
`org.apache.pulsar` | `tiered-storage-parent` | `3.3.6`  
`org.apache.tomcat` | `tomcat-annotations-api` | `10.1.41`  
`org.apache.tomcat` | `tomcat-jdbc` | `10.1.41`  
`org.apache.tomcat` | `tomcat-jsp-api` | `10.1.41`  
`org.apache.tomcat.embed` | `tomcat-embed-core` | `10.1.41`  
`org.apache.tomcat.embed` | `tomcat-embed-el` | `10.1.41`  
`org.apache.tomcat.embed` | `tomcat-embed-jasper` | `10.1.41`  
`org.apache.tomcat.embed` | `tomcat-embed-websocket` | `10.1.41`  
`org.aspectj` | `aspectjrt` | `1.9.24`  
`org.aspectj` | `aspectjtools` | `1.9.24`  
`org.aspectj` | `aspectjweaver` | `1.9.24`  
`org.assertj` | `assertj-core` | `3.26.3`  
`org.assertj` | `assertj-guava` | `3.26.3`  
`org.awaitility` | `awaitility` | `4.2.2`  
`org.awaitility` | `awaitility-groovy` | `4.2.2`  
`org.awaitility` | `awaitility-kotlin` | `4.2.2`  
`org.awaitility` | `awaitility-scala` | `4.2.2`  
`org.cache2k` | `cache2k-api` | `2.6.1.Final`  
`org.cache2k` | `cache2k-config` | `2.6.1.Final`  
`org.cache2k` | `cache2k-core` | `2.6.1.Final`  
`org.cache2k` | `cache2k-jcache` | `2.6.1.Final`  
`org.cache2k` | `cache2k-micrometer` | `2.6.1.Final`  
`org.cache2k` | `cache2k-spring` | `2.6.1.Final`  
`org.codehaus.janino` | `commons-compiler` | `3.1.12`  
`org.codehaus.janino` | `commons-compiler-jdk` | `3.1.12`  
`org.codehaus.janino` | `janino` | `3.1.12`  
`org.crac` | `crac` | `1.5.0`  
`org.eclipse` | `yasson` | `3.0.4`  
`org.eclipse.angus` | `angus-activation` | `2.0.2`  
`org.eclipse.angus` | `angus-core` | `2.0.3`  
`org.eclipse.angus` | `angus-mail` | `2.0.3`  
`org.eclipse.angus` | `dsn` | `2.0.3`  
`org.eclipse.angus` | `gimap` | `2.0.3`  
`org.eclipse.angus` | `imap` | `2.0.3`  
`org.eclipse.angus` | `jakarta.mail` | `2.0.3`  
`org.eclipse.angus` | `logging-mailhandler` | `2.0.3`  
`org.eclipse.angus` | `pop3` | `2.0.3`  
`org.eclipse.angus` | `smtp` | `2.0.3`  
`org.eclipse.jetty` | `jetty-alpn-client` | `12.0.21`  
`org.eclipse.jetty` | `jetty-alpn-conscrypt-client` | `12.0.21`  
`org.eclipse.jetty` | `jetty-alpn-conscrypt-server` | `12.0.21`  
`org.eclipse.jetty` | `jetty-alpn-java-client` | `12.0.21`  
`org.eclipse.jetty` | `jetty-alpn-java-server` | `12.0.21`  
`org.eclipse.jetty` | `jetty-alpn-server` | `12.0.21`  
`org.eclipse.jetty` | `jetty-client` | `12.0.21`  
`org.eclipse.jetty` | `jetty-deploy` | `12.0.21`  
`org.eclipse.jetty` | `jetty-ee` | `12.0.21`  
`org.eclipse.jetty` | `jetty-http` | `12.0.21`  
`org.eclipse.jetty` | `jetty-http-spi` | `12.0.21`  
`org.eclipse.jetty` | `jetty-http-tools` | `12.0.21`  
`org.eclipse.jetty` | `jetty-io` | `12.0.21`  
`org.eclipse.jetty` | `jetty-jmx` | `12.0.21`  
`org.eclipse.jetty` | `jetty-jndi` | `12.0.21`  
`org.eclipse.jetty` | `jetty-keystore` | `12.0.21`  
`org.eclipse.jetty` | `jetty-openid` | `12.0.21`  
`org.eclipse.jetty` | `jetty-osgi` | `12.0.21`  
`org.eclipse.jetty` | `jetty-plus` | `12.0.21`  
`org.eclipse.jetty` | `jetty-proxy` | `12.0.21`  
`org.eclipse.jetty` | `jetty-reactive-httpclient` | `4.0.9`  
`org.eclipse.jetty` | `jetty-rewrite` | `12.0.21`  
`org.eclipse.jetty` | `jetty-security` | `12.0.21`  
`org.eclipse.jetty` | `jetty-server` | `12.0.21`  
`org.eclipse.jetty` | `jetty-session` | `12.0.21`  
`org.eclipse.jetty` | `jetty-slf4j-impl` | `12.0.21`  
`org.eclipse.jetty` | `jetty-start` | `12.0.21`  
`org.eclipse.jetty` | `jetty-unixdomain-server` | `12.0.21`  
`org.eclipse.jetty` | `jetty-util` | `12.0.21`  
`org.eclipse.jetty` | `jetty-util-ajax` | `12.0.21`  
`org.eclipse.jetty` | `jetty-xml` | `12.0.21`  
`org.eclipse.jetty.demos` | `jetty-demo-handler` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-annotations` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-apache-jsp` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-cdi` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-fcgi-proxy` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-glassfish-jstl` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-jaspi` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-jndi` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-jspc-maven-plugin` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-maven-plugin` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-plus` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-proxy` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-quickstart` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-runner` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-servlet` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-servlets` | `12.0.21`  
`org.eclipse.jetty.ee10` | `jetty-ee10-webapp` | `12.0.21`  
`org.eclipse.jetty.ee10.osgi` | `jetty-ee10-osgi-alpn` | `12.0.21`  
`org.eclipse.jetty.ee10.osgi` | `jetty-ee10-osgi-boot` | `12.0.21`  
`org.eclipse.jetty.ee10.osgi` | `jetty-ee10-osgi-boot-jsp` | `12.0.21`  
`org.eclipse.jetty.ee10.websocket` | `jetty-ee10-websocket-jakarta-client` | `12.0.21`  
`org.eclipse.jetty.ee10.websocket` | `jetty-ee10-websocket-jakarta-client-webapp` | `12.0.21`  
`org.eclipse.jetty.ee10.websocket` | `jetty-ee10-websocket-jakarta-common` | `12.0.21`  
`org.eclipse.jetty.ee10.websocket` | `jetty-ee10-websocket-jakarta-server` | `12.0.21`  
`org.eclipse.jetty.ee10.websocket` | `jetty-ee10-websocket-jetty-client-webapp` | `12.0.21`  
`org.eclipse.jetty.ee10.websocket` | `jetty-ee10-websocket-jetty-server` | `12.0.21`  
`org.eclipse.jetty.ee10.websocket` | `jetty-ee10-websocket-servlet` | `12.0.21`  
`org.eclipse.jetty.fcgi` | `jetty-fcgi-client` | `12.0.21`  
`org.eclipse.jetty.fcgi` | `jetty-fcgi-proxy` | `12.0.21`  
`org.eclipse.jetty.fcgi` | `jetty-fcgi-server` | `12.0.21`  
`org.eclipse.jetty.http2` | `jetty-http2-client` | `12.0.21`  
`org.eclipse.jetty.http2` | `jetty-http2-client-transport` | `12.0.21`  
`org.eclipse.jetty.http2` | `jetty-http2-common` | `12.0.21`  
`org.eclipse.jetty.http2` | `jetty-http2-hpack` | `12.0.21`  
`org.eclipse.jetty.http2` | `jetty-http2-server` | `12.0.21`  
`org.eclipse.jetty.http3` | `jetty-http3-client` | `12.0.21`  
`org.eclipse.jetty.http3` | `jetty-http3-client-transport` | `12.0.21`  
`org.eclipse.jetty.http3` | `jetty-http3-common` | `12.0.21`  
`org.eclipse.jetty.http3` | `jetty-http3-qpack` | `12.0.21`  
`org.eclipse.jetty.http3` | `jetty-http3-server` | `12.0.21`  
`org.eclipse.jetty.quic` | `jetty-quic-client` | `12.0.21`  
`org.eclipse.jetty.quic` | `jetty-quic-common` | `12.0.21`  
`org.eclipse.jetty.quic` | `jetty-quic-quiche-common` | `12.0.21`  
`org.eclipse.jetty.quic` | `jetty-quic-quiche-foreign` | `12.0.21`  
`org.eclipse.jetty.quic` | `jetty-quic-quiche-jna` | `12.0.21`  
`org.eclipse.jetty.quic` | `jetty-quic-server` | `12.0.21`  
`org.eclipse.jetty.websocket` | `jetty-websocket-core-client` | `12.0.21`  
`org.eclipse.jetty.websocket` | `jetty-websocket-core-common` | `12.0.21`  
`org.eclipse.jetty.websocket` | `jetty-websocket-core-server` | `12.0.21`  
`org.eclipse.jetty.websocket` | `jetty-websocket-jetty-api` | `12.0.21`  
`org.eclipse.jetty.websocket` | `jetty-websocket-jetty-client` | `12.0.21`  
`org.eclipse.jetty.websocket` | `jetty-websocket-jetty-common` | `12.0.21`  
`org.eclipse.jetty.websocket` | `jetty-websocket-jetty-server` | `12.0.21`  
`org.ehcache` | `ehcache` | `3.10.8`  
`org.ehcache` | `ehcache-clustered` | `3.10.8`  
`org.ehcache` | `ehcache-transactions` | `3.10.8`  
`org.elasticsearch.client` | `elasticsearch-rest-client` | `8.15.5`  
`org.elasticsearch.client` | `elasticsearch-rest-client-sniffer` | `8.15.5`  
`org.firebirdsql.jdbc` | `jaybird` | `5.0.7.java11`  
`org.flywaydb` | `flyway-commandline` | `10.20.1`  
`org.flywaydb` | `flyway-core` | `10.20.1`  
`org.flywaydb` | `flyway-database-cassandra` | `10.20.1`  
`org.flywaydb` | `flyway-database-db2` | `10.20.1`  
`org.flywaydb` | `flyway-database-derby` | `10.20.1`  
`org.flywaydb` | `flyway-database-hsqldb` | `10.20.1`  
`org.flywaydb` | `flyway-database-informix` | `10.20.1`  
`org.flywaydb` | `flyway-database-mongodb` | `10.20.1`  
`org.flywaydb` | `flyway-database-oracle` | `10.20.1`  
`org.flywaydb` | `flyway-database-postgresql` | `10.20.1`  
`org.flywaydb` | `flyway-database-redshift` | `10.20.1`  
`org.flywaydb` | `flyway-database-saphana` | `10.20.1`  
`org.flywaydb` | `flyway-database-snowflake` | `10.20.1`  
`org.flywaydb` | `flyway-database-sybasease` | `10.20.1`  
`org.flywaydb` | `flyway-firebird` | `10.20.1`  
`org.flywaydb` | `flyway-gcp-bigquery` | `10.20.1`  
`org.flywaydb` | `flyway-gcp-spanner` | `10.20.1`  
`org.flywaydb` | `flyway-mysql` | `10.20.1`  
`org.flywaydb` | `flyway-singlestore` | `10.20.1`  
`org.flywaydb` | `flyway-sqlserver` | `10.20.1`  
`org.freemarker` | `freemarker` | `2.3.34`  
`org.glassfish.jaxb` | `codemodel` | `4.0.5`  
`org.glassfish.jaxb` | `jaxb-core` | `4.0.5`  
`org.glassfish.jaxb` | `jaxb-jxc` | `4.0.5`  
`org.glassfish.jaxb` | `jaxb-runtime` | `4.0.5`  
`org.glassfish.jaxb` | `jaxb-xjc` | `4.0.5`  
`org.glassfish.jaxb` | `txw2` | `4.0.5`  
`org.glassfish.jaxb` | `xsom` | `4.0.5`  
`org.glassfish.jersey.bundles` | `jaxrs-ri` | `3.1.10`  
`org.glassfish.jersey.connectors` | `jersey-apache-connector` | `3.1.10`  
`org.glassfish.jersey.connectors` | `jersey-apache5-connector` | `3.1.10`  
`org.glassfish.jersey.connectors` | `jersey-grizzly-connector` | `3.1.10`  
`org.glassfish.jersey.connectors` | `jersey-helidon-connector` | `3.1.10`  
`org.glassfish.jersey.connectors` | `jersey-jdk-connector` | `3.1.10`  
`org.glassfish.jersey.connectors` | `jersey-jetty-connector` | `3.1.10`  
`org.glassfish.jersey.connectors` | `jersey-jetty-http2-connector` | `3.1.10`  
`org.glassfish.jersey.connectors` | `jersey-jetty11-connector` | `3.1.10`  
`org.glassfish.jersey.connectors` | `jersey-jnh-connector` | `3.1.10`  
`org.glassfish.jersey.connectors` | `jersey-netty-connector` | `3.1.10`  
`org.glassfish.jersey.containers` | `jersey-container-grizzly2-http` | `3.1.10`  
`org.glassfish.jersey.containers` | `jersey-container-grizzly2-servlet` | `3.1.10`  
`org.glassfish.jersey.containers` | `jersey-container-jdk-http` | `3.1.10`  
`org.glassfish.jersey.containers` | `jersey-container-jetty-http` | `3.1.10`  
`org.glassfish.jersey.containers` | `jersey-container-jetty-http2` | `3.1.10`  
`org.glassfish.jersey.containers` | `jersey-container-jetty-servlet` | `3.1.10`  
`org.glassfish.jersey.containers` | `jersey-container-jetty11-http` | `3.1.10`  
`org.glassfish.jersey.containers` | `jersey-container-netty-http` | `3.1.10`  
`org.glassfish.jersey.containers` | `jersey-container-servlet` | `3.1.10`  
`org.glassfish.jersey.containers` | `jersey-container-servlet-core` | `3.1.10`  
`org.glassfish.jersey.containers` | `jersey-container-simple-http` | `3.1.10`  
`org.glassfish.jersey.containers.glassfish` | `jersey-gf-ejb` | `3.1.10`  
`org.glassfish.jersey.core` | `jersey-client` | `3.1.10`  
`org.glassfish.jersey.core` | `jersey-common` | `3.1.10`  
`org.glassfish.jersey.core` | `jersey-server` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-bean-validation` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-declarative-linking` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-entity-filtering` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-metainf-services` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-micrometer` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-mvc` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-mvc-bean-validation` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-mvc-freemarker` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-mvc-jsp` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-mvc-mustache` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-proxy-client` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-spring6` | `3.1.10`  
`org.glassfish.jersey.ext` | `jersey-wadl-doclet` | `3.1.10`  
`org.glassfish.jersey.ext.cdi` | `jersey-cdi-rs-inject` | `3.1.10`  
`org.glassfish.jersey.ext.cdi` | `jersey-cdi1x` | `3.1.10`  
`org.glassfish.jersey.ext.cdi` | `jersey-cdi1x-ban-custom-hk2-binding` | `3.1.10`  
`org.glassfish.jersey.ext.cdi` | `jersey-cdi1x-servlet` | `3.1.10`  
`org.glassfish.jersey.ext.cdi` | `jersey-cdi1x-transaction` | `3.1.10`  
`org.glassfish.jersey.ext.cdi` | `jersey-cdi1x-validation` | `3.1.10`  
`org.glassfish.jersey.ext.cdi` | `jersey-weld2-se` | `3.1.10`  
`org.glassfish.jersey.ext.microprofile` | `jersey-mp-config` | `3.1.10`  
`org.glassfish.jersey.ext.microprofile` | `jersey-mp-rest-client` | `3.1.10`  
`org.glassfish.jersey.ext.rx` | `jersey-rx-client-guava` | `3.1.10`  
`org.glassfish.jersey.ext.rx` | `jersey-rx-client-rxjava` | `3.1.10`  
`org.glassfish.jersey.ext.rx` | `jersey-rx-client-rxjava2` | `3.1.10`  
`org.glassfish.jersey.inject` | `jersey-cdi2-se` | `3.1.10`  
`org.glassfish.jersey.inject` | `jersey-hk2` | `3.1.10`  
`org.glassfish.jersey.media` | `jersey-media-jaxb` | `3.1.10`  
`org.glassfish.jersey.media` | `jersey-media-json-binding` | `3.1.10`  
`org.glassfish.jersey.media` | `jersey-media-json-gson` | `3.1.10`  
`org.glassfish.jersey.media` | `jersey-media-json-jackson` | `3.1.10`  
`org.glassfish.jersey.media` | `jersey-media-json-jettison` | `3.1.10`  
`org.glassfish.jersey.media` | `jersey-media-json-processing` | `3.1.10`  
`org.glassfish.jersey.media` | `jersey-media-kryo` | `3.1.10`  
`org.glassfish.jersey.media` | `jersey-media-moxy` | `3.1.10`  
`org.glassfish.jersey.media` | `jersey-media-multipart` | `3.1.10`  
`org.glassfish.jersey.media` | `jersey-media-sse` | `3.1.10`  
`org.glassfish.jersey.security` | `oauth1-client` | `3.1.10`  
`org.glassfish.jersey.security` | `oauth1-server` | `3.1.10`  
`org.glassfish.jersey.security` | `oauth1-signature` | `3.1.10`  
`org.glassfish.jersey.security` | `oauth2-client` | `3.1.10`  
`org.glassfish.jersey.test-framework` | `jersey-test-framework-core` | `3.1.10`  
`org.glassfish.jersey.test-framework` | `jersey-test-framework-util` | `3.1.10`  
`org.glassfish.jersey.test-framework.providers` | `jersey-test-framework-provider-bundle` | `3.1.10`  
`org.glassfish.jersey.test-framework.providers` | `jersey-test-framework-provider-external` | `3.1.10`  
`org.glassfish.jersey.test-framework.providers` | `jersey-test-framework-provider-grizzly2` | `3.1.10`  
`org.glassfish.jersey.test-framework.providers` | `jersey-test-framework-provider-inmemory` | `3.1.10`  
`org.glassfish.jersey.test-framework.providers` | `jersey-test-framework-provider-jdk-http` | `3.1.10`  
`org.glassfish.jersey.test-framework.providers` | `jersey-test-framework-provider-jetty` | `3.1.10`  
`org.glassfish.jersey.test-framework.providers` | `jersey-test-framework-provider-jetty-http2` | `3.1.10`  
`org.glassfish.jersey.test-framework.providers` | `jersey-test-framework-provider-netty` | `3.1.10`  
`org.glassfish.jersey.test-framework.providers` | `jersey-test-framework-provider-simple` | `3.1.10`  
`org.glassfish.web` | `jakarta.servlet.jsp.jstl` | `3.0.1`  
`org.hamcrest` | `hamcrest` | `2.2`  
`org.hamcrest` | `hamcrest-core` | `2.2`  
`org.hamcrest` | `hamcrest-library` | `2.2`  
`org.hibernate.orm` | `hibernate-agroal` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-ant` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-c3p0` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-community-dialects` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-core` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-envers` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-graalvm` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-hikaricp` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-jcache` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-jpamodelgen` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-micrometer` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-proxool` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-spatial` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-testing` | `6.6.15.Final`  
`org.hibernate.orm` | `hibernate-vibur` | `6.6.15.Final`  
`org.hibernate.validator` | `hibernate-validator` | `8.0.2.Final`  
`org.hibernate.validator` | `hibernate-validator-annotation-processor` | `8.0.2.Final`  
`org.hsqldb` | `hsqldb` | `2.7.3`  
`org.htmlunit` | `htmlunit` | `4.5.0`  
`org.infinispan` | `infinispan-anchored-keys` | `15.0.14.Final`  
`org.infinispan` | `infinispan-api` | `15.0.14.Final`  
`org.infinispan` | `infinispan-cachestore-jdbc` | `15.0.14.Final`  
`org.infinispan` | `infinispan-cachestore-jdbc-common` | `15.0.14.Final`  
`org.infinispan` | `infinispan-cachestore-remote` | `15.0.14.Final`  
`org.infinispan` | `infinispan-cachestore-rocksdb` | `15.0.14.Final`  
`org.infinispan` | `infinispan-cachestore-sql` | `15.0.14.Final`  
`org.infinispan` | `infinispan-cdi-common` | `15.0.14.Final`  
`org.infinispan` | `infinispan-cdi-embedded` | `15.0.14.Final`  
`org.infinispan` | `infinispan-cdi-remote` | `15.0.14.Final`  
`org.infinispan` | `infinispan-checkstyle` | `15.0.14.Final`  
`org.infinispan` | `infinispan-cli-client` | `15.0.14.Final`  
`org.infinispan` | `infinispan-client-hotrod` | `15.0.14.Final`  
`org.infinispan` | `infinispan-client-rest` | `15.0.14.Final`  
`org.infinispan` | `infinispan-clustered-counter` | `15.0.14.Final`  
`org.infinispan` | `infinispan-clustered-lock` | `15.0.14.Final`  
`org.infinispan` | `infinispan-commons` | `15.0.14.Final`  
`org.infinispan` | `infinispan-commons-graalvm` | `15.0.14.Final`  
`org.infinispan` | `infinispan-commons-test` | `15.0.14.Final`  
`org.infinispan` | `infinispan-component-annotations` | `15.0.14.Final`  
`org.infinispan` | `infinispan-component-processor` | `15.0.14.Final`  
`org.infinispan` | `infinispan-console` | `15.0.11.Final`  
`org.infinispan` | `infinispan-core` | `15.0.14.Final`  
`org.infinispan` | `infinispan-core-graalvm` | `15.0.14.Final`  
`org.infinispan` | `infinispan-hibernate-cache-commons` | `15.0.14.Final`  
`org.infinispan` | `infinispan-hibernate-cache-spi` | `15.0.14.Final`  
`org.infinispan` | `infinispan-hibernate-cache-v62` | `15.0.14.Final`  
`org.infinispan` | `infinispan-hotrod` | `15.0.14.Final`  
`org.infinispan` | `infinispan-jboss-marshalling` | `15.0.14.Final`  
`org.infinispan` | `infinispan-jcache` | `15.0.14.Final`  
`org.infinispan` | `infinispan-jcache-commons` | `15.0.14.Final`  
`org.infinispan` | `infinispan-jcache-remote` | `15.0.14.Final`  
`org.infinispan` | `infinispan-key-value-store-client` | `15.0.14.Final`  
`org.infinispan` | `infinispan-logging-annotations` | `15.0.14.Final`  
`org.infinispan` | `infinispan-logging-processor` | `15.0.14.Final`  
`org.infinispan` | `infinispan-multimap` | `15.0.14.Final`  
`org.infinispan` | `infinispan-objectfilter` | `15.0.14.Final`  
`org.infinispan` | `infinispan-query` | `15.0.14.Final`  
`org.infinispan` | `infinispan-query-core` | `15.0.14.Final`  
`org.infinispan` | `infinispan-query-dsl` | `15.0.14.Final`  
`org.infinispan` | `infinispan-remote-query-client` | `15.0.14.Final`  
`org.infinispan` | `infinispan-remote-query-server` | `15.0.14.Final`  
`org.infinispan` | `infinispan-scripting` | `15.0.14.Final`  
`org.infinispan` | `infinispan-server-core` | `15.0.14.Final`  
`org.infinispan` | `infinispan-server-hotrod` | `15.0.14.Final`  
`org.infinispan` | `infinispan-server-memcached` | `15.0.14.Final`  
`org.infinispan` | `infinispan-server-resp` | `15.0.14.Final`  
`org.infinispan` | `infinispan-server-rest` | `15.0.14.Final`  
`org.infinispan` | `infinispan-server-router` | `15.0.14.Final`  
`org.infinispan` | `infinispan-server-runtime` | `15.0.14.Final`  
`org.infinispan` | `infinispan-server-testdriver-core` | `15.0.14.Final`  
`org.infinispan` | `infinispan-server-testdriver-junit4` | `15.0.14.Final`  
`org.infinispan` | `infinispan-server-testdriver-junit5` | `15.0.14.Final`  
`org.infinispan` | `infinispan-spring-boot3-starter-embedded` | `15.0.14.Final`  
`org.infinispan` | `infinispan-spring-boot3-starter-remote` | `15.0.14.Final`  
`org.infinispan` | `infinispan-spring6-common` | `15.0.14.Final`  
`org.infinispan` | `infinispan-spring6-embedded` | `15.0.14.Final`  
`org.infinispan` | `infinispan-spring6-remote` | `15.0.14.Final`  
`org.infinispan` | `infinispan-tasks` | `15.0.14.Final`  
`org.infinispan` | `infinispan-tasks-api` | `15.0.14.Final`  
`org.infinispan` | `infinispan-tools` | `15.0.14.Final`  
`org.infinispan.protostream` | `protostream` | `5.0.13.Final`  
`org.infinispan.protostream` | `protostream-processor` | `5.0.13.Final`  
`org.infinispan.protostream` | `protostream-types` | `5.0.13.Final`  
`org.influxdb` | `influxdb-java` | `2.24`  
`org.jboss.logging` | `jboss-logging` | `3.6.1.Final`  
`org.jdom` | `jdom2` | `*******`  
`org.jetbrains.kotlin` | `kotlin-compiler` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-compiler-embeddable` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-daemon-client` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-main-kts` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-osgi-bundle` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-reflect` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-script-runtime` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-scripting-common` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-scripting-ide-services` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-scripting-jvm` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-scripting-jvm-host` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-stdlib` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-stdlib-common` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-stdlib-jdk7` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-stdlib-jdk8` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-stdlib-js` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-test` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-test-annotations-common` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-test-common` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-test-js` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-test-junit` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-test-junit5` | `1.9.25`  
`org.jetbrains.kotlin` | `kotlin-test-testng` | `1.9.25`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-android` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-core` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-core-jvm` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-debug` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-guava` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-javafx` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-jdk8` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-jdk9` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-play-services` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-reactive` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-reactor` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-rx2` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-rx3` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-slf4j` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-swing` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-test` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-coroutines-test-jvm` | `1.8.1`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-cbor` | `1.6.3`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-cbor-jvm` | `1.6.3`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-core` | `1.6.3`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-core-jvm` | `1.6.3`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-hocon` | `1.6.3`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-json` | `1.6.3`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-json-jvm` | `1.6.3`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-json-okio` | `1.6.3`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-json-okio-jvm` | `1.6.3`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-properties` | `1.6.3`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-properties-jvm` | `1.6.3`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-protobuf` | `1.6.3`  
`org.jetbrains.kotlinx` | `kotlinx-serialization-protobuf-jvm` | `1.6.3`  
`org.jooq` | `jooq` | `3.19.23`  
`org.jooq` | `jooq-codegen` | `3.19.23`  
`org.jooq` | `jooq-kotlin` | `3.19.23`  
`org.jooq` | `jooq-meta` | `3.19.23`  
`org.jspecify` | `jspecify` | `1.0.0`  
`org.junit.jupiter` | `junit-jupiter` | `5.11.4`  
`org.junit.jupiter` | `junit-jupiter-api` | `5.11.4`  
`org.junit.jupiter` | `junit-jupiter-engine` | `5.11.4`  
`org.junit.jupiter` | `junit-jupiter-migrationsupport` | `5.11.4`  
`org.junit.jupiter` | `junit-jupiter-params` | `5.11.4`  
`org.junit.platform` | `junit-platform-commons` | `1.11.4`  
`org.junit.platform` | `junit-platform-console` | `1.11.4`  
`org.junit.platform` | `junit-platform-engine` | `1.11.4`  
`org.junit.platform` | `junit-platform-jfr` | `1.11.4`  
`org.junit.platform` | `junit-platform-launcher` | `1.11.4`  
`org.junit.platform` | `junit-platform-reporting` | `1.11.4`  
`org.junit.platform` | `junit-platform-runner` | `1.11.4`  
`org.junit.platform` | `junit-platform-suite` | `1.11.4`  
`org.junit.platform` | `junit-platform-suite-api` | `1.11.4`  
`org.junit.platform` | `junit-platform-suite-commons` | `1.11.4`  
`org.junit.platform` | `junit-platform-suite-engine` | `1.11.4`  
`org.junit.platform` | `junit-platform-testkit` | `1.11.4`  
`org.junit.vintage` | `junit-vintage-engine` | `5.11.4`  
`org.jvnet.staxex` | `stax-ex` | `2.1.0`  
`org.liquibase` | `liquibase-cdi` | `4.29.2`  
`org.liquibase` | `liquibase-core` | `4.29.2`  
`org.mariadb` | `r2dbc-mariadb` | `1.2.2`  
`org.mariadb.jdbc` | `mariadb-java-client` | `3.4.2`  
`org.messaginghub` | `pooled-jms` | `3.1.7`  
`org.mockito` | `mockito-android` | `5.14.2`  
`org.mockito` | `mockito-core` | `5.14.2`  
`org.mockito` | `mockito-errorprone` | `5.14.2`  
`org.mockito` | `mockito-junit-jupiter` | `5.14.2`  
`org.mockito` | `mockito-proxy` | `5.14.2`  
`org.mockito` | `mockito-subclass` | `5.14.2`  
`org.mongodb` | `bson` | `5.2.1`  
`org.mongodb` | `bson-kotlin` | `5.2.1`  
`org.mongodb` | `bson-record-codec` | `5.2.1`  
`org.mongodb` | `mongodb-driver-core` | `5.2.1`  
`org.mongodb` | `mongodb-driver-kotlin-coroutine` | `5.2.1`  
`org.mongodb` | `mongodb-driver-legacy` | `5.2.1`  
`org.mongodb` | `mongodb-driver-reactivestreams` | `5.2.1`  
`org.mongodb` | `mongodb-driver-sync` | `5.2.1`  
`org.neo4j.driver` | `neo4j-java-driver` | `5.28.5`  
`org.osgi` | `org.osgi.annotation.bundle` | `2.0.0`  
`org.osgi` | `org.osgi.annotation.versioning` | `1.1.2`  
`org.osgi` | `osgi.annotation` | `8.1.0`  
`org.postgresql` | `postgresql` | `42.7.5`  
`org.postgresql` | `r2dbc-postgresql` | `1.0.7.RELEASE`  
`org.projectlombok` | `lombok` | `1.18.38`  
`org.quartz-scheduler` | `quartz` | `2.3.2`  
`org.quartz-scheduler` | `quartz-jobs` | `2.3.2`  
`org.reactivestreams` | `reactive-streams` | `1.0.4`  
`org.seleniumhq.selenium` | `htmlunit3-driver` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-api` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-chrome-driver` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-chromium-driver` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-devtools-v127` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-devtools-v128` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-devtools-v129` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-devtools-v85` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-edge-driver` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-firefox-driver` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-grid` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-http` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-ie-driver` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-java` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-json` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-manager` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-remote-driver` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-safari-driver` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-session-map-jdbc` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-session-map-redis` | `4.25.0`  
`org.seleniumhq.selenium` | `selenium-support` | `4.25.0`  
`org.skyscreamer` | `jsonassert` | `1.5.3`  
`org.slf4j` | `jcl-over-slf4j` | `2.0.17`  
`org.slf4j` | `jul-to-slf4j` | `2.0.17`  
`org.slf4j` | `log4j-over-slf4j` | `2.0.17`  
`org.slf4j` | `slf4j-api` | `2.0.17`  
`org.slf4j` | `slf4j-ext` | `2.0.17`  
`org.slf4j` | `slf4j-jdk-platform-logging` | `2.0.17`  
`org.slf4j` | `slf4j-jdk14` | `2.0.17`  
`org.slf4j` | `slf4j-log4j12` | `2.0.17`  
`org.slf4j` | `slf4j-nop` | `2.0.17`  
`org.slf4j` | `slf4j-reload4j` | `2.0.17`  
`org.slf4j` | `slf4j-simple` | `2.0.17`  
`org.springframework` | `spring-aop` | `6.2.7`  
`org.springframework` | `spring-aspects` | `6.2.7`  
`org.springframework` | `spring-beans` | `6.2.7`  
`org.springframework` | `spring-context` | `6.2.7`  
`org.springframework` | `spring-context-indexer` | `6.2.7`  
`org.springframework` | `spring-context-support` | `6.2.7`  
`org.springframework` | `spring-core` | `6.2.7`  
`org.springframework` | `spring-core-test` | `6.2.7`  
`org.springframework` | `spring-expression` | `6.2.7`  
`org.springframework` | `spring-instrument` | `6.2.7`  
`org.springframework` | `spring-jcl` | `6.2.7`  
`org.springframework` | `spring-jdbc` | `6.2.7`  
`org.springframework` | `spring-jms` | `6.2.7`  
`org.springframework` | `spring-messaging` | `6.2.7`  
`org.springframework` | `spring-orm` | `6.2.7`  
`org.springframework` | `spring-oxm` | `6.2.7`  
`org.springframework` | `spring-r2dbc` | `6.2.7`  
`org.springframework` | `spring-test` | `6.2.7`  
`org.springframework` | `spring-tx` | `6.2.7`  
`org.springframework` | `spring-web` | `6.2.7`  
`org.springframework` | `spring-webflux` | `6.2.7`  
`org.springframework` | `spring-webmvc` | `6.2.7`  
`org.springframework` | `spring-websocket` | `6.2.7`  
`org.springframework.amqp` | `spring-amqp` | `3.2.5`  
`org.springframework.amqp` | `spring-rabbit` | `3.2.5`  
`org.springframework.amqp` | `spring-rabbit-junit` | `3.2.5`  
`org.springframework.amqp` | `spring-rabbit-stream` | `3.2.5`  
`org.springframework.amqp` | `spring-rabbit-test` | `3.2.5`  
`org.springframework.batch` | `spring-batch-core` | `5.2.2`  
`org.springframework.batch` | `spring-batch-infrastructure` | `5.2.2`  
`org.springframework.batch` | `spring-batch-integration` | `5.2.2`  
`org.springframework.batch` | `spring-batch-test` | `5.2.2`  
`org.springframework.boot` | `spring-boot` | `3.4.6`  
`org.springframework.boot` | `spring-boot-actuator` | `3.4.6`  
`org.springframework.boot` | `spring-boot-actuator-autoconfigure` | `3.4.6`  
`org.springframework.boot` | `spring-boot-autoconfigure` | `3.4.6`  
`org.springframework.boot` | `spring-boot-autoconfigure-processor` | `3.4.6`  
`org.springframework.boot` | `spring-boot-buildpack-platform` | `3.4.6`  
`org.springframework.boot` | `spring-boot-configuration-metadata` | `3.4.6`  
`org.springframework.boot` | `spring-boot-configuration-processor` | `3.4.6`  
`org.springframework.boot` | `spring-boot-devtools` | `3.4.6`  
`org.springframework.boot` | `spring-boot-docker-compose` | `3.4.6`  
`org.springframework.boot` | `spring-boot-jarmode-tools` | `3.4.6`  
`org.springframework.boot` | `spring-boot-loader` | `3.4.6`  
`org.springframework.boot` | `spring-boot-loader-classic` | `3.4.6`  
`org.springframework.boot` | `spring-boot-loader-tools` | `3.4.6`  
`org.springframework.boot` | `spring-boot-properties-migrator` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-activemq` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-actuator` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-amqp` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-aop` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-artemis` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-batch` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-cache` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-cassandra` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-cassandra-reactive` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-couchbase` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-couchbase-reactive` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-elasticsearch` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-jdbc` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-jpa` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-ldap` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-mongodb` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-mongodb-reactive` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-neo4j` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-r2dbc` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-redis` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-redis-reactive` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-data-rest` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-freemarker` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-graphql` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-groovy-templates` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-hateoas` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-integration` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-jdbc` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-jersey` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-jetty` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-jooq` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-json` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-log4j2` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-logging` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-mail` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-mustache` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-oauth2-authorization-server` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-oauth2-client` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-oauth2-resource-server` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-pulsar` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-pulsar-reactive` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-quartz` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-reactor-netty` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-rsocket` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-security` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-test` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-thymeleaf` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-tomcat` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-undertow` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-validation` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-web` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-web-services` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-webflux` | `3.4.6`  
`org.springframework.boot` | `spring-boot-starter-websocket` | `3.4.6`  
`org.springframework.boot` | `spring-boot-test` | `3.4.6`  
`org.springframework.boot` | `spring-boot-test-autoconfigure` | `3.4.6`  
`org.springframework.boot` | `spring-boot-testcontainers` | `3.4.6`  
`org.springframework.data` | `spring-data-cassandra` | `4.4.6`  
`org.springframework.data` | `spring-data-commons` | `3.4.6`  
`org.springframework.data` | `spring-data-couchbase` | `5.4.6`  
`org.springframework.data` | `spring-data-elasticsearch` | `5.4.6`  
`org.springframework.data` | `spring-data-envers` | `3.4.6`  
`org.springframework.data` | `spring-data-jdbc` | `3.4.6`  
`org.springframework.data` | `spring-data-jpa` | `3.4.6`  
`org.springframework.data` | `spring-data-keyvalue` | `3.4.6`  
`org.springframework.data` | `spring-data-ldap` | `3.4.6`  
`org.springframework.data` | `spring-data-mongodb` | `4.4.6`  
`org.springframework.data` | `spring-data-neo4j` | `7.4.6`  
`org.springframework.data` | `spring-data-r2dbc` | `3.4.6`  
`org.springframework.data` | `spring-data-redis` | `3.4.6`  
`org.springframework.data` | `spring-data-relational` | `3.4.6`  
`org.springframework.data` | `spring-data-rest-core` | `4.4.6`  
`org.springframework.data` | `spring-data-rest-hal-explorer` | `4.4.6`  
`org.springframework.data` | `spring-data-rest-webmvc` | `4.4.6`  
`org.springframework.graphql` | `spring-graphql` | `1.3.5`  
`org.springframework.graphql` | `spring-graphql-test` | `1.3.5`  
`org.springframework.hateoas` | `spring-hateoas` | `2.4.1`  
`org.springframework.integration` | `spring-integration-amqp` | `6.4.5`  
`org.springframework.integration` | `spring-integration-camel` | `6.4.5`  
`org.springframework.integration` | `spring-integration-cassandra` | `6.4.5`  
`org.springframework.integration` | `spring-integration-core` | `6.4.5`  
`org.springframework.integration` | `spring-integration-debezium` | `6.4.5`  
`org.springframework.integration` | `spring-integration-event` | `6.4.5`  
`org.springframework.integration` | `spring-integration-feed` | `6.4.5`  
`org.springframework.integration` | `spring-integration-file` | `6.4.5`  
`org.springframework.integration` | `spring-integration-ftp` | `6.4.5`  
`org.springframework.integration` | `spring-integration-graphql` | `6.4.5`  
`org.springframework.integration` | `spring-integration-groovy` | `6.4.5`  
`org.springframework.integration` | `spring-integration-hazelcast` | `6.4.5`  
`org.springframework.integration` | `spring-integration-http` | `6.4.5`  
`org.springframework.integration` | `spring-integration-ip` | `6.4.5`  
`org.springframework.integration` | `spring-integration-jdbc` | `6.4.5`  
`org.springframework.integration` | `spring-integration-jms` | `6.4.5`  
`org.springframework.integration` | `spring-integration-jmx` | `6.4.5`  
`org.springframework.integration` | `spring-integration-jpa` | `6.4.5`  
`org.springframework.integration` | `spring-integration-kafka` | `6.4.5`  
`org.springframework.integration` | `spring-integration-mail` | `6.4.5`  
`org.springframework.integration` | `spring-integration-mongodb` | `6.4.5`  
`org.springframework.integration` | `spring-integration-mqtt` | `6.4.5`  
`org.springframework.integration` | `spring-integration-r2dbc` | `6.4.5`  
`org.springframework.integration` | `spring-integration-redis` | `6.4.5`  
`org.springframework.integration` | `spring-integration-rsocket` | `6.4.5`  
`org.springframework.integration` | `spring-integration-scripting` | `6.4.5`  
`org.springframework.integration` | `spring-integration-sftp` | `6.4.5`  
`org.springframework.integration` | `spring-integration-smb` | `6.4.5`  
`org.springframework.integration` | `spring-integration-stomp` | `6.4.5`  
`org.springframework.integration` | `spring-integration-stream` | `6.4.5`  
`org.springframework.integration` | `spring-integration-syslog` | `6.4.5`  
`org.springframework.integration` | `spring-integration-test` | `6.4.5`  
`org.springframework.integration` | `spring-integration-test-support` | `6.4.5`  
`org.springframework.integration` | `spring-integration-webflux` | `6.4.5`  
`org.springframework.integration` | `spring-integration-websocket` | `6.4.5`  
`org.springframework.integration` | `spring-integration-ws` | `6.4.5`  
`org.springframework.integration` | `spring-integration-xml` | `6.4.5`  
`org.springframework.integration` | `spring-integration-xmpp` | `6.4.5`  
`org.springframework.integration` | `spring-integration-zeromq` | `6.4.5`  
`org.springframework.integration` | `spring-integration-zip` | `6.4.5`  
`org.springframework.integration` | `spring-integration-zookeeper` | `6.4.5`  
`org.springframework.kafka` | `spring-kafka` | `3.3.6`  
`org.springframework.kafka` | `spring-kafka-test` | `3.3.6`  
`org.springframework.ldap` | `spring-ldap-core` | `3.2.12`  
`org.springframework.ldap` | `spring-ldap-ldif-core` | `3.2.12`  
`org.springframework.ldap` | `spring-ldap-odm` | `3.2.12`  
`org.springframework.ldap` | `spring-ldap-test` | `3.2.12`  
`org.springframework.pulsar` | `spring-pulsar` | `1.2.6`  
`org.springframework.pulsar` | `spring-pulsar-cache-provider` | `1.2.6`  
`org.springframework.pulsar` | `spring-pulsar-cache-provider-caffeine` | `1.2.6`  
`org.springframework.pulsar` | `spring-pulsar-reactive` | `1.2.6`  
`org.springframework.pulsar` | `spring-pulsar-test` | `1.2.6`  
`org.springframework.restdocs` | `spring-restdocs-asciidoctor` | `3.0.3`  
`org.springframework.restdocs` | `spring-restdocs-core` | `3.0.3`  
`org.springframework.restdocs` | `spring-restdocs-mockmvc` | `3.0.3`  
`org.springframework.restdocs` | `spring-restdocs-restassured` | `3.0.3`  
`org.springframework.restdocs` | `spring-restdocs-webtestclient` | `3.0.3`  
`org.springframework.retry` | `spring-retry` | `2.0.12`  
`org.springframework.security` | `spring-security-acl` | `6.4.6`  
`org.springframework.security` | `spring-security-aspects` | `6.4.6`  
`org.springframework.security` | `spring-security-cas` | `6.4.6`  
`org.springframework.security` | `spring-security-config` | `6.4.6`  
`org.springframework.security` | `spring-security-core` | `6.4.6`  
`org.springframework.security` | `spring-security-crypto` | `6.4.6`  
`org.springframework.security` | `spring-security-data` | `6.4.6`  
`org.springframework.security` | `spring-security-ldap` | `6.4.6`  
`org.springframework.security` | `spring-security-messaging` | `6.4.6`  
`org.springframework.security` | `spring-security-oauth2-authorization-server` | `1.4.3`  
`org.springframework.security` | `spring-security-oauth2-client` | `6.4.6`  
`org.springframework.security` | `spring-security-oauth2-core` | `6.4.6`  
`org.springframework.security` | `spring-security-oauth2-jose` | `6.4.6`  
`org.springframework.security` | `spring-security-oauth2-resource-server` | `6.4.6`  
`org.springframework.security` | `spring-security-rsocket` | `6.4.6`  
`org.springframework.security` | `spring-security-saml2-service-provider` | `6.4.6`  
`org.springframework.security` | `spring-security-taglibs` | `6.4.6`  
`org.springframework.security` | `spring-security-test` | `6.4.6`  
`org.springframework.security` | `spring-security-web` | `6.4.6`  
`org.springframework.session` | `spring-session-core` | `3.4.3`  
`org.springframework.session` | `spring-session-data-mongodb` | `3.4.3`  
`org.springframework.session` | `spring-session-data-redis` | `3.4.3`  
`org.springframework.session` | `spring-session-hazelcast` | `3.4.3`  
`org.springframework.session` | `spring-session-jdbc` | `3.4.3`  
`org.springframework.ws` | `spring-ws-core` | `4.0.14`  
`org.springframework.ws` | `spring-ws-security` | `4.0.14`  
`org.springframework.ws` | `spring-ws-support` | `4.0.14`  
`org.springframework.ws` | `spring-ws-test` | `4.0.14`  
`org.springframework.ws` | `spring-xml` | `4.0.14`  
`org.testcontainers` | `activemq` | `1.20.6`  
`org.testcontainers` | `azure` | `1.20.6`  
`org.testcontainers` | `cassandra` | `1.20.6`  
`org.testcontainers` | `chromadb` | `1.20.6`  
`org.testcontainers` | `clickhouse` | `1.20.6`  
`org.testcontainers` | `cockroachdb` | `1.20.6`  
`org.testcontainers` | `consul` | `1.20.6`  
`org.testcontainers` | `couchbase` | `1.20.6`  
`org.testcontainers` | `cratedb` | `1.20.6`  
`org.testcontainers` | `database-commons` | `1.20.6`  
`org.testcontainers` | `databend` | `1.20.6`  
`org.testcontainers` | `db2` | `1.20.6`  
`org.testcontainers` | `dynalite` | `1.20.6`  
`org.testcontainers` | `elasticsearch` | `1.20.6`  
`org.testcontainers` | `gcloud` | `1.20.6`  
`org.testcontainers` | `grafana` | `1.20.6`  
`org.testcontainers` | `hivemq` | `1.20.6`  
`org.testcontainers` | `influxdb` | `1.20.6`  
`org.testcontainers` | `jdbc` | `1.20.6`  
`org.testcontainers` | `junit-jupiter` | `1.20.6`  
`org.testcontainers` | `k3s` | `1.20.6`  
`org.testcontainers` | `k6` | `1.20.6`  
`org.testcontainers` | `kafka` | `1.20.6`  
`org.testcontainers` | `ldap` | `1.20.6`  
`org.testcontainers` | `localstack` | `1.20.6`  
`org.testcontainers` | `mariadb` | `1.20.6`  
`org.testcontainers` | `milvus` | `1.20.6`  
`org.testcontainers` | `minio` | `1.20.6`  
`org.testcontainers` | `mockserver` | `1.20.6`  
`org.testcontainers` | `mongodb` | `1.20.6`  
`org.testcontainers` | `mssqlserver` | `1.20.6`  
`org.testcontainers` | `mysql` | `1.20.6`  
`org.testcontainers` | `neo4j` | `1.20.6`  
`org.testcontainers` | `nginx` | `1.20.6`  
`org.testcontainers` | `oceanbase` | `1.20.6`  
`org.testcontainers` | `ollama` | `1.20.6`  
`org.testcontainers` | `openfga` | `1.20.6`  
`org.testcontainers` | `oracle-free` | `1.20.6`  
`org.testcontainers` | `oracle-xe` | `1.20.6`  
`org.testcontainers` | `orientdb` | `1.20.6`  
`org.testcontainers` | `pinecone` | `1.20.6`  
`org.testcontainers` | `postgresql` | `1.20.6`  
`org.testcontainers` | `presto` | `1.20.6`  
`org.testcontainers` | `pulsar` | `1.20.6`  
`org.testcontainers` | `qdrant` | `1.20.6`  
`org.testcontainers` | `questdb` | `1.20.6`  
`org.testcontainers` | `r2dbc` | `1.20.6`  
`org.testcontainers` | `rabbitmq` | `1.20.6`  
`org.testcontainers` | `redpanda` | `1.20.6`  
`org.testcontainers` | `scylladb` | `1.20.6`  
`org.testcontainers` | `selenium` | `1.20.6`  
`org.testcontainers` | `solace` | `1.20.6`  
`org.testcontainers` | `solr` | `1.20.6`  
`org.testcontainers` | `spock` | `1.20.6`  
`org.testcontainers` | `testcontainers` | `1.20.6`  
`org.testcontainers` | `tidb` | `1.20.6`  
`org.testcontainers` | `timeplus` | `1.20.6`  
`org.testcontainers` | `toxiproxy` | `1.20.6`  
`org.testcontainers` | `trino` | `1.20.6`  
`org.testcontainers` | `typesense` | `1.20.6`  
`org.testcontainers` | `vault` | `1.20.6`  
`org.testcontainers` | `weaviate` | `1.20.6`  
`org.testcontainers` | `yugabytedb` | `1.20.6`  
`org.thymeleaf` | `thymeleaf` | `3.1.3.RELEASE`  
`org.thymeleaf` | `thymeleaf-spring6` | `3.1.3.RELEASE`  
`org.thymeleaf.extras` | `thymeleaf-extras-springsecurity6` | `3.1.3.RELEASE`  
`org.webjars` | `webjars-locator-core` | `0.59`  
`org.webjars` | `webjars-locator-lite` | `1.0.1`  
`org.xerial` | `sqlite-jdbc` | `********`  
`org.xmlunit` | `xmlunit-assertj` | `2.10.1`  
`org.xmlunit` | `xmlunit-assertj3` | `2.10.1`  
`org.xmlunit` | `xmlunit-core` | `2.10.1`  
`org.xmlunit` | `xmlunit-jakarta-jaxb-impl` | `2.10.1`  
`org.xmlunit` | `xmlunit-legacy` | `2.10.1`  
`org.xmlunit` | `xmlunit-matchers` | `2.10.1`  
`org.xmlunit` | `xmlunit-placeholders` | `2.10.1`  
`org.yaml` | `snakeyaml` | `2.3`  
`redis.clients` | `jedis` | `5.2.0`  
`wsdl4j` | `wsdl4j` | `1.6.3`  
[Dependency Versions](index.html) [Version Properties](properties.html)
---
