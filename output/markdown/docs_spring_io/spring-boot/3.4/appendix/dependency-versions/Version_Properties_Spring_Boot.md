Title: Version Properties :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/appendix/dependency-versions/properties.html
HTML: html/docs_spring_io/spring-boot/3.4/appendix/dependency-versions/Version_Properties_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/appendix/dependency-versions/Version_Properties_Spring_Boot.png
crawled_at: 2025-06-04T16:01:03.371148
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/appendix/pages/dependency-versions/properties.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * Appendix
  * [Dependency Versions](index.html)
  * [Version Properties](properties.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../appendix/dependency-versions/properties.html)!  
---|---  
  
# Version Properties

The following table provides all properties that can be used to override the versions managed by Spring Boot. Browse the [`spring-boot-dependencies` build.gradle](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-dependencies/build.gradle) for a complete list of dependencies. You can learn how to customize these versions in your application in the [Build Tool Plugins](../../build-tool-plugin/index.html) documentation.

Library | Version Property  
---|---  
`ActiveMQ` | `activemq.version`  
`Angus Mail` | `angus-mail.version`  
`Artemis` | `artemis.version`  
`AspectJ` | `aspectj.version`  
`AssertJ` | `assertj.version`  
`Awaitility` | `awaitility.version`  
`Brave` | `brave.version`  
`Build Helper Maven Plugin` | `build-helper-maven-plugin.version`  
`Byte Buddy` | `byte-buddy.version`  
`cache2k` | `cache2k.version`  
`Caffeine` | `caffeine.version`  
`Cassandra Driver` | `cassandra-driver.version`  
`Classmate` | `classmate.version`  
`Commons Codec` | `commons-codec.version`  
`Commons DBCP2` | `commons-dbcp2.version`  
`Commons Lang3` | `commons-lang3.version`  
`Commons Pool` | `commons-pool.version`  
`Commons Pool2` | `commons-pool2.version`  
`Couchbase Client` | `couchbase-client.version`  
`Crac` | `crac.version`  
`CycloneDX Maven Plugin` | `cyclonedx-maven-plugin.version`  
`DB2 JDBC` | `db2-jdbc.version`  
`Dependency Management Plugin` | `dependency-management-plugin.version`  
`Derby` | `derby.version`  
`Ehcache3` | `ehcache3.version`  
`Elasticsearch Client` | `elasticsearch-client.version`  
`Flyway` | `flyway.version`  
`FreeMarker` | `freemarker.version`  
`Git Commit ID Maven Plugin` | `git-commit-id-maven-plugin.version`  
`Glassfish JAXB` | `glassfish-jaxb.version`  
`Glassfish JSTL` | `glassfish-jstl.version`  
`GraphQL Java` | `graphql-java.version`  
`Groovy` | `groovy.version`  
`Gson` | `gson.version`  
`H2` | `h2.version`  
`Hamcrest` | `hamcrest.version`  
`Hazelcast` | `hazelcast.version`  
`Hibernate` | `hibernate.version`  
`Hibernate Validator` | `hibernate-validator.version`  
`HikariCP` | `hikaricp.version`  
`HSQLDB` | `hsqldb.version`  
`HtmlUnit` | `htmlunit.version`  
`HttpAsyncClient` | `httpasyncclient.version`  
`HttpClient5` | `httpclient5.version`  
`HttpCore` | `httpcore.version`  
`HttpCore5` | `httpcore5.version`  
`Infinispan` | `infinispan.version`  
`InfluxDB Java` | `influxdb-java.version`  
`Jackson Bom` | `jackson-bom.version`  
`Jakarta Activation` | `jakarta-activation.version`  
`Jakarta Annotation` | `jakarta-annotation.version`  
`Jakarta Inject` | `jakarta-inject.version`  
`Jakarta JMS` | `jakarta-jms.version`  
`Jakarta Json` | `jakarta-json.version`  
`Jakarta Json Bind` | `jakarta-json-bind.version`  
`Jakarta Mail` | `jakarta-mail.version`  
`Jakarta Management` | `jakarta-management.version`  
`Jakarta Persistence` | `jakarta-persistence.version`  
`Jakarta Servlet` | `jakarta-servlet.version`  
`Jakarta Servlet JSP JSTL` | `jakarta-servlet-jsp-jstl.version`  
`Jakarta Transaction` | `jakarta-transaction.version`  
`Jakarta Validation` | `jakarta-validation.version`  
`Jakarta WebSocket` | `jakarta-websocket.version`  
`Jakarta WS RS` | `jakarta-ws-rs.version`  
`Jakarta XML Bind` | `jakarta-xml-bind.version`  
`Jakarta XML SOAP` | `jakarta-xml-soap.version`  
`Jakarta XML WS` | `jakarta-xml-ws.version`  
`Janino` | `janino.version`  
`Javax Cache` | `javax-cache.version`  
`Javax Money` | `javax-money.version`  
`Jaxen` | `jaxen.version`  
`Jaybird` | `jaybird.version`  
`JBoss Logging` | `jboss-logging.version`  
`JDOM2` | `jdom2.version`  
`Jedis` | `jedis.version`  
`Jersey` | `jersey.version`  
`Jetty` | `jetty.version`  
`Jetty Reactive HTTPClient` | `jetty-reactive-httpclient.version`  
`JMustache` | `jmustache.version`  
`jOOQ` | `jooq.version`  
`Json Path` | `json-path.version`  
`Json-smart` | `json-smart.version`  
`JsonAssert` | `jsonassert.version`  
`JTDS` | `jtds.version`  
`JUnit` | `junit.version`  
`JUnit Jupiter` | `junit-jupiter.version`  
`Kafka` | `kafka.version`  
`Kotlin` | `kotlin.version`  
`Kotlin Coroutines` | `kotlin-coroutines.version`  
`Kotlin Serialization` | `kotlin-serialization.version`  
`Lettuce` | `lettuce.version`  
`Liquibase` | `liquibase.version`  
`Log4j2` | `log4j2.version`  
`Logback` | `logback.version`  
`Lombok` | `lombok.version`  
`MariaDB` | `mariadb.version`  
`Maven AntRun Plugin` | `maven-antrun-plugin.version`  
`Maven Assembly Plugin` | `maven-assembly-plugin.version`  
`Maven Clean Plugin` | `maven-clean-plugin.version`  
`Maven Compiler Plugin` | `maven-compiler-plugin.version`  
`Maven Dependency Plugin` | `maven-dependency-plugin.version`  
`Maven Deploy Plugin` | `maven-deploy-plugin.version`  
`Maven Enforcer Plugin` | `maven-enforcer-plugin.version`  
`Maven Failsafe Plugin` | `maven-failsafe-plugin.version`  
`Maven Help Plugin` | `maven-help-plugin.version`  
`Maven Install Plugin` | `maven-install-plugin.version`  
`Maven Invoker Plugin` | `maven-invoker-plugin.version`  
`Maven Jar Plugin` | `maven-jar-plugin.version`  
`Maven Javadoc Plugin` | `maven-javadoc-plugin.version`  
`Maven Resources Plugin` | `maven-resources-plugin.version`  
`Maven Shade Plugin` | `maven-shade-plugin.version`  
`Maven Source Plugin` | `maven-source-plugin.version`  
`Maven Surefire Plugin` | `maven-surefire-plugin.version`  
`Maven War Plugin` | `maven-war-plugin.version`  
`Micrometer` | `micrometer.version`  
`Micrometer Tracing` | `micrometer-tracing.version`  
`Mockito` | `mockito.version`  
`MongoDB` | `mongodb.version`  
`MSSQL JDBC` | `mssql-jdbc.version`  
`MySQL` | `mysql.version`  
`Native Build Tools Plugin` | `native-build-tools-plugin.version`  
`NekoHTML` | `nekohtml.version`  
`Neo4j Java Driver` | `neo4j-java-driver.version`  
`Netty` | `netty.version`  
`OpenTelemetry` | `opentelemetry.version`  
`Oracle Database` | `oracle-database.version`  
`Oracle R2DBC` | `oracle-r2dbc.version`  
`Pooled JMS` | `pooled-jms.version`  
`Postgresql` | `postgresql.version`  
`Prometheus Client` | `prometheus-client.version`  
`Prometheus Simpleclient` | `prometheus-simpleclient.version`  
`Pulsar` | `pulsar.version`  
`Pulsar Reactive` | `pulsar-reactive.version`  
`Quartz` | `quartz.version`  
`QueryDSL` | `querydsl.version`  
`R2DBC H2` | `r2dbc-h2.version`  
`R2DBC MariaDB` | `r2dbc-mariadb.version`  
`R2DBC MSSQL` | `r2dbc-mssql.version`  
`R2DBC MySQL` | `r2dbc-mysql.version`  
`R2DBC Pool` | `r2dbc-pool.version`  
`R2DBC Postgresql` | `r2dbc-postgresql.version`  
`R2DBC Proxy` | `r2dbc-proxy.version`  
`R2DBC SPI` | `r2dbc-spi.version`  
`Rabbit AMQP Client` | `rabbit-amqp-client.version`  
`Rabbit Stream Client` | `rabbit-stream-client.version`  
`Reactive Streams` | `reactive-streams.version`  
`Reactor Bom` | `reactor-bom.version`  
`REST Assured` | `rest-assured.version`  
`RSocket` | `rsocket.version`  
`RxJava3` | `rxjava3.version`  
`SAAJ Impl` | `saaj-impl.version`  
`Selenium` | `selenium.version`  
`Selenium HtmlUnit` | `selenium-htmlunit.version`  
`SendGrid` | `sendgrid.version`  
`SLF4J` | `slf4j.version`  
`SnakeYAML` | `snakeyaml.version`  
`Spring AMQP` | `spring-amqp.version`  
`Spring Authorization Server` | `spring-authorization-server.version`  
`Spring Batch` | `spring-batch.version`  
`Spring Boot` | `null`  
`Spring Data Bom` | `spring-data-bom.version`  
`Spring Framework` | `spring-framework.version`  
`Spring GraphQL` | `spring-graphql.version`  
`Spring HATEOAS` | `spring-hateoas.version`  
`Spring Integration` | `spring-integration.version`  
`Spring Kafka` | `spring-kafka.version`  
`Spring LDAP` | `spring-ldap.version`  
`Spring Pulsar` | `spring-pulsar.version`  
`Spring RESTDocs` | `spring-restdocs.version`  
`Spring Retry` | `spring-retry.version`  
`Spring Security` | `spring-security.version`  
`Spring Session` | `spring-session.version`  
`Spring WS` | `spring-ws.version`  
`SQLite JDBC` | `sqlite-jdbc.version`  
`Testcontainers` | `testcontainers.version`  
`Testcontainers Redis Module` | `testcontainers-redis-module.version`  
`Thymeleaf` | `thymeleaf.version`  
`Thymeleaf Extras Data Attribute` | `thymeleaf-extras-data-attribute.version`  
`Thymeleaf Extras SpringSecurity` | `thymeleaf-extras-springsecurity.version`  
`Thymeleaf Layout Dialect` | `thymeleaf-layout-dialect.version`  
`Tomcat` | `tomcat.version`  
`UnboundID LDAPSDK` | `unboundid-ldapsdk.version`  
`Undertow` | `undertow.version`  
`Versions Maven Plugin` | `versions-maven-plugin.version`  
`WebJars Locator Core` | `webjars-locator-core.version`  
`WebJars Locator Lite` | `webjars-locator-lite.version`  
`WSDL4j` | `wsdl4j.version`  
`XML Maven Plugin` | `xml-maven-plugin.version`  
`XmlUnit2` | `xmlunit2.version`  
`Yasson` | `yasson.version`  
`Zipkin Reporter` | `zipkin-reporter.version`  
[Managed Dependency Coordinates](coordinates.html)
---
