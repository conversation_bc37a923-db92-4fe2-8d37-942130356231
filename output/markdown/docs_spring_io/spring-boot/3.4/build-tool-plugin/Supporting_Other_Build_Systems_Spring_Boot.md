Title: Supporting Other Build Systems :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/build-tool-plugin/other-build-systems.html
HTML: html/docs_spring_io/spring-boot/3.4/build-tool-plugin/Supporting_Other_Build_Systems_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/build-tool-plugin/Supporting_Other_Build_Systems_Spring_Boot.png
crawled_at: 2025-06-04T16:06:59.550669
---
Search CTRL + k

### Supporting Other Build Systems

  * Repackaging Archives
  * Nested Libraries
  * Finding a Main Class
  * Example Repackage Implementation



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/build-tool-plugin/pages/other-build-systems.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](index.html)
  * [Supporting Other Build Systems](other-build-systems.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../build-tool-plugin/other-build-systems.html)!  
---|---  
  
# Supporting Other Build Systems

### Supporting Other Build Systems

  * Repackaging Archives
  * Nested Libraries
  * Finding a Main Class
  * Example Repackage Implementation



If you want to use a build tool other than Maven, Gradle, or Ant, you likely need to develop your own plugin. Executable jars need to follow a specific format and certain entries need to be written in an uncompressed form (see the [executable jar format](../specification/executable-jar/index.html) section in the appendix for details).

The Spring Boot Maven and Gradle plugins both make use of `spring-boot-loader-tools` to actually generate jars. If you need to, you may use this library directly.

## Repackaging Archives

To repackage an existing archive so that it becomes a self-contained executable archive, use [`Repackager`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/loader/tools/Repackager.html). The [`Repackager`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/loader/tools/Repackager.html) class takes a single constructor argument that refers to an existing jar or war archive. Use one of the two available `repackage()` methods to either replace the original file or write to a new destination. Various settings can also be configured on the repackager before it is run.

## Nested Libraries

When repackaging an archive, you can include references to dependency files by using the [`Libraries`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/loader/tools/Libraries.html) interface. We do not provide any concrete implementations of [`Libraries`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/loader/tools/Libraries.html) here as they are usually build-system-specific.

If your archive already includes libraries, you can use [`Libraries.NONE`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/loader/tools/Libraries.html#NONE).

## Finding a Main Class

If you do not use `Repackager.setMainClass()` to specify a main class, the repackager uses [ASM](https://asm.ow2.io/) to read class files and tries to find a suitable class with a `public static void main(String[] args)` method. An exception is thrown if more than one candidate is found.

## Example Repackage Implementation

The following example shows a typical repackage implementation:

  * Java

  * Kotlin



    
    
    import java.io.File;
    import java.io.IOException;
    import java.util.List;
    
    import org.springframework.boot.loader.tools.Library;
    import org.springframework.boot.loader.tools.LibraryCallback;
    import org.springframework.boot.loader.tools.LibraryScope;
    import org.springframework.boot.loader.tools.Repackager;
    
    public class MyBuildTool {
    
    	public void build() throws IOException {
    		File sourceJarFile = ...
    		Repackager repackager = new Repackager(sourceJarFile);
    		repackager.setBackupSource(false);
    		repackager.repackage(this::getLibraries);
    	}
    
    	private void getLibraries(LibraryCallback callback) throws IOException {
    		// Build system specific implementation, callback for each dependency
    		for (File nestedJar : getCompileScopeJars()) {
    			callback.library(new Library(nestedJar, LibraryScope.COMPILE));
    		}
    		// ...
    	}
    
    	private List<File> getCompileScopeJars() {
    		return ...
    	}
    
    }
    
    Copied!
    
    
    import org.springframework.boot.loader.tools.Library
    import org.springframework.boot.loader.tools.LibraryCallback
    import org.springframework.boot.loader.tools.LibraryScope
    import org.springframework.boot.loader.tools.Repackager
    import java.io.File
    import java.io.IOException
    
    class MyBuildTool {
    
    	@Throws(IOException::class)
    	fun build() {
    		val sourceJarFile: File? =  ...
    		val repackager = Repackager(sourceJarFile)
    		repackager.setBackupSource(false)
    		repackager.repackage { callback: LibraryCallback -> getLibraries(callback) }
    	}
    
    	@Throws(IOException::class)
    	private fun getLibraries(callback: LibraryCallback) {
    		// Build system specific implementation, callback for each dependency
    		for (nestedJar in getCompileScopeJars()!!) {
    			callback.library(Library(nestedJar, LibraryScope.COMPILE))
    		}
    		// ...
    	}
    
    	private fun getCompileScopeJars(): List<File?>? {
    		return  ...
    	}
    
    }
    
    Copied!

[Spring Boot AntLib Module](antlib.html) [Spring Boot CLI](../cli/index.html)
---
