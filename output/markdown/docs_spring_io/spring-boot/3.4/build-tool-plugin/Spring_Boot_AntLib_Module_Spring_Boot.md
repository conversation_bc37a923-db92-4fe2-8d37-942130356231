Title: Spring Boot AntLib Module :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/build-tool-plugin/antlib.html
HTML: html/docs_spring_io/spring-boot/3.4/build-tool-plugin/Spring_Boot_AntLib_Module_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/build-tool-plugin/Spring_Boot_AntLib_Module_Spring_Boot.png
crawled_at: 2025-06-04T16:08:34.203984
---
Search CTRL + k

### Spring Boot AntLib Module

  * Spring Boot Ant Tasks
  * Using the “exejar” Task
  * Examples
  * Using the “findmainclass” Task
  * Examples



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/build-tool-plugin/pages/antlib.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](index.html)
  * [Spring Boot AntLib Module](antlib.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../build-tool-plugin/antlib.html)!  
---|---  
  
# Spring Boot AntLib Module

### Spring Boot AntLib Module

  * Spring Boot Ant Tasks
  * Using the “exejar” Task
  * Examples
  * Using the “findmainclass” Task
  * Examples



The Spring Boot AntLib module provides basic Spring Boot support for Apache Ant. You can use the module to create executable jars. To use the module, you need to declare an additional `spring-boot` namespace in your `build.xml`, as shown in the following example:
    
    
    <project xmlns:ivy="antlib:org.apache.ivy.ant"
    	xmlns:spring-boot="antlib:org.springframework.boot.ant"
    	name="myapp" default="build">
    	...
    </project>
    
    Copied!

You need to remember to start Ant using the `-lib` option, as shown in the following example:
    
    
    $ ant -lib <directory containing spring-boot-antlib-3.4.6.jar>
    
    Copied!

__ |  The “Using Spring Boot” section includes a more complete example of [using Apache Ant with `spring-boot-antlib`](../reference/using/build-systems.html#using.build-systems.ant).   
---|---  
  
## Spring Boot Ant Tasks

Once the `spring-boot-antlib` namespace has been declared, the following additional tasks are available:

  * Using the “exejar” Task

  * Using the “findmainclass” Task




### Using the “exejar” Task

You can use the `exejar` task to create a Spring Boot executable jar. The following attributes are supported by the task:

Attribute | Description | Required  
---|---|---  
`destfile` | The destination jar file to create | Yes  
`classes` | The root directory of Java class files | Yes  
`start-class` | The main application class to run | No _(the default is the first class found that declares a`main` method)_  
  
The following nested elements can be used with the task:

Element | Description  
---|---  
`resources` | One or more [Resource Collections](https://ant.apache.org/manual/Types/resources.html#collection) describing a set of [Resources](https://ant.apache.org/manual/Types/resources.html) that should be added to the content of the created jar file.  
`lib` | One or more [Resource Collections](https://ant.apache.org/manual/Types/resources.html#collection) that should be added to the set of jar libraries that make up the runtime dependency classpath of the application.  
  
### Examples

This section shows two examples of Ant tasks.

Specify start-class
    
    
    <spring-boot:exejar destfile="target/my-application.jar"
    		classes="target/classes" start-class="com.example.MyApplication">
    	<resources>
    		<fileset dir="src/main/resources" />
    	</resources>
    	<lib>
    		<fileset dir="lib" />
    	</lib>
    </spring-boot:exejar>
    
    Copied!

Detect start-class
    
    
    <exejar destfile="target/my-application.jar" classes="target/classes">
    	<lib>
    		<fileset dir="lib" />
    	</lib>
    </exejar>
    
    Copied!

## Using the “findmainclass” Task

The `findmainclass` task is used internally by `exejar` to locate a class declaring a `main`. If necessary, you can also use this task directly in your build. The following attributes are supported:

Attribute | Description | Required  
---|---|---  
`classesroot` | The root directory of Java class files | Yes _(unless`mainclass` is specified)_  
`mainclass` | Can be used to short-circuit the `main` class search | No  
`property` | The Ant property that should be set with the result | No _(result will be logged if unspecified)_  
  
### Examples

This section contains three examples of using `findmainclass`.

Find and log
    
    
    <findmainclass classesroot="target/classes" />
    
    Copied!

Find and set
    
    
    <findmainclass classesroot="target/classes" property="main-class" />
    
    Copied!

Override and set
    
    
    <findmainclass mainclass="com.example.MainClass" property="main-class" />
    
    Copied!

[Reacting to Other Plugins](../gradle-plugin/reacting.html) [Supporting Other Build Systems](other-build-systems.html)
---
