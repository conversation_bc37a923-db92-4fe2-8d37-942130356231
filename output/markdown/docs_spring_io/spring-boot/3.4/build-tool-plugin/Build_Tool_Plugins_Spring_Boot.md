Title: Build Tool Plugins :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/build-tool-plugin/index.html
HTML: html/docs_spring_io/spring-boot/3.4/build-tool-plugin/Build_Tool_Plugins_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/build-tool-plugin/Build_Tool_Plugins_Spring_Boot.png
crawled_at: 2025-06-04T19:23:50.074666
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/build-tool-plugin/pages/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../build-tool-plugin/index.html)!  
---|---  
  
# Build Tool Plugins

Spring Boot provides build tool plugins for Maven and Gradle. The plugins offer a variety of features, including the packaging of executable jars. This section provides more details on both plugins as well as some help should you need to extend an unsupported build system. If you are just getting started, you might want to read [Build Systems](../reference/using/build-systems.html) from the [Developing with Spring Boot](../reference/using/index.html) section first.

[Docker Compose](../how-to/docker-compose.html) [Maven Plugin](../maven-plugin/index.html)
---
