Title: Packaging OCI Images :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/maven-plugin/build-image.html
HTML: html/docs_spring_io/spring-boot/3.4/maven-plugin/Packaging_OCI_Images_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/maven-plugin/Packaging_OCI_Images_Spring_Boot.png
crawled_at: 2025-06-04T16:56:14.144572
---
Search CTRL + k

### Packaging OCI Images

  * Docker Daemon
  * Docker Registry
  * Image Customizations
  * Tags Format
  * spring-boot:build-image
  * Required parameters
  * Optional parameters
  * Parameter details
  * spring-boot:build-image-no-fork
  * Required parameters
  * Optional parameters
  * Parameter details
  * Examples
  * Custom Image Builder
  * Builder Configuration
  * Runtime JVM Configuration
  * Custom Image Name
  * Buildpacks
  * Image Publishing
  * Builder Cache and Workspace Configuration
  * Docker Configuration



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-maven-plugin/src/docs/antora/modules/maven-plugin/pages/build-image.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Maven Plugin](index.html)
  * [Packaging OCI Images](build-image.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../maven-plugin/build-image.html)!  
---|---  
  
# Packaging OCI Images

### Packaging OCI Images

  * Docker Daemon
  * Docker Registry
  * Image Customizations
  * Tags Format
  * spring-boot:build-image
  * Required parameters
  * Optional parameters
  * Parameter details
  * spring-boot:build-image-no-fork
  * Required parameters
  * Optional parameters
  * Parameter details
  * Examples
  * Custom Image Builder
  * Builder Configuration
  * Runtime JVM Configuration
  * Custom Image Name
  * Buildpacks
  * Image Publishing
  * Builder Cache and Workspace Configuration
  * Docker Configuration



The plugin can create an [OCI image](https://github.com/opencontainers/image-spec) from a jar or war file using [Cloud Native Buildpacks](https://buildpacks.io/) (CNB). Images can be built on the command-line using the `build-image` goal. This makes sure that the package lifecycle has run before the image is created.

__ |  For security reasons, images build and run as non-root users. See the [CNB specification](https://buildpacks.io/docs/reference/spec/platform-api/#users) for more details.   
---|---  
  
The easiest way to get started is to invoke `mvn spring-boot:build-image` on a project. It is possible to automate the creation of an image whenever the `package` phase is invoked, as shown in the following example:
    
    
    <build>
    	<plugins>
    		<plugin>
    			<groupId>org.springframework.boot</groupId>
    			<artifactId>spring-boot-maven-plugin</artifactId>
    			<executions>
    				<execution>
    					<goals>
    						<goal>build-image-no-fork</goal>
    					</goals>
    				</execution>
    			</executions>
    		</plugin>
    	</plugins>
    </build>
    
    Copied!

__ |  Use `build-image-no-fork` when binding the goal to the package lifecycle. This goal is similar to `build-image` but does not fork the lifecycle to make sure `package` has run. In the rest of this section, `build-image` is used to refer to either the `build-image` or `build-image-no-fork` goals.   
---|---  
  
__ |  While the buildpack runs from an [executable archive](packaging.html), it is not necessary to execute the `repackage` goal first as the executable archive is created automatically if necessary. When the `build-image` repackages the application, it applies the same settings as the `repackage` goal would, that is dependencies can be excluded using one of the exclude options. The `spring-boot-devtools` and `spring-boot-docker-compose` modules are automatically excluded by default (you can control this using the `excludeDevtools` and `excludeDockerCompose` properties).   
---|---  
  
## Docker Daemon

The `build-image` goal requires access to a Docker daemon. The goal will inspect local Docker CLI [configuration files](https://docs.docker.com/engine/reference/commandline/cli/#configuration-files) to determine the current [context](https://docs.docker.com/engine/context/working-with-contexts/) and use the context connection information to communicate with a Docker daemon. If the current context can not be determined or the context does not have connection information, then the goal will use a default local connection. This works with [Docker Engine](https://docs.docker.com/install/) on all supported platforms without configuration.

Environment variables can be set to configure the `build-image` goal to use an alternative local or remote connection. The following table shows the environment variables and their values:

Environment variable | Description  
---|---  
DOCKER_CONFIG | Location of Docker CLI [configuration files](https://docs.docker.com/engine/reference/commandline/cli/#configuration-files) used to determine the current context (defaults to `$HOME/.docker`)  
DOCKER_CONTEXT | Name of a [context](https://docs.docker.com/engine/context/working-with-contexts/) that should be used to retrieve host information from Docker CLI configuration files (overrides `DOCKER_HOST`)  
DOCKER_HOST | URL containing the host and port for the Docker daemon - for example `tcp://**************:2376`  
DOCKER_TLS_VERIFY | Enable secure HTTPS protocol when set to `1` (optional)  
DOCKER_CERT_PATH | Path to certificate and key files for HTTPS (required if `DOCKER_TLS_VERIFY=1`, ignored otherwise)  
  
Docker daemon connection information can also be provided using `docker` parameters in the plugin configuration. The following table summarizes the available parameters:

Parameter | Description  
---|---  
`context` | Name of a [context](https://docs.docker.com/engine/context/working-with-contexts/) that should be used to retrieve host information from Docker CLI [configuration files](https://docs.docker.com/engine/reference/commandline/cli/#configuration-files)  
`host` | URL containing the host and port for the Docker daemon - for example `tcp://**************:2376`  
`tlsVerify` | Enable secure HTTPS protocol when set to `true` (optional)  
`certPath` | Path to certificate and key files for HTTPS (required if `tlsVerify` is `true`, ignored otherwise)  
`bindHostToBuilder` | When `true`, the value of the `host` property will be provided to the container that is created for the CNB builder (optional)  
  
For more details, see also examples.

## Docker Registry

If the Docker images specified by the `builder` or `runImage` parameters are stored in a private Docker image registry that requires authentication, the authentication credentials can be provided using `docker.builderRegistry` parameters.

If the generated Docker image is to be published to a Docker image registry, the authentication credentials can be provided using `docker.publishRegistry` parameters.

Parameters are provided for user authentication or identity token authentication. Consult the documentation for the Docker registry being used to store images for further information on supported authentication methods.

The following table summarizes the available parameters for `docker.builderRegistry` and `docker.publishRegistry`:

Parameter | Description  
---|---  
`username` | Username for the Docker image registry user. Required for user authentication.  
`password` | Password for the Docker image registry user. Required for user authentication.  
`url` | Address of the Docker image registry. Optional for user authentication.  
`email` | E-mail address for the Docker image registry user. Optional for user authentication.  
`token` | Identity token for the Docker image registry user. Required for token authentication.  
  
For more details, see also examples.

## Image Customizations

The plugin invokes a [builder](https://buildpacks.io/docs/for-app-developers/concepts/builder/) to orchestrate the generation of an image. The builder includes multiple [buildpacks](https://buildpacks.io/docs/for-app-developers/concepts/buildpack/) that can inspect the application to influence the generated image. By default, the plugin chooses a builder image. The name of the generated image is deduced from project properties.

The `image` parameter allows configuration of the builder and how it should operate on the project. The following table summarizes the available parameters and their default values:

Parameter / (User Property) | Description | Default value  
---|---|---  
`builder`  
(`spring-boot.build-image.builder`) | Name of the builder image to use. | `paketobuildpacks/builder-jammy-java-tiny:latest`  
`trustBuilder`  
(`spring-boot.build-image.trustBuilder`) | Whether to treat the builder as [trusted](https://buildpacks.io/docs/for-platform-operators/how-to/integrate-ci/pack/concepts/trusted_builders/#what-is-a-trusted-builder). | `true` if the builder is one of `paketobuildpacks/builder-jammy-java-tiny`, `paketobuildpacks/builder-noble-java-tiny`, `paketobuildpacks/builder-jammy-tiny`, `paketobuildpacks/builder-jammy-base`, `paketobuildpacks/builder-jammy-full`, `paketobuildpacks/builder-jammy-buildpackless-tiny`, `paketobuildpacks/builder-jammy-buildpackless-base`, `paketobuildpacks/builder-jammy-buildpackless-full`, `gcr.io/buildpacks/builder`, `heroku/builder`; `false` otherwise.  
`imagePlatform`  
(`spring-boot.build-image.imagePlatform`) |  The platform (operating system and architecture) of any builder, run, and buildpack images that are pulled. Must be in the form of `OS[/architecture[/variant]]`, such as `linux/amd64`, `linux/arm64`, or `linux/arm/v5`. Refer to documentation of the builder being used to determine the image OS and architecture options available. | No default value, indicating that the platform of the host machine should be used.  
`runImage`  
(`spring-boot.build-image.runImage`) | Name of the run image to use. | No default value, indicating the run image specified in Builder metadata should be used.  
`name`  
(`spring-boot.build-image.imageName`) | [Image name](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/buildpack/platform/docker/type/ImageName.html#of-java.lang.String-) for the generated image. | `docker.io/library/`  
`${project.artifactId}:${project.version}`  
`pullPolicy`  
(`spring-boot.build-image.pullPolicy`) | [Policy](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/buildpack/platform/build/PullPolicy.html) used to determine when to pull the builder and run images from the registry. Acceptable values are `ALWAYS`, `NEVER`, and `IF_NOT_PRESENT`. | `ALWAYS`  
`env` | Environment variables that should be passed to the builder. |   
`buildpacks` |  Buildpacks that the builder should use when building the image. Only the specified buildpacks will be used, overriding the default buildpacks included in the builder. Buildpack references must be in one of the following forms:

  * Buildpack in the builder - `[urn:cnb:builder:]<buildpack ID>[@<version>]`
  * Buildpack in a directory on the file system - `[file://]<path>`
  * Buildpack in a gzipped tar (.tgz) file on the file system - `[file://]<path>/<file name>`
  * Buildpack in an OCI image - `[docker://]<host>/<repo>[:<tag>][@<digest>]`

| None, indicating the builder should use the buildpacks included in it.  
`bindings` |  [Volume bind mounts](https://docs.docker.com/storage/bind-mounts/) that should be mounted to the builder container when building the image. The bindings will be passed unparsed and unvalidated to Docker when creating the builder container. Bindings must be in one of the following forms:

  * `<host source path>:<container destination path>[:<options>]`
  * `<host volume name>:<container destination path>[:<options>]`

Where `<options>` can contain:

  * `ro` to mount the volume as read-only in the container
  * `rw` to mount the volume as readable and writable in the container
  * `volume-opt=key=value` to specify key-value pairs consisting of an option name and its value

|   
`network` \+ (`spring-boot.build-image.network`) | The [network driver](https://docs.docker.com/network/#network-drivers) the builder container will be configured to use. The value supplied will be passed unvalidated to Docker when creating the builder container. |   
`cleanCache` \+ (`spring-boot.build-image.cleanCache`) | Whether to clean the cache before building. | `false`  
`verboseLogging` | Enables verbose logging of builder operations. | `false`  
`publish` \+ (`spring-boot.build-image.publish`) | Whether to publish the generated image to a Docker registry. | `false`  
`tags` | One or more additional tags to apply to the generated image. The values provided to the `tags` option should be **full** image references. See the tags section for more details. |   
`buildWorkspace` | A temporary workspace that will be used by the builder and buildpacks to store files during image building. The value can be a named volume or a bind mount location. | A named volume in the Docker daemon, with a name derived from the image name.  
`buildCache` | A cache containing layers created by buildpacks and used by the image building process. The value can be a named volume or a bind mount location. | A named volume in the Docker daemon, with a name derived from the image name.  
`launchCache` | A cache containing layers created by buildpacks and used by the image launching process. The value can be a named volume or a bind mount location. | A named volume in the Docker daemon, with a name derived from the image name.  
`createdDate`  
(`spring-boot.build-image.createdDate`) | A date that will be used to set the `Created` field in the generated image’s metadata. The value must be a string in the ISO 8601 instant format, or `now` to use the current date and time. | A fixed date that enables [build reproducibility](https://buildpacks.io/docs/for-app-developers/concepts/reproducibility/).  
`applicationDirectory`  
(`spring-boot.build-image.applicationDirectory`) | The path to a directory that application contents will be uploaded to in the builder image. Application contents will also be in this location in the generated image. | `/workspace`  
`securityOptions` | [Security options](https://docs.docker.com/engine/reference/run/#security-configuration) that will be applied to the builder container, provided as an array of string values | `["label=disable"]` on Linux and macOS, `[]` on Windows  
  
__ |  The plugin detects the target Java compatibility of the project using the compiler’s plugin configuration or the `maven.compiler.target` property. When using the default Paketo builder and buildpacks, the plugin instructs the buildpacks to install the same Java version. You can override this behaviour as shown in the builder configuration examples.   
---|---  
  
For more details, see also examples.

### Tags Format

The values provided to the `tags` option should be **full** image references. The accepted format is `[domainHost:port/][path/]name[:tag][@digest]`.

If the domain is missing, it defaults to `docker.io`. If the path is missing, it defaults to `library`. If the tag is missing, it defaults to `latest`.

Some examples:

  * `my-image` leads to the image reference `docker.io/library/my-image:latest`

  * `my-repository/my-image` leads to `docker.io/my-repository/my-image:latest`

  * `example.com/my-repository/my-image:1.0.0` will be used as is




## `spring-boot:build-image`

`org.springframework.boot:spring-boot-maven-plugin:3.4.6`

Package an application into an OCI image using a buildpack, forking the lifecycle to make sure that `package` ran. This goal is suitable for command-line invocation. If you need to configure a goal `execution` in your build, use `build-image-no-fork` instead.

### Required parameters

Name | Type | Default  
---|---|---  
sourceDirectory | `File` | `${project.build.directory}`  
  
### Optional parameters

Name | Type | Default  
---|---|---  
classifier | `String` |   
docker | `[Docker](api/java/org/springframework/boot/maven/Docker.html)` |   
excludeDevtools | `boolean` | `true`  
excludeDockerCompose | `boolean` | `true`  
excludeGroupIds | `String` |   
excludes | `List` |   
image | `[Image](api/java/org/springframework/boot/maven/Image.html)` |   
includeSystemScope | `boolean` | `false`  
includeTools | `boolean` | `true`  
includes | `List` |   
layers | `[Layers](api/java/org/springframework/boot/maven/Layers.html)` |   
layout | `[LayoutType](api/java/org/springframework/boot/maven/AbstractPackagerMojo.LayoutType.html)` |   
layoutFactory | `[LayoutFactory](../api/java/org/springframework/boot/loader/tools/LayoutFactory.html)` |   
loaderImplementation | `[LoaderImplementation](../api/java/org/springframework/boot/loader/tools/LoaderImplementation.html)` |   
mainClass | `String` |   
skip | `boolean` | `false`  
  
### Parameter details

#### `classifier`

Classifier used when finding the source archive.

Name | `classifier`  
---|---  
Type | `java.lang.String`  
Default value |   
User property |   
Since | `2.3.0`  
  
#### `docker`

Docker configuration options.

Name | `docker`  
---|---  
Type | `[org.springframework.boot.maven.Docker](api/java/org/springframework/boot/maven/Docker.html)`  
Default value |   
User property |   
Since | `2.4.0`  
  
#### `excludeDevtools`

Exclude Spring Boot devtools from the repackaged archive.

Name | `excludeDevtools`  
---|---  
Type | `boolean`  
Default value | `true`  
User property | `spring-boot.repackage.excludeDevtools`  
Since | `1.3.0`  
  
#### `excludeDockerCompose`

Exclude Spring Boot dev services from the repackaged archive.

Name | `excludeDockerCompose`  
---|---  
Type | `boolean`  
Default value | `true`  
User property | `spring-boot.repackage.excludeDockerCompose`  
Since | `3.1.0`  
  
#### `excludeGroupIds`

Comma separated list of groupId names to exclude (exact match).

Name | `excludeGroupIds`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.excludeGroupIds`  
Since | `1.1.0`  
  
#### `excludes`

Collection of artifact definitions to exclude. The `Exclude` element defines mandatory `groupId` and `artifactId` components and an optional `classifier` component. When configured as a property, values should be comma-separated with colon-separated components: `groupId:artifactId,groupId:artifactId:classifier`

Name | `excludes`  
---|---  
Type | `java.util.List`  
Default value |   
User property | `spring-boot.excludes`  
Since | `1.1.0`  
  
#### `image`

Image configuration, with `builder`, `runImage`, `name`, `env`, `cleanCache`, `verboseLogging`, `pullPolicy`, and `publish` options.

Name | `image`  
---|---  
Type | `[org.springframework.boot.maven.Image](api/java/org/springframework/boot/maven/Image.html)`  
Default value |   
User property |   
Since | `2.3.0`  
  
#### `includeSystemScope`

Include system scoped dependencies.

Name | `includeSystemScope`  
---|---  
Type | `boolean`  
Default value | `false`  
User property |   
Since | `1.4.0`  
  
#### `includeTools`

Include JAR tools.

Name | `includeTools`  
---|---  
Type | `boolean`  
Default value | `true`  
User property |   
Since | `3.3.0`  
  
#### `includes`

Collection of artifact definitions to include. The `Include` element defines mandatory `groupId` and `artifactId` components and an optional `classifier` component. When configured as a property, values should be comma-separated with colon-separated components: `groupId:artifactId,groupId:artifactId:classifier`

Name | `includes`  
---|---  
Type | `java.util.List`  
Default value |   
User property | `spring-boot.includes`  
Since | `1.2.0`  
  
#### `layers`

Layer configuration with options to disable layer creation, exclude layer tools jar, and provide a custom layers configuration file.

Name | `layers`  
---|---  
Type | `[org.springframework.boot.maven.Layers](api/java/org/springframework/boot/maven/Layers.html)`  
Default value |   
User property |   
Since | `2.3.0`  
  
#### `layout`

The type of archive (which corresponds to how the dependencies are laid out inside it). Possible values are `JAR`, `WAR`, `ZIP`, `DIR`, `NONE`. Defaults to a guess based on the archive type.

Name | `layout`  
---|---  
Type | `[org.springframework.boot.maven.AbstractPackagerMojo$LayoutType](api/java/org/springframework/boot/maven/AbstractPackagerMojo.LayoutType.html)`  
Default value |   
User property |   
Since | `2.3.11`  
  
#### `layoutFactory`

The layout factory that will be used to create the executable archive if no explicit layout is set. Alternative layouts implementations can be provided by 3rd parties.

Name | `layoutFactory`  
---|---  
Type | `[org.springframework.boot.loader.tools.LayoutFactory](../api/java/org/springframework/boot/loader/tools/LayoutFactory.html)`  
Default value |   
User property |   
Since | `2.3.11`  
  
#### `loaderImplementation`

The loader implementation that should be used.

Name | `loaderImplementation`  
---|---  
Type | `[org.springframework.boot.loader.tools.LoaderImplementation](../api/java/org/springframework/boot/loader/tools/LoaderImplementation.html)`  
Default value |   
User property |   
Since | `3.2.0`  
  
#### `mainClass`

The name of the main class. If not specified the first compiled class found that contains a `main` method will be used.

Name | `mainClass`  
---|---  
Type | `java.lang.String`  
Default value |   
User property |   
Since | `1.0.0`  
  
#### `skip`

Skip the execution.

Name | `skip`  
---|---  
Type | `boolean`  
Default value | `false`  
User property | `spring-boot.build-image.skip`  
Since | `2.3.0`  
  
#### `sourceDirectory`

Directory containing the source archive.

Name | `sourceDirectory`  
---|---  
Type | `java.io.File`  
Default value | `${project.build.directory}`  
User property |   
Since | `2.3.0`  
  
## `spring-boot:build-image-no-fork`

`org.springframework.boot:spring-boot-maven-plugin:3.4.6`

Package an application into an OCI image using a buildpack, but without forking the lifecycle. This goal should be used when configuring a goal `execution` in your build. To invoke the goal on the command-line, use `build-image` instead.

### Required parameters

Name | Type | Default  
---|---|---  
sourceDirectory | `File` | `${project.build.directory}`  
  
### Optional parameters

Name | Type | Default  
---|---|---  
classifier | `String` |   
docker | `[Docker](api/java/org/springframework/boot/maven/Docker.html)` |   
excludeDevtools | `boolean` | `true`  
excludeDockerCompose | `boolean` | `true`  
excludeGroupIds | `String` |   
excludes | `List` |   
image | `[Image](api/java/org/springframework/boot/maven/Image.html)` |   
includeSystemScope | `boolean` | `false`  
includeTools | `boolean` | `true`  
includes | `List` |   
layers | `[Layers](api/java/org/springframework/boot/maven/Layers.html)` |   
layout | `[LayoutType](api/java/org/springframework/boot/maven/AbstractPackagerMojo.LayoutType.html)` |   
layoutFactory | `[LayoutFactory](../api/java/org/springframework/boot/loader/tools/LayoutFactory.html)` |   
loaderImplementation | `[LoaderImplementation](../api/java/org/springframework/boot/loader/tools/LoaderImplementation.html)` |   
mainClass | `String` |   
skip | `boolean` | `false`  
  
### Parameter details

#### `classifier`

Classifier used when finding the source archive.

Name | `classifier`  
---|---  
Type | `java.lang.String`  
Default value |   
User property |   
Since | `2.3.0`  
  
#### `docker`

Docker configuration options.

Name | `docker`  
---|---  
Type | `[org.springframework.boot.maven.Docker](api/java/org/springframework/boot/maven/Docker.html)`  
Default value |   
User property |   
Since | `2.4.0`  
  
#### `excludeDevtools`

Exclude Spring Boot devtools from the repackaged archive.

Name | `excludeDevtools`  
---|---  
Type | `boolean`  
Default value | `true`  
User property | `spring-boot.repackage.excludeDevtools`  
Since | `1.3.0`  
  
#### `excludeDockerCompose`

Exclude Spring Boot dev services from the repackaged archive.

Name | `excludeDockerCompose`  
---|---  
Type | `boolean`  
Default value | `true`  
User property | `spring-boot.repackage.excludeDockerCompose`  
Since | `3.1.0`  
  
#### `excludeGroupIds`

Comma separated list of groupId names to exclude (exact match).

Name | `excludeGroupIds`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.excludeGroupIds`  
Since | `1.1.0`  
  
#### `excludes`

Collection of artifact definitions to exclude. The `Exclude` element defines mandatory `groupId` and `artifactId` components and an optional `classifier` component. When configured as a property, values should be comma-separated with colon-separated components: `groupId:artifactId,groupId:artifactId:classifier`

Name | `excludes`  
---|---  
Type | `java.util.List`  
Default value |   
User property | `spring-boot.excludes`  
Since | `1.1.0`  
  
#### `image`

Image configuration, with `builder`, `runImage`, `name`, `env`, `cleanCache`, `verboseLogging`, `pullPolicy`, and `publish` options.

Name | `image`  
---|---  
Type | `[org.springframework.boot.maven.Image](api/java/org/springframework/boot/maven/Image.html)`  
Default value |   
User property |   
Since | `2.3.0`  
  
#### `includeSystemScope`

Include system scoped dependencies.

Name | `includeSystemScope`  
---|---  
Type | `boolean`  
Default value | `false`  
User property |   
Since | `1.4.0`  
  
#### `includeTools`

Include JAR tools.

Name | `includeTools`  
---|---  
Type | `boolean`  
Default value | `true`  
User property |   
Since | `3.3.0`  
  
#### `includes`

Collection of artifact definitions to include. The `Include` element defines mandatory `groupId` and `artifactId` components and an optional `classifier` component. When configured as a property, values should be comma-separated with colon-separated components: `groupId:artifactId,groupId:artifactId:classifier`

Name | `includes`  
---|---  
Type | `java.util.List`  
Default value |   
User property | `spring-boot.includes`  
Since | `1.2.0`  
  
#### `layers`

Layer configuration with options to disable layer creation, exclude layer tools jar, and provide a custom layers configuration file.

Name | `layers`  
---|---  
Type | `[org.springframework.boot.maven.Layers](api/java/org/springframework/boot/maven/Layers.html)`  
Default value |   
User property |   
Since | `2.3.0`  
  
#### `layout`

The type of archive (which corresponds to how the dependencies are laid out inside it). Possible values are `JAR`, `WAR`, `ZIP`, `DIR`, `NONE`. Defaults to a guess based on the archive type.

Name | `layout`  
---|---  
Type | `[org.springframework.boot.maven.AbstractPackagerMojo$LayoutType](api/java/org/springframework/boot/maven/AbstractPackagerMojo.LayoutType.html)`  
Default value |   
User property |   
Since | `2.3.11`  
  
#### `layoutFactory`

The layout factory that will be used to create the executable archive if no explicit layout is set. Alternative layouts implementations can be provided by 3rd parties.

Name | `layoutFactory`  
---|---  
Type | `[org.springframework.boot.loader.tools.LayoutFactory](../api/java/org/springframework/boot/loader/tools/LayoutFactory.html)`  
Default value |   
User property |   
Since | `2.3.11`  
  
#### `loaderImplementation`

The loader implementation that should be used.

Name | `loaderImplementation`  
---|---  
Type | `[org.springframework.boot.loader.tools.LoaderImplementation](../api/java/org/springframework/boot/loader/tools/LoaderImplementation.html)`  
Default value |   
User property |   
Since | `3.2.0`  
  
#### `mainClass`

The name of the main class. If not specified the first compiled class found that contains a `main` method will be used.

Name | `mainClass`  
---|---  
Type | `java.lang.String`  
Default value |   
User property |   
Since | `1.0.0`  
  
#### `skip`

Skip the execution.

Name | `skip`  
---|---  
Type | `boolean`  
Default value | `false`  
User property | `spring-boot.build-image.skip`  
Since | `2.3.0`  
  
#### `sourceDirectory`

Directory containing the source archive.

Name | `sourceDirectory`  
---|---  
Type | `java.io.File`  
Default value | `${project.build.directory}`  
User property |   
Since | `2.3.0`  
  
## Examples

### Custom Image Builder

If you need to customize the builder used to create the image or the run image used to launch the built image, configure the plugin as shown in the following example:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<image>
    						<builder>mine/java-cnb-builder</builder>
    						<runImage>mine/java-cnb-run</runImage>
    					</image>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

This configuration will use a builder image with the name `mine/java-cnb-builder` and the tag `latest`, and the run image named `mine/java-cnb-run` and the tag `latest`.

The builder and run image can be specified on the command line as well, as shown in this example:
    
    
    $ mvn spring-boot:build-image -Dspring-boot.build-image.builder=mine/java-cnb-builder -Dspring-boot.build-image.runImage=mine/java-cnb-run
    
    Copied!

### Builder Configuration

If the builder exposes configuration options using environment variables, those can be set using the `env` attributes.

The following is an example of [configuring the JVM version](https://paketo.io/docs/buildpacks/language-family-buildpacks/java/#configuring-the-jvm-version) used by the Paketo Java buildpacks at build time:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<image>
    						<env>
    							<BP_JVM_VERSION>17</BP_JVM_VERSION>
    						</env>
    					</image>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

If there is a network proxy between the Docker daemon the builder runs in and network locations that buildpacks download artifacts from, you will need to configure the builder to use the proxy. When using the Paketo builder, this can be accomplished by setting the `HTTPS_PROXY` and/or `HTTP_PROXY` environment variables as show in the following example:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<image>
    						<env>
    							<HTTP_PROXY>http://proxy.example.com</HTTP_PROXY>
    							<HTTPS_PROXY>https://proxy.example.com</HTTPS_PROXY>
    						</env>
    					</image>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

### Runtime JVM Configuration

Paketo Java buildpacks [configure the JVM runtime environment](https://paketo.io/docs/buildpacks/language-family-buildpacks/java/#runtime-jvm-configuration) by setting the `JAVA_TOOL_OPTIONS` environment variable. The buildpack-provided `JAVA_TOOL_OPTIONS` value can be modified to customize JVM runtime behavior when the application image is launched in a container.

Environment variable modifications that should be stored in the image and applied to every deployment can be set as described in the [Paketo documentation](https://paketo.io/docs/buildpacks/configuration/#environment-variables) and shown in the following example:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<image>
    						<env>
    							<BPE_DELIM_JAVA_TOOL_OPTIONS xml:space="preserve"> </BPE_DELIM_JAVA_TOOL_OPTIONS>
    							<BPE_APPEND_JAVA_TOOL_OPTIONS>-XX:+HeapDumpOnOutOfMemoryError</BPE_APPEND_JAVA_TOOL_OPTIONS>
    						</env>
    					</image>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

### Custom Image Name

By default, the image name is inferred from the `artifactId` and the `version` of the project, something like `docker.io/library/${project.artifactId}:${project.version}`. You can take control over the name, as shown in the following example:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<image>
    						<name>example.com/library/${project.artifactId}</name>
    					</image>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

__ |  This configuration does not provide an explicit tag so `latest` is used. It is possible to specify a tag as well, either using `${project.version}`, any property available in the build or a hardcoded version.   
---|---  
  
The image name can be specified on the command line as well, as shown in this example:
    
    
    $ mvn spring-boot:build-image -Dspring-boot.build-image.imageName=example.com/library/my-app:v1
    
    Copied!

### Buildpacks

By default, the builder will use buildpacks included in the builder image and apply them in a pre-defined order. An alternative set of buildpacks can be provided to apply buildpacks that are not included in the builder, or to change the order of included buildpacks. When one or more buildpacks are provided, only the specified buildpacks will be applied.

The following example instructs the builder to use a custom buildpack packaged in a `.tgz` file, followed by a buildpack included in the builder.
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<image>
    						<buildpacks>
    							<buildpack>file:///path/to/example-buildpack.tgz</buildpack>
    							<buildpack>urn:cnb:builder:paketo-buildpacks/java</buildpack>
    						</buildpacks>
    					</image>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

Buildpacks can be specified in any of the forms shown below.

A buildpack located in a CNB Builder (version may be omitted if there is only one buildpack in the builder matching the `buildpack-id`):

  * `urn:cnb:builder:buildpack-id`

  * `urn:cnb:builder:buildpack-id@0.0.1`

  * `buildpack-id`

  * `buildpack-id@0.0.1`




A path to a directory containing buildpack content (not supported on Windows):

  * `file:///path/to/buildpack/`

  * `/path/to/buildpack/`




A path to a gzipped tar file containing buildpack content:

  * `file:///path/to/buildpack.tgz`

  * `/path/to/buildpack.tgz`




An OCI image containing a [packaged buildpack](https://buildpacks.io/docs/for-buildpack-authors/how-to/distribute-buildpacks/package-buildpack/):

  * `docker://example/buildpack`

  * `docker:///example/buildpack:latest`

  * `docker:///example/buildpack@sha256:45b23dee08…​`

  * `example/buildpack`

  * `example/buildpack:latest`

  * `example/buildpack@sha256:45b23dee08…​`




### Image Publishing

The generated image can be published to a Docker registry by enabling a `publish` option.

If the Docker registry requires authentication, the credentials can be configured using `docker.publishRegistry` parameters. If the Docker registry does not require authentication, the `docker.publishRegistry` configuration can be omitted.

__ |  The registry that the image will be published to is determined by the registry part of the image name (`docker.example.com` in these examples). If `docker.publishRegistry` credentials are configured and include a `url` parameter, this value is passed to the registry but is not used to determine the publishing registry location.   
---|---  
      
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<image>
    						<name>docker.example.com/library/${project.artifactId}</name>
    						<publish>true</publish>
    					</image>
    					<docker>
    						<publishRegistry>
    							<username>user</username>
    							<password>secret</password>
    						</publishRegistry>
    					</docker>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

The `publish` option can be specified on the command line as well, as shown in this example:
    
    
    $ mvn spring-boot:build-image -Dspring-boot.build-image.imageName=docker.example.com/library/my-app:v1 -Dspring-boot.build-image.publish=true
    
    Copied!

When using the `publish` option on the command line with authentication, you can provide credentials using properties as in this example:
    
    
    $ mvn spring-boot:build-image \
          -Ddocker.publishRegistry.username=user \
          -Ddocker.publishRegistry.password=secret \
          -Ddocker.publishRegistry.url=docker.example.com \
          -Dspring-boot.build-image.publish=true \
          -Dspring-boot.build-image.imageName=docker.example.com/library/my-app:v1
    
    Copied!

and reference the properties in the XML configuration:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<docker>
    						<publishRegistry>
    							<url>${docker.publishRegistry.url}</url>
    							<username>${docker.publishRegistry.username}</username>
    							<password>${docker.publishRegistry.password}</password>
    						</publishRegistry>
    					</docker>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

### Builder Cache and Workspace Configuration

The CNB builder caches layers that are used when building and launching an image. By default, these caches are stored as named volumes in the Docker daemon with names that are derived from the full name of the target image. If the image name changes frequently, for example when the project version is used as a tag in the image name, then the caches can be invalidated frequently.

The cache volumes can be configured to use alternative names to give more control over cache lifecycle as shown in the following example:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<image>
    						<buildCache>
    							<volume>
    								<name>cache-${project.artifactId}.build</name>
    							</volume>
    						</buildCache>
    						<launchCache>
    							<volume>
    								<name>cache-${project.artifactId}.launch</name>
    							</volume>
    						</launchCache>
    					</image>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

Builders and buildpacks need a location to store temporary files during image building. By default, this temporary build workspace is stored in a named volume.

The caches and the build workspace can be configured to use bind mounts instead of named volumes, as shown in the following example:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<image>
    						<buildWorkspace>
    							<bind>
    								<source>/tmp/cache-${project.artifactId}.work</source>
    							</bind>
    						</buildWorkspace>
    						<buildCache>
    							<bind>
    								<source>/tmp/cache-${project.artifactId}.build</source>
    							</bind>
    						</buildCache>
    						<launchCache>
    							<bind>
    								<source>/tmp/cache-${project.artifactId}.launch</source>
    							</bind>
    						</launchCache>
    					</image>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

### Docker Configuration

#### Docker Configuration for minikube

The plugin can communicate with the [Docker daemon provided by minikube](https://minikube.sigs.k8s.io/docs/tasks/docker_daemon/) instead of the default local connection.

On Linux and macOS, environment variables can be set using the command `eval $(minikube docker-env)` after minikube has been started.

The plugin can also be configured to use the minikube daemon by providing connection details similar to those shown in the following example:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<docker>
    						<host>tcp://**************:2376</host>
    						<tlsVerify>true</tlsVerify>
    						<certPath>/home/<USER>/.minikube/certs</certPath>
    					</docker>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

#### Docker Configuration for podman

The plugin can communicate with a [podman container engine](https://podman.io/).

The plugin can be configured to use podman local connection by providing connection details similar to those shown in the following example:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<docker>
    						<host>unix:///run/user/1000/podman/podman.sock</host>
    						<bindHostToBuilder>true</bindHostToBuilder>
    					</docker>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

__ |  With the `colima` CLI installed, the command `podman info --format='{{.Host.RemoteSocket.Path}}'` can be used to get the value for the `docker.host` configuration property shown in this example.   
---|---  
  
#### Docker Configuration for Colima

The plugin can communicate with the Docker daemon provided by [Colima](https://github.com/abiosoft/colima). The `DOCKER_HOST` environment variable can be set by using the following command:
    
    
    $ export DOCKER_HOST=$(docker context inspect colima -f '{{.Endpoints.docker.Host}}')
    
    Copied!

The plugin can also be configured to use Colima daemon by providing connection details similar to those shown in the following example:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<docker>
    						<host>unix:///${user.home}/.colima/docker.sock</host>
    					</docker>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

#### Docker Configuration for Authentication

If the builder or run image are stored in a private Docker registry that supports user authentication, authentication details can be provided using `docker.builderRegistry` parameters as shown in the following example:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<docker>
    						<builderRegistry>
    							<username>user</username>
    							<password>secret</password>
    							<url>https://docker.example.com/v1/</url>
    							<email><EMAIL></email>
    						</builderRegistry>
    					</docker>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

If the builder or run image is stored in a private Docker registry that supports token authentication, the token value can be provided using `docker.builderRegistry` parameters as shown in the following example:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<docker>
    						<builderRegistry>
    							<token>9cbaf023786cd7...</token>
    						</builderRegistry>
    					</docker>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

[Packaging Executable Archives](packaging.html) [Running your Application with Maven](run.html)
---
