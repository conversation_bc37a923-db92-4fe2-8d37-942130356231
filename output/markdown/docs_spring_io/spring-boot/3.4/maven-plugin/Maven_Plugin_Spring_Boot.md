Title: Maven Plugin :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/maven-plugin/index.html
HTML: html/docs_spring_io/spring-boot/3.4/maven-plugin/Maven_Plugin_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/maven-plugin/Maven_Plugin_Spring_Boot.png
crawled_at: 2025-06-04T16:55:42.110640
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-maven-plugin/src/docs/antora/modules/maven-plugin/pages/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Maven Plugin](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../maven-plugin/index.html)!  
---|---  
  
# Maven Plugin

The Spring Boot Maven Plugin provides Spring Boot support in [Apache Maven](https://maven.org). It allows you to package executable jar or war archives, run Spring Boot applications, generate build information and start your Spring Boot application prior to running integration tests.

To use it, you must use Maven 3.6.3 or later.

In addition to this user guide, [API documentation](api/java/index.html) is also available.

[Build Tool Plugins](../build-tool-plugin/index.html) [Getting Started](getting-started.html)
---
