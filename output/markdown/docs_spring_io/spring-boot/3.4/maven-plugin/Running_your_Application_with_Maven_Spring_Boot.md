Title: Running your Application with Maven :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/maven-plugin/run.html
HTML: html/docs_spring_io/spring-boot/3.4/maven-plugin/Running_your_Application_with_Maven_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/maven-plugin/Running_your_Application_with_Maven_Spring_Boot.png
crawled_at: 2025-06-04T16:56:08.778004
---
Search CTRL + k

### Running your Application with Maven

  * spring-boot:run
  * Required parameters
  * Optional parameters
  * Parameter details
  * spring-boot:test-run
  * Required parameters
  * Optional parameters
  * Parameter details
  * Examples
  * Debug the Application
  * Using System Properties
  * Using Environment Variables
  * Using Application Arguments
  * Specify Active Profiles



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-maven-plugin/src/docs/antora/modules/maven-plugin/pages/run.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Maven Plugin](index.html)
  * [Running your Application with Maven](run.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../maven-plugin/run.html)!  
---|---  
  
# Running your Application with Maven

### Running your Application with Maven

  * spring-boot:run
  * Required parameters
  * Optional parameters
  * Parameter details
  * spring-boot:test-run
  * Required parameters
  * Optional parameters
  * Parameter details
  * Examples
  * Debug the Application
  * Using System Properties
  * Using Environment Variables
  * Using Application Arguments
  * Specify Active Profiles



The plugin includes a run goal which can be used to launch your application from the command line, as shown in the following example:
    
    
    $ mvn spring-boot:run
    
    Copied!

Application arguments can be specified using the `arguments` parameter, see using application arguments for more details.

The application is executed in a forked process and setting properties on the command-line will not affect the application. If you need to specify some JVM arguments (that is for debugging purposes), you can use the `jvmArguments` parameter, see Debug the application for more details. There is also explicit support for system properties and environment variables.

As enabling a profile is quite common, there is dedicated `profiles` property that offers a shortcut for `-Dspring-boot.run.jvmArguments="-Dspring.profiles.active=dev"`, see Specify active profiles.

Spring Boot `devtools` is a module to improve the development-time experience when working on Spring Boot applications. To enable it, just add the following dependency to your project:
    
    
    <dependencies>
    	<dependency>
    		<groupId>org.springframework.boot</groupId>
    		<artifactId>spring-boot-devtools</artifactId>
    		<optional>true</optional>
    	</dependency>
    </dependencies>
    
    Copied!

When `devtools` is running, it detects changes when you recompile your application and automatically refreshes it. This works for not only resources but code as well. It also provides a LiveReload server so that it can automatically trigger a browser refresh whenever things change.

Devtools can also be configured to only refresh the browser whenever a static resource has changed (and ignore any change in the code). Just include the following property in your project:
    
    
    spring.devtools.remote.restart.enabled=false
    
    Copied!

Prior to `devtools`, the plugin supported hot refreshing of resources by default which has now been disabled in favour of the solution described above. You can restore it at any time by configuring your project:
    
    
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<addResources>true</addResources>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    
    Copied!

When `addResources` is enabled, any `src/main/resources` directory will be added to the application classpath when you run the application and any duplicate found in the classes output will be removed. This allows hot refreshing of resources which can be very useful when developing web applications. For example, you can work on HTML, CSS or JavaScript files and see your changes immediately without recompiling your application. It is also a helpful way of allowing your front end developers to work without needing to download and install a Java IDE.

__ |  A side effect of using this feature is that filtering of resources at build time will not work.   
---|---  
  
In order to be consistent with the `repackage` goal, the `run` goal builds the classpath in such a way that any dependency that is excluded in the plugin’s configuration gets excluded from the classpath as well. For more details, see [the dedicated example](packaging.html#packaging.examples.exclude-dependency).

Sometimes it is useful to run a test variant of your application. For example, if you want to [use Testcontainers at development time](../reference/features/dev-services.html#features.dev-services.testcontainers.at-development-time) or make use of some test stubs. Use the `test-run` goal with many of the same features and configuration options as `run` for this purpose.

## `spring-boot:run`

`org.springframework.boot:spring-boot-maven-plugin:3.4.6`

Run an application in place.

### Required parameters

Name | Type | Default  
---|---|---  
classesDirectory | `File` | `${project.build.outputDirectory}`  
  
### Optional parameters

Name | Type | Default  
---|---|---  
addResources | `boolean` | `false`  
additionalClasspathElements | `String[]` |   
agents | `File[]` |   
arguments | `String[]` |   
commandlineArguments | `String` |   
environmentVariables | `Map` |   
excludeGroupIds | `String` |   
excludes | `List` |   
includes | `List` |   
jvmArguments | `String` |   
mainClass | `String` |   
noverify | `boolean` |   
optimizedLaunch | `boolean` | `true`  
profiles | `String[]` |   
skip | `boolean` | `false`  
systemPropertyVariables | `Map` |   
useTestClasspath | `Boolean` | `false`  
workingDirectory | `File` |   
  
### Parameter details

#### `addResources`

Add maven resources to the classpath directly, this allows live in-place editing of resources. Duplicate resources are removed from `target/classes` to prevent them from appearing twice if `ClassLoader.getResources()` is called. Please consider adding `spring-boot-devtools` to your project instead as it provides this feature and many more.

Name | `addResources`  
---|---  
Type | `boolean`  
Default value | `false`  
User property | `spring-boot.run.addResources`  
Since | `1.0.0`  
  
#### `additionalClasspathElements`

Additional classpath elements that should be added to the classpath. An element can be a directory with classes and resources or a jar file.

Name | `additionalClasspathElements`  
---|---  
Type | `java.lang.String[]`  
Default value |   
User property | `spring-boot.run.additional-classpath-elements`  
Since | `3.2.0`  
  
#### `agents`

Path to agent jars.

Name | `agents`  
---|---  
Type | `java.io.File[]`  
Default value |   
User property | `spring-boot.run.agents`  
Since | `2.2.0`  
  
#### `arguments`

Arguments that should be passed to the application.

Name | `arguments`  
---|---  
Type | `java.lang.String[]`  
Default value |   
User property |   
Since | `1.0.0`  
  
#### `classesDirectory`

Directory containing the classes and resource files that should be used to run the application.

Name | `classesDirectory`  
---|---  
Type | `java.io.File`  
Default value | `${project.build.outputDirectory}`  
User property |   
Since | `1.0.0`  
  
#### `commandlineArguments`

Arguments from the command line that should be passed to the application. Use spaces to separate multiple arguments and make sure to wrap multiple values between quotes. When specified, takes precedence over `#arguments`.

Name | `commandlineArguments`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.run.arguments`  
Since | `2.2.3`  
  
#### `environmentVariables`

List of Environment variables that should be associated with the forked process used to run the application.

Name | `environmentVariables`  
---|---  
Type | `java.util.Map`  
Default value |   
User property |   
Since | `2.1.0`  
  
#### `excludeGroupIds`

Comma separated list of groupId names to exclude (exact match).

Name | `excludeGroupIds`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.excludeGroupIds`  
Since | `1.1.0`  
  
#### `excludes`

Collection of artifact definitions to exclude. The `Exclude` element defines mandatory `groupId` and `artifactId` components and an optional `classifier` component. When configured as a property, values should be comma-separated with colon-separated components: `groupId:artifactId,groupId:artifactId:classifier`

Name | `excludes`  
---|---  
Type | `java.util.List`  
Default value |   
User property | `spring-boot.excludes`  
Since | `1.1.0`  
  
#### `includes`

Collection of artifact definitions to include. The `Include` element defines mandatory `groupId` and `artifactId` components and an optional `classifier` component. When configured as a property, values should be comma-separated with colon-separated components: `groupId:artifactId,groupId:artifactId:classifier`

Name | `includes`  
---|---  
Type | `java.util.List`  
Default value |   
User property | `spring-boot.includes`  
Since | `1.2.0`  
  
#### `jvmArguments`

JVM arguments that should be associated with the forked process used to run the application. On command line, make sure to wrap multiple values between quotes.

Name | `jvmArguments`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.run.jvmArguments`  
Since | `1.1.0`  
  
#### `mainClass`

The name of the main class. If not specified the first compiled class found that contains a 'main' method will be used.

Name | `mainClass`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.run.main-class`  
Since | `1.0.0`  
  
#### `noverify`

Flag to say that the agent requires -noverify.

Name | `noverify`  
---|---  
Type | `boolean`  
Default value |   
User property | `spring-boot.run.noverify`  
Since | `1.0.0`  
  
#### `optimizedLaunch`

Whether the JVM’s launch should be optimized.

Name | `optimizedLaunch`  
---|---  
Type | `boolean`  
Default value | `true`  
User property | `spring-boot.run.optimizedLaunch`  
Since | `2.2.0`  
  
#### `profiles`

The spring profiles to activate. Convenience shortcut of specifying the 'spring.profiles.active' argument. On command line use commas to separate multiple profiles.

Name | `profiles`  
---|---  
Type | `java.lang.String[]`  
Default value |   
User property | `spring-boot.run.profiles`  
Since | `1.3.0`  
  
#### `skip`

Skip the execution.

Name | `skip`  
---|---  
Type | `boolean`  
Default value | `false`  
User property | `spring-boot.run.skip`  
Since | `1.3.2`  
  
#### `systemPropertyVariables`

List of JVM system properties to pass to the process.

Name | `systemPropertyVariables`  
---|---  
Type | `java.util.Map`  
Default value |   
User property |   
Since | `2.1.0`  
  
#### `useTestClasspath`

Flag to include the test classpath when running.

Name | `useTestClasspath`  
---|---  
Type | `java.lang.Boolean`  
Default value | `false`  
User property | `spring-boot.run.useTestClasspath`  
Since | `1.3.0`  
  
#### `workingDirectory`

Current working directory to use for the application. If not specified, basedir will be used.

Name | `workingDirectory`  
---|---  
Type | `java.io.File`  
Default value |   
User property | `spring-boot.run.workingDirectory`  
Since | `1.5.0`  
  
## `spring-boot:test-run`

`org.springframework.boot:spring-boot-maven-plugin:3.4.6`

Run an application in place using the test runtime classpath. The main class that will be used to launch the application is determined as follows: The configured main class, if any. Then the main class found in the test classes directory, if any. Then the main class found in the classes directory, if any.

### Required parameters

Name | Type | Default  
---|---|---  
classesDirectory | `File` | `${project.build.outputDirectory}`  
testClassesDirectory | `File` | `${project.build.testOutputDirectory}`  
  
### Optional parameters

Name | Type | Default  
---|---|---  
addResources | `boolean` | `false`  
additionalClasspathElements | `String[]` |   
agents | `File[]` |   
arguments | `String[]` |   
commandlineArguments | `String` |   
environmentVariables | `Map` |   
excludeGroupIds | `String` |   
excludes | `List` |   
includes | `List` |   
jvmArguments | `String` |   
mainClass | `String` |   
noverify | `boolean` |   
optimizedLaunch | `boolean` | `true`  
profiles | `String[]` |   
skip | `boolean` | `false`  
systemPropertyVariables | `Map` |   
workingDirectory | `File` |   
  
### Parameter details

#### `addResources`

Add maven resources to the classpath directly, this allows live in-place editing of resources. Duplicate resources are removed from `target/classes` to prevent them from appearing twice if `ClassLoader.getResources()` is called. Please consider adding `spring-boot-devtools` to your project instead as it provides this feature and many more.

Name | `addResources`  
---|---  
Type | `boolean`  
Default value | `false`  
User property | `spring-boot.run.addResources`  
Since | `1.0.0`  
  
#### `additionalClasspathElements`

Additional classpath elements that should be added to the classpath. An element can be a directory with classes and resources or a jar file.

Name | `additionalClasspathElements`  
---|---  
Type | `java.lang.String[]`  
Default value |   
User property | `spring-boot.run.additional-classpath-elements`  
Since | `3.2.0`  
  
#### `agents`

Path to agent jars.

Name | `agents`  
---|---  
Type | `java.io.File[]`  
Default value |   
User property | `spring-boot.run.agents`  
Since | `2.2.0`  
  
#### `arguments`

Arguments that should be passed to the application.

Name | `arguments`  
---|---  
Type | `java.lang.String[]`  
Default value |   
User property |   
Since | `1.0.0`  
  
#### `classesDirectory`

Directory containing the classes and resource files that should be used to run the application.

Name | `classesDirectory`  
---|---  
Type | `java.io.File`  
Default value | `${project.build.outputDirectory}`  
User property |   
Since | `1.0.0`  
  
#### `commandlineArguments`

Arguments from the command line that should be passed to the application. Use spaces to separate multiple arguments and make sure to wrap multiple values between quotes. When specified, takes precedence over `#arguments`.

Name | `commandlineArguments`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.run.arguments`  
Since | `2.2.3`  
  
#### `environmentVariables`

List of Environment variables that should be associated with the forked process used to run the application.

Name | `environmentVariables`  
---|---  
Type | `java.util.Map`  
Default value |   
User property |   
Since | `2.1.0`  
  
#### `excludeGroupIds`

Comma separated list of groupId names to exclude (exact match).

Name | `excludeGroupIds`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.excludeGroupIds`  
Since | `1.1.0`  
  
#### `excludes`

Collection of artifact definitions to exclude. The `Exclude` element defines mandatory `groupId` and `artifactId` components and an optional `classifier` component. When configured as a property, values should be comma-separated with colon-separated components: `groupId:artifactId,groupId:artifactId:classifier`

Name | `excludes`  
---|---  
Type | `java.util.List`  
Default value |   
User property | `spring-boot.excludes`  
Since | `1.1.0`  
  
#### `includes`

Collection of artifact definitions to include. The `Include` element defines mandatory `groupId` and `artifactId` components and an optional `classifier` component. When configured as a property, values should be comma-separated with colon-separated components: `groupId:artifactId,groupId:artifactId:classifier`

Name | `includes`  
---|---  
Type | `java.util.List`  
Default value |   
User property | `spring-boot.includes`  
Since | `1.2.0`  
  
#### `jvmArguments`

JVM arguments that should be associated with the forked process used to run the application. On command line, make sure to wrap multiple values between quotes.

Name | `jvmArguments`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.run.jvmArguments`  
Since | `1.1.0`  
  
#### `mainClass`

The name of the main class. If not specified the first compiled class found that contains a 'main' method will be used.

Name | `mainClass`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.run.main-class`  
Since | `1.0.0`  
  
#### `noverify`

Flag to say that the agent requires -noverify.

Name | `noverify`  
---|---  
Type | `boolean`  
Default value |   
User property | `spring-boot.run.noverify`  
Since | `1.0.0`  
  
#### `optimizedLaunch`

Whether the JVM’s launch should be optimized.

Name | `optimizedLaunch`  
---|---  
Type | `boolean`  
Default value | `true`  
User property | `spring-boot.test-run.optimizedLaunch`  
Since |   
  
#### `profiles`

The spring profiles to activate. Convenience shortcut of specifying the 'spring.profiles.active' argument. On command line use commas to separate multiple profiles.

Name | `profiles`  
---|---  
Type | `java.lang.String[]`  
Default value |   
User property | `spring-boot.run.profiles`  
Since | `1.3.0`  
  
#### `skip`

Skip the execution.

Name | `skip`  
---|---  
Type | `boolean`  
Default value | `false`  
User property | `spring-boot.run.skip`  
Since | `1.3.2`  
  
#### `systemPropertyVariables`

List of JVM system properties to pass to the process.

Name | `systemPropertyVariables`  
---|---  
Type | `java.util.Map`  
Default value |   
User property |   
Since | `2.1.0`  
  
#### `testClassesDirectory`

Directory containing the test classes and resource files that should be used to run the application.

Name | `testClassesDirectory`  
---|---  
Type | `java.io.File`  
Default value | `${project.build.testOutputDirectory}`  
User property |   
Since |   
  
#### `workingDirectory`

Current working directory to use for the application. If not specified, basedir will be used.

Name | `workingDirectory`  
---|---  
Type | `java.io.File`  
Default value |   
User property | `spring-boot.run.workingDirectory`  
Since | `1.5.0`  
  
## Examples

### Debug the Application

The `run` and `test-run` goals run your application in a forked process. If you need to debug it, you should add the necessary JVM arguments to enable remote debugging. The following configuration suspend the process until a debugger has joined on port 5005:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<jvmArguments>
    						-agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=*:5005
    					</jvmArguments>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

These arguments can be specified on the command line as well:
    
    
    $ mvn spring-boot:run -Dspring-boot.run.jvmArguments=-agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=*:5005
    
    Copied!

### Using System Properties

System properties can be specified using the `systemPropertyVariables` attribute. The following example sets `property1` to `test` and `property2` to 42:
    
    
    <project>
    	<build>
    		<properties>
    			<my.value>42</my.value>
    		</properties>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<systemPropertyVariables>
    						<property1>test</property1>
    						<property2>${my.value}</property2>
    					</systemPropertyVariables>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

If the value is empty or not defined (that is `<my-property/`>), the system property is set with an empty String as the value. Maven trims values specified in the pom, so it is not possible to specify a System property which needs to start or end with a space through this mechanism: consider using `jvmArguments` instead.

Any String typed Maven variable can be passed as system properties. Any attempt to pass any other Maven variable type (for example a `List` or a `URL` variable) will cause the variable expression to be passed literally (unevaluated).

The `jvmArguments` parameter takes precedence over system properties defined with the mechanism above. In the following example, the value for `property1` is `overridden`:
    
    
    $ mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Dproperty1=overridden"
    
    Copied!

### Using Environment Variables

Environment variables can be specified using the `environmentVariables` attribute. The following example sets the 'ENV1', 'ENV2', 'ENV3', 'ENV4' env variables:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<environmentVariables>
    						<ENV1>5000</ENV1>
    						<ENV2>Some Text</ENV2>
    						<ENV3/>
    						<ENV4></ENV4>
    					</environmentVariables>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

If the value is empty or not defined (that is `<MY_ENV/`>), the env variable is set with an empty String as the value. Maven trims values specified in the pom so it is not possible to specify an env variable which needs to start or end with a space.

Any String typed Maven variable can be passed as system properties. Any attempt to pass any other Maven variable type (for example a `List` or a `URL` variable) will cause the variable expression to be passed literally (unevaluated).

Environment variables defined this way take precedence over existing values.

### Using Application Arguments

Application arguments can be specified using the `arguments` attribute. The following example sets two arguments: `property1` and `property2=42`:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<arguments>
    						<argument>property1</argument>
    						<argument>property2=${my.value}</argument>
    					</arguments>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

On the command-line, arguments are separated by a space the same way `jvmArguments` are. If an argument contains a space, make sure to quote it. In the following example, two arguments are available: `property1` and `property2=Hello World`:
    
    
    $ mvn spring-boot:run -Dspring-boot.run.arguments="property1 'property2=Hello World'"
    
    Copied!

### Specify Active Profiles

The active profiles to use for a particular application can be specified using the `profiles` argument.

The following configuration enables the `local` and `dev` profiles:
    
    
    <project>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<configuration>
    					<profiles>
    						<profile>local</profile>
    						<profile>dev</profile>
    					</profiles>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

The profiles to enable can be specified on the command line as well, make sure to separate them with a comma, as shown in the following example:
    
    
    $ mvn spring-boot:run -Dspring-boot.run.profiles=local,dev
    
    Copied!

[Packaging OCI Images](build-image.html) [Ahead-of-Time Processing](aot.html)
---
