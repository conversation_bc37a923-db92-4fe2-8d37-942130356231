Title: Integrating with Actuator :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/maven-plugin/build-info.html
HTML: html/docs_spring_io/spring-boot/3.4/maven-plugin/Integrating_with_Actuator_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/maven-plugin/Integrating_with_Actuator_Spring_Boot.png
crawled_at: 2025-06-04T19:22:37.227012
---
Search CTRL + k

### Integrating with Actuator

  * spring-boot:build-info
  * Optional parameters
  * Parameter details



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-maven-plugin/src/docs/antora/modules/maven-plugin/pages/build-info.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Maven Plugin](index.html)
  * [Integrating with Actuator](build-info.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../maven-plugin/build-info.html)!  
---|---  
  
# Integrating with Actuator

### Integrating with Actuator

  * spring-boot:build-info
  * Optional parameters
  * Parameter details



Spring Boot Actuator displays build-related information if a `META-INF/build-info.properties` file is present. The `build-info` goal generates such file with the coordinates of the project and the build time. It also allows you to add an arbitrary number of additional properties, as shown in the following example:
    
    
    <project>
    	<modelVersion>4.0.0</modelVersion>
    	<artifactId>build-info</artifactId>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<executions>
    					<execution>
    						<goals>
    							<goal>build-info</goal>
    						</goals>
    						<configuration>
    							<additionalProperties>
    								<encoding.source>UTF-8</encoding.source>
    								<encoding.reporting>UTF-8</encoding.reporting>
    								<java.version>${java.version}</java.version>
    							</additionalProperties>
    						</configuration>
    					</execution>
    				</executions>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

This configuration will generate a `build-info.properties` at the expected location with three additional keys.

__ |  `java.version` is expected to be a regular property available in the project. It will be interpolated as you would expect.   
---|---  
  
## `spring-boot:build-info`

`org.springframework.boot:spring-boot-maven-plugin:3.4.6`

Generate a `build-info.properties` file based on the content of the current `MavenProject`.

### Optional parameters

Name | Type | Default  
---|---|---  
additionalProperties | `Map` |   
excludeInfoProperties | `List` |   
outputFile | `File` | `${project.build.outputDirectory}/META-INF/build-info.properties`  
skip | `boolean` | `false`  
time | `String` | `${project.build.outputTimestamp}`  
  
### Parameter details

#### `additionalProperties`

Additional properties to store in the `build-info.properties` file. Each entry is prefixed by `build.` in the generated `build-info.properties`.

Name | `additionalProperties`  
---|---  
Type | `java.util.Map`  
Default value |   
User property |   
Since |   
  
#### `excludeInfoProperties`

Properties that should be excluded `build-info.properties` file. Can be used to exclude the standard `group`, `artifact`, `name`, `version` or `time` properties as well as items from `additionalProperties`.

Name | `excludeInfoProperties`  
---|---  
Type | `java.util.List`  
Default value |   
User property |   
Since |   
  
#### `outputFile`

The location of the generated `build-info.properties` file.

Name | `outputFile`  
---|---  
Type | `java.io.File`  
Default value | `${project.build.outputDirectory}/META-INF/build-info.properties`  
User property |   
Since |   
  
#### `skip`

Skip the execution.

Name | `skip`  
---|---  
Type | `boolean`  
Default value | `false`  
User property | `spring-boot.build-info.skip`  
Since | `3.1.0`  
  
#### `time`

The value used for the `build.time` property in a form suitable for `Instant#parse(CharSequence)`. Defaults to `project.build.outputTimestamp` or `session.request.startTime` if the former is not set. To disable the `build.time` property entirely, use `'off'` or add it to `excludeInfoProperties`.

Name | `time`  
---|---  
Type | `java.lang.String`  
Default value | `${project.build.outputTimestamp}`  
User property |   
Since | `2.2.0`  
  
[Running Integration Tests](integration-tests.html) [Help Information](help.html)
---
