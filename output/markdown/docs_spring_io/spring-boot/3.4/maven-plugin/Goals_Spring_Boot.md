Title: Goals :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/maven-plugin/goals.html
HTML: html/docs_spring_io/spring-boot/3.4/maven-plugin/Goals_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/maven-plugin/Goals_Spring_Boot.png
crawled_at: 2025-06-04T16:55:44.538882
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-maven-plugin/src/docs/antora/modules/maven-plugin/pages/goals.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Maven Plugin](index.html)
  * [Goals](goals.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../maven-plugin/goals.html)!  
---|---  
  
# Goals

The Spring Boot Plugin has the following goals:

Goal | Description  
---|---  
[spring-boot:build-image](build-image.html#build-image.build-image-goal) | Package an application into an OCI image using a buildpack, forking the lifecycle to make sure that `package` ran. This goal is suitable for command-line invocation. If you need to configure a goal `execution` in your build, use `build-image-no-fork` instead.  
[spring-boot:build-image-no-fork](build-image.html#build-image.build-image-no-fork-goal) | Package an application into an OCI image using a buildpack, but without forking the lifecycle. This goal should be used when configuring a goal `execution` in your build. To invoke the goal on the command-line, use `build-image` instead.  
[spring-boot:build-info](build-info.html#build-info.build-info-goal) | Generate a `build-info.properties` file based on the content of the current `MavenProject`.  
[spring-boot:help](help.html#help.help-goal) | Display help information on spring-boot-maven-plugin. Call `mvn spring-boot:help -Ddetail=true -Dgoal=<goal-name>` to display parameter details.  
[spring-boot:process-aot](aot.html#aot.process-aot-goal) | Invoke the AOT engine on the application.  
[spring-boot:process-test-aot](aot.html#aot.process-test-aot-goal) | Invoke the AOT engine on tests.  
[spring-boot:repackage](packaging.html#packaging.repackage-goal) | Repackage existing JAR and WAR archives so that they can be executed from the command line using `java -jar`. With `layout=NONE` can also be used simply to package a JAR with nested dependencies (and no main class, so not executable).  
[spring-boot:run](run.html#run.run-goal) | Run an application in place.  
[spring-boot:start](integration-tests.html#integration-tests.start-goal) | Start a spring application. Contrary to the `run` goal, this does not block and allows other goals to operate on the application. This goal is typically used in integration test scenario where the application is started before a test suite and stopped after.  
[spring-boot:stop](integration-tests.html#integration-tests.stop-goal) | Stop an application that has been started by the "start" goal. Typically invoked once a test suite has completed.  
[spring-boot:test-run](run.html#run.test-run-goal) | Run an application in place using the test runtime classpath. The main class that will be used to launch the application is determined as follows: The configured main class, if any. Then the main class found in the test classes directory, if any. Then the main class found in the classes directory, if any.  
[Using the Plugin](using.html) [Packaging Executable Archives](packaging.html)
---
