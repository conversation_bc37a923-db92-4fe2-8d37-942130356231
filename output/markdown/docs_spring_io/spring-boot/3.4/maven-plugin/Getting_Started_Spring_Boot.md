Title: Getting Started :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/maven-plugin/getting-started.html
HTML: html/docs_spring_io/spring-boot/3.4/maven-plugin/Getting_Started_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/maven-plugin/Getting_Started_Spring_Boot.png
crawled_at: 2025-06-04T16:56:01.813279
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-maven-plugin/src/docs/antora/modules/maven-plugin/pages/getting-started.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Maven Plugin](index.html)
  * [Getting Started](getting-started.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../maven-plugin/getting-started.html)!  
---|---  
  
# Getting Started

To use the Spring Boot Maven Plugin, include the appropriate XML in the `plugins` section of your `pom.xml`, as shown in the following example:
    
    
    <project>
    	<modelVersion>4.0.0</modelVersion>
    	<artifactId>getting-started</artifactId>
    	<!-- ... -->
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

If you use a milestone or snapshot release, you also need to add the appropriate `pluginRepository` elements, as shown in the following listing:
    
    
    <pluginRepositories>
    	<pluginRepository>
    		<id>spring-snapshots</id>
    		<url>https://repo.spring.io/snapshot</url>
    	</pluginRepository>
    	<pluginRepository>
    		<id>spring-milestones</id>
    		<url>https://repo.spring.io/milestone</url>
    	</pluginRepository>
    </pluginRepositories>
    
    Copied!

[Maven Plugin](index.html) [Using the Plugin](using.html)
---
