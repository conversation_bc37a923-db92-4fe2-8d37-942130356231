Title: Running Integration Tests :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/maven-plugin/integration-tests.html
HTML: html/docs_spring_io/spring-boot/3.4/maven-plugin/Running_Integration_Tests_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/maven-plugin/Running_Integration_Tests_Spring_Boot.png
crawled_at: 2025-06-04T19:23:06.857719
---
Search CTRL + k

### Running Integration Tests

  * Using Failsafe Without Spring Boot’s Parent POM
  * spring-boot:start
  * Required parameters
  * Optional parameters
  * Parameter details
  * spring-boot:stop
  * Optional parameters
  * Parameter details
  * Examples
  * Random Port for Integration Tests
  * Customize JMX Port
  * Skip Integration Tests



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-maven-plugin/src/docs/antora/modules/maven-plugin/pages/integration-tests.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Maven Plugin](index.html)
  * [Running Integration Tests](integration-tests.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../maven-plugin/integration-tests.html)!  
---|---  
  
# Running Integration Tests

### Running Integration Tests

  * Using Failsafe Without Spring Boot’s Parent POM
  * spring-boot:start
  * Required parameters
  * Optional parameters
  * Parameter details
  * spring-boot:stop
  * Optional parameters
  * Parameter details
  * Examples
  * Random Port for Integration Tests
  * Customize JMX Port
  * Skip Integration Tests



While you may start your Spring Boot application very easily from your test (or test suite) itself, it may be desirable to handle that in the build itself. To make sure that the lifecycle of your Spring Boot application is properly managed around your integration tests, you can use the `start` and `stop` goals, as shown in the following example:
    
    
    <build>
    	<plugins>
    		<plugin>
    			<groupId>org.springframework.boot</groupId>
    			<artifactId>spring-boot-maven-plugin</artifactId>
    			<executions>
    				<execution>
    					<id>pre-integration-test</id>
    					<goals>
    						<goal>start</goal>
    					</goals>
    				</execution>
    				<execution>
    					<id>post-integration-test</id>
    					<goals>
    						<goal>stop</goal>
    					</goals>
    				</execution>
    			</executions>
    		</plugin>
    	</plugins>
    </build>
    
    Copied!

Such setup can now use the [failsafe-plugin](https://maven.apache.org/surefire/maven-failsafe-plugin) to run your integration tests as you would expect.

__ |  The application is started in a separate process and JMX is used to communicate with the application. By default, the plugin uses port `9001`. If you need to configure the JMX port, see the dedicated example.   
---|---  
  
You could also configure a more advanced setup to skip the integration tests when a specific property has been set, see the dedicated example.

## Using Failsafe Without Spring Boot’s Parent POM

Spring Boot’s Parent POM, `spring-boot-starter-parent`, configures Failsafe’s `<classesDirectory>` to be `${project.build.outputDirectory}`. Without this configuration, which causes Failsafe to use the compiled classes rather than the repackaged jar, Failsafe cannot load your application’s classes. If you are not using the parent POM, you should configure Failsafe in the same way, as shown in the following example:
    
    
    <plugin>
    	<groupId>org.apache.maven.plugins</groupId>
    	<artifactId>maven-failsafe-plugin</artifactId>
    	<configuration>
    		<classesDirectory>${project.build.outputDirectory}</classesDirectory>
    	</configuration>
    </plugin>
    
    Copied!

## `spring-boot:start`

`org.springframework.boot:spring-boot-maven-plugin:3.4.6`

Start a spring application. Contrary to the `run` goal, this does not block and allows other goals to operate on the application. This goal is typically used in integration test scenario where the application is started before a test suite and stopped after.

### Required parameters

Name | Type | Default  
---|---|---  
classesDirectory | `File` | `${project.build.outputDirectory}`  
  
### Optional parameters

Name | Type | Default  
---|---|---  
addResources | `boolean` | `false`  
additionalClasspathElements | `String[]` |   
agents | `File[]` |   
arguments | `String[]` |   
commandlineArguments | `String` |   
environmentVariables | `Map` |   
excludeGroupIds | `String` |   
excludes | `List` |   
includes | `List` |   
jmxName | `String` | `org.springframework.boot:type=Admin,name=SpringApplication`  
jmxPort | `int` | `9001`  
jvmArguments | `String` |   
mainClass | `String` |   
maxAttempts | `int` | `60`  
noverify | `boolean` |   
profiles | `String[]` |   
skip | `boolean` | `false`  
systemPropertyVariables | `Map` |   
useTestClasspath | `Boolean` | `false`  
wait | `long` | `500`  
workingDirectory | `File` |   
  
### Parameter details

#### `addResources`

Add maven resources to the classpath directly, this allows live in-place editing of resources. Duplicate resources are removed from `target/classes` to prevent them from appearing twice if `ClassLoader.getResources()` is called. Please consider adding `spring-boot-devtools` to your project instead as it provides this feature and many more.

Name | `addResources`  
---|---  
Type | `boolean`  
Default value | `false`  
User property | `spring-boot.run.addResources`  
Since | `1.0.0`  
  
#### `additionalClasspathElements`

Additional classpath elements that should be added to the classpath. An element can be a directory with classes and resources or a jar file.

Name | `additionalClasspathElements`  
---|---  
Type | `java.lang.String[]`  
Default value |   
User property | `spring-boot.run.additional-classpath-elements`  
Since | `3.2.0`  
  
#### `agents`

Path to agent jars.

Name | `agents`  
---|---  
Type | `java.io.File[]`  
Default value |   
User property | `spring-boot.run.agents`  
Since | `2.2.0`  
  
#### `arguments`

Arguments that should be passed to the application.

Name | `arguments`  
---|---  
Type | `java.lang.String[]`  
Default value |   
User property |   
Since | `1.0.0`  
  
#### `classesDirectory`

Directory containing the classes and resource files that should be used to run the application.

Name | `classesDirectory`  
---|---  
Type | `java.io.File`  
Default value | `${project.build.outputDirectory}`  
User property |   
Since | `1.0.0`  
  
#### `commandlineArguments`

Arguments from the command line that should be passed to the application. Use spaces to separate multiple arguments and make sure to wrap multiple values between quotes. When specified, takes precedence over `#arguments`.

Name | `commandlineArguments`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.run.arguments`  
Since | `2.2.3`  
  
#### `environmentVariables`

List of Environment variables that should be associated with the forked process used to run the application.

Name | `environmentVariables`  
---|---  
Type | `java.util.Map`  
Default value |   
User property |   
Since | `2.1.0`  
  
#### `excludeGroupIds`

Comma separated list of groupId names to exclude (exact match).

Name | `excludeGroupIds`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.excludeGroupIds`  
Since | `1.1.0`  
  
#### `excludes`

Collection of artifact definitions to exclude. The `Exclude` element defines mandatory `groupId` and `artifactId` components and an optional `classifier` component. When configured as a property, values should be comma-separated with colon-separated components: `groupId:artifactId,groupId:artifactId:classifier`

Name | `excludes`  
---|---  
Type | `java.util.List`  
Default value |   
User property | `spring-boot.excludes`  
Since | `1.1.0`  
  
#### `includes`

Collection of artifact definitions to include. The `Include` element defines mandatory `groupId` and `artifactId` components and an optional `classifier` component. When configured as a property, values should be comma-separated with colon-separated components: `groupId:artifactId,groupId:artifactId:classifier`

Name | `includes`  
---|---  
Type | `java.util.List`  
Default value |   
User property | `spring-boot.includes`  
Since | `1.2.0`  
  
#### `jmxName`

The JMX name of the automatically deployed MBean managing the lifecycle of the spring application.

Name | `jmxName`  
---|---  
Type | `java.lang.String`  
Default value | `org.springframework.boot:type=Admin,name=SpringApplication`  
User property |   
Since |   
  
#### `jmxPort`

The port to use to expose the platform MBeanServer.

Name | `jmxPort`  
---|---  
Type | `int`  
Default value | `9001`  
User property |   
Since |   
  
#### `jvmArguments`

JVM arguments that should be associated with the forked process used to run the application. On command line, make sure to wrap multiple values between quotes.

Name | `jvmArguments`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.run.jvmArguments`  
Since | `1.1.0`  
  
#### `mainClass`

The name of the main class. If not specified the first compiled class found that contains a 'main' method will be used.

Name | `mainClass`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `spring-boot.run.main-class`  
Since | `1.0.0`  
  
#### `maxAttempts`

The maximum number of attempts to check if the spring application is ready. Combined with the "wait" argument, this gives a global timeout value (30 sec by default)

Name | `maxAttempts`  
---|---  
Type | `int`  
Default value | `60`  
User property | `spring-boot.start.maxAttempts`  
Since |   
  
#### `noverify`

Flag to say that the agent requires -noverify.

Name | `noverify`  
---|---  
Type | `boolean`  
Default value |   
User property | `spring-boot.run.noverify`  
Since | `1.0.0`  
  
#### `profiles`

The spring profiles to activate. Convenience shortcut of specifying the 'spring.profiles.active' argument. On command line use commas to separate multiple profiles.

Name | `profiles`  
---|---  
Type | `java.lang.String[]`  
Default value |   
User property | `spring-boot.run.profiles`  
Since | `1.3.0`  
  
#### `skip`

Skip the execution.

Name | `skip`  
---|---  
Type | `boolean`  
Default value | `false`  
User property | `spring-boot.run.skip`  
Since | `1.3.2`  
  
#### `systemPropertyVariables`

List of JVM system properties to pass to the process.

Name | `systemPropertyVariables`  
---|---  
Type | `java.util.Map`  
Default value |   
User property |   
Since | `2.1.0`  
  
#### `useTestClasspath`

Flag to include the test classpath when running.

Name | `useTestClasspath`  
---|---  
Type | `java.lang.Boolean`  
Default value | `false`  
User property | `spring-boot.run.useTestClasspath`  
Since |   
  
#### `wait`

The number of milliseconds to wait between each attempt to check if the spring application is ready.

Name | `wait`  
---|---  
Type | `long`  
Default value | `500`  
User property | `spring-boot.start.wait`  
Since |   
  
#### `workingDirectory`

Current working directory to use for the application. If not specified, basedir will be used.

Name | `workingDirectory`  
---|---  
Type | `java.io.File`  
Default value |   
User property | `spring-boot.run.workingDirectory`  
Since | `1.5.0`  
  
## `spring-boot:stop`

`org.springframework.boot:spring-boot-maven-plugin:3.4.6`

Stop an application that has been started by the "start" goal. Typically invoked once a test suite has completed.

### Optional parameters

Name | Type | Default  
---|---|---  
jmxName | `String` | `org.springframework.boot:type=Admin,name=SpringApplication`  
jmxPort | `int` | `9001`  
skip | `boolean` | `false`  
  
### Parameter details

#### `jmxName`

The JMX name of the automatically deployed MBean managing the lifecycle of the application.

Name | `jmxName`  
---|---  
Type | `java.lang.String`  
Default value | `org.springframework.boot:type=Admin,name=SpringApplication`  
User property |   
Since |   
  
#### `jmxPort`

The port to use to look up the platform MBeanServer.

Name | `jmxPort`  
---|---  
Type | `int`  
Default value | `9001`  
User property |   
Since |   
  
#### `skip`

Skip the execution.

Name | `skip`  
---|---  
Type | `boolean`  
Default value | `false`  
User property | `spring-boot.stop.skip`  
Since | `1.3.2`  
  
## Examples

### Random Port for Integration Tests

One nice feature of the Spring Boot test integration is that it can allocate a free port for the web application. When the `start` goal of the plugin is used, the Spring Boot application is started separately, making it difficult to pass the actual port to the integration test itself.

The example below showcases how you could achieve the same feature using the [Build Helper Maven Plugin](https://www.mojohaus.org/build-helper-maven-plugin):
    
    
    <build>
    	<plugins>
    		<plugin>
    			<groupId>org.codehaus.mojo</groupId>
    			<artifactId>build-helper-maven-plugin</artifactId>
    			<executions>
    				<execution>
    					<id>reserve-tomcat-port</id>
    					<goals>
    						<goal>reserve-network-port</goal>
    					</goals>
    					<phase>process-resources</phase>
    					<configuration>
    						<portNames>
    							<portName>tomcat.http.port</portName>
    						</portNames>
    					</configuration>
    				</execution>
    			</executions>
    		</plugin>
    		<plugin>
    			<groupId>org.springframework.boot</groupId>
    			<artifactId>spring-boot-maven-plugin</artifactId>
    			<executions>
    				<execution>
    					<id>pre-integration-test</id>
    					<goals>
    						<goal>start</goal>
    					</goals>
    					<configuration>
    						<arguments>
    							<argument>--server.port=${tomcat.http.port}</argument>
    						</arguments>
    					</configuration>
    				</execution>
    				<execution>
    					<id>post-integration-test</id>
    					<goals>
    						<goal>stop</goal>
    					</goals>
    				</execution>
    			</executions>
    		</plugin>
    		<plugin>
    			<groupId>org.apache.maven.plugins</groupId>
    			<artifactId>maven-failsafe-plugin</artifactId>
    			<configuration>
    				<systemPropertyVariables>
    					<test.server.port>${tomcat.http.port}</test.server.port>
    				</systemPropertyVariables>
    			</configuration>
    		</plugin>
    	</plugins>
    </build>
    
    Copied!

You can now retrieve the `test.server.port` system property in any of your integration test to create a proper `URL` to the server.

### Customize JMX Port

The `jmxPort` property allows to customize the port the plugin uses to communicate with the Spring Boot application.

This example shows how you can customize the port in case `9001` is already used:
    
    
    <build>
    	<plugins>
    		<plugin>
    			<groupId>org.springframework.boot</groupId>
    			<artifactId>spring-boot-maven-plugin</artifactId>
    			<configuration>
    				<jmxPort>9009</jmxPort>
    			</configuration>
    			<executions>
    				<execution>
    					<id>pre-integration-test</id>
    					<goals>
    						<goal>start</goal>
    					</goals>
    				</execution>
    				<execution>
    					<id>post-integration-test</id>
    					<goals>
    						<goal>stop</goal>
    					</goals>
    				</execution>
    			</executions>
    		</plugin>
    	</plugins>
    </build>
    
    Copied!

__ |  If you need to configure the JMX port, make sure to do so in the global configuration as shown above so that it is shared by both goals.   
---|---  
  
### Skip Integration Tests

The `skip` property allows to skip the execution of the Spring Boot maven plugin altogether.

This example shows how you can skip integration tests with a command-line property and still make sure that the `repackage` goal runs:
    
    
    <project>
    	<properties>
    		<skip.it>false</skip.it>
    	</properties>
    	<build>
    		<plugins>
    			<plugin>
    				<groupId>org.springframework.boot</groupId>
    				<artifactId>spring-boot-maven-plugin</artifactId>
    				<executions>
    					<execution>
    						<id>pre-integration-test</id>
    						<goals>
    							<goal>start</goal>
    						</goals>
    						<configuration>
    							<skip>${skip.it}</skip>
    						</configuration>
    					</execution>
    					<execution>
    						<id>post-integration-test</id>
    						<goals>
    							<goal>stop</goal>
    						</goals>
    						<configuration>
    							<skip>${skip.it}</skip>
    						</configuration>
    					</execution>
    				</executions>
    			</plugin>
    			<plugin>
    				<groupId>org.apache.maven.plugins</groupId>
    				<artifactId>maven-failsafe-plugin</artifactId>
    				<configuration>
    					<skip>${skip.it}</skip>
    				</configuration>
    			</plugin>
    		</plugins>
    	</build>
    </project>
    
    Copied!

By default, the integration tests will run but this setup allows you to easily disable them on the command-line as follows:
    
    
    $ mvn verify -Dskip.it=true
    
    Copied!

[Ahead-of-Time Processing](aot.html) [Integrating with Actuator](build-info.html)
---
