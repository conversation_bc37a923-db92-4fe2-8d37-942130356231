Title: Help Information :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/maven-plugin/help.html
HTML: html/docs_spring_io/spring-boot/3.4/maven-plugin/Help_Information_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/maven-plugin/Help_Information_Spring_Boot.png
crawled_at: 2025-06-04T16:55:55.680344
---
Search CTRL + k

### Help Information

  * spring-boot:help
  * Optional parameters
  * Parameter details



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-tools/spring-boot-maven-plugin/src/docs/antora/modules/maven-plugin/pages/help.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../index.html)
  * [Build Tool Plugins](../build-tool-plugin/index.html)
  * [Maven Plugin](index.html)
  * [Help Information](help.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../maven-plugin/help.html)!  
---|---  
  
# Help Information

### Help Information

  * spring-boot:help
  * Optional parameters
  * Parameter details



## `spring-boot:help`

`org.springframework.boot:spring-boot-maven-plugin:3.4.6`

Display help information on spring-boot-maven-plugin. Call `mvn spring-boot:help -Ddetail=true -Dgoal=<goal-name>` to display parameter details.

### Optional parameters

Name | Type | Default  
---|---|---  
detail | `boolean` | `false`  
goal | `String` |   
indentSize | `int` | `2`  
lineLength | `int` | `80`  
  
### Parameter details

#### `detail`

If `true`, display all settable properties for each goal.

Name | `detail`  
---|---  
Type | `boolean`  
Default value | `false`  
User property | `detail`  
Since |   
  
#### `goal`

The name of the goal for which to show help. If unspecified, all goals will be displayed.

Name | `goal`  
---|---  
Type | `java.lang.String`  
Default value |   
User property | `goal`  
Since |   
  
#### `indentSize`

The number of spaces per indentation level, should be positive.

Name | `indentSize`  
---|---  
Type | `int`  
Default value | `2`  
User property | `indentSize`  
Since |   
  
#### `lineLength`

The maximum length of a display line, should be positive.

Name | `lineLength`  
---|---  
Type | `int`  
Default value | `80`  
User property | `lineLength`  
Since |   
  
[Integrating with Actuator](build-info.html) [Gradle Plugin](../gradle-plugin/index.html)
---
