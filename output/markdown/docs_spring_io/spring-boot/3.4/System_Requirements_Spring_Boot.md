Title: System Requirements :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/system-requirements.html
HTML: html/docs_spring_io/spring-boot/3.4/System_Requirements_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/System_Requirements_Spring_Boot.png
crawled_at: 2025-06-04T16:04:33.317410
---
Search CTRL + k

### System Requirements

  * Servlet Containers
  * GraalVM Native Images



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/ROOT/pages/system-requirements.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](index.html)
  * [System Requirements](system-requirements.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../system-requirements.html)!  
---|---  
  
# System Requirements

### System Requirements

  * Servlet Containers
  * GraalVM Native Images



Spring Boot 3.4.6 requires at least [Java 17](https://www.java.com) and is compatible with versions up to and including Java 24. [Spring Framework 6.2.7](https://docs.spring.io/spring-framework/reference/6.2/) or above is also required.

Explicit build support is provided for the following build tools:

Build Tool | Version  
---|---  
Maven | 3.6.3 or later  
Gradle | Gradle 7.x (7.6.4 or later) or 8.x (8.4 or later)  
  
## Servlet Containers

Spring Boot supports the following embedded servlet containers:

Name | Servlet Version  
---|---  
Tomcat 10.1 (10.1.25 or later) | 6.0  
Jetty 12.0 | 6.0  
Undertow 2.3 | 6.0  
  
You can also deploy Spring Boot applications to any servlet 5.0+ compatible container.

## GraalVM Native Images

Spring Boot applications can be [converted into a Native Image](reference/packaging/native-image/introducing-graalvm-native-images.html) using GraalVM 22.3 or above.

Images can be created using the [native build tools](https://github.com/graalvm/native-build-tools) Gradle/Maven plugins or `native-image` tool provided by GraalVM. You can also create native images using the [native-image Paketo buildpack](https://github.com/paketo-buildpacks/native-image).

The following versions are supported:

Name | Version  
---|---  
GraalVM Community | 22.3  
Native Build Tools | 0.10.6  
  
[Community](community.html) [Installing Spring Boot](installing.html)
---
