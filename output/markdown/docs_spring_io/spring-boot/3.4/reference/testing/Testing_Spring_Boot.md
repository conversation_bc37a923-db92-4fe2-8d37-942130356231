Title: Testing :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/testing/index.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/testing/Testing_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/testing/Testing_Spring_Boot.png
crawled_at: 2025-06-04T19:22:13.042473
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/testing/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Testing](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/testing/index.html)!  
---|---  
  
# Testing

Spring Boot provides a number of utilities and annotations to help when testing your application. Test support is provided by two modules: `spring-boot-test` contains core items, and `spring-boot-test-autoconfigure` supports auto-configuration for tests.

Most developers use the `spring-boot-starter-test` starter, which imports both Spring Boot test modules as well as JUnit Jupiter, AssertJ, Hamcrest, and a number of other useful libraries.

__ |  If you have tests that use JUnit 4, JUnit 5’s vintage engine can be used to run them. To use the vintage engine, add a dependency on `junit-vintage-engine`, as shown in the following example:
    
    
    <dependency>
    	<groupId>org.junit.vintage</groupId>
    	<artifactId>junit-vintage-engine</artifactId>
    	<scope>test</scope>
    	<exclusions>
    		<exclusion>
    			<groupId>org.hamcrest</groupId>
    			<artifactId>hamcrest-core</artifactId>
    		</exclusion>
    	</exclusions>
    </dependency>Copied!  
  
---|---  
  
`hamcrest-core` is excluded in favor of `org.hamcrest:hamcrest` that is part of `spring-boot-starter-test`.

[WebSockets](../messaging/websockets.html) [Test Scope Dependencies](test-scope-dependencies.html)
---
