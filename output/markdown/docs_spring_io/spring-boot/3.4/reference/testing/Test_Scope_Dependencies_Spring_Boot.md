Title: Test Scope Dependencies :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/testing/test-scope-dependencies.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/testing/Test_Scope_Dependencies_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/testing/Test_Scope_Dependencies_Spring_Boot.png
crawled_at: 2025-06-04T16:03:47.304027
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/testing/test-scope-dependencies.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Testing](index.html)
  * [Test Scope Dependencies](test-scope-dependencies.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/testing/test-scope-dependencies.html)!  
---|---  
  
# Test Scope Dependencies

The `spring-boot-starter-test` starter (in the `test` `scope`) contains the following provided libraries:

  * [JUnit 5](https://junit.org/junit5/): The de-facto standard for unit testing Java applications.

  * [Spring Test](https://docs.spring.io/spring-framework/reference/6.2/testing/integration.html) & Spring Boot Test: Utilities and integration test support for Spring Boot applications.

  * [AssertJ](https://assertj.github.io/doc/): A fluent assertion library.

  * [Hamcrest](https://github.com/hamcrest/JavaHamcrest): A library of matcher objects (also known as constraints or predicates).

  * [Mockito](https://site.mockito.org/): A Java mocking framework.

  * [JSONassert](https://github.com/skyscreamer/JSONassert): An assertion library for JSON.

  * [JsonPath](https://github.com/jayway/JsonPath): XPath for JSON.

  * [Awaitility](https://github.com/awaitility/awaitility): A library for testing asynchronous systems.




We generally find these common libraries to be useful when writing tests. If these libraries do not suit your needs, you can add additional test dependencies of your own.

[Testing](index.html) [Testing Spring Applications](spring-applications.html)
---
