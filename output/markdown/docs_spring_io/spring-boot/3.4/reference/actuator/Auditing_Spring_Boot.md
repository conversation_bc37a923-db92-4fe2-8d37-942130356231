Title: Auditing :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/actuator/auditing.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/actuator/Auditing_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/actuator/Auditing_Spring_Boot.png
crawled_at: 2025-06-04T16:07:28.361892
---
Search CTRL + k

### Auditing

  * Custom Auditing



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/actuator/auditing.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Production-ready Features](index.html)
  * [Auditing](auditing.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/actuator/auditing.html)!  
---|---  
  
# Auditing

### Auditing

  * Custom Auditing



Once Spring Security is in play, Spring Boot Actuator has a flexible audit framework that publishes events (by default, “authentication success”, “failure” and “access denied” exceptions). This feature can be very useful for reporting and for implementing a lock-out policy based on authentication failures.

You can enable auditing by providing a bean of type [`AuditEventRepository`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/audit/AuditEventRepository.html) in your application’s configuration. For convenience, Spring Boot offers an [`InMemoryAuditEventRepository`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/audit/InMemoryAuditEventRepository.html). [`InMemoryAuditEventRepository`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/audit/InMemoryAuditEventRepository.html) has limited capabilities, and we recommend using it only for development environments. For production environments, consider creating your own alternative [`AuditEventRepository`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/audit/AuditEventRepository.html) implementation.

## Custom Auditing

To customize published security events, you can provide your own implementations of [`AbstractAuthenticationAuditListener`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/security/AbstractAuthenticationAuditListener.html) and [`AbstractAuthorizationAuditListener`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/security/AbstractAuthorizationAuditListener.html).

You can also use the audit services for your own business events. To do so, either inject the [`AuditEventRepository`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/audit/AuditEventRepository.html) bean into your own components and use that directly or publish an [`AuditApplicationEvent`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/audit/listener/AuditApplicationEvent.html) with the Spring [`ApplicationEventPublisher`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationEventPublisher.html) (by implementing [`ApplicationEventPublisherAware`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationEventPublisherAware.html)).

[Tracing](tracing.html) [Recording HTTP Exchanges](http-exchanges.html)
---
