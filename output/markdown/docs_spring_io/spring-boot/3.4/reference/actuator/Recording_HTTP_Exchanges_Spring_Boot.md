Title: Recording HTTP Exchanges :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/actuator/http-exchanges.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/actuator/Recording_HTTP_Exchanges_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/actuator/Recording_HTTP_Exchanges_Spring_Boot.png
crawled_at: 2025-06-04T16:02:03.234347
---
Search CTRL + k

### Recording HTTP Exchanges

  * Custom HTTP Exchange Recording



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/actuator/http-exchanges.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Production-ready Features](index.html)
  * [Recording HTTP Exchanges](http-exchanges.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/actuator/http-exchanges.html)!  
---|---  
  
# Recording HTTP Exchanges

### Recording HTTP Exchanges

  * Custom HTTP Exchange Recording



You can enable recording of HTTP exchanges by providing a bean of type [`HttpExchangeRepository`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/web/exchanges/HttpExchangeRepository.html) in your application’s configuration. For convenience, Spring Boot offers [`InMemoryHttpExchangeRepository`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/web/exchanges/InMemoryHttpExchangeRepository.html), which, by default, stores the last 100 request-response exchanges. [`InMemoryHttpExchangeRepository`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/web/exchanges/InMemoryHttpExchangeRepository.html) is limited compared to tracing solutions, and we recommend using it only for development environments. For production environments, we recommend using a production-ready tracing or observability solution, such as Zipkin or OpenTelemetry. Alternatively, you can create your own [`HttpExchangeRepository`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/web/exchanges/HttpExchangeRepository.html).

You can use the `httpexchanges` endpoint to obtain information about the request-response exchanges that are stored in the [`HttpExchangeRepository`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/actuate/web/exchanges/HttpExchangeRepository.html).

## Custom HTTP Exchange Recording

To customize the items that are included in each recorded exchange, use the `management.httpexchanges.recording.include` configuration property.

To disable recording entirely, set `management.httpexchanges.recording.enabled` to `false`.

[Auditing](auditing.html) [Process Monitoring](process-monitoring.html)
---
