Title: Process Monitoring :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/actuator/process-monitoring.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/actuator/Process_Monitoring_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/actuator/Process_Monitoring_Spring_Boot.png
crawled_at: 2025-06-04T16:01:25.134530
---
Search CTRL + k

### Process Monitoring

  * Extending Configuration
  * Programmatically Enabling Process Monitoring



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/actuator/process-monitoring.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Production-ready Features](index.html)
  * [Process Monitoring](process-monitoring.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/actuator/process-monitoring.html)!  
---|---  
  
# Process Monitoring

### Process Monitoring

  * Extending Configuration
  * Programmatically Enabling Process Monitoring



In the `spring-boot` module, you can find two classes to create files that are often useful for process monitoring:

  * [`ApplicationPidFileWriter`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/ApplicationPidFileWriter.html) creates a file that contains the application PID (by default, in the application directory with a file name of `application.pid`).

  * [`WebServerPortFileWriter`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/web/context/WebServerPortFileWriter.html) creates a file (or files) that contain the ports of the running web server (by default, in the application directory with a file name of `application.port`).




By default, these writers are not activated, but you can enable them:

  * Extending Configuration

  * Programmatically Enabling Process Monitoring




## Extending Configuration

In the `META-INF/spring.factories` file, you can activate the listener (or listeners) that writes a PID file:
    
    
    org.springframework.context.ApplicationListener=\
    org.springframework.boot.context.ApplicationPidFileWriter,\
    org.springframework.boot.web.context.WebServerPortFileWriter
    
    Copied!

## Programmatically Enabling Process Monitoring

You can also activate a listener by invoking the `SpringApplication.addListeners(…​)` method and passing the appropriate [`Writer`](https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Writer.html) object. This method also lets you customize the file name and path in the [`Writer`](https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Writer.html) constructor.

[Recording HTTP Exchanges](http-exchanges.html) [Cloud Foundry Support](cloud-foundry.html)
---
