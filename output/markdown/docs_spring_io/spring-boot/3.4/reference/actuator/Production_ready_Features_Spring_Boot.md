Title: Production-ready Features :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/actuator/index.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/actuator/Production_ready_Features_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/actuator/Production_ready_Features_Spring_Boot.png
crawled_at: 2025-06-04T19:23:02.718784
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/actuator/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Production-ready Features](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/actuator/index.html)!  
---|---  
  
# Production-ready Features

Spring Boot includes a number of additional features to help you monitor and manage your application when you push it to production. You can choose to manage and monitor your application by using HTTP endpoints or with JMX. Auditing, health, and metrics gathering can also be automatically applied to your application.

[Cloud Native Buildpacks](../packaging/container-images/cloud-native-buildpacks.html) [Enabling Production-ready Features](enabling.html)
---
