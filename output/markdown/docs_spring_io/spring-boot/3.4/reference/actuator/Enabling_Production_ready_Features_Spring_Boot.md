Title: Enabling Production-ready Features :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/actuator/enabling.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/actuator/Enabling_Production_ready_Features_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/actuator/Enabling_Production_ready_Features_Spring_Boot.png
crawled_at: 2025-06-04T16:01:58.001665
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/actuator/enabling.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Production-ready Features](index.html)
  * [Enabling Production-ready Features](enabling.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/actuator/enabling.html)!  
---|---  
  
# Enabling Production-ready Features

The [`spring-boot-actuator`](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot-actuator) module provides all of Spring Boot’s production-ready features. The recommended way to enable the features is to add a dependency on the `spring-boot-starter-actuator` starter.

Definition of Actuator

An actuator is a manufacturing term that refers to a mechanical device for moving or controlling something. Actuators can generate a large amount of motion from a small change.

To add the actuator to a Maven-based project, add the following starter dependency:
    
    
    <dependencies>
    	<dependency>
    		<groupId>org.springframework.boot</groupId>
    		<artifactId>spring-boot-starter-actuator</artifactId>
    	</dependency>
    </dependencies>
    
    Copied!

For Gradle, use the following declaration:
    
    
    dependencies {
    	implementation 'org.springframework.boot:spring-boot-starter-actuator'
    }
    
    Copied!

[Production-ready Features](index.html) [Endpoints](endpoints.html)
---
