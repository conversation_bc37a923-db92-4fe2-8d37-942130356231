Title: Structuring Your Code :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/using/structuring-your-code.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/using/Structuring_Your_Code_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/using/Structuring_Your_Code_Spring_Boot.png
crawled_at: 2025-06-04T16:00:11.009913
---
Search CTRL + k

### Structuring Your Code

  * Using the “default” Package
  * Locating the Main Application Class



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/using/structuring-your-code.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Developing with Spring Boot](index.html)
  * [Structuring Your Code](structuring-your-code.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/using/structuring-your-code.html)!  
---|---  
  
# Structuring Your Code

### Structuring Your Code

  * Using the “default” Package
  * Locating the Main Application Class



Spring Boot does not require any specific code layout to work. However, there are some best practices that help.

__ |  If you wish to enforce a structure based on domains, take a look at [Spring Modulith](https://spring.io/projects/spring-modulith#overview).   
---|---  
  
## Using the “default” Package

When a class does not include a `package` declaration, it is considered to be in the “default package”. The use of the “default package” is generally discouraged and should be avoided. It can cause particular problems for Spring Boot applications that use the [`@ComponentScan`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/ComponentScan.html), [`@ConfigurationPropertiesScan`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/ConfigurationPropertiesScan.html), [`@EntityScan`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/domain/EntityScan.html), or [`@SpringBootApplication`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html) annotations, since every class from every jar is read.

__ |  We recommend that you follow Java’s recommended package naming conventions and use a reversed domain name (for example, `com.example.project`).   
---|---  
  
## Locating the Main Application Class

We generally recommend that you locate your main application class in a root package above other classes. The [`@SpringBootApplication` annotation](using-the-springbootapplication-annotation.html) is often placed on your main class, and it implicitly defines a base “search package” for certain items. For example, if you are writing a JPA application, the package of the [`@SpringBootApplication`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html) annotated class is used to search for [`@Entity`](https://jakarta.ee/specifications/persistence/3.1/apidocs/jakarta.persistence/jakarta/persistence/Entity.html) items. Using a root package also allows component scan to apply only on your project.

__ |  If you do not want to use [`@SpringBootApplication`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html), the [`@EnableAutoConfiguration`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/EnableAutoConfiguration.html) and [`@ComponentScan`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/ComponentScan.html) annotations that it imports defines that behavior so you can also use those instead.   
---|---  
  
The following listing shows a typical layout:
    
    
    com
     +- example
         +- myapplication
             +- MyApplication.java
             |
             +- customer
             |   +- Customer.java
             |   +- CustomerController.java
             |   +- CustomerService.java
             |   +- CustomerRepository.java
             |
             +- order
                 +- Order.java
                 +- OrderController.java
                 +- OrderService.java
                 +- OrderRepository.java
    
    Copied!

The `MyApplication.java` file would declare the `main` method, along with the basic [`@SpringBootApplication`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html), as follows:

  * Java

  * Kotlin



    
    
    import org.springframework.boot.SpringApplication;
    import org.springframework.boot.autoconfigure.SpringBootApplication;
    
    @SpringBootApplication
    public class MyApplication {
    
    	public static void main(String[] args) {
    		SpringApplication.run(MyApplication.class, args);
    	}
    
    }
    
    Copied!
    
    
    import org.springframework.boot.autoconfigure.SpringBootApplication
    import org.springframework.boot.runApplication
    
    @SpringBootApplication
    class MyApplication
    
    fun main(args: Array<String>) {
    	runApplication<MyApplication>(*args)
    }
    
    Copied!

[Build Systems](build-systems.html) [Configuration Classes](configuration-classes.html)
---
