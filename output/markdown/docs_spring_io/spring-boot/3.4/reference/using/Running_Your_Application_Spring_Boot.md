Title: Running Your Application :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/using/running-your-application.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/using/Running_Your_Application_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/using/Running_Your_Application_Spring_Boot.png
crawled_at: 2025-06-04T16:07:57.699302
---
Search CTRL + k

### Running Your Application

  * Running From an IDE
  * Running as a Packaged Application
  * Using the Maven Plugin
  * Using the Gradle Plugin
  * Hot Swapping



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/using/running-your-application.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Developing with Spring Boot](index.html)
  * [Running Your Application](running-your-application.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/using/running-your-application.html)!  
---|---  
  
# Running Your Application

### Running Your Application

  * Running From an IDE
  * Running as a Packaged Application
  * Using the Maven Plugin
  * Using the Gradle Plugin
  * Hot Swapping



One of the biggest advantages of packaging your application as a jar and using an embedded HTTP server is that you can run your application as you would any other. The same applies to debugging Spring Boot applications. You do not need any special IDE plugins or extensions.

__ |  The options below are best suited for running an application locally for development. For production deployment, see [Packaging Your Application for Production](packaging-for-production.html).   
---|---  
  
__ |  This section only covers jar-based packaging. If you choose to package your application as a war file, see your server and IDE documentation.   
---|---  
  
## Running From an IDE

You can run a Spring Boot application from your IDE as a Java application. However, you first need to import your project. Import steps vary depending on your IDE and build system. Most IDEs can import Maven projects directly. For example, Eclipse users can select `Import…​` → `Existing Maven Projects` from the `File` menu.

If you cannot directly import your project into your IDE, you may be able to generate IDE metadata by using a build plugin. Maven includes plugins for [Eclipse](https://maven.apache.org/plugins/maven-eclipse-plugin/) and [IDEA](https://maven.apache.org/plugins/maven-idea-plugin/). Gradle offers plugins for [various IDEs](https://docs.gradle.org/current/userguide/userguide.html).

__ |  If you accidentally run a web application twice, you see a “Port already in use” error. Spring Tools users can use the `Relaunch` button rather than the `Run` button to ensure that any existing instance is closed.   
---|---  
  
## Running as a Packaged Application

If you use the Spring Boot Maven or Gradle plugins to create an executable jar, you can run your application using `java -jar`, as shown in the following example:
    
    
    $ java -jar target/myapplication-0.0.1-SNAPSHOT.jar
    
    Copied!

It is also possible to run a packaged application with remote debugging support enabled. Doing so lets you attach a debugger to your packaged application, as shown in the following example:
    
    
    $ java -agentlib:jdwp=server=y,transport=dt_socket,address=8000,suspend=n \
           -jar target/myapplication-0.0.1-SNAPSHOT.jar
    
    Copied!

## Using the Maven Plugin

The Spring Boot Maven plugin includes a `run` goal that can be used to quickly compile and run your application. Applications run in an exploded form, as they do in your IDE. The following example shows a typical Maven command to run a Spring Boot application:
    
    
    $ mvn spring-boot:run
    
    Copied!

You might also want to use the `MAVEN_OPTS` operating system environment variable, as shown in the following example:
    
    
    $ export MAVEN_OPTS=-Xmx1024m
    
    Copied!

## Using the Gradle Plugin

The Spring Boot Gradle plugin also includes a `bootRun` task that can be used to run your application in an exploded form. The `bootRun` task is added whenever you apply the `org.springframework.boot` and `java` plugins and is shown in the following example:
    
    
    $ gradle bootRun
    
    Copied!

You might also want to use the `JAVA_OPTS` operating system environment variable, as shown in the following example:
    
    
    $ export JAVA_OPTS=-Xmx1024m
    
    Copied!

## Hot Swapping

Since Spring Boot applications are plain Java applications, JVM hot-swapping should work out of the box. JVM hot swapping is somewhat limited with the bytecode that it can replace. For a more complete solution, [JRebel](https://www.jrebel.com/products/jrebel) can be used.

The `spring-boot-devtools` module also includes support for quick application restarts. See the [Hot Swapping](../../how-to/hotswapping.html) section in “How-to Guides” for details.

[Using the @SpringBootApplication Annotation](using-the-springbootapplication-annotation.html) [Developer Tools](devtools.html)
---
