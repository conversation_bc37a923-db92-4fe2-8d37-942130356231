Title: Spring Beans and Dependency Injection :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/using/spring-beans-and-dependency-injection.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/using/Spring_Beans_and_Dependency_Injection_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/using/Spring_Beans_and_Dependency_Injection_Spring_Boot.png
crawled_at: 2025-06-04T16:05:51.998840
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/using/spring-beans-and-dependency-injection.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Developing with Spring Boot](index.html)
  * [Spring Beans and Dependency Injection](spring-beans-and-dependency-injection.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/using/spring-beans-and-dependency-injection.html)!  
---|---  
  
# Spring Beans and Dependency Injection

You are free to use any of the standard Spring Framework techniques to define your beans and their injected dependencies. We generally recommend using constructor injection to wire up dependencies and [`@ComponentScan`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/ComponentScan.html) to find beans.

If you structure your code as suggested above (locating your application class in a top package), you can add [`@ComponentScan`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/ComponentScan.html) without any arguments or use the [`@SpringBootApplication`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html) annotation which implicitly includes it. All of your application components ([`@Component`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Component.html), [`@Service`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Service.html), [`@Repository`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Repository.html), [`@Controller`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Controller.html), and others) are automatically registered as Spring Beans.

The following example shows a [`@Service`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/stereotype/Service.html) Bean that uses constructor injection to obtain a required `RiskAssessor` bean:

  * Java

  * Kotlin



    
    
    import org.springframework.stereotype.Service;
    
    @Service
    public class MyAccountService implements AccountService {
    
    	private final RiskAssessor riskAssessor;
    
    	public MyAccountService(RiskAssessor riskAssessor) {
    		this.riskAssessor = riskAssessor;
    	}
    
    	// ...
    
    }
    
    Copied!
    
    
    import org.springframework.stereotype.Service
    
    @Service
    class MyAccountService(private val riskAssessor: RiskAssessor) : AccountService
    
    Copied!

If a bean has more than one constructor, you will need to mark the one you want Spring to use with [`@Autowired`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/beans/factory/annotation/Autowired.html):

  * Java

  * Kotlin



    
    
    import java.io.PrintStream;
    
    import org.springframework.beans.factory.annotation.Autowired;
    import org.springframework.stereotype.Service;
    
    @Service
    public class MyAccountService implements AccountService {
    
    	private final RiskAssessor riskAssessor;
    
    	private final PrintStream out;
    
    	@Autowired
    	public MyAccountService(RiskAssessor riskAssessor) {
    		this.riskAssessor = riskAssessor;
    		this.out = System.out;
    	}
    
    	public MyAccountService(RiskAssessor riskAssessor, PrintStream out) {
    		this.riskAssessor = riskAssessor;
    		this.out = out;
    	}
    
    	// ...
    
    }
    
    Copied!
    
    
    import org.springframework.beans.factory.annotation.Autowired
    import org.springframework.stereotype.Service
    import java.io.PrintStream
    
    @Service
    class MyAccountService : AccountService {
    
    	private val riskAssessor: RiskAssessor
    
    	private val out: PrintStream
    
    	@Autowired
    	constructor(riskAssessor: RiskAssessor) {
    		this.riskAssessor = riskAssessor
    		out = System.out
    	}
    
    	constructor(riskAssessor: RiskAssessor, out: PrintStream) {
    		this.riskAssessor = riskAssessor
    		this.out = out
    	}
    
    	// ...
    
    }
    
    Copied!

__ |  Notice how using constructor injection lets the `riskAssessor` field be marked as `final`, indicating that it cannot be subsequently changed.   
---|---  
  
[Auto-configuration](auto-configuration.html) [Using the @SpringBootApplication Annotation](using-the-springbootapplication-annotation.html)
---
