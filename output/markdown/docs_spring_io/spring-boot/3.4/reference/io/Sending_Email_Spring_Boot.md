Title: Sending Email :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/io/email.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/io/Sending_Email_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/io/Sending_Email_Spring_Boot.png
crawled_at: 2025-06-04T19:21:52.300315
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/io/email.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [IO](index.html)
  * [Sending Email](email.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/io/email.html)!  
---|---  
  
# Sending Email

The Spring Framework provides an abstraction for sending email by using the [`JavaMailSender`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/mail/javamail/JavaMailSender.html) interface, and Spring Boot provides auto-configuration for it as well as a starter module.

__ |  See the [reference documentation](https://docs.spring.io/spring-framework/reference/6.2/integration/email.html) for a detailed explanation of how you can use [`JavaMailSender`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/mail/javamail/JavaMailSender.html).   
---|---  
  
If `spring.mail.host` and the relevant libraries (as defined by `spring-boot-starter-mail`) are available, a default [`JavaMailSender`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/mail/javamail/JavaMailSender.html) is created if none exists. The sender can be further customized by configuration items from the `spring.mail` namespace. See [`MailProperties`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/mail/MailProperties.html) for more details.

In particular, certain default timeout values are infinite, and you may want to change that to avoid having a thread blocked by an unresponsive mail server, as shown in the following example:

  * Properties

  * YAML



    
    
    spring.mail.properties[mail.smtp.connectiontimeout]=5000
    spring.mail.properties[mail.smtp.timeout]=3000
    spring.mail.properties[mail.smtp.writetimeout]=5000
    
    Copied!
    
    
    spring:
      mail:
        properties:
          "[mail.smtp.connectiontimeout]": 5000
          "[mail.smtp.timeout]": 3000
          "[mail.smtp.writetimeout]": 5000
    
    Copied!

It is also possible to configure a [`JavaMailSender`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/mail/javamail/JavaMailSender.html) with an existing [`Session`](https://jakarta.ee/specifications/mail/2.1/apidocs/jakarta/mail/Session.html) from JNDI:

  * Properties

  * YAML



    
    
    spring.mail.jndi-name=mail/Session
    
    Copied!
    
    
    spring:
      mail:
        jndi-name: "mail/Session"
    
    Copied!

When a `jndi-name` is set, it takes precedence over all other Session-related settings.

[Quartz Scheduler](quartz.html) [Validation](validation.html)
---
