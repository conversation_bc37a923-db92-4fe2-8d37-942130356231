Title: IO :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/io/index.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/io/IO_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/io/IO_Spring_Boot.png
crawled_at: 2025-06-04T16:02:57.253966
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/io/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [IO](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/io/index.html)!  
---|---  
  
# IO

Most applications will need to deal with input and output concerns at some point. Spring Boot provides utilities and integrations with a range of technologies to help when you need IO capabilities. This section covers standard IO features such as caching and validation as well as more advanced topics such as scheduling and distributed transactions. We will also cover calling remote REST or SOAP services and sending email.

[Working with NoSQL Technologies](../data/nosql.html) [Caching](caching.html)
---
