Title: Web :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/web/index.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/web/Web_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/web/Web_Spring_Boot.png
crawled_at: 2025-06-04T16:04:57.877203
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/web/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Web](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/web/index.html)!  
---|---  
  
# Web

Spring Boot is well suited for web application development. You can create a self-contained HTTP server by using embedded Tomcat, Jetty, Undertow, or Netty. Most web applications use the `spring-boot-starter-web` module to get up and running quickly. You can also choose to build reactive web applications by using the `spring-boot-starter-webflux` module.

If you have not yet developed a Spring Boot web application, you can follow the “Hello World!” example in the [Getting started](../../tutorial/first-application/index.html) section.

[SSL](../features/ssl.html) [Servlet Web Applications](servlet.html)
---
