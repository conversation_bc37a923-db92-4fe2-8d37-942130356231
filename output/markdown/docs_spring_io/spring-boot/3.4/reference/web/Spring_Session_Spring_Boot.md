Title: Spring Session :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/web/spring-session.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/web/Spring_Session_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/web/Spring_Session_Spring_Boot.png
crawled_at: 2025-06-04T16:06:36.378366
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/web/spring-session.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Web](index.html)
  * [Spring Session](spring-session.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/web/spring-session.html)!  
---|---  
  
# Spring Session

Spring Boot provides [Spring Session](https://spring.io/projects/spring-session) auto-configuration for a wide range of data stores. When building a servlet web application, the following stores can be auto-configured:

  * Redis

  * JDBC

  * Hazelcast

  * MongoDB




Additionally, [Spring Boot for Apache Geode](https://github.com/spring-projects/spring-boot-data-geode) provides [auto-configuration for using Apache Geode as a session store](https://docs.spring.io/spring-boot-data-geode-build/2.0.x/reference/html5#geode-session).

The servlet auto-configuration replaces the need to use `@Enable*HttpSession`.

If a single Spring Session module is present on the classpath, Spring Boot uses that store implementation automatically. If you have more than one implementation, Spring Boot uses the following order for choosing a specific implementation:

  1. Redis

  2. JDBC

  3. Hazelcast

  4. MongoDB

  5. If none of Redis, JDBC, Hazelcast and MongoDB are available, we do not configure a [`SessionRepository`](https://docs.spring.io/spring-session/docs/3.4.x/api/org/springframework/session/SessionRepository.html).




When building a reactive web application, the following stores can be auto-configured:

  * Redis

  * MongoDB




The reactive auto-configuration replaces the need to use `@Enable*WebSession`.

Similar to the servlet configuration, if you have more than one implementation, Spring Boot uses the following order for choosing a specific implementation:

  1. Redis

  2. MongoDB

  3. If neither Redis nor MongoDB are available, we do not configure a [`ReactiveSessionRepository`](https://docs.spring.io/spring-session/docs/3.4.x/api/org/springframework/session/ReactiveSessionRepository.html).




Each store has specific additional settings. For instance, it is possible to customize the name of the table for the JDBC store, as shown in the following example:

  * Properties

  * YAML



    
    
    spring.session.jdbc.table-name=SESSIONS
    
    Copied!
    
    
    spring:
      session:
        jdbc:
          table-name: "SESSIONS"
    
    Copied!

For setting the timeout of the session you can use the `spring.session.timeout` property. If that property is not set with a servlet web application, the auto-configuration falls back to the value of `server.servlet.session.timeout`.

You can take control over Spring Session’s configuration using `@Enable*HttpSession` (servlet) or `@Enable*WebSession` (reactive). This will cause the auto-configuration to back off. Spring Session can then be configured using the annotation’s attributes rather than the previously described configuration properties.

[Spring Security](spring-security.html) [Spring for GraphQL](spring-graphql.html)
---
