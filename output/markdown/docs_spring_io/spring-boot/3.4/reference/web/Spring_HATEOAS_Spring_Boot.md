Title: Spring HATEOAS :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/web/spring-hateoas.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/web/Spring_HATEOAS_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/web/Spring_HATEOAS_Spring_Boot.png
crawled_at: 2025-06-04T16:04:05.769876
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/web/spring-hateoas.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Web](index.html)
  * [Spring HATEOAS](spring-hateoas.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/web/spring-hateoas.html)!  
---|---  
  
# Spring HATEOAS

If you develop a RESTful API that makes use of hypermedia, Spring Boot provides auto-configuration for Spring HATEOAS that works well with most applications. The auto-configuration replaces the need to use [`@EnableHypermediaSupport`](https://docs.spring.io/spring-hateoas/docs/2.4.x/api/org/springframework/hateoas/config/EnableHypermediaSupport.html) and registers a number of beans to ease building hypermedia-based applications, including a [`LinkDiscoverers`](https://docs.spring.io/spring-hateoas/docs/2.4.x/api/org/springframework/hateoas/client/LinkDiscoverers.html) (for client side support) and an [`ObjectMapper`](https://javadoc.io/doc/com.fasterxml.jackson.core/jackson-databind/2.18.4/com/fasterxml/jackson/databind/ObjectMapper.html) configured to correctly marshal responses into the desired representation. The [`ObjectMapper`](https://javadoc.io/doc/com.fasterxml.jackson.core/jackson-databind/2.18.4/com/fasterxml/jackson/databind/ObjectMapper.html) is customized by setting the various `spring.jackson.*` properties or, if one exists, by a [`Jackson2ObjectMapperBuilder`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/http/converter/json/Jackson2ObjectMapperBuilder.html) bean.

You can take control of Spring HATEOAS’s configuration by using [`@EnableHypermediaSupport`](https://docs.spring.io/spring-hateoas/docs/2.4.x/api/org/springframework/hateoas/config/EnableHypermediaSupport.html). Note that doing so disables the [`ObjectMapper`](https://javadoc.io/doc/com.fasterxml.jackson.core/jackson-databind/2.18.4/com/fasterxml/jackson/databind/ObjectMapper.html) customization described earlier.

__ |  `spring-boot-starter-hateoas` is specific to Spring MVC and should not be combined with Spring WebFlux. In order to use Spring HATEOAS with Spring WebFlux, you can add a direct dependency on `org.springframework.hateoas:spring-hateoas` along with `spring-boot-starter-webflux`.   
---|---  
  
By default, requests that accept `application/json` will receive an `application/hal+json` response. To disable this behavior set `spring.hateoas.use-hal-as-default-json-media-type` to `false` and define a [`HypermediaMappingInformation`](https://docs.spring.io/spring-hateoas/docs/2.4.x/api/org/springframework/hateoas/config/HypermediaMappingInformation.html) or [`HalConfiguration`](https://docs.spring.io/spring-hateoas/docs/2.4.x/api/org/springframework/hateoas/mediatype/hal/HalConfiguration.html) to configure Spring HATEOAS to meet the needs of your application and its clients.

[Spring for GraphQL](spring-graphql.html) [Data](../data/index.html)
---
