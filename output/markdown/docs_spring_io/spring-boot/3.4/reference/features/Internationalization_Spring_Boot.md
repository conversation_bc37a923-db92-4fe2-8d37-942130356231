Title: Internationalization :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/features/internationalization.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/features/Internationalization_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/features/Internationalization_Spring_Boot.png
crawled_at: 2025-06-04T19:22:29.481723
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/features/internationalization.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Core Features](index.html)
  * [Internationalization](internationalization.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/features/internationalization.html)!  
---|---  
  
# Internationalization

Spring Boot supports localized messages so that your application can cater to users of different language preferences. By default, Spring Boot looks for the presence of a `messages` resource bundle at the root of the classpath.

__ |  The auto-configuration applies when the default properties file for the configured resource bundle is available (`messages.properties` by default). If your resource bundle contains only language-specific properties files, you are required to add the default. If no properties file is found that matches any of the configured base names, there will be no auto-configured [`MessageSource`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/MessageSource.html).   
---|---  
  
The basename of the resource bundle as well as several other attributes can be configured using the `spring.messages` namespace, as shown in the following example:

  * Properties

  * YAML



    
    
    spring.messages.basename=messages, config.i18n.messages
    spring.messages.common-messages=classpath:my-common-messages.properties
    spring.messages.fallback-to-system-locale=false
    
    Copied!
    
    
    spring:
      messages:
        basename: "messages, config.i18n.messages"
        common-messages: "classpath:my-common-messages.properties"
        fallback-to-system-locale: false
    
    Copied!

__ |  The `spring.messages.basename` property supports a list of locations, either a package qualifier or a resource resolved from the classpath root. The `spring.messages.common-messages` property supports a list of property file resources.   
---|---  
  
See [`MessageSourceProperties`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/context/MessageSourceProperties.html) for more supported options.

[Logging](logging.html) [Aspect-Oriented Programming](aop.html)
---
