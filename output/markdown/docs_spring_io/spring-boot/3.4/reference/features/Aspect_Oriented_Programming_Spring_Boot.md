Title: Aspect-Oriented Programming :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/features/aop.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/features/Aspect_Oriented_Programming_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/features/Aspect_Oriented_Programming_Spring_Boot.png
crawled_at: 2025-06-04T16:03:30.749222
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/features/aop.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Core Features](index.html)
  * [Aspect-Oriented Programming](aop.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/features/aop.html)!  
---|---  
  
# Aspect-Oriented Programming

Spring Boot provides auto-configuration for aspect-oriented programming (AOP). You can learn more about AOP with Spring in the [Spring Framework reference documentation](https://docs.spring.io/spring-framework/reference/6.2/core/aop-api.html).

By default, Spring Boot’s auto-configuration configures Spring AOP to use CGLib proxies. To use JDK proxies instead, set `spring.aop.proxy-target-class` to `false`.

If AspectJ is on the classpath, Spring Boot’s auto-configuration will automatically enable AspectJ auto proxy such that [`@EnableAspectJAutoProxy`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/EnableAspectJAutoProxy.html) is not required.

[Internationalization](internationalization.html) [JSON](json.html)
---
