Title: Logging :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/features/logging.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/features/Logging_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/features/Logging_Spring_Boot.png
crawled_at: 2025-06-04T19:22:07.693430
---
Search CTRL + k

### Logging

  * Log Format
  * Console Output
  * Color-coded Output
  * File Output
  * File Rotation
  * Log Levels
  * Log Groups
  * Using a Log Shutdown Hook
  * Custom Log Configuration
  * Structured Logging
  * Elastic Common Schema
  * Graylog Extended Log Format (GELF)
  * Logstash JSON format
  * Customizing Structured Logging JSON
  * Supporting Other Structured Logging Formats
  * Logback Extensions
  * Profile-specific Configuration
  * Environment Properties
  * Log4j2 Extensions
  * Profile-specific Configuration
  * Environment Properties Lookup
  * Log4j2 System Properties



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/features/logging.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Core Features](index.html)
  * [Logging](logging.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/features/logging.html)!  
---|---  
  
# Logging

### Logging

  * Log Format
  * Console Output
  * Color-coded Output
  * File Output
  * File Rotation
  * Log Levels
  * Log Groups
  * Using a Log Shutdown Hook
  * Custom Log Configuration
  * Structured Logging
  * Elastic Common Schema
  * Graylog Extended Log Format (GELF)
  * Logstash JSON format
  * Customizing Structured Logging JSON
  * Supporting Other Structured Logging Formats
  * Logback Extensions
  * Profile-specific Configuration
  * Environment Properties
  * Log4j2 Extensions
  * Profile-specific Configuration
  * Environment Properties Lookup
  * Log4j2 System Properties



Spring Boot uses [Commons Logging](https://commons.apache.org/logging) for all internal logging but leaves the underlying log implementation open. Default configurations are provided for [Java Util Logging](https://docs.oracle.com/en/java/javase/17/docs/api/java.logging/java/util/logging/package-summary.html), [Log4j2](https://logging.apache.org/log4j/2.x/), and [Logback](https://logback.qos.ch/). In each case, loggers are pre-configured to use console output with optional file output also available.

By default, if you use the starters, Logback is used for logging. Appropriate Logback routing is also included to ensure that dependent libraries that use Java Util Logging, Commons Logging, Log4J, or SLF4J all work correctly.

__ |  There are a lot of logging frameworks available for Java. Do not worry if the above list seems confusing. Generally, you do not need to change your logging dependencies and the Spring Boot defaults work just fine.   
---|---  
  
__ |  When you deploy your application to a servlet container or application server, logging performed with the Java Util Logging API is not routed into your application’s logs. This prevents logging performed by the container or other applications that have been deployed to it from appearing in your application’s logs.   
---|---  
  
## Log Format

The default log output from Spring Boot resembles the following example:
    
    
    2025-05-22T10:12:23.994Z  INFO 132620 --- [myapp] [           main] o.s.b.d.f.logexample.MyApplication       : Starting MyApplication using Java 17.0.15 with PID 132620 (/opt/apps/myapp.jar started by myuser in /opt/apps/)
    2025-05-22T10:12:24.017Z  INFO 132620 --- [myapp] [           main] o.s.b.d.f.logexample.MyApplication       : No active profile set, falling back to 1 default profile: "default"
    2025-05-22T10:12:27.767Z  INFO 132620 --- [myapp] [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
    2025-05-22T10:12:27.804Z  INFO 132620 --- [myapp] [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
    2025-05-22T10:12:27.806Z  INFO 132620 --- [myapp] [           main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
    2025-05-22T10:12:27.985Z  INFO 132620 --- [myapp] [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
    2025-05-22T10:12:27.987Z  INFO 132620 --- [myapp] [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3613 ms
    2025-05-22T10:12:29.744Z  INFO 132620 --- [myapp] [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
    2025-05-22T10:12:29.802Z  INFO 132620 --- [myapp] [           main] o.s.b.d.f.logexample.MyApplication       : Started MyApplication in 7.773 seconds (process running for 8.801)
    2025-05-22T10:12:29.823Z  INFO 132620 --- [myapp] [ionShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
    2025-05-22T10:12:29.874Z  INFO 132620 --- [myapp] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
    
    Copied!

The following items are output:

  * Date and Time: Millisecond precision and easily sortable.

  * Log Level: `ERROR`, `WARN`, `INFO`, `DEBUG`, or `TRACE`.

  * Process ID.

  * A `---` separator to distinguish the start of actual log messages.

  * Application name: Enclosed in square brackets (logged by default only if `spring.application.name` is set)

  * Application group: Enclosed in square brackets (logged by default only if `spring.application.group` is set)

  * Thread name: Enclosed in square brackets (may be truncated for console output).

  * Correlation ID: If tracing is enabled (not shown in the sample above)

  * Logger name: This is usually the source class name (often abbreviated).

  * The log message.




__ |  Logback does not have a `FATAL` level. It is mapped to `ERROR`.   
---|---  
  
__ |  If you have a `spring.application.name` property but don’t want it logged you can set `logging.include-application-name` to `false`.   
---|---  
  
__ |  If you have a `spring.application.group` property but don’t want it logged you can set `logging.include-application-group` to `false`.   
---|---  
  
__ |  For more details about correlation IDs, please [see this documentation](../actuator/tracing.html#actuator.micrometer-tracing.logging).   
---|---  
  
## Console Output

The default log configuration echoes messages to the console as they are written. By default, `ERROR`-level, `WARN`-level, and `INFO`-level messages are logged. You can also enable a “debug” mode by starting your application with a `--debug` flag.
    
    
    $ java -jar myapp.jar --debug
    
    Copied!

__ |  You can also specify `debug=true` in your `application.properties`.   
---|---  
  
When the debug mode is enabled, a selection of core loggers (embedded container, Hibernate, and Spring Boot) are configured to output more information. Enabling the debug mode does _not_ configure your application to log all messages with `DEBUG` level.

Alternatively, you can enable a “trace” mode by starting your application with a `--trace` flag (or `trace=true` in your `application.properties`). Doing so enables trace logging for a selection of core loggers (embedded container, Hibernate schema generation, and the whole Spring portfolio).

### Color-coded Output

If your terminal supports ANSI, color output is used to aid readability. You can set `spring.output.ansi.enabled` to a [supported value](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/ansi/AnsiOutput.Enabled.html) to override the auto-detection.

Color coding is configured by using the `%clr` conversion word. In its simplest form, the converter colors the output according to the log level, as shown in the following example:
    
    
    %clr(%5p)
    
    Copied!

The following table describes the mapping of log levels to colors:

Level | Color  
---|---  
`FATAL` | Red  
`ERROR` | Red  
`WARN` | Yellow  
`INFO` | Green  
`DEBUG` | Green  
`TRACE` | Green  
  
Alternatively, you can specify the color or style that should be used by providing it as an option to the conversion. For example, to make the text yellow, use the following setting:
    
    
    %clr(%d{yyyy-MM-dd'T'HH:mm:ss.SSSXXX}){yellow}
    
    Copied!

The following colors and styles are supported:

  * `blue`

  * `cyan`

  * `faint`

  * `green`

  * `magenta`

  * `red`

  * `yellow`




## File Output

By default, Spring Boot logs only to the console and does not write log files. If you want to write log files in addition to the console output, you need to set a `logging.file.name` or `logging.file.path` property (for example, in your `application.properties`). If both properties are set, `logging.file.path` is ignored and only `logging.file.name` is used.

The following table shows how the `logging.*` properties can be used together:

Table 1. Logging properties `logging.file.name` | `logging.file.path` | Description  
---|---|---  
_(none)_ | _(none)_ | Console only logging.  
Specific file (for example, `my.log`) | _(none)_ | Writes to the location specified by `logging.file.name`. The location can be absolute or relative to the current directory.  
_(none)_ | Specific directory (for example, `/var/log`) | Writes `spring.log` to the directory specified by `logging.file.path`. The directory can be absolute or relative to the current directory.  
Specific file | Specific directory | Writes to the location specified by `logging.file.name` and ignores `logging.file.path`. The location can be absolute or relative to the current directory.  
  
Log files rotate when they reach 10 MB and, as with console output, `ERROR`-level, `WARN`-level, and `INFO`-level messages are logged by default.

__ |  Logging properties are independent of the actual logging infrastructure. As a result, specific configuration keys (such as `logback.configurationFile` for Logback) are not managed by spring Boot.   
---|---  
  
## File Rotation

If you are using the Logback, it is possible to fine-tune log rotation settings using your `application.properties` or `application.yaml` file. For all other logging system, you will need to configure rotation settings directly yourself (for example, if you use Log4j2 then you could add a `log4j2.xml` or `log4j2-spring.xml` file).

The following rotation policy properties are supported:

Name | Description  
---|---  
`logging.logback.rollingpolicy.file-name-pattern` | The filename pattern used to create log archives.  
`logging.logback.rollingpolicy.clean-history-on-start` | If log archive cleanup should occur when the application starts.  
`logging.logback.rollingpolicy.max-file-size` | The maximum size of log file before it is archived.  
`logging.logback.rollingpolicy.total-size-cap` | The maximum amount of size log archives can take before being deleted.  
`logging.logback.rollingpolicy.max-history` | The maximum number of archive log files to keep (defaults to 7).  
  
## Log Levels

All the supported logging systems can have the logger levels set in the Spring [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html) (for example, in `application.properties`) by using `logging.level.<logger-name>=<level>` where `level` is one of TRACE, DEBUG, INFO, WARN, ERROR, FATAL, or OFF. The `root` logger can be configured by using `logging.level.root`.

The following example shows potential logging settings in `application.properties`:

  * Properties

  * YAML



    
    
    logging.level.root=warn
    logging.level.org.springframework.web=debug
    logging.level.org.hibernate=error
    
    Copied!
    
    
    logging:
      level:
        root: "warn"
        org.springframework.web: "debug"
        org.hibernate: "error"
    
    Copied!

It is also possible to set logging levels using environment variables. For example, `LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WEB=DEBUG` will set `org.springframework.web` to `DEBUG`.

__ |  The above approach will only work for package level logging. Since relaxed binding [always converts environment variables to lowercase](external-config.html#features.external-config.typesafe-configuration-properties.relaxed-binding.maps-from-environment-variables), it is not possible to configure logging for an individual class in this way. If you need to configure logging for a class, you can use [the `SPRING_APPLICATION_JSON`](external-config.html#features.external-config.application-json) variable.   
---|---  
  
## Log Groups

It is often useful to be able to group related loggers together so that they can all be configured at the same time. For example, you might commonly change the logging levels for _all_ Tomcat related loggers, but you can not easily remember top level packages.

To help with this, Spring Boot allows you to define logging groups in your Spring [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html). For example, here is how you could define a “tomcat” group by adding it to your `application.properties`:

  * Properties

  * YAML



    
    
    logging.group.tomcat=org.apache.catalina,org.apache.coyote,org.apache.tomcat
    
    Copied!
    
    
    logging:
      group:
        tomcat: "org.apache.catalina,org.apache.coyote,org.apache.tomcat"
    
    Copied!

Once defined, you can change the level for all the loggers in the group with a single line:

  * Properties

  * YAML



    
    
    logging.level.tomcat=trace
    
    Copied!
    
    
    logging:
      level:
        tomcat: "trace"
    
    Copied!

Spring Boot includes the following pre-defined logging groups that can be used out-of-the-box:

Name | Loggers  
---|---  
web | `org.springframework.core.codec`, `org.springframework.http`, `org.springframework.web`, `org.springframework.boot.actuate.endpoint.web`, `org.springframework.boot.web.servlet.ServletContextInitializerBeans`  
sql | `org.springframework.jdbc.core`, `org.hibernate.SQL`, [`LoggerListener`](https://www.jooq.org/javadoc/3.19.23/org/jooq/tools/LoggerListener.html)  
  
## Using a Log Shutdown Hook

In order to release logging resources when your application terminates, a shutdown hook that will trigger log system cleanup when the JVM exits is provided. This shutdown hook is registered automatically unless your application is deployed as a war file. If your application has complex context hierarchies the shutdown hook may not meet your needs. If it does not, disable the shutdown hook and investigate the options provided directly by the underlying logging system. For example, Logback offers [context selectors](https://logback.qos.ch/manual/loggingSeparation.html) which allow each Logger to be created in its own context. You can use the `logging.register-shutdown-hook` property to disable the shutdown hook. Setting it to `false` will disable the registration. You can set the property in your `application.properties` or `application.yaml` file:

  * Properties

  * YAML



    
    
    logging.register-shutdown-hook=false
    
    Copied!
    
    
    logging:
      register-shutdown-hook: false
    
    Copied!

## Custom Log Configuration

The various logging systems can be activated by including the appropriate libraries on the classpath and can be further customized by providing a suitable configuration file in the root of the classpath or in a location specified by the following Spring [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html) property: `logging.config`.

You can force Spring Boot to use a particular logging system by using the `org.springframework.boot.logging.LoggingSystem` system property. The value should be the fully qualified class name of a [`LoggingSystem`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/logging/LoggingSystem.html) implementation. You can also disable Spring Boot’s logging configuration entirely by using a value of `none`.

__ |  Since logging is initialized **before** the [`ApplicationContext`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/ApplicationContext.html) is created, it is not possible to control logging from [`@PropertySources`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/PropertySources.html) in Spring [`@Configuration`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html) files. The only way to change the logging system or disable it entirely is through System properties.   
---|---  
  
Depending on your logging system, the following files are loaded:

Logging System | Customization  
---|---  
Logback | `logback-spring.xml`, `logback-spring.groovy`, `logback.xml`, or `logback.groovy`  
Log4j2 | `log4j2-spring.xml` or `log4j2.xml`  
JDK (Java Util Logging) | `logging.properties`  
  
__ |  When possible, we recommend that you use the `-spring` variants for your logging configuration (for example, `logback-spring.xml` rather than `logback.xml`). If you use standard configuration locations, Spring cannot completely control log initialization.   
---|---  
  
__ |  There are known classloading issues with Java Util Logging that cause problems when running from an 'executable jar'. We recommend that you avoid it when running from an 'executable jar' if at all possible.   
---|---  
  
To help with the customization, some other properties are transferred from the Spring [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html) to System properties. This allows the properties to be consumed by logging system configuration. For example, setting `logging.file.name` in `application.properties` or `LOGGING_FILE_NAME` as an environment variable will result in the `LOG_FILE` System property being set. The properties that are transferred are described in the following table:

Spring Environment | System Property | Comments  
---|---|---  
`logging.exception-conversion-word` | `LOG_EXCEPTION_CONVERSION_WORD` | The conversion word used when logging exceptions.  
`logging.file.name` | `LOG_FILE` | If defined, it is used in the default log configuration.  
`logging.file.path` | `LOG_PATH` | If defined, it is used in the default log configuration.  
`logging.pattern.console` | `CONSOLE_LOG_PATTERN` | The log pattern to use on the console (stdout).  
`logging.pattern.dateformat` | `LOG_DATEFORMAT_PATTERN` | Appender pattern for log date format.  
`logging.charset.console` | `CONSOLE_LOG_CHARSET` | The charset to use for console logging.  
`logging.threshold.console` | `CONSOLE_LOG_THRESHOLD` | The log level threshold to use for console logging.  
`logging.pattern.file` | `FILE_LOG_PATTERN` | The log pattern to use in a file (if `LOG_FILE` is enabled).  
`logging.charset.file` | `FILE_LOG_CHARSET` | The charset to use for file logging (if `LOG_FILE` is enabled).  
`logging.threshold.file` | `FILE_LOG_THRESHOLD` | The log level threshold to use for file logging.  
`logging.pattern.level` | `LOG_LEVEL_PATTERN` | The format to use when rendering the log level (default `%5p`).  
`logging.structured.format.console` | `CONSOLE_LOG_STRUCTURED_FORMAT` | The structured logging format to use for console logging.  
`logging.structured.format.file` | `FILE_LOG_STRUCTURED_FORMAT` | The structured logging format to use for file logging.  
`PID` | `PID` | The current process ID (discovered if possible and when not already defined as an OS environment variable).  
  
If you use Logback, the following properties are also transferred:

Spring Environment | System Property | Comments  
---|---|---  
`logging.logback.rollingpolicy.file-name-pattern` | `LOGBACK_ROLLINGPOLICY_FILE_NAME_PATTERN` | Pattern for rolled-over log file names (default `${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz`).  
`logging.logback.rollingpolicy.clean-history-on-start` | `LOGBACK_ROLLINGPOLICY_CLEAN_HISTORY_ON_START` | Whether to clean the archive log files on startup.  
`logging.logback.rollingpolicy.max-file-size` | `LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE` | Maximum log file size.  
`logging.logback.rollingpolicy.total-size-cap` | `LOGBACK_ROLLINGPOLICY_TOTAL_SIZE_CAP` | Total size of log backups to be kept.  
`logging.logback.rollingpolicy.max-history` | `LOGBACK_ROLLINGPOLICY_MAX_HISTORY` | Maximum number of archive log files to keep.  
  
All the supported logging systems can consult System properties when parsing their configuration files. See the default configurations in `spring-boot.jar` for examples:

  * [Logback](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/logback/defaults.xml)

  * [Log4j 2](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/log4j2/log4j2.xml)

  * [Java Util logging](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/java/logging-file.properties)




__ |  If you want to use a placeholder in a logging property, you should use [Spring Boot’s syntax](external-config.html#features.external-config.files.property-placeholders) and not the syntax of the underlying framework. Notably, if you use Logback, you should use `:` as the delimiter between a property name and its default value and not use `:-`.  
---|---  
  
__ |  You can add MDC and other ad-hoc content to log lines by overriding only the `LOG_LEVEL_PATTERN` (or `logging.pattern.level` with Logback). For example, if you use `logging.pattern.level=user:%X{user} %5p`, then the default log format contains an MDC entry for "user", if it exists, as shown in the following example.
    
    
    2019-08-30 12:30:04.031 user:someone INFO 22174 --- [  nio-8080-exec-0] demo.Controller
    Handling authenticated requestCopied!  
  
---|---  
  
## Structured Logging

Structured logging is a technique where the log output is written in a well-defined, often machine-readable format. Spring Boot supports structured logging and has support for the following JSON formats out of the box:

  * Elastic Common Schema (ECS)

  * Graylog Extended Log Format (GELF)

  * Logstash




To enable structured logging, set the property `logging.structured.format.console` (for console output) or `logging.structured.format.file` (for file output) to the id of the format you want to use.

If you are using Custom Log Configuration, update your configuration to respect `CONSOLE_LOG_STRUCTURED_FORMAT` and `FILE_LOG_STRUCTURED_FORMAT` system properties. Take `CONSOLE_LOG_STRUCTURED_FORMAT` for example:

  * Logback

  * Log4j2



    
    
    <!-- replace your encoder with StructuredLogEncoder -->
    <encoder class="org.springframework.boot.logging.logback.StructuredLogEncoder">
    	<format>${CONSOLE_LOG_STRUCTURED_FORMAT}</format>
    	<charset>${CONSOLE_LOG_CHARSET}</charset>
    </encoder>
    
    Copied!

You can also refer to the default configurations included in Spring Boot:

  * [Logback Structured Console Appender](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/logback/structured-console-appender.xml)

  * [Logback Structured File Appender](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/logback/structured-file-appender.xml)



    
    
    <!-- replace your PatternLayout with StructuredLogLayout -->
    <StructuredLogLayout format="${sys:CONSOLE_LOG_STRUCTURED_FORMAT}" charset="${sys:CONSOLE_LOG_CHARSET}"/>
    
    Copied!

You can also refer to the default configurations included in Spring Boot:

  * [Log4j2 Console Appender](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/log4j2/log4j2.xml)

  * [Log4j2 Console and File Appender](https://github.com/spring-projects/spring-boot/tree/v3.4.6/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/log4j2/log4j2-file.xml)




### Elastic Common Schema

[Elastic Common Schema](https://www.elastic.co/guide/en/ecs/8.11/ecs-reference.html) is a JSON based logging format.

To enable the Elastic Common Schema log format, set the appropriate `format` property to `ecs`:

  * Properties

  * YAML



    
    
    logging.structured.format.console=ecs
    logging.structured.format.file=ecs
    
    Copied!
    
    
    logging:
      structured:
        format:
          console: ecs
          file: ecs
    
    Copied!

A log line looks like this:
    
    
    {"@timestamp":"2024-01-01T10:15:00.067462556Z","log.level":"INFO","process.pid":39599,"process.thread.name":"main","service.name":"simple","log.logger":"org.example.Application","message":"No active profile set, falling back to 1 default profile: \"default\"","ecs.version":"8.11"}
    
    Copied!

This format also adds every key value pair contained in the MDC to the JSON object. You can also use the [SLF4J fluent logging API](https://www.slf4j.org/manual.html#fluent) to add key value pairs to the logged JSON object with the [addKeyValue](https://www.slf4j.org/apidocs/org/slf4j/spi/LoggingEventBuilder.html#addKeyValue\(java.lang.String,java.lang.Object\)) method.

The `service` values can be customized using `logging.structured.ecs.service` properties:

  * Properties

  * YAML



    
    
    logging.structured.ecs.service.name=MyService
    logging.structured.ecs.service.version=1
    logging.structured.ecs.service.environment=Production
    logging.structured.ecs.service.node-name=Primary
    
    Copied!
    
    
    logging:
      structured:
        ecs:
          service:
            name: MyService
            version: 1.0
            environment: Production
            node-name: Primary
    
    Copied!

__ |  `logging.structured.ecs.service.name` will default to `spring.application.name` if not specified.   
---|---  
  
__ |  `logging.structured.ecs.service.version` will default to `spring.application.version` if not specified.   
---|---  
  
### Graylog Extended Log Format (GELF)

[Graylog Extended Log Format](https://go2docs.graylog.org/current/getting_in_log_data/gelf.html) is a JSON based logging format for the Graylog log analytics platform.

To enable the Graylog Extended Log Format, set the appropriate `format` property to `gelf`:

  * Properties

  * YAML



    
    
    logging.structured.format.console=gelf
    logging.structured.format.file=gelf
    
    Copied!
    
    
    logging:
      structured:
        format:
          console: gelf
          file: gelf
    
    Copied!

A log line looks like this:
    
    
    {"version":"1.1","short_message":"No active profile set, falling back to 1 default profile: \"default\"","timestamp":1725958035.857,"level":6,"_level_name":"INFO","_process_pid":47649,"_process_thread_name":"main","_log_logger":"org.example.Application"}
    
    Copied!

This format also adds every key value pair contained in the MDC to the JSON object. You can also use the [SLF4J fluent logging API](https://www.slf4j.org/manual.html#fluent) to add key value pairs to the logged JSON object with the [addKeyValue](https://www.slf4j.org/apidocs/org/slf4j/spi/LoggingEventBuilder.html#addKeyValue\(java.lang.String,java.lang.Object\)) method.

Several fields can be customized using `logging.structured.gelf` properties:

  * Properties

  * YAML



    
    
    logging.structured.gelf.host=MyService
    logging.structured.gelf.service.version=1
    
    Copied!
    
    
    logging:
      structured:
        gelf:
          host: MyService
          service:
            version: 1.0
    
    Copied!

__ |  `logging.structured.gelf.host` will default to `spring.application.name` if not specified.   
---|---  
  
__ |  `logging.structured.gelf.service.version` will default to `spring.application.version` if not specified.   
---|---  
  
### Logstash JSON format

The [Logstash JSON format](https://github.com/logfellow/logstash-logback-encoder?tab=readme-ov-file#standard-fields) is a JSON based logging format.

To enable the Logstash JSON log format, set the appropriate `format` property to `logstash`:

  * Properties

  * YAML



    
    
    logging.structured.format.console=logstash
    logging.structured.format.file=logstash
    
    Copied!
    
    
    logging:
      structured:
        format:
          console: logstash
          file: logstash
    
    Copied!

A log line looks like this:
    
    
    {"@timestamp":"2024-01-01T10:15:00.111037681+02:00","@version":"1","message":"No active profile set, falling back to 1 default profile: \"default\"","logger_name":"org.example.Application","thread_name":"main","level":"INFO","level_value":20000}
    
    Copied!

This format also adds every key value pair contained in the MDC to the JSON object. You can also use the [SLF4J fluent logging API](https://www.slf4j.org/manual.html#fluent) to add key value pairs to the logged JSON object with the [addKeyValue](https://www.slf4j.org/apidocs/org/slf4j/spi/LoggingEventBuilder.html#addKeyValue\(java.lang.String,java.lang.Object\)) method.

If you add [markers](https://www.slf4j.org/api/org/slf4j/Marker.html), these will show up in a `tags` string array in the JSON.

### Customizing Structured Logging JSON

Spring Boot attempts to pick sensible defaults for the JSON names and values output for structured logging. Sometimes, however, you may want to make small adjustments to the JSON for your own needs. For example, it’s possible that you might want to change some of the names to match the expectations of your log ingestion system. You might also want to filter out certain members since you don’t find them useful.

The following properties allow you to change the way that structured logging JSON is written:

Property | Description  
---|---  
`logging.structured.json.include` & `logging.structured.json.exclude` | Filters specific paths from the JSON  
`logging.structured.json.rename` | Renames a specific member in the JSON  
`logging.structured.json.add` | Adds additional members to the JSON  
  
For example, the following will exclude `log.level`, rename `process.id` to `procid` and add a fixed `corpname` field:

  * Properties

  * YAML



    
    
    logging.structured.json.exclude=log.level
    logging.structured.json.rename.process.id=procid
    logging.structured.json.add.corpname=mycorp
    
    Copied!
    
    
    logging:
      structured:
        json:
          exclude: log.level
          rename:
            process.id: procid
          add:
            corpname: mycorp
    
    Copied!

__ |  For more advanced customizations, you can write your own class that implements the [`StructuredLoggingJsonMembersCustomizer`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/logging/structured/StructuredLoggingJsonMembersCustomizer.html) interface and declare it using the `logging.structured.json.customizer` property. You can also declare implementations by listing them in a `META-INF/spring.factories` file.   
---|---  
  
### Supporting Other Structured Logging Formats

The structured logging support in Spring Boot is extensible, allowing you to define your own custom format. To do this, implement the [`StructuredLogFormatter`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/logging/structured/StructuredLogFormatter.html) interface. The generic type argument has to be [`ILoggingEvent`](https://logback.qos.ch/apidocs/ch.qos.logback.core/ch/qos/logback/classic/spi/ILoggingEvent.html) when using Logback and [`LogEvent`](https://logging.apache.org/log4j/2.x/javadoc/log4j-core/org/apache/logging/log4j/core/LogEvent.html) when using Log4j2 (that means your implementation is tied to a specific logging system). Your implementation is then called with the log event and returns the [`String`](https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html) to be logged, as seen in this example:

  * Java

  * Kotlin



    
    
    import ch.qos.logback.classic.spi.ILoggingEvent;
    
    import org.springframework.boot.logging.structured.StructuredLogFormatter;
    
    class MyCustomFormat implements StructuredLogFormatter<ILoggingEvent> {
    
    	@Override
    	public String format(ILoggingEvent event) {
    		return "time=" + event.getInstant() + " level=" + event.getLevel() + " message=" + event.getMessage() + "\n";
    	}
    
    }
    
    Copied!
    
    
    import ch.qos.logback.classic.spi.ILoggingEvent
    import org.springframework.boot.logging.structured.StructuredLogFormatter
    
    class MyCustomFormat : StructuredLogFormatter<ILoggingEvent> {
    
    	override fun format(event: ILoggingEvent): String {
    		return "time=${event.instant} level=${event.level} message=${event.message}\n"
    	}
    
    }
    
    Copied!

As you can see in the example, you can return any format, it doesn’t have to be JSON.

To enable your custom format, set the property `logging.structured.format.console` or `logging.structured.format.file` to the fully qualified class name of your implementation.

Your implementation can use some constructor parameters, which are injected automatically. Please see the JavaDoc of [`StructuredLogFormatter`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/logging/structured/StructuredLogFormatter.html) for more details.

## Logback Extensions

Spring Boot includes a number of extensions to Logback that can help with advanced configuration. You can use these extensions in your `logback-spring.xml` configuration file.

__ |  Because the standard `logback.xml` configuration file is loaded too early, you cannot use extensions in it. You need to either use `logback-spring.xml` or define a `logging.config` property.   
---|---  
  
__ |  The extensions cannot be used with Logback’s [configuration scanning](https://logback.qos.ch/manual/configuration.html#autoScan). If you attempt to do so, making changes to the configuration file results in an error similar to one of the following being logged:   
---|---  
      
    
    ERROR in ch.qos.logback.core.joran.spi.Interpreter@4:71 - no applicable action for [springProperty], current ElementPath is [[configuration][springProperty]]
    ERROR in ch.qos.logback.core.joran.spi.Interpreter@4:71 - no applicable action for [springProfile], current ElementPath is [[configuration][springProfile]]
    
    Copied!

### Profile-specific Configuration

The `<springProfile>` tag lets you optionally include or exclude sections of configuration based on the active Spring profiles. Profile sections are supported anywhere within the `<configuration>` element. Use the `name` attribute to specify which profile accepts the configuration. The `<springProfile>` tag can contain a profile name (for example `staging`) or a profile expression. A profile expression allows for more complicated profile logic to be expressed, for example `production & (eu-central | eu-west)`. Check the [Spring Framework reference guide](https://docs.spring.io/spring-framework/reference/6.2/core/beans/environment.html#beans-definition-profiles-java) for more details. The following listing shows three sample profiles:
    
    
    <springProfile name="staging">
    	<!-- configuration to be enabled when the "staging" profile is active -->
    </springProfile>
    
    <springProfile name="dev | staging">
    	<!-- configuration to be enabled when the "dev" or "staging" profiles are active -->
    </springProfile>
    
    <springProfile name="!production">
    	<!-- configuration to be enabled when the "production" profile is not active -->
    </springProfile>
    
    Copied!

### Environment Properties

The `<springProperty>` tag lets you expose properties from the Spring [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html) for use within Logback. Doing so can be useful if you want to access values from your `application.properties` file in your Logback configuration. The tag works in a similar way to Logback’s standard `<property>` tag. However, rather than specifying a direct `value`, you specify the `source` of the property (from the [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html)). If you need to store the property somewhere other than in `local` scope, you can use the `scope` attribute. If you need a fallback value (in case the property is not set in the [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html)), you can use the `defaultValue` attribute. The following example shows how to expose properties for use within Logback:
    
    
    <springProperty scope="context" name="fluentHost" source="myapp.fluentd.host"
    		defaultValue="localhost"/>
    <appender name="FLUENT" class="ch.qos.logback.more.appenders.DataFluentAppender">
    	<remoteHost>${fluentHost}</remoteHost>
    	...
    </appender>
    
    Copied!

__ |  The `source` must be specified in kebab case (such as `my.property-name`). However, properties can be added to the [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html) by using the relaxed rules.   
---|---  
  
## Log4j2 Extensions

Spring Boot includes a number of extensions to Log4j2 that can help with advanced configuration. You can use these extensions in any `log4j2-spring.xml` configuration file.

__ |  Because the standard `log4j2.xml` configuration file is loaded too early, you cannot use extensions in it. You need to either use `log4j2-spring.xml` or define a `logging.config` property.   
---|---  
  
__ |  The extensions supersede the [Spring Boot support](https://logging.apache.org/log4j/2.x/log4j-spring-boot.html) provided by Log4J. You should make sure not to include the `org.apache.logging.log4j:log4j-spring-boot` module in your build.   
---|---  
  
### Profile-specific Configuration

The `<SpringProfile>` tag lets you optionally include or exclude sections of configuration based on the active Spring profiles. Profile sections are supported anywhere within the `<Configuration>` element. Use the `name` attribute to specify which profile accepts the configuration. The `<SpringProfile>` tag can contain a profile name (for example `staging`) or a profile expression. A profile expression allows for more complicated profile logic to be expressed, for example `production & (eu-central | eu-west)`. Check the [Spring Framework reference guide](https://docs.spring.io/spring-framework/reference/6.2/core/beans/environment.html#beans-definition-profiles-java) for more details. The following listing shows three sample profiles:
    
    
    <SpringProfile name="staging">
    	<!-- configuration to be enabled when the "staging" profile is active -->
    </SpringProfile>
    
    <SpringProfile name="dev | staging">
    	<!-- configuration to be enabled when the "dev" or "staging" profiles are active -->
    </SpringProfile>
    
    <SpringProfile name="!production">
    	<!-- configuration to be enabled when the "production" profile is not active -->
    </SpringProfile>
    
    Copied!

### Environment Properties Lookup

If you want to refer to properties from your Spring [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html) within your Log4j2 configuration you can use `spring:` prefixed [lookups](https://logging.apache.org/log4j/2.x/manual/lookups.html). Doing so can be useful if you want to access values from your `application.properties` file in your Log4j2 configuration.

The following example shows how to set Log4j2 properties named `applicationName` and `applicationGroup` that read `spring.application.name` and `spring.application.group` from the Spring [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html):
    
    
    <Properties>
    	<Property name="applicationName">${spring:spring.application.name}</Property>
    	<Property name="applicationGroup">${spring:spring.application.group}</Property>
    </Properties>
    
    Copied!

__ |  The lookup key should be specified in kebab case (such as `my.property-name`).   
---|---  
  
### Log4j2 System Properties

Log4j2 supports a number of [System Properties](https://logging.apache.org/log4j/2.x/manual/systemproperties.html) that can be used to configure various items. For example, the `log4j2.skipJansi` system property can be used to configure if the [`ConsoleAppender`](https://logging.apache.org/log4j/2.x/javadoc/log4j-core/org/apache/logging/log4j/core/appender/ConsoleAppender.html) will try to use a [Jansi](https://github.com/fusesource/jansi) output stream on Windows.

All system properties that are loaded after the Log4j2 initialization can be obtained from the Spring [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html). For example, you could add `log4j2.skipJansi=false` to your `application.properties` file to have the [`ConsoleAppender`](https://logging.apache.org/log4j/2.x/javadoc/log4j-core/org/apache/logging/log4j/core/appender/ConsoleAppender.html) use Jansi on Windows.

__ |  The Spring [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html) is only considered when system properties and OS environment variables do not contain the value being loaded.   
---|---  
  
__ |  System properties that are loaded during early Log4j2 initialization cannot reference the Spring [`Environment`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/core/env/Environment.html). For example, the property Log4j2 uses to allow the default Log4j2 implementation to be chosen is used before the Spring Environment is available.   
---|---  
  
[Profiles](profiles.html) [Internationalization](internationalization.html)
---
