Title: Checkpoint and Restore With the JVM :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/packaging/checkpoint-restore.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/packaging/Checkpoint_and_Restore_With_the_JVM_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/packaging/Checkpoint_and_Restore_With_the_JVM_Spring_Boot.png
crawled_at: 2025-06-04T19:22:50.089492
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/packaging/checkpoint-restore.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Packaging Spring Boot Applications](index.html)
  * [Checkpoint and Restore With the JVM](checkpoint-restore.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/packaging/checkpoint-restore.html)!  
---|---  
  
# Checkpoint and Restore With the JVM

[Coordinated Restore at Checkpoint](https://wiki.openjdk.org/display/crac/Main) (CRaC) is an OpenJDK project that defines a new Java API to allow you to checkpoint and restore an application on the HotSpot JVM. It is based on [CRIU](https://github.com/checkpoint-restore/criu), a project that implements checkpoint/restore functionality on Linux.

The principle is the following: you start your application almost as usual but with a CRaC enabled version of the JDK like [BellSoft Liberica JDK with CRaC](https://bell-sw.com/pages/downloads/?package=jdk-crac) or [Azul Zulu JDK with CRaC](https://www.azul.com/downloads/?package=jdk-crac#zulu). Then at some point, potentially after some workloads that will warm up your JVM by executing all common code paths, you trigger a checkpoint using an API call, a `jcmd` command, an HTTP endpoint, or a different mechanism.

A memory representation of the running JVM, including its warmness, is then serialized to disk, allowing a fast restoration at a later point, potentially on another machine with a similar operating system and CPU architecture. The restored process retains all the capabilities of the HotSpot JVM, including further JIT optimizations at runtime.

Based on the foundations provided by Spring Framework, Spring Boot provides support for checkpointing and restoring your application, and manages out-of-the-box the lifecycle of resources such as socket, files and thread pools [on a limited scope](https://github.com/spring-projects/spring-lifecycle-smoke-tests/blob/ci/STATUS.adoc). Additional lifecycle management is expected for other dependencies and potentially for the application code dealing with such resources.

You can find more details about the two modes supported ("on demand checkpoint/restore of a running application" and "automatic checkpoint/restore at startup"), how to enable checkpoint and restore support and some guidelines in [the Spring Framework JVM Checkpoint Restore support documentation](https://docs.spring.io/spring-framework/reference/6.2/integration/checkpoint-restore.html).

[Advanced Native Images Topics](native-image/advanced-topics.html) [Container Images](container-images/index.html)
---
