Title: Advanced Native Images Topics :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/packaging/native-image/advanced-topics.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/packaging/native-image/Advanced_Native_Images_Topics_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/packaging/native-image/Advanced_Native_Images_Topics_Spring_Boot.png
crawled_at: 2025-06-04T16:07:47.375266
---
Search CTRL + k

### Advanced Native Images Topics

  * Nested Configuration Properties
  * Converting a Spring Boot Executable Jar
  * Using Buildpacks
  * Using GraalVM native-image
  * Using the Tracing Agent
  * Launch the Application Directly
  * Custom Hints
  * Testing Custom Hints
  * Providing Hints Statically
  * Known Limitations



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/packaging/native-image/advanced-topics.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * [Reference](../../index.html)
  * [Packaging Spring Boot Applications](../index.html)
  * [GraalVM Native Images](index.html)
  * [Advanced Native Images Topics](advanced-topics.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../reference/packaging/native-image/advanced-topics.html)!  
---|---  
  
# Advanced Native Images Topics

### Advanced Native Images Topics

  * Nested Configuration Properties
  * Converting a Spring Boot Executable Jar
  * Using Buildpacks
  * Using GraalVM native-image
  * Using the Tracing Agent
  * Launch the Application Directly
  * Custom Hints
  * Testing Custom Hints
  * Providing Hints Statically
  * Known Limitations



## Nested Configuration Properties

Reflection hints are automatically created for configuration properties by the Spring ahead-of-time engine. Nested configuration properties which are not inner classes, however, **must** be annotated with [`@NestedConfigurationProperty`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/NestedConfigurationProperty.html), otherwise they won’t be detected and will not be bindable.
    
    
    import org.springframework.boot.context.properties.ConfigurationProperties;
    import org.springframework.boot.context.properties.NestedConfigurationProperty;
    
    @ConfigurationProperties(prefix = "my.properties")
    public class MyProperties {
    
    	private String name;
    
    	@NestedConfigurationProperty
    	private final Nested nested = new Nested();
    
    	// getters / setters...
    
    	public String getName() {
    		return this.name;
    	}
    
    	public void setName(String name) {
    		this.name = name;
    	}
    
    	public Nested getNested() {
    		return this.nested;
    	}
    
    }
    
    Copied!

where `Nested` is:
    
    
    public class Nested {
    
    	private int number;
    
    	// getters / setters...
    
    	public int getNumber() {
    		return this.number;
    	}
    
    	public void setNumber(int number) {
    		this.number = number;
    	}
    
    }
    
    Copied!

The example above produces configuration properties for `my.properties.name` and `my.properties.nested.number`. Without the [`@NestedConfigurationProperty`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/NestedConfigurationProperty.html) annotation on the `nested` field, the `my.properties.nested.number` property would not be bindable in a native image. You can also annotate the getter method.

When using constructor binding, you have to annotate the field with [`@NestedConfigurationProperty`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/NestedConfigurationProperty.html):
    
    
    import org.springframework.boot.context.properties.ConfigurationProperties;
    import org.springframework.boot.context.properties.NestedConfigurationProperty;
    
    @ConfigurationProperties(prefix = "my.properties")
    public class MyPropertiesCtor {
    
    	private final String name;
    
    	@NestedConfigurationProperty
    	private final Nested nested;
    
    	public MyPropertiesCtor(String name, Nested nested) {
    		this.name = name;
    		this.nested = nested;
    	}
    
    	// getters / setters...
    
    	public String getName() {
    		return this.name;
    	}
    
    	public Nested getNested() {
    		return this.nested;
    	}
    
    }
    
    Copied!

When using records, you have to annotate the parameter with [`@NestedConfigurationProperty`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/NestedConfigurationProperty.html):
    
    
    import org.springframework.boot.context.properties.ConfigurationProperties;
    import org.springframework.boot.context.properties.NestedConfigurationProperty;
    
    @ConfigurationProperties(prefix = "my.properties")
    public record MyPropertiesRecord(String name, @NestedConfigurationProperty Nested nested) {
    
    }
    
    Copied!

When using Kotlin, you need to annotate the parameter of a data class with [`@NestedConfigurationProperty`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/context/properties/NestedConfigurationProperty.html):
    
    
    import org.springframework.boot.context.properties.ConfigurationProperties
    import org.springframework.boot.context.properties.NestedConfigurationProperty
    
    @ConfigurationProperties(prefix = "my.properties")
    data class MyPropertiesKotlin(
    	val name: String,
    	@NestedConfigurationProperty val nested: Nested
    )
    
    Copied!

__ |  Please use public getters and setters in all cases, otherwise the properties will not be bindable.   
---|---  
  
## Converting a Spring Boot Executable Jar

It is possible to convert a Spring Boot [executable jar](../../../specification/executable-jar/index.html) into a native image as long as the jar contains the AOT generated assets. This can be useful for a number of reasons, including:

  * You can keep your regular JVM pipeline and turn the JVM application into a native image on your CI/CD platform.

  * As `native-image` [does not support cross-compilation](https://github.com/oracle/graal/issues/407), you can keep an OS neutral deployment artifact which you convert later to different OS architectures.




You can convert a Spring Boot executable jar into a native image using Cloud Native Buildpacks, or using the `native-image` tool that is shipped with GraalVM.

__ |  Your executable jar must include AOT generated assets such as generated classes and JSON hint files.   
---|---  
  
### Using Buildpacks

Spring Boot applications usually use Cloud Native Buildpacks through the Maven (`mvn spring-boot:build-image`) or Gradle (`gradle bootBuildImage`) integrations. You can, however, also use [`pack`](https://buildpacks.io/docs/for-platform-operators/how-to/integrate-ci/pack/) to turn an AOT processed Spring Boot executable jar into a native container image.

First, make sure that a Docker daemon is available (see [Get Docker](https://docs.docker.com/installation/#installation) for more details). [Configure it to allow non-root user](https://docs.docker.com/engine/install/linux-postinstall/#manage-docker-as-a-non-root-user) if you are on Linux.

You also need to install `pack` by following [the installation guide on buildpacks.io](https://buildpacks.io/docs/for-platform-operators/how-to/integrate-ci/pack/#install).

Assuming an AOT processed Spring Boot executable jar built as `myproject-0.0.1-SNAPSHOT.jar` is in the `target` directory, run:
    
    
    $ pack build --builder paketobuildpacks/builder-jammy-java-tiny \
        --path target/myproject-0.0.1-SNAPSHOT.jar \
        --env 'BP_NATIVE_IMAGE=true' \
        my-application:0.0.1-SNAPSHOT
    
    Copied!

__ |  You do not need to have a local GraalVM installation to generate an image in this way.   
---|---  
  
Once `pack` has finished, you can launch the application using `docker run`:
    
    
    $ docker run --rm -p 8080:8080 docker.io/library/myproject:0.0.1-SNAPSHOT
    
    Copied!

### Using GraalVM native-image

Another option to turn an AOT processed Spring Boot executable jar into a native executable is to use the GraalVM `native-image` tool. For this to work, you’ll need a GraalVM distribution on your machine. You can either download it manually on the [Liberica Native Image Kit page](https://bell-sw.com/pages/downloads/native-image-kit/#/nik-22-17) or you can use a download manager like SDKMAN!.

Assuming an AOT processed Spring Boot executable jar built as `myproject-0.0.1-SNAPSHOT.jar` is in the `target` directory, run:
    
    
    $ rm -rf target/native
    $ mkdir -p target/native
    $ cd target/native
    $ jar -xvf ../myproject-0.0.1-SNAPSHOT.jar
    $ native-image -H:Name=myproject @META-INF/native-image/argfile -cp .:BOOT-INF/classes:`find BOOT-INF/lib | tr '\n' ':'`
    $ mv myproject ../
    
    Copied!

__ |  These commands work on Linux or macOS machines, but you will need to adapt them for Windows.   
---|---  
  
__ |  The `@META-INF/native-image/argfile` might not be packaged in your jar. It is only included when reachability metadata overrides are needed.   
---|---  
  
__ |  The `native-image` `-cp` flag does not accept wildcards. You need to ensure that all jars are listed (the command above uses `find` and `tr` to do this).   
---|---  
  
## Using the Tracing Agent

The GraalVM native image [tracing agent](https://www.graalvm.org/22.3/reference-manual/native-image/metadata/AutomaticMetadataCollection) allows you to intercept reflection, resources or proxy usage on the JVM in order to generate the related hints. Spring should generate most of these hints automatically, but the tracing agent can be used to quickly identify the missing entries.

When using the agent to generate hints for a native image, there are a couple of approaches:

  * Launch the application directly and exercise it.

  * Run application tests to exercise the application.




The first option is interesting for identifying the missing hints when a library or a pattern is not recognized by Spring.

The second option sounds more appealing for a repeatable setup, but by default the generated hints will include anything required by the test infrastructure. Some of these will be unnecessary when the application runs for real. To address this problem the agent supports an access-filter file that will cause certain data to be excluded from the generated output.

### Launch the Application Directly

Use the following command to launch the application with the native image tracing agent attached:
    
    
    $ java -Dspring.aot.enabled=true \
        -agentlib:native-image-agent=config-output-dir=/path/to/config-dir/ \
        -jar target/myproject-0.0.1-SNAPSHOT.jar
    
    Copied!

Now you can exercise the code paths you want to have hints for and then stop the application with `ctrl-c`.

On application shutdown the native image tracing agent will write the hint files to the given config output directory. You can either manually inspect these files, or use them as input to the native image build process. To use them as input, copy them into the `src/main/resources/META-INF/native-image/` directory. The next time you build the native image, GraalVM will take these files into consideration.

There are more advanced options which can be set on the native image tracing agent, for example filtering the recorded hints by caller classes, etc. For further reading, please see [the official documentation](https://www.graalvm.org/22.3/reference-manual/native-image/metadata/AutomaticMetadataCollection).

## Custom Hints

If you need to provide your own hints for reflection, resources, serialization, proxy usage and so on, you can use the [`RuntimeHintsRegistrar`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/aot/hint/RuntimeHintsRegistrar.html) API. Create a class that implements the [`RuntimeHintsRegistrar`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/aot/hint/RuntimeHintsRegistrar.html) interface, and then make appropriate calls to the provided [`RuntimeHints`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/aot/hint/RuntimeHints.html) instance:
    
    
    import java.lang.reflect.Method;
    
    import org.springframework.aot.hint.ExecutableMode;
    import org.springframework.aot.hint.RuntimeHints;
    import org.springframework.aot.hint.RuntimeHintsRegistrar;
    import org.springframework.util.ReflectionUtils;
    
    public class MyRuntimeHints implements RuntimeHintsRegistrar {
    
    	@Override
    	public void registerHints(RuntimeHints hints, ClassLoader classLoader) {
    		// Register method for reflection
    		Method method = ReflectionUtils.findMethod(MyClass.class, "sayHello", String.class);
    		hints.reflection().registerMethod(method, ExecutableMode.INVOKE);
    
    		// Register resources
    		hints.resources().registerPattern("my-resource.txt");
    
    		// Register serialization
    		hints.serialization().registerType(MySerializableClass.class);
    
    		// Register proxy
    		hints.proxies().registerJdkProxy(MyInterface.class);
    	}
    
    }
    
    Copied!

You can then use [`@ImportRuntimeHints`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/ImportRuntimeHints.html) on any [`@Configuration`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/context/annotation/Configuration.html) class (for example your [`@SpringBootApplication`](https://docs.spring.io/spring-boot/3.4.6/api/java/org/springframework/boot/autoconfigure/SpringBootApplication.html) annotated application class) to activate those hints.

If you have classes which need binding (mostly needed when serializing or deserializing JSON), you can use [`@RegisterReflectionForBinding`](https://docs.spring.io/spring-framework/reference/6.2/core/aot.html#aot.hints.register-reflection-for-binding) on any bean. Most of the hints are automatically inferred, for example when accepting or returning data from a [`@RestController`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/bind/annotation/RestController.html) method. But when you work with [`WebClient`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/reactive/function/client/WebClient.html), [`RestClient`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestClient.html) or [`RestTemplate`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/web/client/RestTemplate.html) directly, you might need to use [`@RegisterReflectionForBinding`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/aot/hint/annotation/RegisterReflectionForBinding.html).

### Testing Custom Hints

The [`RuntimeHintsPredicates`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/aot/hint/predicate/RuntimeHintsPredicates.html) API can be used to test your hints. The API provides methods that build a [`Predicate`](https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Predicate.html) that can be used to test a [`RuntimeHints`](https://docs.spring.io/spring-framework/docs/6.2.x/javadoc-api/org/springframework/aot/hint/RuntimeHints.html) instance.

If you’re using AssertJ, your test would look like this:
    
    
    import org.junit.jupiter.api.Test;
    
    import org.springframework.aot.hint.RuntimeHints;
    import org.springframework.aot.hint.predicate.RuntimeHintsPredicates;
    import org.springframework.boot.docs.packaging.nativeimage.advanced.customhints.MyRuntimeHints;
    
    import static org.assertj.core.api.Assertions.assertThat;
    
    class MyRuntimeHintsTests {
    
    	@Test
    	void shouldRegisterHints() {
    		RuntimeHints hints = new RuntimeHints();
    		new MyRuntimeHints().registerHints(hints, getClass().getClassLoader());
    		assertThat(RuntimeHintsPredicates.resource().forResource("my-resource.txt")).accepts(hints);
    	}
    
    }
    
    Copied!

### Providing Hints Statically

If you prefer, custom hints can be provided statically in one or more GraalVM JSON hint files. Such files should be placed in `src/main/resources/` within a `META-INF/native-image/*/*/` directory. The [hints generated during AOT processing](introducing-graalvm-native-images.html#packaging.native-image.introducing-graalvm-native-images.understanding-aot-processing) are written to a directory named `META-INF/native-image/{groupId}/{artifactId}/`. Place your static hint files in a directory that does not clash with this location, such as `META-INF/native-image/{groupId}/{artifactId}-additional-hints/`.

## Known Limitations

GraalVM native images are an evolving technology and not all libraries provide support. The GraalVM community is helping by providing [reachability metadata](https://github.com/oracle/graalvm-reachability-metadata) for projects that don’t yet ship their own. Spring itself doesn’t contain hints for 3rd party libraries and instead relies on the reachability metadata project.

If you encounter problems when generating native images for Spring Boot applications, please check the [Spring Boot with GraalVM](https://github.com/spring-projects/spring-boot/wiki/Spring-Boot-with-GraalVM) page of the Spring Boot wiki. You can also contribute issues to the [spring-aot-smoke-tests](https://github.com/spring-projects/spring-aot-smoke-tests) project on GitHub which is used to confirm that common application types are working as expected.

If you find a library which doesn’t work with GraalVM, please raise an issue on the [reachability metadata project](https://github.com/oracle/graalvm-reachability-metadata).

[Introducing GraalVM Native Images](introducing-graalvm-native-images.html) [Checkpoint and Restore With the JVM](../checkpoint-restore.html)
---
