Title: GraalVM Native Images :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/packaging/native-image/index.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/packaging/native-image/GraalVM_Native_Images_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/packaging/native-image/GraalVM_Native_Images_Spring_Boot.png
crawled_at: 2025-06-04T19:23:26.082446
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/packaging/native-image/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * [Reference](../../index.html)
  * [Packaging Spring Boot Applications](../index.html)
  * [GraalVM Native Images](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../reference/packaging/native-image/index.html)!  
---|---  
  
# GraalVM Native Images

[GraalVM Native Images](https://www.graalvm.org/native-image/) are standalone executables that can be generated by processing compiled Java applications ahead-of-time. Native Images generally have a smaller memory footprint and start faster than their JVM counterparts.

[Ahead-of-Time Processing With the JVM](../aot.html) [Introducing GraalVM Native Images](introducing-graalvm-native-images.html)
---
