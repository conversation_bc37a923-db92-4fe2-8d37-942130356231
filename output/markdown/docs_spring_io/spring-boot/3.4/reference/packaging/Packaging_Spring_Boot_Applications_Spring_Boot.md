Title: Packaging Spring Boot Applications :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/packaging/index.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/packaging/Packaging_Spring_Boot_Applications_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/packaging/Packaging_Spring_Boot_Applications_Spring_Boot.png
crawled_at: 2025-06-04T19:22:57.566772
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/packaging/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../index.html)
  * [Reference](../index.html)
  * [Packaging Spring Boot Applications](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../reference/packaging/index.html)!  
---|---  
  
# Packaging Spring Boot Applications

Spring Boot supports several technologies for optimizing applications for deployment, including [GraalVM native images](native-image/index.html), [Class Data Sharing](class-data-sharing.html), and [Checkpoint and Restore](checkpoint-restore.html).

Spring Boot applications can be packaged in Docker containers using techniques described in [Container Images](container-images/index.html).

[Test Utilities](../testing/test-utilities.html) [Efficient Deployments](efficient.html)
---
