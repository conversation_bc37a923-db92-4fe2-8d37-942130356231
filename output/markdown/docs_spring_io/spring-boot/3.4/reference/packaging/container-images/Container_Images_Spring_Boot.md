Title: Container Images :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/packaging/container-images/index.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/packaging/container-images/Container_Images_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/packaging/container-images/Container_Images_Spring_Boot.png
crawled_at: 2025-06-04T19:23:42.927241
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/packaging/container-images/index.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * [Reference](../../index.html)
  * [Packaging Spring Boot Applications](../index.html)
  * [Container Images](index.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../reference/packaging/container-images/index.html)!  
---|---  
  
# Container Images

Spring Boot applications can be containerized [using Dockerfiles](dockerfiles.html), or by [using Cloud Native Buildpacks](cloud-native-buildpacks.html) to create optimized docker compatible container images that you can run anywhere.

[Checkpoint and Restore With the JVM](../checkpoint-restore.html) [Efficient Container Images](efficient-images.html)
---
