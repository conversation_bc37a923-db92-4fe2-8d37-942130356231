Title: Dockerfiles :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/packaging/container-images/dockerfiles.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/packaging/container-images/Dockerfiles_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/packaging/container-images/Dockerfiles_Spring_Boot.png
crawled_at: 2025-06-04T16:03:25.509151
---
Search CTRL + k

### Dockerfiles

  * CDS
  * AOT cache



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/packaging/container-images/dockerfiles.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * [Reference](../../index.html)
  * [Packaging Spring Boot Applications](../index.html)
  * [Container Images](index.html)
  * [Dockerfiles](dockerfiles.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../reference/packaging/container-images/dockerfiles.html)!  
---|---  
  
# Dockerfiles

### Dockerfiles

  * CDS
  * AOT cache



While it is possible to convert a Spring Boot uber jar into a Docker image with just a few lines in the `Dockerfile`, using the [layering feature](efficient-images.html#packaging.container-images.efficient-images.layering) will result in an optimized image. When you create a jar containing the layers index file, the `spring-boot-jarmode-tools` jar will be added as a dependency to your jar. With this jar on the classpath, you can launch your application in a special mode which allows the bootstrap code to run something entirely different from your application, for example, something that extracts the layers.

__ |  The `tools` mode can not be used with a [fully executable Spring Boot archive](../../../how-to/deployment/installing.html) that includes a launch script. Disable launch script configuration when building a jar file that is intended to be used with the `extract` tools mode command.   
---|---  
  
Here’s how you can launch your jar with a `tools` jar mode:
    
    
    $ java -Djarmode=tools -jar my-app.jar
    
    Copied!

This will provide the following output:
    
    
    Usage:
      java -Djarmode=tools -jar my-app.jar
    
    Available commands:
      extract      Extract the contents from the jar
      list-layers  List layers from the jar that can be extracted
      help         Help about any command

The `extract` command can be used to easily split the application into layers to be added to the `Dockerfile`. Here is an example of a `Dockerfile` using `jarmode`.
    
    
    # Perform the extraction in a separate builder container
    FROM bellsoft/liberica-openjre-debian:24-cds AS builder
    WORKDIR /builder
    # This points to the built jar file in the target folder
    # Adjust this to 'build/libs/*.jar' if you're using Gradle
    ARG JAR_FILE=target/*.jar
    # Copy the jar file to the working directory and rename it to application.jar
    COPY ${JAR_FILE} application.jar
    # Extract the jar file using an efficient layout
    RUN java -Djarmode=tools -jar application.jar extract --layers --destination extracted
    
    # This is the runtime container
    FROM bellsoft/liberica-openjre-debian:24-cds
    WORKDIR /application
    # Copy the extracted jar contents from the builder container into the working directory in the runtime container
    # Every copy step creates a new docker layer
    # This allows docker to only pull the changes it really needs
    COPY --from=builder /builder/extracted/dependencies/ ./
    COPY --from=builder /builder/extracted/spring-boot-loader/ ./
    COPY --from=builder /builder/extracted/snapshot-dependencies/ ./
    COPY --from=builder /builder/extracted/application/ ./
    # Start the application jar - this is not the uber jar used by the builder
    # This jar only contains application code and references to the extracted jar files
    # This layout is efficient to start up and CDS/AOT cache friendly
    ENTRYPOINT ["java", "-jar", "application.jar"]
    
    Copied!

Assuming the above `Dockerfile` is in the current directory, your Docker image can be built with `docker build .`, or optionally specifying the path to your application jar, as shown in the following example:
    
    
    $ docker build --build-arg JAR_FILE=path/to/myapp.jar .
    
    Copied!

This is a multi-stage `Dockerfile`. The builder stage extracts the directories that are needed later. Each of the `COPY` commands relates to the layers extracted by the jarmode.

Of course, a `Dockerfile` can be written without using the `jarmode`. You can use some combination of `unzip` and `mv` to move things to the right layer but `jarmode` simplifies that. Additionally, the layout created by the `jarmode` is CDS and AOT cache friendly out of the box.

## CDS

If you want to additionally enable [CDS](../class-data-sharing.html#packaging.class-data-sharing.cds), you can use this `Dockerfile`:
    
    
    # Perform the extraction in a separate builder container
    FROM bellsoft/liberica-openjre-debian:24-cds AS builder
    WORKDIR /builder
    # This points to the built jar file in the target folder
    # Adjust this to 'build/libs/*.jar' if you're using Gradle
    ARG JAR_FILE=target/*.jar
    # Copy the jar file to the working directory and rename it to application.jar
    COPY ${JAR_FILE} application.jar
    # Extract the jar file using an efficient layout
    RUN java -Djarmode=tools -jar application.jar extract --layers --destination extracted
    
    # This is the runtime container
    FROM bellsoft/liberica-openjre-debian:24-cds
    WORKDIR /application
    # Copy the extracted jar contents from the builder container into the working directory in the runtime container
    # Every copy step creates a new docker layer
    # This allows docker to only pull the changes it really needs
    COPY --from=builder /builder/extracted/dependencies/ ./
    COPY --from=builder /builder/extracted/spring-boot-loader/ ./
    COPY --from=builder /builder/extracted/snapshot-dependencies/ ./
    COPY --from=builder /builder/extracted/application/ ./
    # Execute the CDS training run
    RUN java -XX:ArchiveClassesAtExit=application.jsa -Dspring.context.exit=onRefresh -jar application.jar
    # Start the application jar with CDS enabled - this is not the uber jar used by the builder
    # This jar only contains application code and references to the extracted jar files
    # This layout is efficient to start up and CDS friendly
    ENTRYPOINT ["java", "-XX:SharedArchiveFile=application.jsa", "-jar", "application.jar"]
    
    Copied!

This is mostly the same as the above `Dockerfile`. As the last steps, it creates the CDS archive by doing a training run and passes the CDS parameter to `java -jar`.

## AOT cache

If you want to additionally enable the [AOT cache](../class-data-sharing.html#packaging.class-data-sharing.aot-cache), you can use this `Dockerfile`:
    
    
    # Perform the extraction in a separate builder container
    FROM bellsoft/liberica-openjre-debian:24-cds AS builder
    WORKDIR /builder
    # This points to the built jar file in the target folder
    # Adjust this to 'build/libs/*.jar' if you're using Gradle
    ARG JAR_FILE=target/*.jar
    # Copy the jar file to the working directory and rename it to application.jar
    COPY ${JAR_FILE} application.jar
    # Extract the jar file using an efficient layout
    RUN java -Djarmode=tools -jar application.jar extract --layers --destination extracted
    
    # This is the runtime container
    FROM bellsoft/liberica-openjre-debian:24-cds
    WORKDIR /application
    # Copy the extracted jar contents from the builder container into the working directory in the runtime container
    # Every copy step creates a new docker layer
    # This allows docker to only pull the changes it really needs
    COPY --from=builder /builder/extracted/dependencies/ ./
    COPY --from=builder /builder/extracted/spring-boot-loader/ ./
    COPY --from=builder /builder/extracted/snapshot-dependencies/ ./
    COPY --from=builder /builder/extracted/application/ ./
    # Execute the AOT cache training run
    RUN java -XX:AOTMode=record -XX:AOTConfiguration=app.aotconf -Dspring.context.exit=onRefresh -jar application.jar
    RUN java -XX:AOTMode=create -XX:AOTConfiguration=app.aotconf -XX:AOTCache=app.aot -jar application.jar && rm app.aotconf
    # Start the application jar with AOT cache enabled - this is not the uber jar used by the builder
    # This jar only contains application code and references to the extracted jar files
    # This layout is efficient to start up and AOT cache friendly
    ENTRYPOINT ["java", "-XX:AOTCache=app.aot", "-jar", "application.jar"]
    
    Copied!

This is mostly the same as the above `Dockerfile`. As the last steps, it creates the AOT cache file by doing a training run and passes the AOT cache parameter to `java -jar`.

[Efficient Container Images](efficient-images.html) [Cloud Native Buildpacks](cloud-native-buildpacks.html)
---
