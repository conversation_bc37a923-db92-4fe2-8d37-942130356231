Title: Cloud Native Buildpacks :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/packaging/container-images/cloud-native-buildpacks.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/packaging/container-images/Cloud_Native_Buildpacks_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/packaging/container-images/Cloud_Native_Buildpacks_Spring_Boot.png
crawled_at: 2025-06-04T16:01:19.974954
---
Search CTRL + k

[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/packaging/container-images/cloud-native-buildpacks.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * [Reference](../../index.html)
  * [Packaging Spring Boot Applications](../index.html)
  * [Container Images](index.html)
  * [Cloud Native Buildpacks](cloud-native-buildpacks.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../reference/packaging/container-images/cloud-native-buildpacks.html)!  
---|---  
  
# Cloud Native Buildpacks

Docker images can be built directly from your Maven or Gradle plugin using [Cloud Native Buildpacks](https://buildpacks.io). If you’ve ever used an application platform such as Cloud Foundry or Heroku then you’ve probably used a buildpack. Buildpacks are the part of the platform that takes your application and converts it into something that the platform can actually run. For example, Cloud Foundry’s Java buildpack will notice that you’re pushing a `.jar` file and automatically add a relevant JRE.

With Cloud Native Buildpacks, you can create Docker compatible images that you can run anywhere. Spring Boot includes buildpack support directly for both Maven and Gradle. This means you can just type a single command and quickly get a sensible image into your locally running Docker daemon.

See the individual plugin documentation on how to use buildpacks with [Maven](../../../maven-plugin/build-image.html#build-image) and [Gradle](../../../gradle-plugin/packaging-oci-image.html).

__ |  The [Paketo Spring Boot buildpack](https://github.com/paketo-buildpacks/spring-boot) supports the `layers.idx` file, so any [layer customization](efficient-images.html#packaging.container-images.efficient-images.layering) that is applied to it will be reflected in the image created by the buildpacks.   
---|---  
  
__ |  In order to achieve reproducible builds and container image caching, buildpacks can manipulate the application resources metadata (such as the file "last modified" information). You should ensure that your application does not rely on that metadata at runtime. Spring Boot can use that information when serving static resources, but this can be disabled with `spring.web.resources.cache.use-last-modified`.   
---|---  
  
[Dockerfiles](dockerfiles.html) [Production-ready Features](../../actuator/index.html)
---
