Title: Efficient Container Images :: Spring Boot
Source: https://docs.spring.io/spring-boot/3.4/reference/packaging/container-images/efficient-images.html
HTML: html/docs_spring_io/spring-boot/3.4/reference/packaging/container-images/Efficient_Container_Images_Spring_Boot.html
Screenshot: screenshot/docs_spring_io/spring-boot/3.4/reference/packaging/container-images/Efficient_Container_Images_Spring_Boot.png
crawled_at: 2025-06-04T19:22:52.683364
---
Search CTRL + k

### Efficient Container Images

  * Layering Docker Images



[ Edit this Page ](https://github.com/spring-projects/spring-boot/blob/v3.4.6/spring-boot-project/spring-boot-docs/src/docs/antora/modules/reference/pages/packaging/container-images/efficient-images.adoc) [ GitHub Project ](https://github.com/spring-projects/spring-boot "GitHub") [ Stack Overflow ](https://stackoverflow.com/tags/spring-boot)

  * [Spring Boot](../../../index.html)
  * [Reference](../../index.html)
  * [Packaging Spring Boot Applications](../index.html)
  * [Container Images](index.html)
  * [Efficient Container Images](efficient-images.html)



__ |  For the latest stable version, please use [Spring Boot 3.5.0](../../../../reference/packaging/container-images/efficient-images.html)!  
---|---  
  
# Efficient Container Images

### Efficient Container Images

  * Layering Docker Images



It is easily possible to package a Spring Boot uber jar as a Docker image. However, there are various downsides to copying and running the uber jar as-is in the Docker image. There’s always a certain amount of overhead when running an uber jar without unpacking it, and in a containerized environment this can be noticeable. The other issue is that putting your application’s code and all its dependencies in one layer in the Docker image is not optimal. Since you probably recompile your code more often than you upgrade the version of Spring Boot you use, it’s often better to separate things a bit more. If you put jar files in the layer before your application classes, Docker often only needs to change the very bottom layer and can pick others up from its cache.

## Layering Docker Images

To make it easier to create optimized Docker images, Spring Boot supports adding a layer index file to the jar. It provides a list of layers and the parts of the jar that should be contained within them. The list of layers in the index is ordered based on the order in which the layers should be added to the Docker/OCI image. Out-of-the-box, the following layers are supported:

  * `dependencies` (for regular released dependencies)

  * `spring-boot-loader` (for everything under `org/springframework/boot/loader`)

  * `snapshot-dependencies` (for snapshot dependencies)

  * `application` (for application classes and resources)




The following shows an example of a `layers.idx` file:
    
    
    - "dependencies":
      - BOOT-INF/lib/library1.jar
      - BOOT-INF/lib/library2.jar
    - "spring-boot-loader":
      - org/springframework/boot/loader/launch/JarLauncher.class
      - ... <other classes>
    - "snapshot-dependencies":
      - BOOT-INF/lib/library3-SNAPSHOT.jar
    - "application":
      - META-INF/MANIFEST.MF
      - BOOT-INF/classes/a/b/C.class
    
    Copied!

This layering is designed to separate code based on how likely it is to change between application builds. Library code is less likely to change between builds, so it is placed in its own layers to allow tooling to re-use the layers from cache. Application code is more likely to change between builds so it is isolated in a separate layer.

Spring Boot also supports layering for war files with the help of a `layers.idx`.

For Maven, see the [packaging layered jar or war section](../../../maven-plugin/packaging.html#packaging.layers) for more details on adding a layer index to the archive. For Gradle, see the [packaging layered jar or war section](../../../gradle-plugin/packaging.html#packaging-executable.configuring.layered-archives) of the Gradle plugin documentation.

[Container Images](index.html) [Dockerfiles](dockerfiles.html)
---
